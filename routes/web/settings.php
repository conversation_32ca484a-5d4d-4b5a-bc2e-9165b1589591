<?php

use App\Constants\Subdomain;
use App\Controllers\Redirect\RedirectController;
use Cfa\Common\Application\Middleware\VerifyUserMfa;
use Cfa\Common\Domain\Gate\GateName;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\User\Mfa\UserMfa;

Route::group(
    [
        'middleware' => [VerifyUserMfa::class, 'can:' . GateName::CanAccessSettingsOrHistory->value],
    ],
    function (): void {
        require 'settings/common.php';
    },
);

Route::group(
    [
        'domain' => get_domain(Subdomain::PLANNER),
        'middleware' => [VerifyUserMfa::class, 'can:' . GateName::CanAccessSettingsOrHistory->value],
    ],
    function (): void {
        Route::get('/', [RedirectController::class, 'plannerSettings'])
            ->name('web.settings.planner.home');

        Route::group(['middleware' => 'can:' . PermissionName::HasAccessToSettings->name], function (): void {
            require 'settings/planner.php';
        });
    },
);

// Care settings.
Route::group(
    [
        'domain' => get_domain(Subdomain::CARE),
        'middleware' => [VerifyUserMfa::class, 'can:' . GateName::CanAccessSettingsOrHistory->value],
    ],
    function (): void {
        Route::get('/', [RedirectController::class, 'careSettings'])
            ->name('web.settings.care.home');

        Route::group(['middleware' => 'can:' . PermissionName::HasAccessToSettings->name], function (): void {
            require 'settings/care.php';
        });
    },
);

// Evaluation settings.
Route::group(
    [
        'domain' => get_domain(Subdomain::EVALUATION),
        'middleware' => [VerifyUserMfa::class, 'can:' . GateName::CanAccessSettingsOrHistory->value],
    ],
    function (): void {
        Route::get('/', [RedirectController::class, 'evaluationSettings'])
            ->name('web.settings.evaluation.home');

        Route::group(['middleware' => 'can:' . PermissionName::HasAccessToSettings->name], function (): void {
            require 'settings/evaluation.php';
        });
    },
);

// Security settings.
Route::group(
    [
        'domain' => get_domain(Subdomain::TMS),
        'middleware' => [VerifyUserMfa::class, 'can:' . GateName::CanAccessSettingsOrHistory->value],
    ],
    function (): void {
        Route::get('/', [RedirectController::class, 'tmsSettings'])
            ->name('web.settings.tms.home');

        Route::group(
            [
                'prefix' => 'security',
                'middleware' => [
                    'can:update,' . UserMfa::class,
                    'can:' . PermissionName::HasAccessToSettings->name,
                ],
            ],
            function (): void {
                require 'settings/security.php';
            },
        );

        Route::group(
            [
                'prefix' => 'history',
                'middleware' => 'can:' . GateName::CanAccessHistory->value,
            ],
            function (): void {
                require 'settings/history.php';
            },
        );
    },
);
