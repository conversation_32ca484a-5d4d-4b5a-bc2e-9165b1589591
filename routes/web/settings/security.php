<?php

use App\Controllers\Redirect\RedirectController;
use Cfa\Common\Application\Controllers\Settings\Security\Mfa\MfaConfirmResetController;
use Cfa\Common\Application\Controllers\Settings\Security\Mfa\MfaDenyResetController;
use Cfa\Common\Application\Controllers\Settings\Security\Mfa\MfaSchoolToggleController;
use Cfa\Common\Application\Controllers\Settings\Security\Permission\PermissionIndexController;
use Cfa\Common\Domain\User\Mfa\UserMfa;

Route::get('/', [RedirectController::class, 'securitySettings'])
    ->withoutMiddleware(['can:update,' . UserMfa::class])
    ->name('web.settings.security.home');

Route::get('permissions', PermissionIndexController::class)
    ->withoutMiddleware(['can:update,' . UserMfa::class])
    ->name('web.settings.security.permission.index');

Route::group(
    [
        'prefix' => 'mfa/users/{userToResetMfa}',
        'middleware' => 'can:reset,' . UserMfa::class . ',userToResetMfa',
    ],
    function (): void {
        Route::post('confirm-reset', MfaConfirmResetController::class)
            ->name('web.settings.security.mfa.confirm-reset');
        Route::post('deny-reset', MfaDenyResetController::class)
            ->name('web.settings.security.mfa.deny-reset');
    },
);

Route::post('toggle-for-school', MfaSchoolToggleController::class)
    ->name('web.settings.security.mfa.school-toggle');
