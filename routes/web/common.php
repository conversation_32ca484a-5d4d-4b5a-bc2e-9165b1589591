<?php

use App\Controllers\Redirect\BingelRedirectController;
use App\Controllers\Redirect\RedirectController;
use App\Controllers\System\LogController;
use Cfa\Care\Application\Controllers\CareInfo\CareInfoController;
use Cfa\Common\Application\Controllers\Dashboard\FollowUpSystemGoalDetailController;
use Cfa\Common\Application\Controllers\Dashboard\FollowUpSystemLastInputMomentController;
use Cfa\Common\Application\Controllers\Dashboard\FollowUpSystemLastQuotationController;
use Cfa\Common\Application\Controllers\Dashboard\FollowUpSystemStructureController;
use Cfa\Common\Application\Controllers\Dashboard\GroupDashboardFollowUpSystemsController;
use Cfa\Common\Application\Controllers\Dashboard\GroupDashboardIndexRedirectController;
use Cfa\Common\Application\Controllers\Dashboard\GroupDashboardPreschoolController;
use Cfa\Common\Application\Controllers\Dashboard\GroupDashboardSubjectController;
use Cfa\Common\Application\Controllers\Dashboard\PupilDashboardIndexRedirectController;
use Cfa\Common\Application\Controllers\Dashboard\PupilDashboardInfoSheetController;
use Cfa\Common\Application\Controllers\Dashboard\PupilDashboardPreschoolController;
use Cfa\Common\Application\Controllers\Dashboard\PupilDashboardSubjectController;
use Cfa\Common\Application\Controllers\Error\NoDataController;
use Cfa\Common\Application\Controllers\Error\NoGroupWithLicencesController;
use Cfa\Common\Application\Controllers\Export\DataTableExportController;
use Cfa\Common\Application\Controllers\FollowUpSystem\FollowUpSystemNoDataController;
use Cfa\Common\Application\Controllers\FollowUpSystem\FollowUpSystemRedirectController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\Avi\AviIndexController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\Avi\AviZoneController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\PredefinedFollowUpSystemCommentController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\PredefinedFollowUpSystemIndexController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\PredefinedFollowUpSystemScoreController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\FollowUpSystemGoalByGoalFetchController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\FollowUpSystemGoalByGoalIndexController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\FollowUpSystemGoalByPupilFetchController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\FollowUpSystemGoalByPupilIndexController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation\Comment\FollowUpSystemGoalCommentStoreController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation\Comment\FollowUpSystemGoalCommentSyncController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation\FollowUpSystemGoalQuotationStoreController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal\Quotation\FollowUpSystemGoalQuotationSyncController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\InputMoment\FollowUpSystemInputMomentDeleteController;
use Cfa\Common\Application\Controllers\FollowUpSystem\Standard\InputMoment\FollowUpSystemInputMomentSaveController;
use Cfa\Common\Application\Controllers\Notifications\CustomerNotificationConfirmReadController;
use Cfa\Common\Application\Controllers\Notifications\CustomerNotificationIndexController;
use Cfa\Common\Application\Controllers\School\SchoolController;
use Cfa\Common\Application\Controllers\User\UserController;
use Cfa\Common\Application\Controllers\UserDataTransfer\UserDataTransferIndexController;
use Cfa\Common\Application\Controllers\UserDataTransfer\UserDataTransferMailSentController;
use Cfa\Common\Application\Controllers\UserDataTransfer\UserDataTransferSendMailController;
use Cfa\Common\Application\Middleware\CheckForPupilChangeNotifications;
use Cfa\Common\Domain\Gate\GateName;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Application\Middleware\ReportPeriodsExistInCurrentSchoolYear;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;

Route::fallback([RedirectController::class, 'fallback'])
    ->name('web.common.fallback')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('no-group-with-licences', NoGroupWithLicencesController::class)
    ->name('web.common.fallback.no-group-with-licences')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('no-group', [NoDataController::class, 'group'])
    ->name('web.common.fallback.group')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('no-pupil', [NoDataController::class, 'pupil'])
    ->name('web.common.fallback.pupil')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);

Route::get('schools/{school}', [SchoolController::class, 'switch'])
    ->name('web.common.schools.switch');
Route::get('teachers/search', [UserController::class, 'searchTeachers'])
    ->name('web.common.teachers.search');
Route::get('pupils/search', [UserController::class, 'searchPupils'])
    ->name('web.common.pupils.search');
Route::group(
    ['prefix' => 'users/data-transfer'],
    function (): void {
        Route::get('/', UserDataTransferIndexController::class)
            ->name('web.common.users.data-transfer');
        Route::post('send-mail', UserDataTransferSendMailController::class)
            ->name('web.common.users.data-transfer.send-mail');
        Route::get('mail-sent', UserDataTransferMailSentController::class)
            ->name('web.common.users.data-transfer.mail-sent');
    },
);

Route::group(
    ['prefix' => 'customer-notifications'],
    function (): void {
        Route::get('/{customerNotificationType}', CustomerNotificationIndexController::class)
            ->name('web.common.customer-notifications.index');
        Route::post('/{customerNotificationType}/confirm-read', CustomerNotificationConfirmReadController::class)
            ->name('web.common.customer-notifications.confirm-read');
    },
);

// Bingel.
Route::get('profile', [BingelRedirectController::class, 'profile'])
    ->name('web.common.profile')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('help', [BingelRedirectController::class, 'help'])
    ->name('web.common.help')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('user-settings', [BingelRedirectController::class, 'userSettings'])
    ->name('web.common.user-settings')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('schools', [BingelRedirectController::class, 'schools'])
    ->name('web.common.schools')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);
Route::get('bingel-settings', [BingelRedirectController::class, 'settings'])
    ->name('web.settings.bingel.home')
    ->withoutMiddleware(['can:accessCfa,' . School::class]);

Route::post('data-table-print', DataTableExportController::class)
    ->name('web.common.data-table-print');

Route::post('log', LogController::class)->name('web.common.log');

Route::group(
    [
        'middleware' => [
            'smsdata',
            'badge',
            'message.inactive-pupils',
            CheckForPupilChangeNotifications::class,
            'can:' . GateName::CanAccessCareOrEvaluation->value,
        ],
    ],
    function (): void {
        Route::get('follow-up-systems/no-report-periods', [FollowUpSystemNoDataController::class, 'reportPeriods'])
            ->name('web.common.follow-up-systems.fallback.report-periods');
        Route::get('follow-up-systems/no-follow-up-systems', [FollowUpSystemNoDataController::class, 'followUpSystems'])
            ->name('web.common.follow-up-systems.fallback.follow-up-systems');
        Route::get(
            'follow-up-systems/no-follow-up-systems-for-group',
            [FollowUpSystemNoDataController::class, 'followUpSystemsForGroup'],
        )
            ->name('web.common.follow-up-systems.fallback.follow-up-systems-for-group');

        Route::group(
            [
                'middleware' => 'follow-up-systems.preference',
                'prefix' => '/follow-up-systems',
            ],
            function (): void {
                Route::get('/', FollowUpSystemRedirectController::class)
                    ->name('web.common.follow-up-systems.redirect');

                Route::group(
                    [
                        'middleware' => [
                            'can:view,' . FollowUpSystem::class . ',followUpSystem',
                            ReportPeriodsExistInCurrentSchoolYear::class,
                        ],
                    ],
                    function (): void {
                        Route::group(
                            [
                                'middleware' => [
                                    'can:evaluateUsingGoals,' . FollowUpSystem::class . ',followUpSystem',
                                    'can:view,reportPeriod',
                                ],
                                'prefix' => '/{followUpSystem}/report-period/{reportPeriod}',
                            ],
                            function (): void {
                                Route::get('/pupil', FollowUpSystemGoalByPupilIndexController::class)
                                    ->name('web.common.follow-up-systems.goals.by-pupil.index');

                                Route::get('/goal', FollowUpSystemGoalByGoalIndexController::class)
                                    ->name('web.common.follow-up-systems.goals.by-goal.index');

                                Route::post(
                                    '/input-moment/{followUpSystemInputMoment?}',
                                    FollowUpSystemInputMomentSaveController::class,
                                )->name('web.evaluation.follow-up-system.input-moment.save');

                                Route::delete(
                                    '/input-moment/{followUpSystemInputMoment}',
                                    FollowUpSystemInputMomentDeleteController::class,
                                )->name('web.evaluation.follow-up-system.input-moment.delete');
                            },
                        );
                        Route::group(
                            [
                                'middleware' => [
                                    'can:view,' . FollowUpSystem::class . ',followUpSystem',
                                    'can:evaluateUsingGoals,' . FollowUpSystem::class . ',followUpSystem',
                                    'follow-up-systems.preference',
                                ],
                            ],
                            function (): void {
                                Route::post(
                                    '{followUpSystem}/input-moment/{followUpSystemInputMoment}/goals/quotations/sync',
                                    FollowUpSystemGoalQuotationSyncController::class,
                                )->name('web.evaluation.follow-up-system.goals.quotations.sync');

                                Route::get(
                                    '{followUpSystem}/input-moment/{followUpSystemInputMoment}/pupils/{pupil}/goals',
                                    FollowUpSystemGoalByPupilFetchController::class,
                                )->name('web.common.follow-up-systems.goals.by-pupil.fetch')
                                    ->middleware(['can:viewPupil,' . User::class . ',pupil']);

                                Route::post(
                                    '{followUpSystem}/input-moment/{followUpSystemInputMoment}/goals/comments/sync',
                                    FollowUpSystemGoalCommentSyncController::class,
                                )->name('web.evaluation.follow-up-system.goals.comments.sync');

                                Route::group(
                                    [
                                        'middleware' => [
                                            'can:view,' . FollowUpSystemGoal::class . ',followUpSystemGoal',
                                        ],
                                        'prefix' => '/{followUpSystem}/goals/{followUpSystemGoal}/' .
                                            'input-moment/{followUpSystemInputMoment}',
                                    ],
                                    function (): void {
                                        Route::group(
                                            [
                                                'middleware' => [
                                                    'can:quotate,' . FollowUpSystemInputMoment::class
                                                    . ',followUpSystemInputMoment',
                                                    'can:viewPupil,' . User::class . ',pupil',
                                                    'can:quotate,' . FollowUpSystemGoal::class . ',followUpSystemGoal',
                                                ],
                                                'prefix' => '/pupils/{pupil}',
                                            ],
                                            function (): void {
                                                Route::post(
                                                    '/',
                                                    FollowUpSystemGoalQuotationStoreController::class,
                                                )->name('web.common.follow-up-systems.goals.quotations.store')
                                                    ->middleware(['mysql.write']);
                                                Route::post(
                                                    '/comment',
                                                    FollowUpSystemGoalCommentStoreController::class,
                                                )->name('web.common.follow-up-systems.goals.comment.store');
                                            },
                                        );
                                        Route::post(
                                            '/quotations',
                                            FollowUpSystemGoalByGoalFetchController::class,
                                        )->name('web.common.follow-up-systems.goals.by-goal.fetch');
                                    },
                                );
                            },
                        );
                    },
                );
            },
        );

        Route::group(
            [
                'middleware' => [
                    'can:view,' . FollowUpSystem::class . ',followUpSystem',
                    'follow-up-systems.preference',
                ],
            ],
            function (): void {
                Route::group(
                    [
                        'middleware' => 'can:accessPredefined,' . FollowUpSystem::class . ',followUpSystem',
                    ],
                    function (): void {
                        Route::get(
                            'predefined-follow-up-systems/{followUpSystem}/type/{followUpSystemSubType}',
                            PredefinedFollowUpSystemIndexController::class,
                        )->name('web.common.follow-up-systems.predefined')
                            ->middleware(['follow-up-systems.group']);
                        Route::post(
                            'predefined-follow-up-systems/{followUpSystem}/type/{followUpSystemSubType}' .
                            '/pupils/{pupil}/score',
                            PredefinedFollowUpSystemScoreController::class,
                        )->name('web.common.follow-up-systems.predefined.score')
                            ->middleware(
                                [
                                    'can:viewPupil,' . User::class . ',pupil',
                                    'can:editScores,' . FollowUpSystem::class . ',followUpSystem',
                                ],
                            );
                    },
                );

                Route::group(
                    [
                        'middleware' => 'can:accessPredefinedAvi,' . FollowUpSystem::class . ',followUpSystem',
                    ],
                    function (): void {
                        Route::get(
                            'avi-follow-up-systems/{followUpSystem}',
                            AviIndexController::class,
                        )->name('web.common.follow-up-systems.avi')
                            ->middleware(['follow-up-systems.group']);
                        Route::post(
                            'avi-follow-up-systems/{followUpSystem}/pupils/{pupil}/zone',
                            AviZoneController::class,
                        )->name('web.common.follow-up-systems.avi.zone')
                            ->middleware(['can:viewPupil,' . User::class . ',pupil']);
                    },
                );

                Route::post(
                    'predefined-follow-up-systems/{followUpSystem}/type/{followUpSystemSubType}' .
                    '/pupils/{pupil}/comment',
                    PredefinedFollowUpSystemCommentController::class,
                )->name('web.common.follow-up-systems.predefined.comment')
                    ->middleware(['can:viewPupil,' . User::class . ',pupil']);
            },
        );

        // Dashboard.
        Route::group(
            [
                'prefix' => 'dashboard',
            ],
            function (): void {
                Route::middleware('can:view,' . Group::class)
                    ->get(
                        'groups/preschool/{group?}',
                        GroupDashboardPreschoolController::class,
                    )
                    ->name('web.common.dashboard.groups.preschool');
                Route::middleware('can:view,' . Group::class)
                    ->get(
                        'groups/subject/{fusSubject}/{group?}',
                        [GroupDashboardSubjectController::class, 'invokeSubject'],
                    )
                    ->name('web.common.dashboard.groups.subject');

                Route::get('pupils/{pupil?}', PupilDashboardIndexRedirectController::class)
                    ->name('web.common.dashboard.pupils.index');

                Route::get('groups', GroupDashboardIndexRedirectController::class)
                    ->name('web.common.dashboard.groups.index');

                Route::group(
                    [
                        'middleware' => 'can:view,group',
                        'prefix' => 'groups/{group}',
                    ],
                    function (): void {
                        Route::get('follow-up-systems', GroupDashboardFollowUpSystemsController::class)
                            ->name('web.common.dashboard.groups.follow-up-systems');

                        Route::group(
                            [
                                'middleware' => 'can:view,' . FollowUpSystem::class . ',followUpSystem',
                                'prefix' => 'follow-up-systems/{followUpSystem}',
                            ],
                            function (): void {
                                Route::get(
                                    'last-quotation/{parentUid?}',
                                    FollowUpSystemLastQuotationController::class,
                                )->name('web.common.dashboard.groups.follow-up-systems.last-quotation');

                                Route::get(
                                    'last-input-moment/{parentUid?}',
                                    FollowUpSystemLastInputMomentController::class,
                                )->name('web.common.dashboard.groups.follow-up-systems.last-input-moment');

                                Route::get(
                                    'structure',
                                    FollowUpSystemStructureController::class,
                                )->name('web.common.dashboard.groups.follow-up-systems.structure');

                                Route::middleware(['can:viewPupil,' . User::class . ',pupil'])
                                    ->get(
                                        'pupils/{pupil}/goals/{followUpSystemGoal}/detail',
                                        FollowUpSystemGoalDetailController::class,
                                    )->name('web.common.dashboard.groups.follow-up-systems.pupils.goals.detail');
                            },
                        );
                    },
                );

                Route::group(
                    [
                        'middleware' => 'can:view,' . Group::class,
                    ],
                    function (): void {
                        Route::group(
                            [
                                'middleware' => [
                                    'can:viewPupil,' . User::class . ',pupil',
                                ],
                                'prefix' => 'pupils',
                            ],
                            function (): void {
                                Route::get('{pupil}/preschool', PupilDashboardPreschoolController::class)
                                    ->name('web.common.dashboard.pupils.preschool');
                                Route::get('{pupil}/info-sheet', PupilDashboardInfoSheetController::class)
                                    ->name('web.common.dashboard.pupils.info-sheet');
                                Route::get('{pupil}/subject/{fusSubject}', PupilDashboardSubjectController::class)
                                    ->name('web.common.dashboard.pupils.subject');
                            },
                        );
                    },
                );
            },
        );

        // Care info
        Route::group(
            [
                'middleware' => ['can:viewPupil,' . User::class . ',pupil'],
                'prefix' => 'overview',
            ],
            function (): void {
                Route::group(
                    [
                        'middleware' => [
                            'can:updatePupilCareInfo,' . User::class . ',pupil',
                            'can:viewCareData,' . User::class . ',pupil',
                        ],
                    ],
                    function (): void {
                        Route::get('pupils/{pupil}/info-sheet/edit', [CareInfoController::class, 'edit'])
                            ->name('web.common.care-info.edit');
                        Route::group(
                            [
                                'middleware' => ['can:canSaveCareInfo,' . User::class . ',pupil'],
                            ],
                            function (): void {
                                Route::post('pupils/{pupil}/info-sheet/save', [CareInfoController::class, 'save'])
                                    ->name('web.common.care-info.save');
                            },
                        );
                    },
                );
            },
        );
    },
);
