<?php

return [
    'delete-disable-reason-care-types' => 'Delete disabled because care type has dependencies.',
    'delete-disable-reason-care-themes' => 'Delete disabled because care theme has dependencies.',
    'delete-disable-reason-redicodis' => 'Delete disabled because redicodi has dependencies.',
    'delete-disable-reason-subjects' => 'Delete disabled because subject has dependencies.',
    'delete-disable-reason-diagnoses' => 'Delete disabled because diagnosis has dependencies.',
    'delete-disable-reason-blocks' => 'Delete disabled because block has dependencies.',
    'delete-disable-reason-quotation-systems' => [
        'text' => 'Delete disabled because quotation system has dependencies: :data',
        'follow-up-systems' => 'follow up systems',
        'evaluations' => 'evaluations',
        'subject-quotation-settings' => 'report settings',
    ],
    'delete-disable-reason-report-periods' => 'Already contains results',
    'edit-disable-reason-quotation-systems' => 'Edit disabled because quotation system has dependencies.',
    'period-expired-evaluation-test' => 'Period is closed',
    'quotation-system-type' => [
        'named' => [
            'title' => 'Grading with colors',
            'description' => [
                'line1' => 'With this method, you give a score based on colors.',
                'line2' => 'Traffic lights is a frequently used method.',
            ],
            'image' => '/img/named.png',
        ],
        'scaled' => [
            'title' => 'Grading based on scale',
            'description' => [
                'line1' => 'This method indicates a score by the number of times an icon is repeated.',
                'line2' => 'A common scale is one based on thumbs.',
            ],
            'image' => '/img/scaled.jpg',
        ],
        'icon_per_score' => [
            'title' => 'Grading with icons',
            'description' => [
                'line1' => 'This method uses a different icon for each score.',
                'line2' => 'A popular system is one based on smileys.',
            ],
            'image' => '/img/icon_per_score.jpg',
        ],
        'rating' => [
            'title' => 'Grading with rating',
            'description' => [
                'line1' => 'The rating method uses a scale that allows you to assign a score based on an icon.',
                'line2' => 'A popular rating system is the star system.',
            ],
            'image' => '/img/star-ratings.jpg',
        ],
    ],
    'mfa-school-toggle' => [
        'title' => 'Require two-factor authentication',
        'description' => [
            'paragraph1' => 'This setting ensures every access to Bingel Care is protected by two-factor authentication. Enabling this option offers the highest security.',
            'paragraph2' => 'Note that it may take up to 24 hours before the setting takes effect for the whole school.',
            'forbidden' => 'Activate your two-factor authentication to manage the two-factor authentication of other users.',
        ],
    ],
    'dashboard' => [
        'zones-not-dutch' => '* Zones for home language not Dutch',
        'not-dutch' => '* Home language not Dutch',
        'repeater' => '** Repeater',
        'not-dutch-repeater' => '*** Home language not Dutch en repeater',
    ],
    'share-status' => [
        'owner' => 'You own this collection. Your edits will be visible to anyone with access and you can manage this collection\'s sharing settings.',
        'co-author' => 'Edits you make to this shared file will be visible to other teachers with access.',
        'reader' => 'You will see edits made by author and co-authors. If you edit the file, changes will be visible only to you.',
        'calendar' => 'Edits you make will only be visible in the agenda.',
    ],
];
