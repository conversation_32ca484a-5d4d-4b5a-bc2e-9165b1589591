<?php

return [
    'menu' => [
        'my-account' => 'Account',
    ],

    'dashboard' => [
        'main' => 'Dashboard',
    ],

    'groups' => [
        'general' => 'General',
    ],

    'resources' => [
        'careers' => 'Career|Careers',
        'school' => 'School|Schools',
        'cmsuser' => 'CMS User|CMS Users',
        'user' => 'User|Users',
        'role' => 'Role|Roles',
        'permissionrolename' => 'Permission|Permissions',
        'permissionschool' => 'Permission|Permissions',
        'group' => 'Group|Groups',
        'schoolyear' => 'Schoolyear|Schoolyears',
        'featuretoggle' => 'Feature toggle|Feature toggles',
        'customernotification' => 'Customer Notification|Customer Notifications',
        'career' => 'Career|Careers',
        'pupil' => 'Student|Students',
        'staff' => 'Staff',
        'publisher' => 'Publisher|Publishers',
        'queryexport' => 'Export|Exports',
    ],

    'labels' => [
        'yes' => 'Yes',
        'no' => 'No',
        'users' => 'Users',
        'schools' => 'Schools',
        'is_classgroup' => 'Is class',
    ],

    'actions' => [
        'trigger_school' => 'Trigger SMD sync',
        'smd_sync_triggered' => 'SMD sync triggered',
        'group_linked_to_roster_for_user' => 'Group is linked to calendar items',
        'link_group_to_roster' => 'Change calendar',
        'export' => 'Export',
        'export_already_started' => 'Export already started',
        'export_started' => 'Export started',
        'export_school_data' => 'Export data for school',
        'export_pupil_data' => 'Export data for pupil',
        'export_school_data_already_pending' => 'Export already started',
        'export_school_data_started' => 'Export started',
        'export_school_data_only_one_school' => 'Export can only be started for one school',
        'impersonate_user' => 'Impersonate user',
        'impersonated' => 'Impersonated the user',
        'reset_user_mfa' => 'Reset two-factor authentication',
        'mfa_reset' => 'Reset two-factor authentication',
        'no_mfa_reset' => 'User does not have two-factor authentication',
        'mfa_reset_wrong_model' => 'Can not reset two-factor authentication for this model',
        'feature_toggle' => 'Change status of feature',
        'feature_activated' => 'Status of feature changed',
        'unblock_pupil' => 'Unblock pupil',
        'pupil_unblocked' => 'Pupil unblocked',
        'mark_blocked_pupil_as_seen' => 'Mark blocked pupil as seen',
        'blocked_pupil_marked_as_seen' => 'Blocked pupil marked as seen',
        'request_wisa_care_data' => 'Request Wisa care data',
        'requested_wisa_care_data' => 'Wisa care data requested',
        'export_group_changes' => 'Download Excel with changes',
        'trigger_wisa_resync' => 'Force Wisa resync',
        'wisa_resync_fail_data_exists' => 'Force Wisa resync',
        'merge_pupil' => 'Merge pupils',
        'pupils_merged' => 'Pupils are merged',
        'pupil_merge_failed' => 'Pupils could not be merged',
        'impersonate_deleted_user' => 'Delete user cannot be impersonated',
        'stop_wisa_sync_for_school' => 'Stop Wisa sync',
        'stop_wisa_sync_for_school_done' => 'Wisa sync stopped for school',
    ],

    'headings' => [
        'reset_2fa_heading' => 'Checking the box below, will force the user to re-scan the QR code upon next login.',
    ],

    'fields' => [
        'choose_group' => 'Choose a group',
        'update_roster' => 'Update roster',
        'update_calendar' => 'Update calendar',
        'reason_for_impersonate' => 'Reason for impersonation',
        'reason_for_pupil_merge' => 'Reason for merging pupils',
        'jira_ticket' => 'Jira/Salesforce Ticket',
        'school_id' => 'School Id',
        'school_partner_number' => 'School partner nr',
        'school' => 'School',
        'firstname' => 'Firstname',
        'lastname' => 'Lastname',
        'first_name' => 'First name',
        'second_name' => 'Second name',
        'email' => 'Email',
        'role' => 'Role',
        'permission' => 'Permission',
        'reset_2fa' => 'Reset 2FA',
        'end_date' => 'End date',
    ],

    'messages' => [
        'wisa_resync_fail_data_exists' => 'Could not delete pupil. Pupil has existing data attached',
        'wisa_resync_success' => 'Pupil has been queued for resync',
        'wisa_resync_delete_failed' => 'Could not delete pupil. ',
    ],

    'metrics' => [
        'ranges' => [
            'today' => 'Today',
            'days' => 'Days',
            'ytd' => 'Year To Date',
            'mtd' => 'Month To Date',
            'qtd' => 'Quarter To Date',
        ],
        'page_views' => 'Page views',
        'users' => 'Users',
        'active-care-users' => 'Active care users',
        'total-care-users' => 'Total amount of care users',
        'active-care-schools' => 'Active care schools',
        'total-care-schools' => 'Schools with care licence',
        'active-planner-schools' => 'Active planner schools',
        'active-planner-users' => 'Active planner users',
        'zill-schools' => 'Schools with KathOndVla hidden',
        'mfa-schools' => 'Schools with 2FA mandatory',
        'mfa-users-active' => 'Users with 2FA active',
        'mfa-users-requested' => 'Users with 2FA requested',
        'mfa-users-reset' => 'Users with 2FA reset',
    ],

    'filters' => [
        'is_active' => [
            'name' => 'Active',
            'only-active' => 'Only active',
            'only-inactive' => 'Only inactive',
        ],
        'pupil_status_filter' => [
            'name' => 'Pupil status',
        ],
        'has_care_evaluation_license' => 'Only care/evaluation schools',
    ],

    'sync_draft' => [
        'only_van_in_exception' => 'Synchronizing draft collections only works for VAN IN',
        'success' => ':collection synchronized successfully',
        'action' => 'Synchronize',
    ],

    'create_draft' => [
        'only_van_in_exception' => 'Creating draft collections only works for VAN IN',
        'draft_exists_exception' => 'A draft version already exists',
        'success' => 'Draft for :collection created successfully',
        'action' => 'Create draft version',
    ],

    'activate_licensed_collection' => [
        'action' => 'Trigger activations',
    ],
];
