<?php

return [
    'salutation' => 'Dear',
    'closing-salutation' => 'Kind regards',
    'closing-salutation-name' => 'Team Bingel',
    'footer' => 'Publisher VAN IN',
    'user-data-transfer' => [
        'subject' => 'Transfer lesson files',
        'email-body-user' => [
            'line1' => 'You requested to transfer your lesson files to your new Bingel account.',
            'line2' => 'Click the link above to complete this action.',
        ],
        'email-body-ict-coordinator' => [
            'line1' => 'Teacher ‘:name’ has just submitted a request to transfer their lesson files to a new Bingel account.',
            'line2' => 'If the teacher can still access their old Bingel account at your Bingel school, then no further action is required, and the teacher in question can transfer their lesson files without your intervention.',
            'line3' => 'It is possible that the teacher no longer has access to the email address (:email1 or :email2) that was linked to the Bingel account.',
            'line4' => 'In that case, you can still transfer the teachers lesson files by clicking on the link above.',
            'line5' => [
                'pre' => 'If you have any questions, feel free to contact us via ',
                'post' => '.',
            ],
        ],
    ],
    'mfa' => [
        'user' => [
            'reset-requested' => [
                'subject' => 'Two-factor authentication reset received',
                'body' => [
                    'paragraph1' => 'Your reset request for two-factor authentication has been received and your school administrator has been notified.',
                    'paragraph2' => 'Did you not request a reset? Please contact your school administrator immediately so they can deny the request. We also recommend you to change your password in this situation.',
                ],
            ],
            'reset-confirmed' => [
                'subject' => 'Two-factor authentication reset approved',
                'body' => [
                    'line1' => 'Your request to reset your two-factor authentication has been approved.',
                    'line2' => 'Log in to Bingel Care to start the configuration.',
                ],
            ],
            'reset-denied' => [
                'subject' => 'Two-factor authentication reset denied',
                'body' => [
                    'paragraph1' => 'Your request to reset your two-factor authentication has been denied. Please contact your school administrator if this was unexpected.',
                    'paragraph2' => 'You have access again to Bingel Care with the two-factor authentication that you previously set up.',
                ],
            ],
        ],
        'administrator' => [
            'reset-requested' => [
                'subject' => 'Request to reset two-factor authentication',
                'body' => [
                    'paragraph1' => 'There is a new request to reset two-factor authentication by :userToReset in :schoolName.',
                    'paragraph2' => 'Please verify the request and approve it if confirmed. The user will not have access to Bingel Care before you approve the request.',
                    'paragraph3' => [
                        'pre' => 'Please go to the ',
                        'text' => 'configuration page',
                        'post' => ' to manage the two-factor authentication requests.',
                    ],
                ],
            ],
        ],
    ],
];
