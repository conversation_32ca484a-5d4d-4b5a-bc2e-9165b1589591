{"ACTION": {"ADD": "Add", "CANCEL": "Cancel", "DELETE": {"CONFIRM": {"ACTION": "I am sure"}, "DELETE": "Delete"}, "DUPLICATE": "Duplicate", "OK": "Ok", "SAVE": {"CHANGES": "Save changes", "SAVE": "Save"}, "SELECT": "Select", "SEARCH": "Search..."}, "MESSAGE": {"FORM_INVALID": "There was a problem with the form. Please correct the mistakes.", "FORM_SAVED": "The changes were saved successfully."}, "MODEL": {"DURATION": {"FROM": "From", "TO": "to"}, "SETTINGS": "Settings", "TIME": {"MINUTES": "minutes"}}, "MODULE": {"CALENDARS": {"CALENDAR": {"CONTEXT": {"MY_CONTEXT": {"MY_CONTEXT": "My calendars", "PERSONAL_CALENDAR": "Personal calendar", "SCHOOL_CALENDAR": "School calendar", "COLLEAGUE_CALENDAR": "Colleague calendar"}, "SYSTEM_CONTEXT": {"SYSTEM_CALENDAR": "System calendar", "SYSTEM_CONTEXT": "System calendars"}, "SCHOOL_CONTEXT": "@:MODULE.CALENDARS.CALENDAR.CONTEXT.MY_CONTEXT.SCHOOL_CALENDAR"}, "ITEM": {"BEGIN": "@:MODULE.ROSTERS.ROSTER.ITEM.BEGIN", "DATE": {"DATE": "Date", "FROM": "Begin date", "TO": "End date"}, "DURATION": "@:MODULE.ROSTERS.ROSTER.ITEM.DURATION", "REPEAT": {"REPEAT": "Repeat", "REPEAT_END": "Repeat until", "NEVER": "Don't repeat", "WEEKDAY": "Daily", "WEEK": "Weekly", "BIWEEKLY": "Fortnightly", "UPDATE": {"TITLE": "It<PERSON> is part of a series", "WARNING": "Change all items in series starting from {from}?", "CONFIRM": "Yes", "CANCEL": "No, only this item"}, "CHANGE_TYPE_WARNING": "You cannot make a lesson out of an activity which is part of a series."}, "SELECTED": {"EMPTY": "Select a course or activity"}, "GROUP": {"PLACEHOLDER": "@:MODULE.ROSTERS.ROSTER.ITEM.GROUP.PLACEHOLDER", "GROUP": "@:MODULE.ROSTERS.ROSTER.ITEM.GROUP", "EMPTY": "@:MODULE.ROSTERS.ROSTER.ITEM.GROUP.EMPTY"}, "TYPE": {"CHOICE": "I plan a(n)", "ACTIVITY": {"ACTIVITY": "Activity", "ALL_DAY": "All day", "MULTI_DAY_DURATION": "from {from} to {to}", "LOCATION": {"LOCATION": "Location", "PLACEHOLDER": "Add a location..."}, "NOTES": {"NOTES": "Notes", "PLACEHOLDER": "Add some notes..."}, "TITLE": {"PLACEHOLDER": "Add a title...", "TITLE": "Title"}, "TYPE": {"ADMINISTRATION": "Administration", "ABSENT": "Abscent", "EMPTY": "Not defined", "EVALUATIONS": "Evaluations", "NO_CLASS": "No class", "OTHER_WITH_PARTICIPANTS": "Other with participants", "OTHER_RELEVANT_FOR_PUPIL": "Other also relevant for pupils/parents", "DELIBERATION": "Deliberation", "PLACEHOLDER": "Choose A category...", "BREAK": "Break", "SPORT_AND_CULTURE": "Sport and culture", "INTERNSHIP": "Internship", "THEME_PROJECT": "Theme project", "SURVEILLANCE": "Surveillance", "TYPE": "Category", "TRIP": "Trip", "TRAINING": "Training", "CARE_AND_SUPPORT": "Care and support"}}, "COURSE": {"COURSE": "Course", "ARRANGEMENT": "Arrangement", "ACTIVITY": "Activity", "LOCATION": {"LOCATION": "Location", "PLACEHOLDER": "Add location..."}, "ROW": {"ADD": "Add course topic", "DELETE": {"CONFIRM": {"MESSAGE": "Are you sure you want to delete course topic {name}? Linked local record will be deleted. This can't be undone.", "TITLE": "Delete course topic {name}"}, "DELETE": "Delete course topic"}, "PLACEHOLDER": "Add course topic...", "RECORD": {"LINK": {"LINK": "Link record", "NEW": "Create new record", "SELECTED": "Link selected record", "SELECTED_MULTIPLE": "Link {count} selected records", "FINDER": {"SEARCH": {"SEARCH": "@:MODULE.COLLECTIONS.RECORD.GOALS.FINDER.SEARCH", "PLACEHOLDER": "Search record"}, "RECORDS": "Records"}}}, "TO": {"COURSE": "To record", "ANNOUNCED_EVALUATION": "To announced evaluation", "EVALUATION": "To evaluation", "UNANNOUNCED_EVALUATION": "To unannounced evaluation"}}, "ROWS": {"EMPTY": "No topics yet", "ROWS": "Topics"}, "SUBJECT": {"PLACEHOLDER": "@:MODULE.ROSTERS.ROSTER.ITEM.SUBJECT.PLACEHOLDER", "SUBJECT": "@:MODULE.ROSTERS.ROSTER.ITEM.SUBJECT"}, "BLOCK": {"PLACEHOLDER": "@:MODULE.ROSTERS.ROSTER.ITEM.BLOCK.PLACEHOLDER", "BLOCK": "@:MODULE.ROSTERS.ROSTER.ITEM.BLOCK"}}, "TYPE": "Type"}}, "OPTIONS": {"OPTIONS": "Options", "SHOW_RECORD_DETAILS": {"SHOW_RECORD_DETAILS": "Show record details", "TITLE": "Filter details", "SELECT_ALL": "Select all", "DESELECT_ALL": "Deselect all", "CONFIRM": "confirm", "DURATION": "@:MODULE.COLLECTIONS.RECORD.PURPOSE.COURSE.DURATION", "TEACHER_GOALS": "@:MODULE.COLLECTIONS.RECORD.PURPOSE.COURSE.TEACHER_GOALS", "GOALS": "@:MODULE.COLLECTIONS.RECORD.GOALS.GOALS", "START_SITUATION": "@:MODULE.COLLECTIONS.RECORD.START_SITUATION", "LESSON_STEPS": "@:MODULE.COLLECTIONS.RECORD.LESSON_STEPS", "MATERIALS": "@:MODULE.COLLECTIONS.RECORD.MATERIALS.MATERIALS", "REFLECTION": "@:MODULE.COLLECTIONS.RECORD.REFLECTION", "DIFFERENTIATION": "@:MODULE.COLLECTIONS.RECORD.DIFFERENTIATION", "WORK_METHODS": "@:MODULE.COLLECTIONS.RECORD.WORK_METHODS"}, "SHOW_WEEKENDS": "Show weekends", "ZOOM_IN": "Zoom in to half day", "PRINT": "Print", "GO_TO_DATE": "Go to date", "COLOR_MODES": {"COLOR_MODES": "Colors", "AUTO": "Automatic", "COURSE": "Per Course", "GROUP": "Per Group", "CALENDAR": "Per Calendar"}}, "PLANNER": {"DATE_HEADER_FORMAT": "dd DD/MM", "DAY_HEADER_FORMAT": "dddd", "HOUR_HEADER_FORMAT": "HH:mm", "LIST_DATE_HEADER_FORMAT": "dddd D MMMM", "SHOW_HIDE_CALENDARS": "Show or hide calendars"}, "GROUPS": {"RECENT_GROUPS": "Recent groups", "ALL_GROUPS": "All groups"}, "ROSTER": {"EMPTY": {"EMPTY": "You don't have any personal items yet. You can apply a schedule to create them for the whole week.", "NO_ROSTERS": {"MESSAGE": "You don't have any schedules yet. {action}", "ACTION": "Make a new schedule here."}}, "PLACEHOLDER": "Select a schedule"}, "VIEW": {"DAY": "Day", "LIST": {"LIST": "List", "HEADER": "Week from {from} to {to}"}, "WEEK": "Week"}, "PLANNER_MODE": {"MY_CALENDAR": "My calendar", "MY_GROUPS": "My groups", "MY_COLLEAGUES": "My colleagues"}}, "BULK": {"SELECT_ALL": "Select all", "SELECTION": "{count} selected item | {count} selected items", "DELETE": {"DELETE": "@:ACTION.DELETE.DELETE", "TITLE": "Delete multiple items", "MESSAGE": "Are you sure you want to delete {selectedItems} selected items? This can't be undone.", "FORBIDDEN": "You have selected items for which you lack the necessary permissions to delete them.", "CLEAR_FORBIDDEN": "Deselect these items"}, "DUPLICATE": "Copy to my calendar", "LINK_GROUPS": "Link groups", "LINK_RECORDS": "Link records"}}, "COLLECTIONS": {"COLLECTION": {"CHAPTER": {"ADD": {"DEFAULT": "Add chapter"}, "CHAPTER": "Chapter", "CHAPTERS": "Chapters", "DEFAULT_NAME": "Chapter {chapterNumber}", "DELETE": {"CONFIRM": {"MESSAGE": "Are you sure you want to delete chapter \"{name}\" permanently?", "TITLE": "Delete chapter {name}"}, "CAN_NOT_DELETE_CONTAINS_RECORDS": "You cannot delete a chapter that contains lesson files.", "CAN_NOT_DELETE_INHERITED": "You cannot delete a chapter if it was not created by you."}, "LESSON": "record", "LESSONS": "records", "SELECT": "Select chapter"}, "CHAPTERS": {"EMPTY": "No chapters yet"}, "DELETE": {"CONFIRM": {"MESSAGE": "Are you sure you want to delete collection \"{name}\" permanently?", "TITLE": "Delete collection {name}"}, "DELETE": "Remove"}, "ARCHIVE": "Archive", "UNARCHIVE": "Unarchive", "NAME": "Name", "TARGET_AUDIENCE": "Target audience", "ATTACHMENTS": "Attachments", "ATTACHMENTS_UPLOAD": "Upload new attachment", "MULTIPLE_AUDIENCES": "Multiple targetaudiences", "SETTINGS": "Collection settings", "TEMPLATE": "Record template", "BRAINSTORM": "Brainstorm", "ZILL_PERSONAL": "This record contains personalized ZILL-goals", "ZILL_GENERAL": "This record contains ZILL-goals", "FOCUSGOALS": "Focus", "COVER": {"CHANGE": {"TITLE": "Change cover image"}}, "EDIT": "Edit name", "DUPLICATE": "Duplicate", "DUPLICATE_TO_COLLECTION": "Duplicate to another collection", "DUPLICATE_TO_COLLECTION_SUCCESS": "{record} has been duplicated to {collection} - {chapter}", "ARCHIVE_SUCCESS": "{name} has been archived", "DEARCHIVE_SUCCESS": "{name} has been dearchived"}, "COLLECTIONS": {"VIEW_MODE": {"ARCHIVED": "Archived collections"}}, "RECORD": {"CONTEXT": {"CALENDAR": "Course of {begin}"}, "LINKED_CALENDAR_ITEMS": "Planned on", "GOAL": {"CODE": "Code", "DESCRIPTION": "Description"}, "GOALS": {"EMPTY": "No goals linked yet", "FINDER": {"SEARCH": {"SEARCH": "Quicksearch", "PLACEHOLDER": "Name, description, code..."}, "FILTER": {"GRADE": {"GRADE": "Grade", "ALL": "Show everything", "REPETITION": {"FALSE": {"FALSE": "Hide repetition", "TIP": "Show all goals for this grade except those that can be repeated"}, "TRUE": {"TRUE": "Show repetition", "TIP": "Show all goals for this grade"}}}}, "CURRICULUM": {"CURRICULUM": "Curriculum", "SELECT": "Select a domain, subsection or goal"}, "FINDER": "Goal finder", "NETWORK": {"SELECT": "Select a network"}}, "GOALS": "Curriculum goals", "LINK": "Link goals"}, "DELETE": {"CONFIRM": {"SHARED": "Warning! This collection is used by 1 other colleague. This lesson file will also be deleted for them. | Warning! This collection is used by {count} other colleagues. This lesson file will also be deleted for them.", "MESSAGE": "Are you sure you want to delete record \"{name}\" permanently?", "TITLE": "Delete record {name}"}, "DISABLED_NOT_OWNER": "You cannot delete a record if it was not created by you."}, "DIFFERENTIATION": "Differentiation", "LESSON_STEPS": "Lesson steps", "MANUAL": "Instructions and practice", "MATERIALS": {"MATERIALS": "Didactic materials", "TARGET": {"CLASSROOM": {"CLASSROOM": "In the classroom", "INFO": "Material available in the classroom.", "OPTION": "Classroom"}, "ONLINE": {"INFO": "Material available online.", "ONLINE": "Online", "OPTION": "Online"}, "PUPIL": {"INFO": "Material that pupils have to bring.", "OPTION": "Pupils", "PUPIL": "For pupils"}, "TARGET": "Target", "TEACHER": {"INFO": "Material provided by the teacher.", "OPTION": "Teacher", "TEACHER": "For the teacher"}}, "TYPE": {"FILE": {"ADD": "Add file", "EDIT": "Edit file", "BROWSE": "Choose a file", "DROP": "or drag it here", "DESCRIPTION": "Description", "UPLOAD": "Upload new file"}, "LINK": {"ADD": "Add link", "EDIT": "Edit link", "PREVIEW": "Preview", "PROVIDER": "Provider", "DESCRIPTION": "Description", "URL": "URL"}, "PHYSICAL": {"ADD": "Add physical material", "EDIT": "Edit physical material", "DESCRIPTION": "Description"}}, "DELETE": {"CONFIRM": {"MESSAGE": "Are you sure you want to delete material {name} permanently?", "TITLE": "Delete material{name}"}}}, "NAME": {"DEFAULT": "New local record", "NAME": "Name"}, "PURPOSE": {"COURSE": {"COURSE": "Record", "DURATION": "Duration", "TEACHER_GOALS": "Teacher Course goals"}, "EVALUATION": {"DURATION": "Evaluaton duration", "EVALUATION": "Evaluation  Record", "TEACHER_GOALS": "Teacher Evaluation goals"}}, "EXPERIENCE_SITUATION": {"EXPERIENCE_SITUATION": "Experience situation", "NONE": "Select an experience situation", "MEET": "Meeting", "PLAYING_INDEPENDENTLY": "Playing independently", "EXPERIENCE_EXPLORATION": "Experience exploration", "DEVELOPMENT_SUPPORTED_LEARNING": "Development supported learning"}, "START_SITUATION": "Start situation", "REFLECTION": "Reflection", "CREATE_BINGEL_NOTE": "Create a Bingel care note", "WORK_METHODS": "Work methods", "EVALUATION": "Evaluation", "EVALUATION_DESCRIPTION": "This means you are turning this record into an evaluation. The goals in this activity will be shown as \"evaluated\" in the  goal rapportage.", "RECORDS": "records", "INSTRUCTIONS_PRACTICE": {"INSTRUCTION_MOVIE_1": "Instruction movie 1", "INSTRUCTION_MOVIE_2": "Instruction movie 2", "INSTRUCTION_MOVIE_3": "Instruction movie 3", "BOARDBOOK": "Boardbook", "MANUAL": "Manual"}}, "RECORDS": {"EMPTY": "No records yet"}}, "REPORTS": {"MODULE": "Reports", "DASHBOARD": {"MY_SCHOOLS": "My schools", "NO_SUMMARY": "No summary available for this group.", "INFO": "The graphs below only show goals that are intended for the age of the class group.", "NO_GOALS_ACHIEVED": "No goals achieved for:", "HOW_TO_ACHIEVE_GOALS": "Add goals for these learning areas to records in your calendar.", "SHOW_ALL_GOALS": "Show all planned goals for this group", "SHOW_ALL_GOALS_AND_ATTITUDES": "Show all planned goals and attitudes for this group.", "SHOW_ALL_GOALS_AND_ATTITUDES_INFO": "Attitudes apply at all ages and serve as a basic attitude for all goals", "NO_GROUPS": "@:MODULE.ROSTERS.ROSTER.ITEM.GROUP.EMPTY", "SHOW_ALL_GROUPS": "Show all groups", "SCHOOLYEAR": "Schoolyear", "GOALS": "Curriculum goals", "ZILL": "<PERSON><PERSON>", "ZILL_NO_GOALS": "You don't have any zill goals in the selected period", "ZILL_DETAILS": "See all planned goals"}, "REPORT": {"FILTERS": "Filters", "DOMAIN": "Domain", "THEME": "Theme", "ALL_DOMAINS": "All domains", "ALL_THEMES": "All themes", "ALL_LEARNING_PATHS": "All learning paths", "LEARNING_PATHS": "Learning paths", "CLUSTER": "Cluster", "DATE": {"DATE": "Date", "FROM": "@:MODULE.CALENDARS.CALENDAR.ITEM.DATE.FROM", "TO": "@:MODULE.CALENDARS.CALENDAR.ITEM.DATE.TO"}, "REPORT_TYPE": "Type of report", "REPORT_TYPE_NEG": "Negative", "REPORT_TYPE_POS": "Positive", "TEACHER": "Teacher", "ALL_TEACHERS": "All teachers", "LEARNING_AREA": "Learning area", "ALL_LEARNING_AREAS": "All learning areas", "PRINT": {"TITLE": "Goal report - {school} - {group}", "TYPE_POS": "Positive report", "TYPE_NEG": "Negative report", "PERIOD": "Period: from {from} until {to}"}, "EMPTY": "No goals found for the given criteria.", "RESET": "Reset criteria"}}, "ROSTERS": {"MODULE": "Schedules", "NO_GROUP_IN_ROSTER": {"MESSAGE": "Link groups in your roster to see goal reporting. {action}", "ACTION": "See manual"}, "ROSTER": {"ACTIONS": {"EDIT": "Edit schedule settings", "DUPLICATE": "Copy schedule", "DELETE": "Delete schedule"}, "ADD": "Add schedule", "APPLY": "Apply schedule", "DELETE": {"CONFIRM": {"MESSAGE": "Are you sure you want to delete the roster \"{name}\"? This can't be undone.", "TITLE": "Delete roster {name}"}}, "DUPLICATE": "Duplicate schedule", "EDIT": "Edit schedule", "ITEM": {"BEGIN": "Start at", "DURATION": "Duration", "GROUP": {"PLACEHOLDER": "Choose a group...", "GROUP": "Group", "EMPTY": "No groups available"}, "SUBJECT": {"SUBJECT": "Subject"}, "BLOCK": {"BLOCK": "Block"}, "CHANGE_TYPE": {"TITLE": "Type change", "MESSAGE": "Watch out: you are about to change the type of the item. Some filled-in fields will be erased."}}, "NAME": {"DEFAULT": "Standard week", "NAME": "Name", "PLACEHOLDER": "Give the roster a name ..."}, "PERIOD": {"INFO": "Enter an optional start- and enddate to use the roster within that given date.", "PERIOD": "Period", "FROM": "@:MODEL.DURATION.FROM", "TO": "@:MODEL.DURATION.TO", "UNLIMITED": "Unlimited validity"}}, "ROSTERS": {"APPLY": "Apply schedules", "EMPTY": "No schedules yet"}, "BULK": {"SELECT_ALL": "@:MODULE.CALENDARS.BULK.SELECT_ALL", "SELECTION": "@:MODULE.CALENDARS.BULK.SELECTION", "DELETE": {"DELETE": "@:MODULE.CALENDARS.BULK.DELETE.DELETE", "TITLE": "@:MODULE.CALENDARS.BULK.DELETE.TITLE", "MESSAGE": "@:MODULE.CALENDARS.BULK.DELETE.MESSAGE", "FORBIDDEN": "@:MODULE.CALENDARS.BULK.DELETE.FORBIDDEN", "CLEAR_FORBIDDEN": "@:MODULE.CALENDARS.BULK.DELETE.CLEAR_FORBIDDEN"}, "DUPLICATE": "Copy to my roster"}}, "SETTINGS": {"VISIBILITY": {"ZILL": {"REPORTS": {"CATEGORY_TITLE": "How <PERSON><PERSON> is the activiy of this group", "LEARNINGAREA_TITLE": "In how much % of the lessons/activities are the development fields used?", "DOMAIN": {"TITLE": "Usage of domains", "SUBTITLE": "Which domains are used most/least?", "ALL_DOMAINS": "All learningareas", "ASCENDING": "Ascending", "DESCENDING": "Descending", "NO_PLANNED_DOMAINS": "No planned domains in the current learningarea"}}}}}}, "TOUR": {"ROSTER": {"TITLE_CREATE_ROSTER": "You don't have any rosters yet", "CREATE_ROSTER": "Click here to create a roster."}}, "TOOLTIPS": {"COLLECTION": {"EDIT": "Change name", "DELETE": "Delete"}}}