@import 'vuetify/settings';

@mixin text-color($color_value, $lightness_amount) {
  color: scale-color($color_value, $lightness: calc($lightness_amount/10 * 100%)) !important;
  caret-color: scale-color($color_value, $lightness: calc($lightness_amount/10 * 100%)) !important;
}

@mixin background-color($color_value, $lightness_amount) {
  background-color: scale-color(
    $color_value,
    $lightness: calc($lightness_amount/10 * 100%)
  ) !important;
}

@mixin border-color($color_value, $lightness_amount) {
  border-color: scale-color($color_value, $lightness: calc($lightness_amount/10 * 100%)) !important;
  border-top-color: scale-color(
    $color_value,
    $lightness: calc($lightness_amount/10 * 100%)
  ) !important;
  border-bottom-color: scale-color(
    $color_value,
    $lightness: calc($lightness_amount/10 * 100%)
  ) !important;
  border-left-color: scale-color(
    $color_value,
    $lightness: calc($lightness_amount/10 * 100%)
  ) !important;
  border-right-color: scale-color(
    $color_value,
    $lightness: calc($lightness_amount/10 * 100%)
  ) !important;
}

$zill-colors: (
  'zill-color-1': #e9252d,
  'zill-color-2': #ed7928,
  'zill-color-3': #f79739,
  'zill-color-4': #f6bd16,
  'zill-color-5': #b1d03d,
  'zill-color-6': #8cb83a,
  'zill-color-7': #46bc95,
  'zill-color-8': #7ccdcf,
  'zill-color-9': #0a82be,
  'zill-color-10': #5b54a4,
) !default;

// Put the loop in a function so it can't leak
@each $color_name, $color_value in $zill-colors {
  .#{$color_name} {
    @include text-color($color_value, 0);
    background-color: transparent;
    border-color: inherit;
  }

  .text-#{$color_name} {
    @include text-color($color_value, 0);
  }

  .bg-#{$color_name} {
    @include background-color($color_value, 0);
  }

  .background-#{$color_name} {
    @include background-color($color_value, 0);
  }

  .border-#{$color_name} {
    @include border-color($color_value, 0);
  }
}

// Put the loop in a function so it can't leak
@each $color_name, $color_value in $shades {
  .#{$color_name} {
    @include text-color($color_value, 0);
  }

  .text-#{$color_name} {
    @include text-color($color_value, 0);
  }

  .bg-#{$color_name} {
    @include background-color($color_value, 0);
  }

  .background-#{$color_name} {
    @include background-color($color_value, 0);
  }

  .border-#{$color_name} {
    @include border-color($color_value, 0);
  }
}

@each $color_name, $color_color in $colors {
  @each $color_type, $color_value in $color_color {
    @if ($color_type == 'base') {
      .#{$color_name} {
        @include text-color($color_value, 0);
      }
      .background-#{$color_name} {
        @include background-color($color_value, 0);
      }
      .border-#{$color_name} {
        @include border-color($color_value, 0);
      }
    } @else if ($color_type != 'shades') {
      .#{$color_name}-#{$color_type} {
        @include text-color($color_value, 1);
      }
      .background-#{$color_name}-#{$color_type} {
        @include background-color($color_value, 1);
      }
      .border-#{$color_name}-#{$color_type} {
        @include border-color($color_value, 1);
      }
    }

    @for $i from 1 through 10 {
      @if ($color_type == 'base') {
        .#{$color_name}.text-lighten-#{$i} {
          @include text-color($color_value, $i);
        }
        .background-#{$color_name}.background-whiten-#{$i} {
          @include background-color($color_value, $i);
        }
        .border-#{$color_name}.border-whiten-#{$i} {
          @include border-color($color_value, $i);
        }
      } @else if ($color_type != 'shades') {
        .#{$color_name}-#{$color_type}.text-whiten-#{$i} {
          @include text-color($color_value, $i);
        }
        .background-#{$color_name}-#{$color_type}.background-whiten-#{$i} {
          @include background-color($color_value, $i);
        }
        .border-#{$color_name}-#{$color_type}.border-whiten-#{$i} {
          @include border-color($color_value, $i);
        }
      }
    }
  }
}
