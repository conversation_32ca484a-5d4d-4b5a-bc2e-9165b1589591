@import './fonts.scss';
@import './settings.scss';
@import './icons.scss';
@import './v-additions.scss';
@import './internet-explorer-fixes.scss';
@import './print.scss';
@import './nprogress.scss';

// ress.css (imported by vuetify) sets overflow-y to scroll, which is not desired
html {
  overflow-y: auto !important;
}

//based on Vuetify website css
ul:not([class]),
ol:not([class]) {
  padding-left: 20px;
  margin-bottom: 16px;
}

#app,
.v-overlay-container {
  text-transform: none !important;
  font-family: 'proximanova', 'Roboto', sans-serif;
  font-size: 16px;

  .container.grid-list-lg {
    padding: 16px;
  }
}

.v-application,
.v-overlay-container {
  .text-h1,
  .text-h2,
  .text-h3,
  .text-h4,
  .text-h5,
  .text-h6,
  .text-subtitle-1,
  .text-subtitle-2,
  .text-body-2,
  .text-body-1,
  .text-caption,
  .text-overline {
    font-family: 'proximanova', 'Roboto', sans-serif !important;
  }
}

.theme--light.v-list-item--active:hover::before,
.theme--light.v-list-item--active::before {
  opacity: 0;
}

.theme--dark.v-list-item--active:hover::before,
.theme--dark.v-list-item--active::before {
  opacity: 0;
}

[fullheight] {
  position: fixed;
  height: 100%;
  width: 100%;
}

[v-cloak] {
  display: none;
}

.full-height {
  height: calc(100vh - 60px);
}

.large-header {
  .full-height {
    height: calc(100vh - 60px - 60px);
  }
}

.unknown-value {
  color: lighten(#8898a6, 15);
  font-style: oblique;
}

// Fix for ellipsis in v-chips
.v-chip {
  max-width: 100%;

  .v-chip__content {
    max-width: 100%;
  }
}

.v-chip[role='button'] .v-chip__content {
  cursor: pointer;
}

.v-list-item {
  height: auto;
  min-height: 48px;
}

// Fix bug that made avatars in the notes go above the navbars
main {
  z-index: 1;
}

// Make all v-icons grey-darken-1 by default.
.v-icon {
  color: #757575;
}

// If a color modifier is applied to the icon, revert to initial behaviour.
[class*=' text-'] .v-icon,
[class^='text-'] .v-icon,
[class*=' bg-'] .v-icon,
[class^='bg-'] .v-icon {
  color: inherit;
}

.v-navigation-drawer {
  .v-list-item--density-compact:not(.v-list-item--nav).v-list-item--one-line {
    padding-inline: 18px;
  }

  .v-toolbar {
    .v-list-item {
      height: 100%;
    }
  }

  &.v-navigation-drawer--mini-variant {
    .v-list-item__action,
    .v-list-item__avatar {
      align-content: inherit;
      justify-content: inherit;
    }
  }

  .bottom-item {
    position: absolute;
    bottom: 0;
    width: 60px;

    .v-list-item {
      padding: 0;

      .v-list-item__action {
        align-content: center !important;
        justify-content: center !important;
        width: 100%;
      }
    }
  }
}

.flex,
.v-col {
  // Add animation to grid elements
  &.animated {
    transition: all 200ms ease;
  }

  &.xs0,
  &.sm0,
  &.md0,
  &.lg0,
  &.xl0,
  &.v-col-0,
  &.v-col-xs-0,
  &.v-col-sm-0,
  &.v-col-md-0,
  &.v-col-lg-0 {
    width: 0;
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 0;
    padding: 0 !important;
    overflow: hidden;
  }
}

.isoGrid {
  .flex,
  .v-col {
    transition: none;

    &.xs1,
    &.sm1,
    &.md1,
    &.lg1,
    &.xl1,
    &.v-col-1,
    &.v-col-xs-1,
    &.v-col-sm-1,
    &.v-col-md-1,
    &.v-col-lg-1 {
      width: 8.333333333333332%;
    }

    &.xs2,
    &.sm2,
    &.md2,
    &.lg2,
    &.xl2,
    &.v-col-2,
    &.v-col-xs-2,
    &.v-col-sm-2,
    &.v-col-md-2,
    &.v-col-lg-2 {
      width: 16.666666666666664%;
    }

    &.xs3,
    &.sm3,
    &.md3,
    &.lg3,
    &.xl3,
    &.v-col-3,
    &.v-col-xs-3,
    &.v-col-sm-3,
    &.v-col-md-3,
    &.v-col-lg-3 {
      width: 25%;
    }

    &.xs4,
    &.sm4,
    &.md4,
    &.lg4,
    &.xl4,
    &.v-col-4,
    &.v-col-xs-4,
    &.v-col-sm-4,
    &.v-col-md-4,
    &.v-col-lg-4 {
      width: 33.33333333333333%;
    }

    &.xs5,
    &.sm5,
    &.md5,
    &.lg5,
    &.xl5,
    &.v-col-5,
    &.v-col-xs-5,
    &.v-col-sm-5,
    &.v-col-md-5,
    &.v-col-lg-5 {
      width: 41.66666666666667%;
    }

    &.xs6,
    &.sm6,
    &.md6,
    &.lg6,
    &.xl6,
    &.v-col-6,
    &.v-col-xs-6,
    &.v-col-sm-6,
    &.v-col-md-6,
    &.v-col-lg-6 {
      width: 50%;
    }

    &.xs7,
    &.sm7,
    &.md7,
    &.lg7,
    &.xl7,
    &.v-col-7,
    &.v-col-xs-7,
    &.v-col-sm-7,
    &.v-col-md-7,
    &.v-col-lg-7 {
      width: 58.333333333333336%;
    }

    &.xs8,
    &.sm8,
    &.md8,
    &.lg8,
    &.xl8,
    &.v-col-8,
    &.v-col-xs-8,
    &.v-col-sm-8,
    &.v-col-md-8,
    &.v-col-lg-8 {
      width: 66.66666666666666%;
    }

    &.xs9,
    &.sm9,
    &.md9,
    &.lg9,
    &.xl9,
    &.v-col-9,
    &.v-col-xs-9,
    &.v-col-sm-9,
    &.v-col-md-9,
    &.v-col-lg-9 {
      width: 75%;
    }

    &.xs10,
    &.sm10,
    &.md10,
    &.lg10,
    &.xl10,
    &.v-col-10,
    &.v-col-xs-10,
    &.v-col-sm-10,
    &.v-col-md-10,
    &.v-col-lg-10 {
      width: 83.33333333333334%;
    }

    &.xs11,
    &.sm11,
    &.md11,
    &.lg11,
    &.xl11,
    &.v-col-11,
    &.v-col-xs-11,
    &.v-col-sm-11,
    &.v-col-md-11,
    &.v-col-lg-11 {
      width: 91.66666666666666%;
    }

    &.xs12,
    &.sm12,
    &.md12,
    &.lg12,
    &.xl12,
    &.v-col-12,
    &.v-col-xs-12,
    &.v-col-sm-12,
    &.v-col-md-12,
    &.v-col-lg-12 {
      width: 100%;
    }
  }
}

.v-stepper .custom-stepper {
  .v-divider {
    border-top-width: 2px;
  }

  .v-stepper-item {
    &::before {
      border-bottom: 2px solid rgb(var(--v-theme-primary));
      content: '';
      position: absolute;
      left: calc(50% - 12px);
      width: 0;
      top: 35px;
      visibility: hidden;
      transition: 0.3s ease all;
    }

    &::after {
      content: '';
      position: absolute;
      left: calc(50% + 12px);
      width: 0;
      top: 35px;
      border-bottom: 2px solid rgb(var(--v-theme-primary));
      visibility: hidden;
      transition: 0.3s ease all;
    }

    .v-avatar {
      visibility: hidden;
      background-color: white !important;
      color: rgb(var(--v-theme-primary));
      border: 2px solid rgb(var(--v-theme-primary));
      opacity: 0;
      transition: 0.3s ease all;

      .v-icon {
        color: rgb(var(--v-theme-primary));
        font-size: 16px;
      }
    }

    .v-stepper-item__content {
      .v-icon {
        margin: auto;
      }

      text-align: center;
    }

    &.v-stepper-item--selected,
    &.v-stepper-item--complete:hover,
    &.v-stepper-item--complete:focus,
    &.editable:hover,
    &.editable:focus {
      opacity: 1;

      &::before {
        left: 0;
        width: calc(50% - 12px);
        visibility: visible;
      }

      &::after {
        left: calc(50% + 12px);
        width: calc(50% - 12px);
        visibility: visible;
      }

      .v-avatar {
        visibility: visible;
        opacity: 1;
      }

      .v-stepper-item__content {
        text-shadow: none;
        color: rgb(var(--v-theme-primary));

        .v-icon {
          margin: auto;
          color: rgb(var(--v-theme-primary));
        }
      }
    }
  }
}

.inputLabel {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.54);
}

.report-toggle {
  margin-top: 0;
  padding-top: 0;

  .v-input__slot {
    margin-bottom: 0;
  }
}

.v-list-item.text-white .v-list-item__subtitle {
  color: rgba(255, 255, 255, 0.6);
}

.nowrap {
  white-space: nowrap;
}

$zill-colors:
  #e9252d, #ed7928, #f79739, #f6bd16, #b1d03d, #8cb83a, #46bc95, #7ccdcf, #0a82be, #5b54a4;
@for $i from 1 through length($zill-colors) {
  .bg-zill-color-#{$i} {
    background-color: nth($zill-colors, $i) !important;
    border-color: nth($zill-colors, $i) !important;
  }
  .text-zill-color-#{$i} {
    color: nth($zill-colors, $i) !important;
    caret-color: nth($zill-colors, $i) !important;
  }
}

.visually-hidden {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap;
}

.v-list-item__prepend > .v-badge .v-icon,
.v-list-item__prepend > .v-icon,
.v-list-item__append > .v-badge .v-icon,
.v-list-item__append > .v-icon {
  opacity: 1 !important;
}
