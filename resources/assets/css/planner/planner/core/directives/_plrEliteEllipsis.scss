.expandable {
  cursor: pointer;

  .ellipsis-content {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
  }

  a.more {
    display: block;
    background: white;
    box-shadow: 0 0 40px 30px white;
  }
  a.less {
    display: none;
  }

  &.expanded {
    .ellipsis-content {
      overflow: hidden;
      max-height: none !important;
      padding-right: 90px;
    }

    a.more {
      display: none;
    }
    a.less {
      display: block;
    }
  }

  a.more,
  a.less {
    position: absolute;
    top: 0;
    right: 0;
    padding-right: 20px;
    pointer-events: none;
  }
}
