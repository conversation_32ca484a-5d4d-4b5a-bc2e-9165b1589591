// This is a sass fix for bootstrap, as otherwise, buttons will have 1 pixel less then inputs!
// This has something to do with sass calculations and rounding errors, but couldn't solve that.
// Note that there isn't an xs version of inputs, so we'll not set the height of xs buttons explicitly!

.btn {
  height: $input-height-base;
  &.btn-sm {
    height: $input-height-small;
  }
  &.btn-lg {
    height: $input-height-large;
  }
}
