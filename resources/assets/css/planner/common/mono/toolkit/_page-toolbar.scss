// For the page toolbar, we use the bootstrap navbar, which can have buttons on it through navbar-form.
// We'll also have the collapsing, which is great for smaller screens and too many buttons.
// Though custom stuff is needed as the collapsing will work differently in our app-layout!

@mixin tk-navbar() {
  //position: absolute; top: 0; left: 0; right: 0; // >> Seems to let the navbar-left float into the brand span...
  height: $navbar-height !important;
  border-width: 0 0 1px 0;
  border-style: solid;
  border-color: $navbar-default-border;
  background-color: $navbar-default-bg;
}

.navbar {
  border: none;
}
.navbar-default {
  background-color: transparent;
}
.navbar-brand {
  font-weight: bold;
}
.navbar-form {
  & > * {
    margin-right: 0;
    display: block;
  }
  & .btn-default {
    border-color: $navbar-default-border;
  }
  .form-control-static {
    min-height: $input-height-base;
    height: $input-height-base;
  }
}

body.page-toolbar-opened > .popover {
  display: none !important;
}

@media (max-width: $grid-float-breakpoint-max) {
  .navbar-header {
    @include tk-navbar();
  }
  .navbar-collapse {
    z-index: 1; // To have it over .page-main!
    position: absolute;
    top: $navbar-height;
    left: 0;
    right: 0;
    bottom: 0;
    background: $gray-lighter;
    border: none;
    padding-bottom: calc($grid-gutter-width / 2); // Cfr. padding left & right.
    overflow-y: auto;
  }

  .navbar-form {
    border: none;

    .navbar-header & {
      float: left !important;
      margin: 0; // Bootstrap override, as we use this differently...
    }

    .navbar-collapse & {
      display: block;
      margin-top: 0;
      margin-bottom: 0;
      padding-top: 0;
      padding-bottom: 0;

      & > *,
      :not(.btn-group) > .btn-group {
        margin-top: calc($grid-gutter-width / 3);
      }
      & > *,
      .btn {
        width: 100%;
      }
      & > .btn-group,
      :not(.btn-group) > .btn-group {
        display: table;
        table-layout: fixed;
        border-collapse: separate;
        & > .btn-group {
          display: table-cell;
          float: none;
          width: 1%;
        }
      }
    }
  }
}

@media (min-width: $grid-float-breakpoint) {
  .navbar-collapse,
  .navbar-collapse.collapse {
    @include tk-navbar();
  }
  .navbar-form {
    & > * {
      float: left;
      &:not(:first-child) {
        margin-left: calc($grid-gutter-width / 3);
      }
    }
  }
}
