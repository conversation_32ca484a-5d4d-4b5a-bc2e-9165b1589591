/*! Select2 Bootstrap Theme v0.1.0-beta.4 | MIT License | github.com/select2/select2-bootstrap-theme */

//
// Variables
// --------------------------------------------------

// Bootstrap defaults not available as pre-defined variables
// These should not be overridden.
// @see https://github.com/twbs/bootstrap-sass/blob/master/assets/stylesheets/bootstrap/_forms.scss#L127

$form-control-default-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);

// @see https://github.com/twbs/bootstrap-sass/blob/master/assets/stylesheets/bootstrap/mixins/_forms.scss#L43
$form-control-focus-box-shadow:
  $form-control-default-box-shadow,
  0 0 8px rgba($input-border-focus, 0.6);

// @see https://github.com/twbs/bootstrap-sass/blob/master/assets/stylesheets/bootstrap/_forms.scss#L128
$form-control-transition:
  border-color ease-in-out 0.15s,
  box-shadow ease-in-out 0.15s;

// Custom variables
// -------------------------

$dropdown-arrow-color: $input-color-placeholder !default;
$dropdown-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175) !default;
$clear-selection-color: $dropdown-arrow-color !default;

//
// Mixins
// --------------------------------------------------

// @see https://github.com/twbs/bootstrap-sass/blob/master/assets/stylesheets/bootstrap/_forms.scss#L115
@mixin bootstrap-input-defaults {
  @include box-shadow($form-control-default-box-shadow);
  background-color: $input-bg;
  border: 1px solid $input-border;
  border-radius: $input-border-radius;
  color: $input-color;
  font-family: $font-family-base;
  font-size: $font-size-base;
}

// @see http://getbootstrap.com/css/#forms-control-validation
// @see https://github.com/twbs/bootstrap-sass/blob/master/assets/stylesheets/bootstrap/_forms.scss#L388
@mixin validation-state-focus($color) {
  $shadow:
    inset 0 1px 1px rgba(0, 0, 0, 0.075),
    0 0 6px lighten($color, 20%);

  .select2-dropdown,
  .select2-selection {
    border-color: $color;
  }

  .select2-container--focus .select2-selection,
  .select2-container--open .select2-selection {
    @include box-shadow($shadow);
    border-color: darken($color, 10%);
  }

  &.select2-drop-active {
    border-color: darken($color, 10%);

    &.select2-drop.select2-drop-above {
      border-top-color: darken($color, 10%);
    }
  }
}

// dropdown arrow when dropdown is open
@mixin dropdown-arrow {
  .select2-selection--single {
    /**
         * Make the dropdown arrow point up while the dropdown is visible.
         */

    .select2-selection__arrow b {
      border-color: transparent transparent $dropdown-arrow-color transparent;
      border-width: 0 $caret-width-large $caret-width-large $caret-width-large;
    }
  }
}

.select2-container--bootstrap {
  display: block;

  /*------------------------------------*\
            #COMMON STYLES
    \*------------------------------------*/

  .select2-selection {
    @include bootstrap-input-defaults;
    outline: 0;
  }

  /**
     * Search field in the Select2 dropdown.
     */

  .select2-search--dropdown {
    .select2-search__field {
      @include bootstrap-input-defaults;
    }
  }

  /**
     * No outline for all search fields - in the dropdown
     * and inline in multi Select2s.
     */

  .select2-search__field {
    outline: 0;

    &::-webkit-input-placeholder {
      color: $input-color-placeholder;
    }

    /* Firefox 18- */
    &:-moz-placeholder {
      color: $input-color-placeholder;
    }

    /**
         * Firefox 19+
         *
         * @see http://stackoverflow.com/questions/24236240/color-for-styled-placeholder-text-is-muted-in-firefox
         */
    &::-moz-placeholder {
      color: $input-color-placeholder;
      opacity: 1;
    }

    &:-ms-input-placeholder {
      color: $input-color-placeholder;
    }
  }

  /**
     * Adjust Select2's choices hover and selected styles to match
     * Bootstrap 3's default dropdown styles.
     *
     * @see http://getbootstrap.com/components/#dropdowns
     */

  .select2-results__option {
    &[role='group'] {
      padding: 0;
    }

    /**
         * Disabled results.
         *
         * @see https://select2.github.io/examples.html#disabled-results
         */

    &[aria-disabled='true'] {
      color: $dropdown-link-disabled-color;
      cursor: $cursor-disabled;
    }

    /**
         * Hover state.
         */

    &[aria-selected='true'] {
      background-color: $dropdown-link-hover-bg;
      color: $dropdown-link-hover-color;
    }

    /**
         * Selected state.
         */

    &--highlighted[aria-selected] {
      background-color: $dropdown-link-active-bg;
      color: $dropdown-link-active-color;
    }

    .select2-results__option {
      padding: $padding-base-vertical $padding-base-horizontal;

      .select2-results__group {
        padding-left: 0;
      }

      .select2-results__option {
        margin-left: -$padding-base-horizontal;
        padding-left: $padding-base-horizontal * 2;

        .select2-results__option {
          margin-left: -$padding-base-horizontal * 2;
          padding-left: $padding-base-horizontal * 3;

          .select2-results__option {
            margin-left: -$padding-base-horizontal * 3;
            padding-left: $padding-base-horizontal * 4;

            .select2-results__option {
              margin-left: -$padding-base-horizontal * 4;
              padding-left: $padding-base-horizontal * 5;

              .select2-results__option {
                margin-left: -$padding-base-horizontal * 5;
                padding-left: $padding-base-horizontal * 6;
              }
            }
          }
        }
      }
    }
  }

  .select2-results__group {
    color: $dropdown-header-color;
    display: block;
    padding: $padding-base-vertical $padding-base-horizontal;
    font-size: $font-size-small;
    line-height: $line-height-base;
    white-space: nowrap;
  }

  &.select2-container--focus,
  &.select2-container--open {
    .select2-selection {
      @include box-shadow($form-control-focus-box-shadow);
      @include transition($form-control-transition);
      border-color: $input-border-focus;
    }
  }

  &.select2-container--open {
    /**
         * Make the dropdown arrow point up while the dropdown is visible.
         */

    .select2-selection .select2-selection__arrow b {
      border-color: transparent transparent $dropdown-arrow-color transparent;
      border-width: 0 $caret-width-base $caret-width-base $caret-width-base;
    }

    /**
         * Handle border radii of the container when the dropdown is showing.
         */

    &.select2-container--below {
      .select2-selection {
        @include border-bottom-radius(0);
        border-bottom-color: transparent;
      }
    }

    &.select2-container--above {
      .select2-selection {
        @include border-top-radius(0);
        border-top-color: transparent;
      }
    }
  }

  /**
     * Clear the selection.
     */

  .select2-selection__clear {
    color: $clear-selection-color;
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: 10px;

    &:hover {
      color: #333;
    }
  }

  /**
     * Address disabled Select2 styles.
     *
     * @see https://select2.github.io/examples.html#disabled
     * @see http://getbootstrap.com/css/#forms-control-disabled
     */

  &.select2-container--disabled {
    .select2-selection {
      border-color: $input-border;
      @include box-shadow(none);
    }

    .select2-selection,
    .select2-search__field {
      cursor: $cursor-disabled;
    }

    .select2-selection,
    .select2-selection--multiple .select2-selection__choice {
      background-color: $input-bg-disabled;
    }

    .select2-selection__clear,
    .select2-selection--multiple .select2-selection__choice__remove {
      display: none;
    }
  }

  /*------------------------------------*\
            #DROPDOWN
    \*------------------------------------*/

  /**
     * Dropdown border color and box-shadow.
     */

  .select2-dropdown {
    @include box-shadow($dropdown-box-shadow);
    border-color: $input-border-focus;
    overflow-x: hidden;
    margin-top: -1px;
    &--above {
      margin-top: 1px;
    }
  }

  /**
     * Limit the dropdown height.
     */

  .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto;
  }

  /*------------------------------------*\
            #SINGLE SELECT2
    \*------------------------------------*/

  .select2-selection--single {
    height: $input-height-base;
    line-height: $line-height-base;
    padding: $padding-base-vertical $padding-base-horizontal + $caret-width-base * 3
      $padding-base-vertical $padding-base-horizontal;

    /**
         * Adjust the single Select2's dropdown arrow button appearance.
         */

    .select2-selection__arrow {
      position: absolute;
      bottom: 0;
      right: $padding-base-horizontal;
      top: 0;
      width: $caret-width-base;

      b {
        border-color: $dropdown-arrow-color transparent transparent transparent;
        border-style: solid;
        border-width: $caret-width-base $caret-width-base 0 $caret-width-base;
        height: 0;
        left: 0;
        margin-left: -$caret-width-base;
        margin-top: -$caret-width-base * 0.5;
        position: absolute;
        top: 50%;
        width: 0;
      }
    }

    .select2-selection__rendered {
      color: $input-color;
      padding: 0;
    }

    .select2-selection__placeholder {
      color: $input-color-placeholder;
    }
  }

  /*------------------------------------*\
        #MULTIPLE SELECT2
    \*------------------------------------*/

  .select2-selection--multiple {
    min-height: $input-height-base;

    .select2-selection__rendered {
      box-sizing: border-box;
      display: block;
      line-height: $line-height-base;
      list-style: none;
      margin: 0;
      overflow: hidden;
      padding: 0;
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .select2-selection__placeholder {
      color: $input-color-placeholder;
      float: left;
      margin-top: 5px;
    }

    /**
         * Make Multi Select2's choices match Bootstrap 3's default button styles.
         */

    .select2-selection__choice {
      color: $input-color;
      background: $btn-default-bg;
      border: 1px solid $btn-default-border;
      border-radius: 15px;
      font-size: 13px;
      line-height: 30px;
      cursor: default;
      float: left;
      margin: 2px;
      padding: 0 5px 0 10px;
    }

    /**
         * Minus 2px borders.
         */

    .select2-search--inline {
      .select2-search__field {
        background: transparent;
        padding: 0 $padding-base-horizontal;
        height: $input-height-base - 2;
        line-height: $line-height-base;
        margin-top: 0;
        min-width: 5em;
      }
    }

    .select2-selection__choice__remove {
      color: $gray-dark;
      background: $gray-lighter;
      border: 1px solid darken($gray-lighter-border, 20%);
      width: 26px;
      height: 26px;
      margin-top: 2px;
      line-height: 26px;
      font-size: 17px;
      text-align: center;
      border-radius: 100%;
      float: right;
      cursor: pointer;
      display: inline-block;
      font-weight: bold;
      opacity: 0.5;
      margin-left: $padding-base-vertical * 0.5;
      background: url(../common/images/select2-customizations/cross.svg) no-repeat 7px 7px;
      font-size: 0;
      &:hover {
        background-color: #fff;
        opacity: 1;
      }
    }

    /**
         * Clear the selection.
         */

    .select2-selection__clear {
      margin-top: $padding-base-vertical;
    }
  }

  /**
     * Address Bootstrap control sizing classes
     *
     * 1. Reset Bootstrap defaults.
     * 2. Adjust the dropdown arrow button icon position.
     *
     * @see http://getbootstrap.com/css/#forms-control-sizes
     */

  /* 1 */
  &.input-sm,
  &.input-lg {
    border-radius: 0;
    font-size: 12px;
    height: auto;
    line-height: 1;
    padding: 0;
  }

  &.input-sm,
  .input-group-sm &,
  .form-group-sm & {
    .select2-selection--single {
      border-radius: $border-radius-small;
      font-size: $font-size-small;
      height: $input-height-small;
      line-height: $line-height-small;
      padding: $padding-small-vertical $padding-small-horizontal + $caret-width-base * 3
        $padding-small-vertical $padding-small-horizontal;

      /* 2 */
      .select2-selection__arrow b {
        margin-left: -$padding-small-vertical;
      }
    }

    .select2-selection--multiple {
      min-height: $input-height-small;

      .select2-selection__choice {
        font-size: $font-size-small;
        line-height: $line-height-small;
        margin: ($padding-small-vertical - 1) 0 0 $padding-small-horizontal * 0.5;
        padding: 0 $padding-small-vertical;
      }

      .select2-search--inline .select2-search__field {
        padding: 0 $padding-small-horizontal;
        font-size: $font-size-small;
        height: $input-height-small - 2;
        line-height: $line-height-small;
      }

      .select2-selection__clear {
        margin-top: $padding-small-vertical;
      }
    }
  }

  &.input-lg,
  .input-group-lg &,
  .form-group-lg & {
    .select2-selection--single {
      border-radius: $border-radius-large;
      font-size: $font-size-large;
      height: $input-height-large;
      line-height: $line-height-large;
      padding: $padding-large-vertical $padding-large-horizontal + $caret-width-large * 3
        $padding-large-vertical $padding-large-horizontal;

      /* 1 */
      .select2-selection__arrow {
        width: $caret-width-large;

        b {
          border-width: $caret-width-large $caret-width-large 0 $caret-width-large;
          margin-left: -$caret-width-large;
          margin-left: -$padding-large-vertical;
          margin-top: -$caret-width-large * 0.5;
        }
      }
    }

    .select2-selection--multiple {
      min-height: $input-height-large;

      .select2-selection__choice {
        font-size: $font-size-large;
        line-height: $line-height-large;
        border-radius: 4px;
        margin: ($padding-large-vertical - 1) 0 0 $padding-large-horizontal * 0.5;
        padding: 0 $padding-large-vertical;
      }

      .select2-search--inline .select2-search__field {
        padding: 0 $padding-large-horizontal;
        font-size: $font-size-large;
        height: $input-height-large - 2;
        line-height: $line-height-large;
      }

      .select2-selection__clear {
        margin-top: $padding-large-vertical;
      }
    }
  }

  &.input-lg.select2-container--open {
    @include dropdown-arrow;
  }

  .input-group-lg & {
    &.select2-container--open {
      @include dropdown-arrow;
    }
  }

  /*------------------------------------*\
        #RTL SUPPORT
    \*------------------------------------*/

  &[dir='rtl'] {
    /**
         * Single Select2
         *
         * 1. Makes sure that .select2-selection__placeholder is positioned
         *    correctly.
         */

    .select2-selection--single {
      padding-left: $padding-base-horizontal + $caret-width-base * 3;
      padding-right: $padding-base-horizontal;
      .select2-selection__rendered {
        padding-right: 0;
        padding-left: 0;
        text-align: right; /* 1 */
      }

      .select2-selection__clear {
        float: left;
      }

      .select2-selection__arrow {
        left: $padding-base-horizontal;
        right: auto;

        b {
          margin-left: 0;
        }
      }
    }

    /**
         * Multiple Select2
         */

    .select2-selection--multiple {
      .select2-selection__choice,
      .select2-selection__placeholder {
        float: right;
      }

      .select2-selection__choice {
        margin-left: 0;
        margin-right: $padding-base-horizontal * 0.5;
      }

      .select2-selection__choice__remove {
        margin-left: 2px;
        margin-right: auto;
      }
    }
  }
}

/*------------------------------------*\
    #ADDITIONAL GOODIES
\*------------------------------------*/

/**
 * Address Bootstrap's validation states
 *
 * If a Select2 widget parent has one of Bootstrap's validation state modifier
 * classes, adjust Select2's border colors and focus states accordingly.
 * You may apply said classes to the Select2 dropdown (body > .select2-container)
 * via JavaScript match Bootstraps' to make its styles match.
 *
 * @see http://getbootstrap.com/css/#forms-control-validation
 */

.has-warning {
  @include validation-state-focus($state-warning-text);
}

.has-error {
  @include validation-state-focus($state-danger-text);
}

.has-success {
  @include validation-state-focus($state-success-text);
}

/**
 * Select2 widgets in Bootstrap Input Groups
 *
 * When Select2 widgets are combined with other elements using Bootstraps
 * "Input Group" component, we don't want specific edges of the Select2
 * container to have a border-radius.
 *
 * Use .select2-bootstrap-prepend and .select2-bootstrap-append on
 * a Bootstrap 3 .input-group to let the contained Select2 widget know which
 * edges should not be rounded as they are directly followed by another element.
 *
 * @see http://getbootstrap.com/components/#input-groups
 */

/**
 * Mimick Bootstraps .input-group .form-control styles.
 *
 * @see https://github.com/twbs/bootstrap/blob/master/less/input-groups.less
 */

.input-group .select2-container--bootstrap {
  display: table;
  table-layout: fixed;
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}

.input-group.select2-bootstrap-prepend .select2-container--bootstrap {
  .select2-selection {
    @include border-left-radius(0);
  }
}

.input-group.select2-bootstrap-append .select2-container--bootstrap {
  .select2-selection {
    @include border-right-radius(0);
  }
}

/**
 * Adjust alignment of Bootstrap buttons in Bootstrap Input Groups to address
 * Multi Select2's height which - depending on how many elements have been selected -
 * may grow taller than its initial size.
 *
 * @see http://getbootstrap.com/components/#input-groups
 */

.select2-bootstrap-append,
.select2-bootstrap-prepend {
  .select2-container--bootstrap,
  .input-group-btn,
  .input-group-btn .btn {
    vertical-align: top;
  }
}

/**
 * Temporary fix for https://github.com/select2/select2-bootstrap-theme/issues/9
 *
 * Provides `!important` for certain properties of the class applied to the
 * original `<select>` element to hide it.
 *
 * @see https://github.com/select2/select2/pull/3301
 * @see https://github.com/fk/select2/commit/31830c7b32cb3d8e1b12d5b434dee40a6e753ada
 */

.form-control.select2-hidden-accessible {
  position: absolute !important;
  width: 1px !important;
}

/**
 * Display override for inline forms
*/

.form-inline .select2-container--bootstrap {
  display: inline-block;
}
