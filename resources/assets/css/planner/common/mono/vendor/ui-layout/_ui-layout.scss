/**
  UI.Layout CSS
  Edited for our purposes (Mono)
*************************************/

.stretch {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Can be changed by hand ;)*/
  overflow: auto;
}

.ui-splitbar {
  display: flex;
  justify-content: center;
  background-color: #fff;
  right: auto;
  position: absolute;
  z-index: 1;
}

.ui-layout-row > .ui-splitbar {
  height: 8px;
  width: 100%;
  cursor: row-resize;
  text-align: center;
  justify-content: center;
  align-items: center;
  background: $gray-lighter;
  overflow-y: hidden;
}

.ui-layout-column > .ui-splitbar {
  width: 8px;
  height: 100%;
  cursor: col-resize;
  -webkit-flex-direction: column;
  flex-direction: column;
  background: $gray-lighter;
  border-right: 1px solid $gray-lighter-border;
  overflow-x: hidden;
}

.ui-layout-column > .ui-splitbar > a,
.ui-layout-row > .ui-splitbar > a {
  cursor: pointer;
  text-align: center;
  font-size: 16px;
  color: #aaa;
}

.ui-layout-column > .ui-splitbar > a:nth-child(2) {
  margin-top: 0.35rem;
}

.ui-layout-row > .ui-splitbar > a:nth-child(2) {
  margin-left: 0.35rem;
}

/**
* Icons
**/

.ui-splitbar-icon {
  width: 0;
  height: 0;
  display: inline-block;
}

.ui-splitbar-icon-up {
  border-left: 0.25em solid transparent;
  border-right: 0.25em solid transparent;
  border-bottom: 0.25em solid;
}

.ui-splitbar-icon-down {
  border-left: 0.25em solid transparent;
  border-right: 0.25em solid transparent;
  border-top: 0.25em solid;
  margin-right: 0.25em;
}

.ui-splitbar-icon-right {
  border-top: 0.25em solid transparent;
  border-bottom: 0.25em solid transparent;
  border-left: 0.25em solid;
}

.ui-splitbar-icon-left {
  border-top: 0.25em solid transparent;
  border-bottom: 0.25em solid transparent;
  border-right: 0.25em solid;
  margin-top: 0.25em;
}

/* Allow disabling of icons */
.no-toggle .ui-splitbar-icon {
  display: none;
}

@media only screen and (max-device-width: 480px) {
  .no-mobile-toggle .ui-splitbar-icon {
    display: none;
  }
}

@media print {
  .ui-splitbar {
    display: none;
  }

  .stretch {
    position: relative;
  }
  /* The last item can take up any amount of space. */
  .stretch.ui-layout-container:last-child {
    position: static;
    overflow: visible;
  }
}

/* Make sure hidden elements are in fact not rendered. */
.ui-layout-hidden {
  display: none;
}
