/* ==========================================================================
  c-calendar
   ========================================================================== */

$calendar-add-preview-background: #747474 !default;

/* Structure
   ========================================================================== */

.c-calendar-scroll-context {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.c-calendar {
  width: 100%;

  td {
    vertical-align: top;
    height: 100px;
    width: 300px;
  }
}

.c-calendar-head-day {
  font-size: 12px;
  line-height: 24px;
  text-align: center;
}

.c-calendar-head-holder {
  flex: 1 0 auto;
  z-index: 300;

  th {
    width: 300px;
    vertical-align: top;
    border-bottom: 2px solid $gray-light-border;
    padding-bottom: 2px;
  }
}

.c-calendar-body-holder {
  flex: 0 1 auto;
  z-index: 200;
}

.c-calendar-body > tbody > tr > td {
  border-bottom: 1px solid $gray-lighter-border;
  position: relative;
  height: 100px;
}

.c-calendar-end {
  width: 12px !important;
}

.c-calendar-hours {
  width: 52px !important;
  text-align: center;
  font-size: 11px;
  font-weight: 400;
  color: $gray;
  vertical-align: top;
  position: relative;
  bottom: 8px;

  .c-calendar-body tr:first-child & {
    bottom: 0;
  }
}

.c-calendar-cell-weekend {
  display: none;
}

.c-calendar-add-preview {
  left: 0;
  right: 3px;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    width: 100%;
    display: block;
    border: 1px dashed $gray-light-border;
    height: 6px;
  }
}

.c-calendar-add-preview-tooltip {
  background-color: $calendar-add-preview-background;
  position: absolute;
  top: 20px;
  width: 100%;
  padding: 5px 10px;
  text-align: center;
  font-size: 11px;
  z-index: 1;

  &:after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: -10px;
    left: calc(50% - 10px);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid $calendar-add-preview-background;
  }

  .svg-icon {
    svg path {
      fill: #fff;
    }
  }
}

.c-calendar-add-preview-tooltip-info {
  color: rgba(#fff, 0.6);
}

/* Unassigned style
   ========================================================================== */

/* States
   ========================================================================== */

.c-calendar .c-calendar-event-selected {
  position: relative;
  z-index: 100;
}

/* Contents
   ========================================================================== */

.c-calendar-event-header {
  display: flex;
  align-items: center;
  padding-right: 3px;
}

.c-calendar-event-title {
  flex: 1 0 auto;
  font-weight: 600;
  font-size: 12px;
  line-height: 14px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-right: 6px;
}

.c-calendar-event-hints {
  display: flex;
  align-items: center;
}

.c-calendar-event-hint {
  margin-right: 5px;

  &:last-child {
    margin-right: 0;
  }
}

.c-calendar-event-subtitle,
.c-calendar-event-group-long {
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: rgba($gray-dark, 0.5);
}

.c-calendar-event-time {
  font-size: 11px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.c-calendar-event-group {
  flex: 0 1 auto;
  font-size: 11px;
  color: $gray;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.c-calendar-event-smart-indicator {
  position: absolute;
  right: 0;
  bottom: 0;
  white-space: nowrap;
  font-size: 12px;
}
