/* ==========================================================================
  bc-tags
  Tags
   ========================================================================== */

/* Variables
   ========================================================================== */

$bc-tag-bg: $gray-light !default;
$bg-tag-border-color: darken($gray-light, 10%) !default;

/* Simple tags
   ========================================================================== */

.bc-tag {
  background: $bc-tag-bg;
  border: 1px solid $bg-tag-border-color;
  padding: 0;
  margin: 2px 4px 2px 0;
  border-radius: 13px;
  height: 26px;
}

/* Outline tags
   ========================================================================== */

.bc-tag-outline {
  background: transparent;
  border: 1px solid $gray-light-border;
}

/* Extended tags
   ========================================================================== */

.bc-tag-list {
  @include clearfix;
  .bc-tag {
    float: left;
  }
}

.bc-tag {
  display: block;
  position: relative;
  @include clearfix;
  > a,
  > span {
    float: left;
    display: block;
  }
  .bc-tag-label {
    font-size: 13px;
    line-height: 1;
    padding: 5px 8px; // We cannot do absolute line height since firefox fucks up
    // Multiple labels
    & + .bc-tag-label {
      padding-left: 4px;
      &:before {
        border-left: 1px solid rgba(0, 0, 0, 0.33);
        content: '';
        height: 100%;
        position: relative;
        top: 1px;
        right: 6px;
      }
    }
  }
  > a {
    display: inline-block;
    width: 20px;
    height: 20px;
    font-size: 13px;
    line-height: 20px;
  }

  // Close button
  > a {
    border-radius: 100%;
    background: #fff;
    color: $brand-primary;
    margin: 2px 2px 0 -4px;
    font-weight: 700;
    text-align: center;
    border: none;
    i {
      margin: 0 auto;
      display: block;
      font-size: 10px;
      position: relative;
      left: 0.5px;
    }
    &:hover,
    &:active,
    &:focus {
      background: $brand-danger;
      text-decoration: none;
      color: #fff;
      @include box-shadow(0 0 0 2px #fff);
    }
  }
}
