/* ==========================================================================
   bc-minimal-forms
   ========================================================================== */

/* Variables
   ========================================================================== */

$bc-form-minimal-font-size: 13px;
$bc-form-minimal-input-height: 26px;

/* Component
   ========================================================================== */

.c-label-minimal {
  font-weight: 600;
  font-size: $bc-form-minimal-font-size;
  vertical-align: top;
  line-height: 26px;
  margin: 0;
}

.c-input-minimal[type='text'],
.c-input-minimal[type='number'],
.c-input-minimal[type='email'],
.c-input-minimal[type='password'],
textarea.c-input-minimal {
  padding: 0 5px;
  height: $bc-form-minimal-input-height;
  font-size: $bc-form-minimal-font-size;
  border: 1px solid $gray-light;
  border-radius: $border-radius-base;
  background: none;
  width: 100%;
  @include placeholder($input-color-placeholder);
  box-shadow: none;
  outline: 0;

  &:focus {
    @include placeholder(#8898a6);
    border-color: $blue;
  }
}

textarea.c-input-minimal {
  min-height: 100px;
  padding: 5px;
}

.c-select-minimal-holder {
  display: inline-block;
  position: relative;
  width: 100%;

  &:after {
    width: 14px;
    height: 18px;
    display: block;
    position: absolute;
    top: 1px;
    right: 3px;
    background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+DQo8c3ZnIHdpZHRoPSIxNHB4IiBoZWlnaHQ9IjE5cHgiIHZpZXdCb3g9IjAgMCAxNCAxOSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4NCiAgICA8IS0tIEdlbmVyYXRvcjogU2tldGNoIDMuNi4xICgyNjMxMykgLSBodHRwOi8vd3d3LmJvaGVtaWFuY29kaW5nLmNvbS9za2V0Y2ggLS0+DQogICAgPHRpdGxlPnNlbGVjdC1taW5pbWFsPC90aXRsZT4NCiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4NCiAgICA8ZGVmcz48L2RlZnM+DQogICAgPGcgaWQ9ImFzc2V0cyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+DQogICAgICAgIDxnIGlkPSJzZWxlY3QyLWN1c3RvbWl6YXRpb25zIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtNzcuMDAwMDAwLCAtMzEuMDAwMDAwKSIgZmlsbD0iIzQ0NEI1MSI+DQogICAgICAgICAgICA8cGF0aCBkPSJNODUuNzM4MDMxOSw0Ny4yNzY2MjA2IEw4Mi4zOTg4ODcyLDQzLjkzODg2NjcgTDg1LjczODAzMTksNDAuNjAxMTEyOCBMODYuMjc2NjIwNiw0MS4yMTkzMzAyIEw4My41NTU3ODczLDQzLjkzODg2NjcgTDg2LjI3NjYyMDYsNDYuNjU4MTIxMiBMODUuNzM4MDMxOSw0Ny4yNzY2MjA2IFoiIGlkPSJhcnJvdy1kb3duIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg4NC4zMzc3NTQsIDQzLjkzODg2Nykgcm90YXRlKC05MC4wMDAwMDApIHRyYW5zbGF0ZSgtODQuMzM3NzU0LCAtNDMuOTM4ODY3KSAiPjwvcGF0aD4NCiAgICAgICAgICAgIDxwYXRoIGQ9Ik04NS43MzgwMzE5LDQwLjI3NjYyMDYgTDgyLjM5ODg4NzIsMzYuOTM4ODY2NyBMODUuNzM4MDMxOSwzMy42MDExMTI4IEw4Ni4yNzY2MjA2LDM0LjIxOTMzMDIgTDgzLjU1NTc4NzMsMzYuOTM4ODY2NyBMODYuMjc2NjIwNiwzOS42NTgxMjEyIEw4NS43MzgwMzE5LDQwLjI3NjYyMDYgWiIgaWQ9ImFycm93LWRvd24iIHRyYW5zZm9ybT0idHJhbnNsYXRlKDg0LjMzNzc1NCwgMzYuOTM4ODY3KSBzY2FsZSgxLCAtMSkgcm90YXRlKC05MC4wMDAwMDApIHRyYW5zbGF0ZSgtODQuMzM3NzU0LCAtMzYuOTM4ODY3KSAiPjwvcGF0aD4NCiAgICAgICAgPC9nPg0KICAgIDwvZz4NCjwvc3ZnPg==')
      no-repeat 0 2px;
    content: ' ';
  }
}

.c-select-minimal {
  appearance: none;
  background: none;
  padding-left: 5px;
  padding-right: 15px;
  height: $bc-form-minimal-input-height;
  width: 100%;
  position: relative;
  font-size: $bc-form-minimal-font-size;
  border: 1px solid $gray-light;
  border-radius: $border-radius-base;

  &.select2-hidden-accessible {
    position: absolute;
  }

  &:focus {
    outline: 0;
    @include placeholder(#8898a6);
    border-color: $blue;
  }
}

.c-select-minimal-placeholder {
  color: $gray;
}

.c-minimal-form-group {
  margin-bottom: 5px;

  &:last-child {
    margin-bottom: 0;
  }
}

.c-checkboxes {
  display: flex;
  flex-wrap: wrap;
}

label.c-checkbox-minimal-label,
label.c-radio-minimal-label {
  font-size: $bc-form-minimal-font-size;
  font-weight: 400;
  margin: 0;
  margin-right: 8px;
}

label.c-checkbox-minimal-label,
label.c-radio-minimal-label {
  .c-checkbox-minimal-label-text,
  .c-radio-minimal-label-text {
    line-height: 20px;
    position: relative;
    bottom: 3px;
  }
}

label.c-checkbox-minimal-label,
label.c-radio-minimal-label {
  input {
    margin: 0;
    padding: 0;
    appearance: none;
    border: none;

    width: 16px;
    height: 16px;
    background: #fff;
  }
}

label.c-checkbox-minimal-label {
  input {
    border-radius: 2px;
    font-weight: 400;
    border: 1px solid $input-border;
    margin-right: 0;
    margin-top: 4px;
    &:checked {
      background: #fff url(../common/images/custom-forms/check-14.svg) no-repeat 0 0;
    }
    &:focus {
      outline: 0;
      border: 1px solid $brand-primary;
    }
  }
}

label.c-radio-minimal-label {
  input {
    border-radius: 100px;
    border: 1px solid $input-border;
    font-weight: 400;
    margin-right: 0;
    margin-top: 4px;
    position: relative;
    &:checked:before {
      content: ' ';
      width: 8px;
      height: 8px;
      background: $brand-primary;
      position: absolute;
      top: 3px;
      left: 3px;
      border-radius: 100px;
    }
    &:focus {
      outline: 0;
      border: 1px solid $brand-primary;
    }
  }
}

.c-minimal-form-group .bc-form-flex input[type='text'] {
  max-height: 21px;
}

.c-minimal-form-group .bc-form-flex-text {
  max-height: 21px;
  line-height: 21px;
}
