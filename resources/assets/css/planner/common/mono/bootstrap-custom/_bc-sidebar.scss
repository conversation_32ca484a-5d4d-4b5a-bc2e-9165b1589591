/* ==========================================================================
  bc-sidebar
   ========================================================================== */

.bc-sidebar {
  .form-group-block-labels {
    .radio,
    .checkbox {
      label {
        margin: 0;
        padding-left: 32px;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    .active-highlight {
      &:hover {
        label {
          background-color: $gray;
        }
      }

      label {
        border-radius: 0;
        color: #fff;
        background-color: $gray;

        .btn-icon {
          svg path {
            fill: rgba(#fff, 0.8);
          }
        }
      }

      .text-muted {
        color: rgba(#fff, 0.6);
      }
    }
  }

  .form-group-block-labels-with-options {
    .radio,
    .checkbox {
      label {
        padding-right: 32px;
      }
    }
  }
}

.bc-sidebar-left {
  border-right: 1px solid $gray-light-border;
}

.bc-sidebar-right {
  border-left: 1px solid $gray-light-border;
}

.bc-sidebar-header {
  font-weight: 600;
  padding: 6px 12px;
  border-bottom: 1px solid $gray-light-border;
}

.bc-sidebar-section-header {
  font-weight: 600;
  padding: 6px 12px;
  border-bottom: 1px solid $gray-light;
}

.bc-sidebar-section {
  padding: 12px 0;

  .form-group-block-labels {
    margin: 0;
  }
}
