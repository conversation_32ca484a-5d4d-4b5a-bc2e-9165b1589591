/* ==========================================================================
  Block list
   ========================================================================== */

$block-list-item-bg: #fff !default;
$block-list-item-border-color: $gray-light !default;
$block-list-item-hover-color: $gray-lighter !default;
$block-list-item-active-color: $gray-lighter !default;
$block-list-padding: calc($grid-gutter-width / 2) calc($grid-gutter-width / 2) !default;
$block-list-highlight-color: #ffc !default;

/* Block list item
   ========================================================================== */

.bc-block-list {
  // ul or ol
  margin: 0;
  padding: 0;
}

/* Block list item
   ========================================================================== */

.bc-block-list-item {
  border-bottom: 1px solid $block-list-item-border-color;
  background: $block-list-item-bg;
  display: flex;
}

/*
  Sometimes a block list item is a list item, with a contained <a>; sometimes it
  is directly an <a> element.We support both situations for interactive lists (go to a link)
  or non interactive lists.
*/

a.bc-block-list-item,
.bc-block-list-item a {
  // We are supposing the block-list item in an <a> element here
  padding: $block-list-padding;
  text-decoration: none;
  &:hover,
  &:active,
  &:focus {
    background: $block-list-item-hover-color;
  }
}

.bc-block-list-item-active {
  background: $block-list-item-active-color;
}

/*
  This class needs to be applied to list items that don't contain links to switch over the padding
  from the <a> element to the list element
*/

.bc-block-list-item-no-link {
  padding: $block-list-padding;
}

/* Block item new
   ========================================================================== */

.bc-block-list-item-new {
  background: $block-list-highlight-color;
}

/* Disclosure arrow on right side
   ========================================================================== */

.bc-block-list-item-disclosure {
  margin-top: 6px;
  flex: 0 1 auto; // shrink
}

.bc-block-list-item-info {
  flex: 1 0 auto; // grow
  display: flex;
}

/* Variant with icons
   ========================================================================== */

.bc-block-list-item-has-icon {
  .bc-block-list-item-icon {
    flex: 0 1 auto; // shrink
    margin-right: 12px;
    img {
      width: 40px;
      height: 40px;
      display: block;
    }
  }

  /* Variant with gear icons
     ========================================================================== */

  .bc-block-list-item-icon-gear {
    img {
      width: 40px;
      height: 20px;
      margin-top: 8px;
    }
  }
}

/* Variant with avatars
   ========================================================================== */

.bc-block-list-item-has-avatar {
  .c-avatar {
    flex: 0 1 auto; // shrink
    margin-top: 4px;
    margin-right: 10px;
  }
  .bc-block-list-item-info-text-holder {
    margin-top: 2px;
  }
}

/* Innner text
   ========================================================================== */

.bc-block-list-item-info-text {
  .bc-block-list-item-info-text-holder &:only-child {
    margin-top: 10px;
  }
}

.bc-block-list-item-info-text-big {
  font-size: 17px;
  .bc-block-list-item-info-text-holder &:only-child {
    margin-top: 7px;
  }
}

.bc-block-list-item-info-text-medium {
  font-size: 15px;
  .bc-block-list-item-info-text-holder &:only-child {
    margin-top: 7px;
  }
}

.bc-block-list-item-info-text-mini {
  font-size: 12px;
}

.bc-block-list-item-info-text-bold {
  font-weight: $headings-font-weight;
}

/* Modifiers to change colors
   ========================================================================== */

.bc-block-list-colors-standard {
  .bc-block-list-item-info-text {
    color: $gray;
  }
  .bc-block-list-item-info-text-big,
  .bc-block-list-item-info-text-medium,
  .bc-block-list-item-info-text-bold {
    color: $gray-dark;
  }
}
