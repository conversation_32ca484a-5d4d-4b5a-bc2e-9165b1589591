<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   fill="#000000"
   height="24"
   viewBox="0 0 24 24"
   width="24"
   id="svg2"
   version="1.1"
   inkscape:version="0.91 r13725"
   sodipodi:docname="icon-vanin-settings.svg">
  <metadata
     id="metadata12">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs10" />
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1842"
     inkscape:window-height="1057"
     id="namedview8"
     showgrid="false"
     inkscape:zoom="27.812867"
     inkscape:cx="13.028"
     inkscape:cy="10.550794"
     inkscape:window-x="1990"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2" />
  <path
     d="M 21.738079,18.308711 13.477712,10.048478 C 14.294671,7.9607273 13.840804,5.509889 12.116112,3.7852248 10.300647,1.9697891 7.5774489,1.6067019 5.3988903,2.6051916 L 9.3021411,6.5083787 6.5789429,9.2315322 2.5849188,5.3283453 C 1.4956395,7.5068684 1.9495059,10.230022 3.7649713,12.045458 5.4896636,13.770121 7.940542,14.22398 10.028327,13.407035 l 8.260368,8.260232 c 0.363093,0.363088 0.907732,0.363088 1.270826,0 l 2.087785,-2.087751 c 0.453866,-0.363087 0.453866,-0.998489 0.09077,-1.270805 z"
     id="path6"
     inkscape:connector-curvature="0" />
</svg>
