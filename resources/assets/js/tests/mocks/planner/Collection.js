import Seeder from '../Seeder';
import { faker } from '@faker-js/faker';
import blueCover from '../../../../css/planner/common/images/placeholders/cover-blue.svg';
import greenCover from '../../../../css/planner/common/images/placeholders/cover-green.svg';
import Chapter from './Chapter';

export default class Collection extends Seeder {
  constructor(data = {}) {
    super();
    this.shareStatus = data.shareStatus || 'owner';
    this.archived = data.archived || null;
    this.auditInfo = {
      createdAt: this.created_at,
      updatedAt: this.updated_at,
    };
    this.owner = data.owner || null;
    this.authors = data.authors || this.owner ? [this.owner] : [];
    this.cover = data.cover || '';
    this.fallbackCover = data.activatedFrom?.publisherName == null ? blueCover : greenCover;
    this.cover_large = data.cover_large || '';
    this.name = data.name || faker.lorem.words();
    this.sharable = data.hasOwnProperty('sharable') ? data.sharable : true;
    this.is_shared = data.hasOwnProperty('is_shared') ? data.is_shared : false;
    this.targetAudiences = data.targetAudiences || [];
    this.favourited = data.hasOwnProperty('favourited') ? data.favourited : false;
    this.activatedFrom = data.activatedFrom || null;
    this.isAlreadyImported = data.hasOwnProperty('isAlreadyImported')
      ? data.isAlreadyImported
      : false;
    this.isWriteVersionAlreadyImported = data.hasOwnProperty('isWriteVersionAlreadyImported')
      ? data.isWriteVersionAlreadyImported
      : false;
    this.isWritable = data.hasOwnProperty('isWritable') ? data.isWritable : false;
    this.isCoAuthor = data.hasOwnProperty('isCoAuthor') ? data.isCoAuthor : false;
    this.canBeDeleted = data.hasOwnProperty('canBeDeleted') ? data.canBeDeleted : false;
    this.canUpdateOwnerProperties = data.hasOwnProperty('canUpdateOwnerProperties')
      ? data.canUpdateOwnerProperties
      : false;
    this.isDeactivated = data.hasOwnProperty('isDeactivated') ? data.isDeactivated : false;
    this.chapters = data.chapters || Chapter.createMultiple();
    this.template = data.template || 'DEFAULT';
    this.isLegacyCollection = data.isLegacyCollection ?? false;
    this.isMigrationPending = data.isMigrationPending ?? false;
  }
}
