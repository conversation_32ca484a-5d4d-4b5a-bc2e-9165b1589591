import { createPinia, setActivePinia } from 'pinia';
import { useFollowUpSystemButtons } from '../../stores/followUpSystemButtons';

setActivePinia(createPinia());

let store;

describe('followUpSystemButtons', () => {
  beforeEach(() => {
    store = useFollowUpSystemButtons();
    store.$reset();
  });

  it('set default state correctly', () => {
    expect(store.disabled).toBeFalsy();
    expect(store.errors).toStrictEqual({});
  });

  describe('actions', () => {
    it('sets disabled state correctly', () => {
      store.setDisabled(true);
      expect(store.buttonDisabled).toBeTruthy();
      store.setDisabled(false);
      expect(store.buttonDisabled).toBeFalsy();
    });

    it('sets errors correctly', () => {
      const errors = {
        test: 'test',
      };
      store.updateErrors(errors);
      expect(store.errors).toStrictEqual(errors);
    });
  });

  describe('getters', () => {
    describe('buttonDisabled', () => {
      it('returns correctly based on disabled state', () => {
        expect(store.buttonDisabled).toBeFalsy();
        store.setDisabled(true);
        expect(store.buttonDisabled).toBeTruthy();
        store.setDisabled(false);
        expect(store.buttonDisabled).toBeFalsy();
      });

      it('returns correctly based on error state', () => {
        expect(store.buttonDisabled).toBeFalsy();
        store.updateErrors({ test: 'test' });
        expect(store.hasErrors).toBeTruthy();
        expect(store.buttonDisabled).toBeTruthy();
      });
    });

    it('returns hasErrors correctly', () => {
      expect(store.hasErrors).toBeFalsy();
      store.updateErrors({ test: 'test' });
      expect(store.hasErrors).toBeTruthy();
    });
  });
});
