import { create<PERSON><PERSON>, setActivePinia } from 'pinia';
import { useFormButtons } from '../../stores/formButtons';

setActivePinia(createPinia());

let store;

describe('formButtons', () => {
  beforeEach(() => {
    store = useFormButtons();
    store.$reset();
  });

  it('set default state correctly', () => {
    expect(store.disableSave).toBeFalsy();
    expect(store.disabled).toBeFalsy();
    expect(store.disabledCounter).toBe(0);
    expect(store.loading).toBeFalsy();
    expect(store.errors).toStrictEqual({});
  });

  describe('actions', () => {
    it('sets disableSave correctly', () => {
      expect(store.disableSave).toBeFalsy();
      store.setDisableSave(true);
      expect(store.disableSave).toBeTruthy();
      store.setDisableSave(false);
      expect(store.disableSave).toBeFalsy();
    });

    it('sets disabled state correctly', () => {
      store.setDisabled(true);
      expect(store.disabledCounter).toBe(1);
      expect(store.buttonDisabled).toBeTruthy();
      store.setDisabled(true);
      expect(store.disabledCounter).toBe(2);
      expect(store.buttonDisabled).toBeTruthy();
      store.setDisabled(false);
      expect(store.disabledCounter).toBe(1);
      expect(store.buttonDisabled).toBeTruthy();
      store.setDisabled(false);
      expect(store.disabledCounter).toBe(0);
      expect(store.buttonDisabled).toBeFalsy();

      //test set disabled with 'force'
      store.disabledCounter = 20;
      store.disabled = true;
      expect(store.buttonDisabled).toBeTruthy();
      store.setDisabled(false, true);
      expect(store.buttonDisabled).toBeFalsy();
    });

    it('sets loading state correclty', () => {
      store.setLoading(true);
      expect(store.loading).toBeTruthy();
      store.setLoading(false);
      expect(store.loading).toBeFalsy();
    });

    it('sets errors correctly', () => {
      const errors = {
        test: 'test',
      };
      store.updateErrors(errors);
      expect(store.errors).toStrictEqual(errors);
    });
  });

  describe('getters', () => {
    describe('buttonDisabled', () => {
      it('returns correctly if disableSave is set', () => {
        expect(store.buttonDisabled).toBeFalsy();
        store.setDisableSave(true);
        expect(store.buttonDisabled).toBeTruthy();
      });

      it('returns correctly based on disabled state', () => {
        expect(store.buttonDisabled).toBeFalsy();
        store.setDisabled(true);
        expect(store.buttonDisabled).toBeTruthy();
        store.setDisabled(false);
        expect(store.buttonDisabled).toBeFalsy();
      });

      it('returns correctly based on error state', () => {
        expect(store.buttonDisabled).toBeFalsy();
        store.updateErrors({ test: 'test' });
        expect(store.hasErrors).toBeTruthy();
        expect(store.buttonDisabled).toBeTruthy();
      });
    });

    it('returns hasErrors correctly', () => {
      expect(store.hasErrors).toBeFalsy();
      store.updateErrors({ test: 'test' });
      expect(store.hasErrors).toBeTruthy();
    });
  });
});
