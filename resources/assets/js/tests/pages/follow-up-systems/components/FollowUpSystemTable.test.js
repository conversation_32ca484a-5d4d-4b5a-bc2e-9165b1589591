import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import FollowUpSystemTable from '../../../../pages/follow-up-systems/components/FollowUpSystemTable.vue';
import createTestingVuetify from '../../../helpers/CreateTestingVuetify.js';

const tableData = [
  {
    uid: 'cec0905a-a973-40f9-9e14-603f014c3e34',
    name: '<PERSON>',
    test_audience: 'LO-J3',
    test_moments: [
      {
        name: 'begin',
        score: 44,
        minScore: 0,
        maxScore: 120,
        is_editable: false,
        percentile: 10,
        zone: 'E',
        comment: null,
      },
      {
        name: 'middle',
        score: null,
        minScore: 0,
        maxScore: 120,
        is_editable: true,
        percentile: null,
        zone: null,
        comment: null,
      },
    ],
  },
];
let wrapper;

describe('FollowUpSystemTable', () => {
  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('shows editable scores as editable when the follow up system is editable', () => {
    wrapper = mountWrapper({ isEditable: true });
    expect(wrapper.vm.isScoreEditable(tableData[0]['test_moments'][0])).toBeFalsy();
    expect(wrapper.vm.isScoreEditable(tableData[0]['test_moments'][1])).toBeTruthy();
  });

  it('shows editable scores as non-editable when the follow up system is not editable', () => {
    wrapper = mountWrapper({ isEditable: false });
    expect(wrapper.vm.isScoreEditable(tableData[0]['test_moments'][0])).toBeFalsy();
    expect(wrapper.vm.isScoreEditable(tableData[0]['test_moments'][1])).toBeFalsy();
  });

  it('does not hide the bingelMax tooltip when the follow up system is editable', () => {
    wrapper = mountWrapper({ isEditable: true });
    expect(wrapper.vm.hideBlockedTooltip(tableData[0]['test_moments'][0])).toBeFalsy();
    expect(wrapper.vm.hideBlockedTooltip(tableData[0]['test_moments'][1])).toBeFalsy();
  });

  it('hides bingelMax tooltip when the follow up system is not editable, but the score is editable', () => {
    wrapper = mountWrapper({ isEditable: false });
    expect(wrapper.vm.hideBlockedTooltip(tableData[0]['test_moments'][0])).toBeFalsy();
    expect(wrapper.vm.hideBlockedTooltip(tableData[0]['test_moments'][1])).toBeTruthy();
  });
});

function mountWrapper(data = {}) {
  return mount(FollowUpSystemTable, {
    props: lodashDefaults(data, {
      followUpSystem: {},
      scoreBaseUrl: 'scoreBaseUrl',
      commentBaseUrl: 'commentBaseUrl',
      tableData: tableData,
      testMoments: [],
      percentileMapping: {},
      percentileAware: true,
      isVclb: true,
      zoneMapping: {},
      zoneName: 'zoneName',
    }),
    global: {
      plugins: [createTestingVuetify()],
    },
  });
}
