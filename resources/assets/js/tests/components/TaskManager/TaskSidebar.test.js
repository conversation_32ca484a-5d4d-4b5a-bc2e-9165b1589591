import lodashDefaults from 'lodash/defaults';
import User from '../../mocks/User';
import { default as UserDataModel } from '../../../models/dataModels/User';
import Task from '../../mocks/Task';
import { createTestingPinia } from '@pinia/testing';
import { useTaskStore } from '../../../stores/taskStore';
import createTestingVuetify from '../../helpers/CreateTestingVuetify.js';
import TaskSidebar from '../../../components/TaskManager/TaskSidebar.vue';
import { VApp } from 'vuetify/components';
import mountUsingParent from '../../helpers/mountUsingParent.js';

const user = new UserDataModel(new User());

let taskStore;
let wrapper;

describe('TaskSidebar', () => {
  it('renders correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  describe('can edit check', () => {
    it('it can edit a task when its in edit state', () => {
      wrapper = mountWrapper();
      taskStore.setTasks([new Task()]);
      taskStore.editTask(0);
      expect(wrapper.vm.canEdit).toBeTruthy();
    });

    it('it can edit a task when its in create state', () => {
      wrapper = mountWrapper();
      taskStore.setTasks([new Task()]);
      taskStore.createTaskInStore(0);
      expect(wrapper.vm.canEdit).toBeTruthy();
    });

    it('it cant edit a task when its in show state', () => {
      wrapper = mountWrapper();
      taskStore.setTasks([new Task()]);
      taskStore.showTask(0);
      expect(wrapper.vm.canEdit).toBeFalsy();
    });
  });

  describe('container height', () => {
    it('it has a larger container height in show state', () => {
      wrapper = mountWrapper();
      taskStore.setTasks([new Task()]);
      taskStore.showTask(0);
      expect(wrapper.vm.containerStyle).toStrictEqual({
        height: 'calc(100% - 64px)',
        overflow: 'auto',
      });
    });

    it('it has a smaller container height in edit state', () => {
      wrapper = mountWrapper();

      taskStore.setTasks([new Task()]);
      taskStore.editTask(0);
      expect(wrapper.vm.containerStyle).toStrictEqual({
        height: 'calc(100% - 64px - 36px)',
        overflow: 'auto',
      });
    });

    it('it has a smaller container height in create state', () => {
      wrapper = mountWrapper();
      taskStore.setTasks([new Task()]);
      taskStore.createTaskInStore(0);

      expect(wrapper.vm.containerStyle).toStrictEqual({
        height: 'calc(100% - 64px - 36px)',
        overflow: 'auto',
      });
    });
  });

  it('it increments the component key when the selected task changes', () => {
    const task = new Task();
    wrapper = mountWrapper();
    taskStore.setTasks([task]);
    expect(wrapper.vm.componentKey).toBe(0);
    taskStore.selectTask(task);
    wrapper.vm.$options.watch.selectedTask.call(wrapper.vm);
    expect(wrapper.vm.componentKey).toBe(1);
  });

  it('it shows the sidebar when the show task drawer state is changed', () => {
    wrapper = mountWrapper();
    wrapper.vm.$refs.validationObserver.resetForm = jest.fn();
    taskStore.setTasks([new Task()]);
    expect(wrapper.vm.drawerIsOpen).toBeFalsy();
    taskStore.showDrawer(true);
    wrapper.vm.$options.watch.showTaskDrawer.call(wrapper.vm);
    expect(wrapper.vm.drawerIsOpen).toBeTruthy();
    expect(wrapper.vm.$refs.validationObserver.resetForm).toHaveBeenCalled();
  });
});

function mountWrapper(parent, data = {}) {
  const pinia = createTestingPinia({ stubActions: false });
  const mounted = mountUsingParent(TaskSidebar, {
    parentComponent: VApp,
    props: lodashDefaults(data, {
      baseUrl: '/base-url',
      userSearchApi: '/api-search-url',
      user: user,
      careInputId: 1,
      tasksArray: [],
      element: {},
      parentErrors: {},
      validations: '',
      parentModel: {},
      parentFields: {},
      linkedFields: {},
    }),
    global: {
      plugins: [pinia, createTestingVuetify()],
      stubs: {
        DatePicker: { template: '<div><slot></slot></div>' },
        S3FileUploadButton: { template: '<div><slot></slot></div>' },
        VNavigationDrawer: { template: '<div><slot></slot></div>' },
      },
    },
  });

  taskStore = useTaskStore(pinia);
  taskStore.$reset();

  mounted.vm.$refs.validationObserver.resetForm = jest.fn();
  return mounted;
}
