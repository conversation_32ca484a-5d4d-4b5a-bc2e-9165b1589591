import GroupSelector from '../../components/GroupSelector';
import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import lodashCloneDeep from 'lodash/cloneDeep';
import Group from '../mocks/Group';
import createTestingVuetify from '../helpers/CreateTestingVuetify.js';

const groupSwitcherGroups = Group.createGroupList();
const groupSelector = {
  isSchoolRoute: false,
  schoolUrl: faker.internet.url(),
  showSchool: false,
  selected: {
    uid: groupSwitcherGroups[2][1][0].uid,
    name: groupSwitcherGroups[2][1][0].name,
  },
};
let wrapper;

enableAutoUnmount(afterEach);

describe('GroupSelector', () => {
  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('fetches the correct data from the $page props', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm.groups).toBe(groupSwitcherGroups.all);
    expect(wrapper.vm.isSchoolRoute).toBe(groupSelector.isSchoolRoute);
    expect(wrapper.vm.selectedGroupUid).toBe(groupSelector.selected.uid);
    expect(wrapper.vm.showSchool).toBe(groupSelector.showSchool);
    expect(wrapper.vm.schoolUrl).toBe(groupSelector.schoolUrl);
    expect(wrapper.vm.selectedGroupName).toBe(groupSelector.selected.name);
    expect(wrapper.vm.mappedGroups).toStrictEqual({
      1: groupSwitcherGroups['1'],
      2: groupSwitcherGroups['2'],
    });
  });

  it('shows correct selected group', () => {
    wrapper = mountWrapper();
    expect(wrapper.find('.value').text()).toBe(groupSelector.selected.name);
  });

  it('shows the groups correctly', async () => {
    wrapper = mountWrapper();
    wrapper.vm.mainMenuOpen = true;
    await wrapper.vm.$nextTick();
    const vMenuComponents = wrapper.findAllComponents({ name: 'VMenu' });
    const listItems = new DOMWrapper(document.body).findAll('.v-list-item');

    for (let i = 0; i < Object.keys(groupSwitcherGroups['1']).length; i++) {
      expect(listItems.at(i).find('.v-list-item-title').text()).toContain('Preschool');
      expect(listItems.at(i).find('.v-list-item-title').text()).toContain((i + 1).toString());
      await vMenuComponents
        .at(i + 1)
        .find('.v-list-item')
        .trigger('mouseenter');
      expect(new DOMWrapper(document.body).findAll('.v-list-item__content').at(-1).text()).toBe(
        groupSwitcherGroups['1'][i + 1][0].name
      );
    }
    for (let i = 0; i < Object.keys(groupSwitcherGroups['2']).length; i++) {
      expect(
        listItems
          .at(i + 3)
          .find('.v-list-item-title')
          .text()
      ).toContain('Schoolyear');
      expect(
        listItems
          .at(i + 3)
          .find('.v-list-item-title')
          .text()
      ).toContain((i + 1).toString());
      await vMenuComponents
        .at(i + 4)
        .find('.v-list-item')
        .trigger('mouseenter');
      await wrapper.vm.$nextTick();
      expect(new DOMWrapper(document.body).findAll('.v-list-item__content').at(-1).text()).toBe(
        groupSwitcherGroups['2'][i + 1][0].name
      );
    }
  });

  it('shows careGroups correctly', async () => {
    const groupsWitchCareGroups = groupSwitcherGroups.all;
    const careGroup = new Group({
      natural_study_year: null,
      target_audience_type: Group.targetAudienceTypes[1],
    });
    groupsWitchCareGroups.push(careGroup);
    wrapper = mountWrapper({ groupSwitcherGroups: groupsWitchCareGroups });
    wrapper.vm.mainMenuOpen = true;
    await wrapper.vm.$nextTick();
    const vMenuComponents = wrapper.findAllComponents({ name: 'VMenu' });
    const listItems = new DOMWrapper(document.body).findAll('.v-list-item');
    await vMenuComponents.at(-1).find('.v-list-item').trigger('mouseenter');
    expect(listItems.at(-1).text()).toContain('Caregroups');
    expect(new DOMWrapper(document.body).findAll('.v-list-item__content').at(-1).text()).toBe(
      careGroup.name
    );
  });

  it('does not show school item if showSchool is false', async () => {
    wrapper = mountWrapper();
    wrapper.vm.mainMenuOpen = true;
    await wrapper.vm.$nextTick();
    expect(new DOMWrapper(document.body).find('.v-list-item').text()).not.toBe('School');
  });

  it('shows school item if showSchool is true', async () => {
    const currentGroupSelector = lodashCloneDeep(groupSelector);
    currentGroupSelector.showSchool = true;
    wrapper = mountWrapper({ groupSelector: currentGroupSelector });
    wrapper.vm.mainMenuOpen = true;
    await wrapper.vm.$nextTick();
    expect(new DOMWrapper(document.body).find('.v-list-item').text()).toBe('School');
  });

  it('shows selected group as school when isSchoolRoute is true', () => {
    const currentGroupSelector = lodashCloneDeep(groupSelector);
    currentGroupSelector.isSchoolRoute = true;
    wrapper = mountWrapper({ groupSelector: currentGroupSelector });
    expect(wrapper.find('.value').text()).toBe('School');
  });

  describe('item click', () => {
    afterEach(() => {
      clearHTML();
      jest.resetAllMocks();
    });

    for (let i = 0; i < groupSwitcherGroups.all.length; i++) {
      const currentGroup = groupSwitcherGroups.all[i];
      it('goes to correct url when ' + currentGroup.name + ' item is clicked', async () => {
        wrapper = mountWrapper();
        wrapper.vm.mainMenuOpen = true;
        await wrapper.vm.$nextTick();
        const vMenuComponents = wrapper.findAllComponents({ name: 'VMenu' });
        await vMenuComponents
          .at(i + 1)
          .find('.v-list-item')
          .trigger('mouseenter');
        new DOMWrapper(document.body)
          .findAll('.v-overlay__content')
          .at(-1)
          .find('.v-list-item')
          .trigger('click');
        expect(window.location.assign).toHaveBeenCalled();
        expect(window.location.assign).toHaveBeenCalledWith(currentGroup.redirectLink);
      });
    }

    it('goes to correct url when the school item is clicked', async () => {
      const currentGroupSelector = lodashCloneDeep(groupSelector);
      currentGroupSelector.showSchool = true;
      wrapper = mountWrapper({ groupSelector: currentGroupSelector });
      wrapper.vm.mainMenuOpen = true;
      await wrapper.vm.$nextTick();
      new DOMWrapper(document.body).find('.v-list-item').trigger('click');
      expect(window.location.assign).toHaveBeenCalled();
      expect(window.location.assign).toHaveBeenCalledWith(currentGroupSelector.schoolUrl);
    });
  });
});

function mountWrapper(data = {}) {
  return mount(GroupSelector, {
    global: {
      plugins: [createTestingVuetify()],
      mocks: {
        $page: {
          props: lodashDefaults(data, {
            groupSwitcherGroups: groupSwitcherGroups.all,
            groupSelector: groupSelector,
          }),
        },
      },
    },
  });
}
