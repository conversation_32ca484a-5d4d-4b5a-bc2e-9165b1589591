import { flushPromises, mount } from '@vue/test-utils';
import createTestingVuetify from '../../../helpers/CreateTestingVuetify.js';
import ReportsDashboardDetail from '../../../../components/Planner/Reports/ReportsDashboardDetail.vue';
import DatePicker from '../../../../components/DatePicker.vue';
import School from '../../../../models/dataModels/School.js';
import User from '../../../../models/dataModels/User.js';
import Schoolyear from '../../../../models/dataModels/Schoolyear.js';
import axios from 'axios';

jest.mock('axios');
jest.mock('../../../../models/dataModels/School.js');
jest.mock('../../../../models/dataModels/User.js');
jest.mock('../../../../models/dataModels/Schoolyear.js');

describe('ReportsDashboardDetail', () => {
  let wrapper;

  const mockProps = {
    school: { uid: '123' },
    schoolyear: { startYear: 2023, start: '2023-09-01', end: '2024-06-30' },
    group: { uid: '456', name: 'Test Group' },
    learningArea: null,
    type: 'default',
  };

  const mockCurriculum = [
    { id: 'attitudes', name: 'Attitudes', childNodes: [] },
    { id: 'area1', name: 'Area 1', childNodes: [{ id: 'domain1', name: 'Domain 1' }] },
    { id: 'area2', name: 'Area 2', childNodes: [{ id: 'domain2', name: 'Domain 2' }] },
    {
      id: 'area3',
      name: 'Area 3',
      childNodes: [
        {
          id: 'domain3',
          name: 'Domain 3',
          childNodes: [{ id: 'learningPath1', name: 'Learning Path 1' }],
        },
      ],
    },
  ];

  beforeEach(() => {
    User.getCurrent.mockResolvedValue({});
    School.get.mockResolvedValue({
      getNetwork: jest.fn().mockResolvedValue({}),
      getCurriculum: jest.fn().mockResolvedValue(mockCurriculum),
      name: 'Test School',
    });
    Schoolyear.getSchoolyears.mockResolvedValue([]);
    School.getGroups.mockResolvedValue([]);
    User.search.mockResolvedValue([]);
    axios.get.mockResolvedValue({ data: '<span>Some data</span>' });

    wrapper = mount(ReportsDashboardDetail, {
      global: {
        plugins: [createTestingVuetify()],
        stubs: {
          PlannerIcon: true,
          DatePicker: true,
          SmartSvg: true,
          TkLoader: true,
          TkContent: true,
          TkWorkspaceMain: true,
          TkWorkspaceToolbox: true,
          TkWorkspace: true,
          TkPageMain: true,
          TkPage: true,
          ZillDetailWrapper: true,
        },
      },
      props: mockProps,
    });
  });

  it('initializes with correct data', async () => {
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isFetching).toBe(false);
    expect(wrapper.vm.contextCollapsed).toBe(false);
    expect(wrapper.vm.reportType).toBe('POS');
  });

  it('calls necessary methods on mount', async () => {
    await wrapper.vm.$nextTick();
    expect(User.getCurrent).toHaveBeenCalled();
    expect(School.get).toHaveBeenCalledWith(mockProps.school.uid);
    expect(Schoolyear.getSchoolyears).toHaveBeenCalledWith(mockProps.school.uid);
  });

  it('updates dates correctly', async () => {
    const newDate = new Date('2023-10-01');
    await wrapper.setData({ beginDate: newDate });
    expect(wrapper.vm.beginDate).toEqual(newDate);
  });

  it('loads report when necessary data changes', async () => {
    const loadReportSpy = jest.spyOn(wrapper.vm, 'loadReport');
    await wrapper.setData({ beginDate: new Date('2023-10-01') });
    expect(loadReportSpy).toHaveBeenCalled();
  });

  it('resets filter correctly', async () => {
    await wrapper.vm.resetFilter();
    expect(wrapper.vm.reportType).toBe('POS');
    expect(wrapper.vm.selectedTeacher).toBe('');
    expect(wrapper.vm.selectedLearningArea).toBe('');
    expect(wrapper.vm.selectedDomain).toBe('');
  });

  it('handles learning area change correctly', async () => {
    await wrapper.vm.changeSelectedLearningArea();
    expect(wrapper.vm.selectedDomain).toBe('');
  });

  it('handles domain change correctly', async () => {
    await wrapper.vm.changeSelectedLearningArea();
    expect(wrapper.vm.selectedLearningPath).toBe('');
  });

  it('generates correct Zill link', () => {
    const link = wrapper.vm.generateZillLink('/test');
    expect(link).toBe('https://cached-api.katholiekonderwijs.vlaanderen/test');
  });

  /** INFO: HTML checks **/

  it('renders the correct toolbar title', async () => {
    await wrapper.vm.$nextTick();
    const toolbarTitle = wrapper.find('.bc-toolbar-title');
    expect(toolbarTitle.exists()).toBe(true);
    expect(toolbarTitle.text()).toContain('Reports - Test School - Test Group');
  });

  it('renders the filters section', async () => {
    await wrapper.vm.$nextTick();
    const filtersHeader = wrapper.find('form header');
    expect(filtersHeader.exists()).toBe(true);
    expect(filtersHeader.text()).toBe('Filters');
  });

  it('renders the learning area select', async () => {
    await wrapper.vm.$nextTick();
    const learningAreaSelect = wrapper.findAll('.c-spacer-top-s .v-select').at(0);
    expect(learningAreaSelect.exists()).toBe(true);
  });

  it('renders the domain select when a learning area is selected', async () => {
    await wrapper.setData({
      selectedLearningArea: 'area1',
      curriculum: mockCurriculum,
    });
    await wrapper.vm.$nextTick();
    const domainSelect = wrapper.find('.c-spacer-top .v-select');
    expect(domainSelect.exists()).toBe(true);
  });

  it('renders the learning path select when a domain is selected and is Leer Lokaal', async () => {
    await wrapper.setData({
      selectedLearningArea: 'area3',
      selectedDomain: 'domain3',
      isLeerLokaal: true,
      curriculum: mockCurriculum,
    });
    await wrapper.vm.$nextTick();
    const learningPathSelect = wrapper.get('#learningPathSelect');
    expect(learningPathSelect.exists()).toBe(true);
  });

  it('shows report for specific learning path', async () => {
    await wrapper.setData({
      selectedLearningArea: 'area3',
      selectedDomain: 'domain3',
      selectedLearningPath: 'learningPath1',
      isLeerLokaal: true,
      curriculum: mockCurriculum,
    });
    await wrapper.vm.$nextTick();

    const learningAreaBlock = wrapper.find('zill-detail-wrapper-stub > div');

    expect(learningAreaBlock.find('h1').text()).toBe('Area 3');
    expect(learningAreaBlock.find('h3').text()).toBe('Domain 3');
  });

  it('does not render the learning path select when a domain is not selected', async () => {
    await wrapper.setData({
      selectedLearningArea: 'area3',
      isLeerLokaal: true,
      curriculum: mockCurriculum,
    });
    await wrapper.vm.$nextTick();
    const learningPathSelect = wrapper.find('#learningPathSelect');
    expect(learningPathSelect.exists()).toBe(false);
  });

  it('does not render the learning path select when is not Leer Lokaal', async () => {
    await wrapper.setData({
      selectedLearningArea: 'area3',
      selectedDomain: 'domain3',
      isLeerLokaal: false,
      curriculum: mockCurriculum,
    });
    await wrapper.vm.$nextTick();
    const learningPathSelect = wrapper.find('#learningPathSelect');
    expect(learningPathSelect.exists()).toBe(false);
  });

  it('renders the date pickers', async () => {
    await wrapper.vm.$nextTick();
    const datePickers = wrapper.findAllComponents(DatePicker);
    expect(datePickers.length).toBe(2);
    const labels = wrapper.findAll('.v-col-12 label');
    expect(labels[0].text()).toBe('Begin date');
    expect(labels[1].text()).toBe('End date');
  });

  it('renders the report type buttons for non-Zill reports', async () => {
    await wrapper.vm.$nextTick();
    const reportTypeButtons = wrapper.findAll('.v-btn-toggle .v-btn');
    expect(reportTypeButtons.length).toBe(2);
    expect(reportTypeButtons[0].text()).toBe('Positive');
    expect(reportTypeButtons[1].text()).toBe('Negative');
  });

  it('does not render report type buttons for Zill reports', async () => {
    await wrapper.setProps({ type: 'zill' });
    await wrapper.vm.$nextTick();
    const reportTypeButtons = wrapper.find('.v-btn-toggle');
    expect(reportTypeButtons.exists()).toBe(false);
  });

  it('renders the teacher select', async () => {
    await wrapper.vm.$nextTick();
    const teacherSelect = wrapper.findAll('.c-spacer-top-s .v-select').at(1);
    expect(teacherSelect.exists()).toBe(true);
  });

  it('displays empty report message when there are no reports', async () => {
    await wrapper.setData({
      emptyReports: true,
      isFetching: false,
      loading: false,
    });
    await wrapper.vm.$nextTick();
    const emptyMessage = wrapper.find('.c-spacer.c-util-is-scrollable');
    expect(emptyMessage.text()).toContain('No goals found for the given criteria.');
    const resetLink = emptyMessage.find('a');
    expect(resetLink.exists()).toBe(true);
    expect(resetLink.text()).toBe('Reset criteria');
  });

  it('displays report message when there are reports', async () => {
    await flushPromises();

    const learningAreaBlocks = wrapper.findAll('zill-detail-wrapper-stub > div');
    expect(learningAreaBlocks.length).toBe(4);

    expect(learningAreaBlocks[0].find('h1').text()).toBe('Attitudes');
    expect(learningAreaBlocks[0].find('h3').text()).toBe('');
    expect(learningAreaBlocks[0].find('span').text()).toBe('Some data');

    expect(learningAreaBlocks[1].find('h1').text()).toBe('Area 1');
    expect(learningAreaBlocks[1].find('h3').text()).toBe('Domain 1');
    expect(learningAreaBlocks[1].find('span').text()).toBe('Some data');

    expect(learningAreaBlocks[2].find('h1').text()).toBe('Area 2');
    expect(learningAreaBlocks[2].find('h3').text()).toBe('Domain 2');
    expect(learningAreaBlocks[2].find('span').text()).toBe('Some data');

    expect(learningAreaBlocks[3].find('h1').text()).toBe('Area 3');
    expect(learningAreaBlocks[3].find('h3').text()).toBe('Domain 3');
    expect(learningAreaBlocks[3].find('span').text()).toBe('Some data');
  });

  it('renders the print button', async () => {
    await wrapper.vm.$nextTick();
    const printButton = wrapper.find('.bc-toolbar-right .v-btn');
    expect(printButton.exists()).toBe(true);
  });

  it('toggles context collapse when button is clicked', async () => {
    const collapseButton = wrapper.find('.bc-toolbar-element button');
    await collapseButton.trigger('click');
    expect(wrapper.vm.contextCollapsed).toBe(true);
    await collapseButton.trigger('click');
    expect(wrapper.vm.contextCollapsed).toBe(false);
  });
});
