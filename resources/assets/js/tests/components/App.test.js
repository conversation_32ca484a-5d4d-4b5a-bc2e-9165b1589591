import App from '../../components/App';
import { shallowMount } from '@vue/test-utils';
import lodashCloneDeep from 'lodash/cloneDeep';
import { createTestingPinia } from '@pinia/testing';
import dayjs from 'dayjs';
import createTestingVuetify from '../helpers/CreateTestingVuetify.js';
import PageData from '../mocks/PageData.js';
import { productFruits } from 'product-fruits';

const $page = {
  props: new PageData(),
};

jest.mock('product-fruits', () => ({
  productFruits: {
    init: jest.fn(),
  },
}));

describe('App.vue', () => {
  const title = faker.lorem.word();
  const tenantConfig = {
    tenant: 'sol-fl',
    i18n: {
      preferred: 'en',
      fullPreferred: 'en-gb',
      fallback: 'en',
      fullFallback: 'en-gb',
    },
    timezone: 'Europe/Brussels',
    analytics: {
      agenda: 'UA-17925767-30',
    },
    educationalNetworkConfig: {
      '51704c81-595a-427e-90cf-74b2f50c13f5': {
        learningTrailConfigs: [
          { name: 'KK', naturalStudyYear: null, targetAudienceType: 'jk' },
          { name: 'GK', naturalStudyYear: null, targetAudienceType: 'ok' },
          { name: '1ste', naturalStudyYear: 1, targetAudienceType: 'lo' },
          { name: '2de', naturalStudyYear: 2, targetAudienceType: 'lo' },
          { name: '3de', naturalStudyYear: 3, targetAudienceType: 'lo' },
          { name: '4de', naturalStudyYear: 4, targetAudienceType: 'lo' },
          { name: '5de', naturalStudyYear: 5, targetAudienceType: 'lo' },
          { name: '6de', naturalStudyYear: 6, targetAudienceType: 'lo' },
        ],
        gradeLevelClassification: { A: 1, B: 3, H: 5 },
      },
    },
    externalUrls: {
      help: 'https://int.bingel.be/teacher/help#planner',
      logout: 'https://int.bingel.be/login#planner',
      profile: 'https://int.bingel.be/teacher/profile/edit#planner',
      return: 'https://int.bingel.be/teacher#planner',
      schools: 'https://int.bingel.be/teacher/profile/switch#planner',
      settings: 'https://int.bingel.be/teacher/admin#planner',
      'user-settings': 'https://int.bingel.be/teacher/profile/user-settings#planner',
    },
    features: {
      solCollectionImport: true,
      solPreschoolCollectionImport: true,
      showProductFruits: true,
    },
    aws: {
      s3Url: 'https://edubase-sol2.s3-eu-west-1.amazonaws.com',
      s3Prefix: 'sol2-acc-app',
      cloudfrontUrl: 'https://tms-cdn-acc.bingel.be',
      maxUploadSize: 262144000,
      useCdn: false,
    },
    maxUploadSize: 524288000,
    bingelBaseUrl: 'https://int.bingel.be',
    appSwitcherConfig: [
      { app: 'home', icon: 'mdi-home', href: 'https://int.bingel.be/teacher/dashboard' },
      {
        app: 'methods',
        icon: 'sol-bingel-collections',
        href: 'https://int.bingel.be/teacher/my-methods',
      },
      {
        app: 'island',
        icon: 'sol-bingel-island',
        href: 'https://int.bingel.be/teacher/bingel-island',
      },
      { app: 'care', icon: 'group', permission: 3 },
      { app: 'planner', icon: 'today', permission: 2 },
      { app: 'bingel-settings', icon: 'mdi-wrench', href: '/settings', permission: 5 },
      { app: 'evaluation', icon: 'show_chart', permission: 4 },
    ],
  };

  const contentSlotData = '<div class="content-slot-data">content slot</div>';
  const navbarTopSlotData = '<div class="navbar-top-slot-data">navbarTop slot</div>';
  const appSwitcherSlotData = '<div class="app-switcher-slot-data">appSwitcher slot</div>';
  const navigationTopSlotData = '<div class="navigation-top-slot-data">navigationTop slot</div>';
  const tag = document.createElement('meta');
  tag.setAttribute('name', 'csrf-token');
  tag.setAttribute('content', 'abc123');
  document.head.appendChild(tag);

  const wrapper = shallowMount(App, {
    axios,
    props: {
      title: title,
      tenantConfig: tenantConfig,
    },
    slots: {
      content: contentSlotData,
      'navbar-top': navbarTopSlotData,
      'app-switcher': appSwitcherSlotData,
      'navigation-top': navigationTopSlotData,
    },
    global: {
      plugins: [createTestingPinia(), createTestingVuetify()],
      mocks: {
        $page,
      },
    },
  });

  it('is a Vue instance', () => {
    expect(wrapper.vm).toBeTruthy();
  });

  it('receives title property', () => {
    expect(wrapper.props().title).toBe(title);
  });

  it('receives tenantConfig property', () => {
    expect(wrapper.props().tenantConfig).toStrictEqual(tenantConfig);
  });

  it('renders content slot data correctly', () => {
    expect(wrapper.find('.content-slot-data').html()).toBe(contentSlotData);
  });

  it('renders navbarTop slot data correctly', () => {
    expect(wrapper.find('.navbar-top-slot-data').html()).toBe(navbarTopSlotData);
  });

  it('renders navigationTop slot data correctly', () => {
    expect(wrapper.find('.navigation-top-slot-data').html()).toBe(navigationTopSlotData);
  });

  it('renders appSwitcher slot data correctly', () => {
    expect(wrapper.find('.app-switcher-slot-data').html()).toBe(appSwitcherSlotData);
  });

  it('sets the dayjs locale correctly when fullPreferred is nl-be', () => {
    const dutchConfig = lodashCloneDeep(tenantConfig);
    dutchConfig.i18n.fullPreferred = 'nl-be';
    wrapper.vm.configureApp(dutchConfig);
    expect(wrapper.vm.$dayjs.locale()).toBe('nl-be');
    expect(dayjs.locale()).toBe('nl-be');
  });

  it('initializes productFruits correctly', () => {
    expect(productFruits.init).toHaveBeenCalled();
    expect(productFruits.init).toHaveBeenCalledWith('mocked-product-fruits-key', 'en', {
      firstname: expect.any(String),
      signUpAt: expect.any(String),
      username: expect.any(String),
    });
  });
});
