import ListTitle from '../../components/ListTitle';
import { shallowMount } from '@vue/test-utils';

describe('FormTitle.vue', () => {
  const title = faker.lorem.word();

  const wrapper = shallowMount(ListTitle, {
    slots: {
      default: title,
    },
  });

  it('is a Vue instance', () => {
    expect(wrapper.vm).toBeTruthy();
  });

  it('renders formTitle correctly', () => {
    expect(wrapper.find('.list-title').text()).toBe(title);
  });
});
