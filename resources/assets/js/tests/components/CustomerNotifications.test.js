import CustomerNotifications from '../../components/CustomerNotifications';
import { DOMWrapper, flushPromises, mount } from '@vue/test-utils';
import axios from 'axios';
import moxios from 'moxios';
import CustomerNotification from '../mocks/CustomerNotification';
import createTestingVuetify from '../helpers/CreateTestingVuetify.js';
import moxiosWait from '../helpers/MoxiosWait.js';

let wrapper;

const notificationType = 'planner';
const customerNotifications = CustomerNotification.createMultiple(2);

describe('CustomerNotifications', () => {
  beforeEach(() => {
    moxios.install(axios);
    jest.resetAllMocks();
  });

  afterEach(() => {
    moxios.uninstall(axios);
    wrapper.unmount();
  });

  it('destroys itself when check_new_messages is false', async () => {
    const selfDestructSpy = jest.spyOn(CustomerNotifications.methods, 'selfDestruct');
    wrapper = mountWrapper({
      check_new_messages: false,
    });

    await flushPromises();

    expect(wrapper.vm.notificationType).toBeNull();
    expect(moxios.requests.__items.length).toBe(0);
    expect(selfDestructSpy).toHaveBeenCalled();
  });

  it('fetches the notifications when check_new_messages is true', async () => {
    moxios.stubRequest('/customer-notifications/' + notificationType, {
      status: 200,
      response: customerNotifications,
    });

    wrapper = mountWrapper({
      check_new_messages: true,
    });

    await flushPromises();

    expect(wrapper.vm.notificationType).toBe(notificationType);
    expect(moxios.requests.mostRecent().url).toBe('/customer-notifications/' + notificationType);
  });

  it('destroys itself when notifications are empty', async () => {
    const selfDestructSpy = jest.spyOn(CustomerNotifications.methods, 'selfDestruct');
    moxios.stubRequest('/customer-notifications/' + notificationType, {
      status: 200,
      response: [],
    });

    wrapper = mountWrapper({
      check_new_messages: true,
    });

    expect(wrapper.vm.notificationType).toBe(notificationType);
    expect(moxios.requests.mostRecent().url).toBe('/customer-notifications/' + notificationType);
    await moxiosWait();
    expect(selfDestructSpy).toHaveBeenCalled();
  });

  describe('there are notifications', () => {
    beforeEach(async () => {
      moxios.stubRequest('/customer-notifications/' + notificationType, {
        status: 200,
        response: customerNotifications,
      });
      moxios.stubRequest('/customer-notifications/' + notificationType + '/confirm-read', {
        status: 200,
      });
      wrapper?.unmount();
      wrapper = mountWrapper({
        check_new_messages: true,
      });
      expect(moxios.requests.mostRecent().url).toBe('/customer-notifications/' + notificationType);
      await moxios.wait();
      await wrapper.vm.$nextTick();
      await flushPromises();
    });

    it('fetches the notifications correctly', () => {
      expect(wrapper.vm.notifications).toEqual(customerNotifications);
    });

    it('shows the modal and first item when there are notifications', async () => {
      expect(wrapper.vm.isOpen).toBeTruthy();
      const dialogContent = new DOMWrapper(document.body);

      expect(dialogContent.html()).toContain(customerNotifications[0].title);
      expect(dialogContent.html()).toContain(customerNotifications[0].message);
      expect(dialogContent.html()).toContain(customerNotifications[0].link);
      expect(dialogContent.html()).not.toContain(customerNotifications[1].title);
    });

    it('shows the second notification when clicking next', async () => {
      const showNotificationSpy = jest.spyOn(wrapper.vm, 'showNotification');
      expect(wrapper.vm.isOpen).toBeTruthy();
      const dialogContent = new DOMWrapper(document.body).find('.v-dialog');
      expect(dialogContent.html()).toContain(customerNotifications[0].title);

      await dialogContent.find('.v-btn.text-primary').trigger('click');
      await flushPromises();

      expect(wrapper.vm.shownNotification).toBe(1);
      expect(showNotificationSpy).toHaveBeenCalledWith(1);
      expect(dialogContent.html()).toContain(customerNotifications[1].title);
    });

    it('sends the viewed notifications to the backend when closed', async () => {
      const setNotificationsAsReadSpy = jest.spyOn(wrapper.vm, 'setNotificationsAsRead');
      expect(wrapper.vm.isOpen).toBeTruthy();
      const dialogContent = new DOMWrapper(document.body).find('.v-dialog');
      expect(dialogContent.html()).toContain(customerNotifications[0].title);

      await dialogContent.find('.v-btn.text-primary').trigger('click');
      await flushPromises();
      await dialogContent.find('.v-btn.text-primary').trigger('click');
      await flushPromises();

      await wrapper.vm.$nextTick();
      expect(wrapper.vm.isOpen).toBeFalsy();
      expect(setNotificationsAsReadSpy).toHaveBeenCalled();
      const mostRecentRequest = moxios.requests.mostRecent();
      expect(mostRecentRequest.url).toBe(
        '/customer-notifications/' + notificationType + '/confirm-read'
      );
      expect(mostRecentRequest.config.data).toEqual(
        JSON.stringify({
          notifications: customerNotifications,
        })
      );
    });
  });
});

function mountWrapper(data = {}) {
  return mount(CustomerNotifications, {
    global: {
      plugins: [createTestingVuetify()],
      mocks: {
        $page: {
          props: {
            customerNotification: {
              check_new_messages: data.check_new_messages || false,
              type: data.type || notificationType,
            },
          },
        },
      },
    },
  });
}
