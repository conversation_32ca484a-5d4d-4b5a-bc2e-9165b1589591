import RedicodisSelector from '../../../components/RedicodisManager/RedicodisSelector';
import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import Redicodi from '../../mocks/Redicodi';
import LearningArea from '../../mocks/LearningArea';
import moxios from 'moxios';
import { createTestingPinia } from '@pinia/testing';
import createTestingVuetify from '../../helpers/CreateTestingVuetify.js';

let items;
let parentItems;
let selectedItems;
const saveUrl = 'saveUrl';
const redicodiLinkUrl = 'redicodiLinkUrl';
let reportItems;
let linkedItems;

function setUp() {
  items = Redicodi.createRedicodisTree();
  parentItems = [items[0], items[1], items[2]];
  selectedItems = [
    items[0].children[0],
    items[0].children[1],
    items[1].children[1],
    items[2].children[0],
  ];
  reportItems = LearningArea.createLearningAreaTree();

  linkedItems = [
    {
      uid: reportItems[0].uid,
      name: reportItems[0].name,
      parent_name: null,
    },
    {
      uid: reportItems[0].domains[0].uid,
      name: reportItems[0].domains[0].name,
      parent_name: reportItems[0].name,
    },
    {
      uid: reportItems[1].domains[0].uid,
      name: reportItems[1].domains[0].name,
      parent_name: reportItems[1].name,
    },
  ];
}

enableAutoUnmount(afterEach);

describe('RedicodisSelector', () => {
  let wrapper;
  jest.useFakeTimers();
  let dialogContent;

  beforeEach(() => {
    setUp();
    moxios.install(axios);
  });

  afterEach(() => {
    moxios.uninstall(axios);
    wrapper.unmount();
  });

  it('renders correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('shows the selected redicodis as chips', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.$nextTick();

    const chips = wrapper.findAll('.v-chip--label');
    expect(chips.length).toBe(3);
    for (let i = 0; i < chips.length; i++) {
      const currentChip = chips.at(i);
      expect(currentChip.text()).toContain(parentItems[i].name);
    }
  });

  it('shows the correct count next to the redicodi chip', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.$nextTick();

    expect(wrapper.find('.v-chip--label').text()).toContain(parentItems[0].name);
    //there are 2 children of this item in selectedItems
    expect(wrapper.find('.v-chip--label').text()).toContain('2');

    expect(wrapper.findAll('.v-chip--label').at(1).text()).toContain(parentItems[1].name);
    expect(wrapper.findAll('.v-chip--label').at(1).text()).toContain('1');
  });

  it('opens the dialog when clicking the button', async () => {
    wrapper = mountWrapper();
    const setDialogSpy = jest.spyOn(wrapper.vm, 'setDialog');
    wrapper.find('.v-btn').trigger('click');
    expect(setDialogSpy).toHaveBeenCalledWith(true);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isOpen).toBeTruthy();
  });

  it('shows the tree data correctly', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    expect(dialogContent.exists()).toBeTruthy();

    const treeNodes = dialogContent.findAll('.v-list-item');
    expect(treeNodes.length).toBe(3);
    for (let i = 0; i < treeNodes.length; i++) {
      const currentTreeNode = treeNodes.at(i);
      expect(currentTreeNode.text()).toContain(parentItems[i].name);
    }

    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();
    const childrenContent = dialogContent.findAll('.v-list-group__items .v-list-item');

    expect(childrenContent.at(0).text()).toContain(selectedItems[0].name);
    expect(childrenContent.at(1).text()).toContain(selectedItems[1].name);
    //check if lowest level has checkbox
    expect(childrenContent.at(0).find('input[type=checkbox]').exists()).toBe(true);

    //check if the RedicodiLinkModal is shown
    expect(childrenContent.at(0).find('.v-list-item__append button').exists()).toBeTruthy();
  });

  it('shows checkboxes when no children are present', async () => {
    let childlessitems = Redicodi.createRedicodisTree(1, 1);
    parentItems = [items[0], items[1], items[2], childlessitems[0]];

    wrapper = mountWrapper({ items: parentItems });
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    expect(dialogContent.exists()).toBeTruthy();

    const treeNodes = dialogContent.findAll('.v-list-item');
    expect(treeNodes.length).toBe(4);
    for (let i = 0; i < treeNodes.length; i++) {
      const currentTreeNode = treeNodes.at(i);
      expect(currentTreeNode.text()).toContain(parentItems[i].name);
      let shouldHaveCheckbox = i === 3;
      expect(currentTreeNode.find('input[type=checkbox]').exists()).toBe(shouldHaveCheckbox);
    }
  });

  it('shows reportItems linked to redicodis', async () => {
    parentItems[0].subjects_follow_up_systems = linkedItems;
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');

    expect(dialogContent.text()).toContain(linkedItems[0].name);
    expect(dialogContent.text()).toContain(
      linkedItems[1].parent_name + ' - ' + linkedItems[1].name
    );
    expect(dialogContent.text()).toContain(
      linkedItems[2].parent_name + ' - ' + linkedItems[2].name
    );
  });

  test('next button is disabled when nothing is changed', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');

    expect(
      dialogContent.find('.v-card-actions button:not(.v-btn--variant-text)').classes()
    ).toContain('v-btn--disabled');
  });

  it('adds selected item to addedItems array when clicked', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();

    const firstDeselectedChild = dialogContent.find(
      '.v-selection-control__wrapper:not(.text-primary)'
    );
    await firstDeselectedChild.find('input').trigger('click');
    //wait for debounced function to be called
    jest.runAllTimers();
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.addedItems.length).toBe(1);
    expect(firstDeselectedChild.classes()).toContain('text-primary');
  });

  it('adds selected item to removedItems array when clicked', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();

    const firstChildNode = dialogContent.find('.v-list-group__items .v-list-item');
    await firstChildNode.find('input').trigger('click');
    //wait for debounced function to be called
    jest.runAllTimers();
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.removedItems.length).toBe(1);
    expect(firstChildNode.find('.v-selection-control__wrapper').classes()).not.toContain(
      'text-primary'
    );
  });

  test('next button is active when selection has changed', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();

    const firstDeselectedChild = dialogContent.find(
      '.v-selection-control__wrapper:not(.text-primary)'
    );
    const firstSelectedChild = dialogContent.find('.v-list-group__items .v-list-item');
    await firstDeselectedChild.find('input').trigger('click');
    await firstSelectedChild.find('input').trigger('click');

    //wait for debounced function to be called
    jest.runAllTimers();
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.addedItems.length).toBe(1);
    expect(wrapper.vm.removedItems.length).toBe(1);
    expect(
      dialogContent.find('.v-card-actions button:not(.v-btn--variant-text)').classes()
    ).not.toContain('v-btn--disabled');
  });

  it('goes to next step when next is clicked', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');
    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();

    const firstDeselectedChild = dialogContent.find(
      '.v-selection-control__wrapper:not(.text-primary)'
    );
    const firstSelectedChild = dialogContent.find('.v-list-group__items .v-list-item');
    await firstDeselectedChild.find('input').trigger('click');
    await firstSelectedChild.find('input').trigger('click');

    //wait for debounced function to be called
    jest.runAllTimers();
    await wrapper.vm.$nextTick();

    await dialogContent.find('.v-card-actions button:not(.v-btn--variant-text)').trigger('click');
    expect(wrapper.vm.windowStep).toBe(2);

    const activeWindows = dialogContent.findAll('.v-window-item--active');

    expect(activeWindows.at(0).text()).toContain(i18n.t('labels.redicodis-selector.change-reason'));

    expect(activeWindows.at(1).text()).toContain(i18n.t('labels.redicodi.added'));
    expect(activeWindows.at(1).find('.v-container').html()).toContain('v-autocomplete');
    expect(activeWindows.at(1).find('.v-container').html()).toContain('v-textarea');

    expect(activeWindows.at(1).text()).toContain(i18n.t('labels.redicodi.removed'));
    expect(activeWindows.at(1).findAll('.v-container').at(1).html()).not.toContain(
      'v-autocomplete'
    );
    expect(activeWindows.at(1).findAll('.v-container').at(1).html()).toContain('v-textarea');
  });

  it('sends the correct data to the backend to be saved', async () => {
    wrapper = mountWrapper();
    await wrapper.find('.v-btn').trigger('click');
    dialogContent = new DOMWrapper(document.body).find('.v-dialog');

    wrapper.vm.openChildren = [parentItems[0].uid];
    await wrapper.vm.$nextTick();

    const firstDeselectedChild = dialogContent.find(
      '.v-selection-control__wrapper:not(.text-primary)'
    );
    const firstSelectedChild = dialogContent.find('.v-list-group__items .v-list-item');
    await firstDeselectedChild.find('input').trigger('click');
    await firstSelectedChild.find('input').trigger('click');

    //wait for debounced function to be called
    jest.runAllTimers();
    await wrapper.vm.$nextTick();

    await dialogContent.find('.v-card-actions button:not(.v-btn--variant-text)').trigger('click');
    const saveBtn = dialogContent.find('.v-card-actions button:not(.v-btn--variant-text)');
    const saveSpy = jest.spyOn(wrapper.vm, 'saveSelectedItems');

    wrapper.vm.addedItems[0].comment = 'hello there';
    wrapper.vm.removedItems[0].comment = 'goodbye';
    await saveBtn.trigger('click');
    expect(saveSpy).toHaveBeenCalled();
    const requestData = JSON.parse(moxios.requests.mostRecent().config.data);

    expect(moxios.requests.mostRecent().url).toBe(saveUrl);

    expect(requestData.redicodis).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          uid: items[0].children[2].uid,
          comment: 'hello there',
        }),
      ])
    );

    expect(requestData.redicodis).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          uid: items[0].children[0].uid,
          deleted: true,
          comment: 'goodbye',
        }),
      ])
    );
  });

  it('opens the dialog and goes to correct item when clicking on chip', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.$nextTick();
    const setDialogSpy = jest.spyOn(wrapper.vm, 'setDialog');

    wrapper.find('.v-chip').trigger('click');
    expect(setDialogSpy).toHaveBeenCalled();
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isOpen).toBeTruthy();
    expect(wrapper.vm.openChildren).toStrictEqual([parentItems[0].uid]);
  });

  it('does not render the linked items when feature flag is off', async () => {
    parentItems[0].subjects_follow_up_systems = linkedItems;
    wrapper = mountWrapper({ showRedicodisOnReport: false });
    await wrapper.vm.$nextTick();

    await wrapper.find('.v-chip').trigger('click');

    dialogContent = new DOMWrapper(document.body).find('.v-dialog');

    expect(dialogContent.text()).not.toContain(linkedItems[0].name);
    expect(dialogContent.text()).not.toContain(
      linkedItems[1].parent_name + ' - ' + linkedItems[1].name
    );
    expect(dialogContent.text()).not.toContain(
      linkedItems[2].parent_name + ' - ' + linkedItems[2].name
    );
  });
});

function mountWrapper(data = {}) {
  return mount(RedicodisSelector, {
    attachTo: document.body,
    props: lodashDefaults(data, {
      items: parentItems,
      modelValue: selectedItems,
      saveUrl: saveUrl,
      redicodiLinkUrl: redicodiLinkUrl,
      reportItems: reportItems,
      showRedicodisOnReport:
        data.showRedicodisOnReport !== undefined ? data.showRedicodisOnReport : true,
    }),
    global: {
      plugins: [
        createTestingPinia({
          initialState: {
            tenantConfig: {
              features: {
                showRedicodisOnReport:
                  data.showRedicodisOnReport !== undefined ? data.showRedicodisOnReport : true,
              },
            },
          },
        }),
        createTestingVuetify(),
      ],
    },
  });
}
