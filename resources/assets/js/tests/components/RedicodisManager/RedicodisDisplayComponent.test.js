import RedicodisDisplayComponent from '../../../components/RedicodisManager/RedicodisDisplayComponent';
import { DOMWrapper, enableAutoUnmount, mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import Redicodi from '../../mocks/Redicodi';
import lodashKeyBy from 'lodash/keyBy';
import { createTestingPinia } from '@pinia/testing';
import createTestingVuetify from '../../helpers/CreateTestingVuetify.js';

const counts = {};
const tree = Redicodi.createRedicodisTree();
const parentItems = [tree[0], tree[1], tree[2]];

enableAutoUnmount(afterEach);

describe('RedicodisDisplayComponent', () => {
  let wrapper;

  it('renders correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('shows the selected redicodis as chips', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.$nextTick();

    const chips = wrapper.findAll('.v-chip--label');
    expect(chips.length).toBe(3);
    for (let i = 0; i < chips.length; i++) {
      const currentChip = chips.at(i);
      expect(currentChip.text()).toContain(parentItems[i].name);
      expect(currentChip.text()).toContain('0');
    }
  });

  it('opens the dialog and goes to correct item when clicking on chip', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.$nextTick();

    const setDialogSpy = jest.spyOn(wrapper.vm, 'setDialog');

    await wrapper.find('.v-chip').trigger('click');
    expect(setDialogSpy).toHaveBeenCalled();
    expect(wrapper.vm.isOpen).toBeTruthy();
    expect(wrapper.vm.openChildren).toStrictEqual([parentItems[0].uid]);
  });

  it('shows the comments', async () => {
    tree[0].comment = 'My little comment';
    tree[0].name = 'My little redicodi';
    wrapper = mountWrapper();
    wrapper.vm.setDialog(true, tree[0]);
    await wrapper.vm.$nextTick();
    const recursiveItem = new DOMWrapper(document.body).find('#recursive_item_' + tree[0].uid);
    expect(recursiveItem.text()).toBe('My little redicodiinfoMy little comment');
    expect(recursiveItem.findAllComponents({ name: 'v-icon' }).at(-1).props().icon).toBe('info');
  });
});

function mountWrapper(data = {}) {
  return mount(RedicodisDisplayComponent, {
    attachTo: document.body,
    props: lodashDefaults(data, {
      tree: lodashKeyBy(tree, 'uid'),
      counts: counts,
      showRedicodisOnReport:
        data.showRedicodisOnReport !== undefined ? data.showRedicodisOnReport : true,
    }),
    global: {
      plugins: [
        createTestingPinia({
          initialState: {
            tenantConfig: {
              features: {
                showRedicodisOnReport:
                  data.showRedicodisOnReport !== undefined ? data.showRedicodisOnReport : true,
              },
            },
          },
        }),
        createTestingVuetify(),
      ],
    },
  });
}
