import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import Avatar from '../../components/Avatar';
import User from '../mocks/User';
import DialogMock from '../mocks/components/DialogMock';
import createTestingVuetify from '../helpers/CreateTestingVuetify.js';

let wrapper;

const pupil = new User();
const careInfo = {
  profile_picture_thumb_parsed: faker.image.url(),
  profile_picture_parsed: faker.image.url(),
  ...pupil,
};

describe('Avatar', () => {
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the pupil avatar when there is one', () => {
    wrapper = mountWrapper();
    expect(wrapper.findComponent({ name: 'VAvatar' }).find('img').exists()).toBeTruthy();
    expect(wrapper.findComponent({ name: 'VAvatar' }).find('img').element.src).toBe(
      careInfo.profile_picture_thumb_parsed
    );
  });

  it('shows the avatar in the dialog when there is one', async () => {
    wrapper = mountWrapper();
    await wrapper.findComponent({ name: 'VAvatar' }).trigger('click');

    expect(wrapper.findComponent(DialogMock).findComponent({ name: 'VImg' }).props().src).toBe(
      careInfo.profile_picture_parsed
    );
  });

  it('renders pupil initials when there is no image', () => {
    wrapper = mountWrapper({
      avatar: {
        ...careInfo,
        profile_picture_thumb_parsed: '',
        profile_picture_parsed: '',
      },
    });

    expect(wrapper.findComponent({ name: 'VAvatar' }).find('img').exists()).toBeFalsy();
    expect(wrapper.findComponent({ name: 'VAvatar' }).text()).toBe(pupil.initials);
  });
});

function mountWrapper(data = {}) {
  return mount(Avatar, {
    props: lodashDefaults(data, {
      avatar: careInfo,
    }),
    global: {
      stubs: {
        VDialog: DialogMock,
      },
      plugins: [createTestingVuetify()],
    },
  });
}
