import { mount } from '@vue/test-utils';
import moxios from 'moxios';
import PupilList from '../../components/PupilList';
import createTestingVuetify from '../helpers/CreateTestingVuetify.js';

describe('PupilList.vue', () => {
  beforeEach(() => {
    moxios.install(axios);
  });

  afterEach(() => {
    moxios.uninstall(axios);
  });

  const title = faker.lorem.word();

  const selectedPupilUid = null;
  const placeholder = faker.lorem.word();
  const autocompleteUrl = '/autocomplete/url';
  const redirectRoute = 'https://www.redirect-route.com';
  const studentArray = [
    {
      uid: '4c8cba7e-d917-4441-864d-07c9f60932ec',
      name: '<PERSON><PERSON>gem',
      redirectLink:
        'https://zorg.sol2.test/overview/pupils/4c8cba7e-d917-4441-864d-07c9f60932ec/notes',
    },
    {
      uid: '5343ef02-66b4-49da-9bc1-0da47ab37efb',
      name: '<PERSON>',
      redirectLink:
        'https://zorg.sol2.test/overview/pupils/5343ef02-66b4-49da-9bc1-0da47ab37efb/notes',
    },
    {
      uid: 'bc2542c5-0fe3-4dd8-8caa-e3c83266fec1',
      name: "Carmela O'Reilly",
      redirectLink:
        'https://zorg.sol2.test/overview/pupils/bc2542c5-0fe3-4dd8-8caa-e3c83266fec1/notes',
    },
    {
      uid: 'e7d2f612-056d-479c-8d6d-1809915dab11',
      name: 'Candace Kuvalis',
      redirectLink:
        'https://zorg.sol2.test/overview/pupils/e7d2f612-056d-479c-8d6d-1809915dab11/notes',
    },
    {
      uid: '21f8b5a3-49a0-446d-8af0-09764618459b',
      name: 'Marcellus Walter',
      redirectLink:
        'https://zorg.sol2.test/overview/pupils/21f8b5a3-49a0-446d-8af0-09764618459b/notes',
    },
  ];
  const autocompleteArray = [
    {
      user_uid: '4f2fd48c-b231-4980-91b1-************',
      fullname: 'Adaline Bechtelar',
      group_uid: '63eba1e7-5c75-4340-927c-b402c8e59cfd',
      group_name: '1A',
      url: 'https://zorg.sol2.test/overview/pupils/4f2fd48c-b231-4980-91b1-************/notes',
    },
  ];

  const wrapper = mount(PupilList, {
    props: {
      pupils: studentArray,
      selectedPupilUid: selectedPupilUid,
      title: title,
      placeholder: placeholder,
      autocompleteUrl: autocompleteUrl,
      redirectRoute: redirectRoute,
      redirectParameters: '',
    },
    sync: false,
    global: {
      plugins: [createTestingVuetify()],
    },
  });

  it('is a Vue instance', () => {
    expect(wrapper.vm).toBeTruthy();
  });

  it('receives title property', () => {
    expect(wrapper.props().title).toBe(title);
  });

  it('renders the title correctly', () => {
    expect(wrapper.find('.list-title').text()).toBe(title);
  });

  it('renders pupils correctly', () => {
    // +1 because input element is also a list item.
    expect(wrapper.findAll('.v-list-item')).toHaveLength(studentArray.length + 1);
  });

  it('calls the autocompleteUrl when searching', (done) => {
    moxios.stubRequest(new RegExp(autocompleteUrl + '*'), {
      status: 200,
      response: autocompleteArray,
    });

    wrapper.vm.search = 'test';

    moxios.wait(() => {
      expect(moxios.requests.mostRecent().url).toMatch(autocompleteUrl);
      done();
    });
  });

  it('updates the autoCompleteOptions data when searching', (done) => {
    moxios.stubRequest(new RegExp(autocompleteUrl + '*'), {
      status: 200,
      response: autocompleteArray,
    });

    wrapper.vm.search = 'test';

    moxios.wait(() => {
      expect(wrapper.vm.autoCompleteOptions).toStrictEqual(autocompleteArray);
      done();
    });
  });
});
