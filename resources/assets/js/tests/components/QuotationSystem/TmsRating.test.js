import TmsRating from '../../../components/QuotationSystem/TmsRating';
import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import QuotationSystem from '../../mocks/QuotationSystem';
import createTestingVuetify from '../../helpers/CreateTestingVuetify.js';

const ratingType = QuotationSystem.ratingTypes[0];

let wrapper;
let quotationSystem;

describe('TmsRating', () => {
  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('shows the correct rating without a selected value', () => {
    wrapper = mountWrapper();
    const icons = wrapper.findAll('.v-icon');
    expect(icons.length).toBe(quotationSystem.quotations.length);

    for (let i = 0; i < quotationSystem.quotations.length; i++) {
      expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon.handle);
      expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon_color);
    }
  });

  it('shows the correct rating when the selected value changes', async () => {
    wrapper = mountWrapper({ selected: 1 });
    const icons = wrapper.findAll('.v-icon');
    expect(icons.length).toBe(quotationSystem.quotations.length);

    expect(icons.at(0).html()).toContain(quotationSystem.quotations[0].icon.handle);
    expect(icons.at(0).html()).toContain(quotationSystem.quotations[0].icon_color);
    expect(icons.at(1).html()).toContain(quotationSystem.quotations[0].empty_icon.handle);
    expect(icons.at(1).html()).toContain(quotationSystem.quotations[0].empty_icon_color);

    wrapper.setProps({ selected: 2 });
    await wrapper.vm.$nextTick();

    expect(icons.at(0).html()).toContain(quotationSystem.quotations[0].icon.handle);
    expect(icons.at(0).html()).toContain(quotationSystem.quotations[0].icon_color);
    expect(icons.at(1).html()).toContain(quotationSystem.quotations[0].icon.handle);
    expect(icons.at(1).html()).toContain(quotationSystem.quotations[0].icon_color);
  });

  describe('rating type', () => {
    for (let selectedIndex = 0; selectedIndex < 5; selectedIndex++) {
      it('shows the correct amount of selected items - ' + (selectedIndex + 1), () => {
        wrapper = mountWrapper({ selectedIndex: selectedIndex });
        const icons = wrapper.findAll('.v-icon');
        expect(icons.length).toBe(quotationSystem.quotations.length);

        for (let i = 0; i < quotationSystem.quotations.length; i++) {
          if (i <= selectedIndex) {
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].icon.handle);
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].icon_color);
          } else {
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon.handle);
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon_color);
          }
        }
      });
    }
  });

  describe('selection type', () => {
    for (let selectedIndex = 0; selectedIndex < 5; selectedIndex++) {
      it('correct item is selected - ' + (selectedIndex + 1), () => {
        wrapper = mountWrapper({
          selectedIndex: selectedIndex,
          ratingType: QuotationSystem.ratingTypes[1],
        });
        const icons = wrapper.findAll('.v-icon');
        expect(icons.length).toBe(quotationSystem.quotations.length);

        for (let i = 0; i < quotationSystem.quotations.length; i++) {
          if (i === selectedIndex) {
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].icon.handle);
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].icon_color);
          } else {
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon.handle);
            expect(icons.at(i).html()).toContain(quotationSystem.quotations[i].empty_icon_color);
          }
        }
      });
    }
  });

  it('also works when value is an object', () => {
    quotationSystem = new QuotationSystem({
      ratingType: ratingType,
      type: QuotationSystem.types[3],
    });
    const quotation = quotationSystem.quotations[2];
    const quotationsObject = {
      quotation: quotation,
    };
    wrapper = mountWrapper({ modelValue: quotationsObject, selected: 3 });

    const icons = wrapper.findAll('.v-icon');
    expect(icons.length).toBe(5);
    for (let i = 0; i < 5; i++) {
      if (i <= 2) {
        expect(icons.at(i).html()).toContain(quotation.icon.handle);
        expect(icons.at(i).html()).toContain(quotation.icon_color);
      } else {
        expect(icons.at(i).html()).toContain(quotation.empty_icon.handle);
        expect(icons.at(i).html()).toContain(quotation.empty_icon_color);
      }
    }
  });
});

function mountWrapper(data = {}) {
  quotationSystem = new QuotationSystem({
    ratingType: data.ratingType || ratingType,
    type: QuotationSystem.types[3],
  });
  let selected = null;
  if (data.selectedIndex !== null && data.selectedIndex !== undefined) {
    selected = quotationSystem.quotations[data.selectedIndex].icon_count;
  }
  return mount(TmsRating, {
    props: lodashDefaults(data, {
      ratingType: ratingType,
      selected: selected,
      modelValue: quotationSystem.quotations,
    }),
    global: {
      plugins: [createTestingVuetify()],
    },
  });
}
