import CollectionDetailRecordsTable from '../../../../../../IPages/Planner/Collections/CollectionDetail/partials/CollectionDetailRecordsTable.vue';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import lodashDefaults from 'lodash/defaults';
import User from '../../../../../mocks/User';
import Collection from '../../../../../mocks/planner/Collection';
import TargetAudience from '../../../../../mocks/TargetAudience';
import CollectionLibrary from '../../../../../../models/dataModels/CollectionLibrary';
import TargetAudiences from '../../../../../../models/dataModels/TargetAudiences';
import { default as CollectionDataModel } from '../../../../../../models/dataModels/Collection';
import CollectionDetailRecordOptions from '../../../../../../IPages/Planner/Collections/CollectionDetail/partials/CollectionDetailRecordOptions.vue';
import { useCollectionDetailStore } from '../../../../../../stores/collectionDetailStore';
import createTestingVuetify from '../../../../../helpers/CreateTestingVuetify.js';

let collectionDetailStore;

const user = new User();
const collection = new Collection({ owner: user.id });
const targetAudiences = TargetAudience.createMultiple();

let collectionData;
let wrapper;
let chapter;

describe('CollectionDetailRecordsTable', () => {
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the correct data', () => {
    wrapper = mountWrapper();

    const records = wrapper.findAll('.recordRow');
    expect(records).toHaveLength(chapter.records.length);
    for (let i = 0; i < records.length; i++) {
      const currentRecordRow = records[i];
      const currentRecord = chapter.records[i];
      expect(currentRecordRow.find('.fully-clickable').text()).toBe(currentRecord.name);
      expect(currentRecordRow.findComponent(CollectionDetailRecordOptions).exists()).toBeTruthy();
    }
  });

  it('renders the no-data div when no records', async () => {
    wrapper = mountWrapper();
    wrapper.vm.chapter.records = [];
    await wrapper.vm.$nextTick();
    expect(wrapper.text()).toContain(i18n.t('MODULE.COLLECTIONS.RECORDS.EMPTY'));
  });

  it('renders the text-field when the record is in edit mode', async () => {
    wrapper = mountWrapper();
    collectionDetailStore.setEdit(chapter.records[0]);
    await wrapper.vm.$nextTick();
    expect(wrapper.findAllComponents({ name: 'VTextField' })).toHaveLength(1);
    expect(wrapper.find('.recordRow').findComponent({ name: 'VTextField' }).exists()).toBeTruthy();
  });

  it('updates the name value correctly', () => {
    wrapper = mountWrapper();
    const setNameSpy = jest.spyOn(collectionDetailStore, 'setName');
    wrapper.vm.name = 'test';
    expect(setNameSpy).toHaveBeenCalledWith('test');
  });

  it('redirects to the correct url when clicking on a record', () => {
    wrapper = mountWrapper();
    wrapper.vm.toRecordDetail(chapter, chapter.records[0]);
    expect(window.location.assign).toHaveBeenCalledWith(
      window.location.href + '/chapter/' + chapter.id + '/record/' + chapter.records[0].id
    );
  });

  it('shows the record is edited when is_user_edit is true', async () => {
    wrapper = mountWrapper();
    const record = chapter.records[0];
    record.is_user_edit = true;
    await resetStore(true);
    expect(
      wrapper
        .find('#record' + record.id)
        .text()
        .toLowerCase()
    ).toContain(i18n.t('labels.modules.planner.collections.detail.edited'));
  });

  it('shows the record is own lesson file when is_own_record is true', async () => {
    wrapper = mountWrapper();
    const record = chapter.records[0];
    record.is_own_record = true;
    await resetStore(true);
    expect(
      wrapper
        .find('#record' + record.id)
        .text()
        .toLowerCase()
    ).toContain(i18n.t('labels.modules.planner.collections.detail.own-record'));
  });

  it('shows the record is edited when is_own_record and is_user_edit is true', async () => {
    wrapper = mountWrapper();
    const record = chapter.records[0];
    record.is_own_record = true;
    record.is_user_edit = true;
    await resetStore(true);
    expect(
      wrapper
        .find('#record' + record.id)
        .text()
        .toLowerCase()
    ).toContain(i18n.t('labels.modules.planner.collections.detail.edited'));
    expect(
      wrapper
        .find('#record' + record.id)
        .text()
        .toLowerCase()
    ).not.toContain(i18n.t('labels.modules.planner.collections.detail.own-record'));
  });

  it('goes to record correctly', () => {
    wrapper = mountWrapper();
    const setLastShownRecordSpy = jest.spyOn(collectionDetailStore, 'setLastShownRecord');

    wrapper.vm.toRecordDetail(chapter, chapter.records[0]);
    expect(setLastShownRecordSpy).toHaveBeenCalledWith(chapter.records[0].id, chapter.id);
    expect(collectionDetailStore.lastShownRecord.record).toBe(chapter.records[0].id);
    expect(collectionDetailStore.lastShownRecord.chapter).toBe(chapter.id);

    expect(window.location.assign).toHaveBeenCalledWith(
      window.location.href + '/chapter/' + chapter.id + '/record/' + chapter.records[0].id
    );
  });
});

function resetStore(isReadMode = false) {
  collectionDetailStore.setCollectionData({
    collectionData: collectionData,
    isInit: true,
  });

  if (isReadMode) {
    collectionDetailStore.collectionData.shareStatus = 'reader';
  }

  collectionDetailStore.setTargetAudiencesData(TargetAudiences.pushAll(targetAudiences));
  collectionDetailStore.setLoading(false);
}

function mountWrapper(data = {}) {
  collectionData = new CollectionDataModel(new CollectionLibrary({}), collection);
  collectionData.enrichWithData(collectionData);
  chapter = collectionData.chapters[0];

  const mountedWrapper = mount(CollectionDetailRecordsTable, {
    props: lodashDefaults(data, {
      chapter: chapter,
      onDragEnd: jest.fn(),
    }),
    global: {
      plugins: [
        createTestingPinia({
          stubActions: false,
        }),
        createTestingVuetify(),
      ],
    },
  });

  collectionDetailStore = useCollectionDetailStore();

  resetStore();

  return mountedWrapper;
}
