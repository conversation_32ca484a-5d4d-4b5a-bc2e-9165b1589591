import CollectionCover from '../../../../../IPages/Planner/Collections/partials/CollectionCover.vue';
import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import Collection from '../../../../mocks/planner/Collection';
import CollectionType from '../../../../../IPages/Planner/Collections/dataModels/CollectionType';
import { default as CollectionDataModel } from '../../../../../models/dataModels/Collection';
import User from '../../../../mocks/User';
import { default as UserDataModel } from '../../../../../models/dataModels/User';
import TargetAudience from '../../../../mocks/TargetAudience';
import TooltipMock from '../../../../mocks/components/TooltipMock';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';

const user = new UserDataModel(new User());
const targetAudiences = TargetAudience.createMultiple();
const collection = createCollection({ owner: user });
let wrapper;

describe('CollectionCover', () => {
  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  describe('cover', () => {
    it('renders the blue cover by default', () => {
      wrapper = mountWrapper();
      expect(wrapper.vm.cover).toBe('cover-blue.svg');
    });

    it('renders the green cover when is publisher collection', () => {
      const publisherCollection = createCollection({
        owner: {
          uid: '0001',
          fullname: 'Uitgeverij Van In',
          isPublisher: true,
        },
      });
      wrapper = mountWrapper({ collection: publisherCollection });
      expect(wrapper.vm.cover).toBe('cover-green.svg');
    });

    it('renders the cover when there is one', () => {
      const collectionWithCover = createCollection({ cover: 'coverUrl' });
      wrapper = mountWrapper({ collection: collectionWithCover });
      expect(wrapper.vm.cover).toBe('coverUrl');
    });
  });

  it('goes to the correct url when clicking the cover', () => {
    wrapper = mountWrapper();
    wrapper.vm.visitCollection();
    expect(window.location.assign).toHaveBeenCalledWith('/collections/collection/' + collection.id);
  });

  describe('deactivated', () => {
    it('it shows the collection as deactivated', () => {
      collection.isDeactivated = true;
      wrapper = mountWrapper();

      expect(wrapper.find('.collectionImage').classes()).toContain('disabled');
      expect(wrapper.find('.collectionCoverBody').classes()).toContain('disabled');
      expect(wrapper.find('.collectionName').classes()).toContain('disabled');
    });

    it('it prevents you from visiting the collection', () => {
      collection.isDeactivated = true;
      wrapper = mountWrapper();

      expect(wrapper.vm.visitCollection()).toBeFalsy();
    });
  });

  it('renders the collection data correctly', () => {
    collection.owner.fullname = 'test (me)';
    wrapper = mountWrapper();
    expect(wrapper.find('.collectionName').text()).toContain(collection.name);
    expect(wrapper.find('.collectionOwner').text()).toContain('test (me)');
    expect(wrapper.find('.test-collectionMetaData').text()).toContain(targetAudiences[0].name);
  });

  it('renders multiple targetAudiences correctly', async () => {
    const currentTargetAudiences = [targetAudiences[0], targetAudiences[1], targetAudiences[2]];
    const collectionWithMultipleTargetAudiences = createCollection({
      targetAudiences: currentTargetAudiences,
    });
    wrapper = mountWrapper({
      collection: collectionWithMultipleTargetAudiences,
    });

    expect(
      wrapper
        .find('.test-collectionMetaData')
        .findComponent(TooltipMock)
        .find('.activatorSlot')
        .text()
    ).toBe(i18n.t('MODULE.COLLECTIONS.COLLECTION.MULTIPLE_AUDIENCES'));
    expect(
      wrapper
        .find('.test-collectionMetaData')
        .findComponent(TooltipMock)
        .find('.defaultSlot')
        .text()
    ).toBe(
      currentTargetAudiences
        .map((targetAudience) => {
          return targetAudience.name;
        })
        .join(', ')
    );
  });
});

function createCollection(data = {}) {
  const collectionData = lodashDefaults(data, {
    targetAudiences: [targetAudiences[0]],
  });
  return new CollectionDataModel({ url: 'parentUrl/' }, new Collection(collectionData));
}

function mountWrapper(data = {}) {
  return mount(CollectionCover, {
    props: lodashDefaults(data, {
      collection: collection,
      collectionType: CollectionType.MY_COLLECTIONS,
      user: user,
    }),
    global: {
      stubs: {
        VTooltip: TooltipMock,
      },
      plugins: [createTestingVuetify()],
    },
  });
}
