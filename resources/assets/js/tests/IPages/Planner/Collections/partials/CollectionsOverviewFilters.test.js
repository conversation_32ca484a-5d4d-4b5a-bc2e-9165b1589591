import CollectionsOverviewFilters from '../../../../../IPages/Planner/Collections/partials/CollectionsOverviewFilters';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import User from '../../../../mocks/User';
import Collection from '../../../../mocks/planner/Collection';
import CollectionType from '../../../../../IPages/Planner/Collections/dataModels/CollectionType';
import moxios from 'moxios';
import TargetAudience from '../../../../mocks/TargetAudience';
import { useCollectionOverviewStore } from '../../../../../stores/collectionOverviewStore';
import CollectionLibrary from '../../../../../models/dataModels/CollectionLibrary';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';
import { usePiwikProStore } from '../../../../../stores/piwikProStore';

const users = User.createMultiple();
const user = users[0];

const collections = [
  new Collection({
    owner: users[0],
  }),
  new Collection({
    owner: users[1],
  }),
  new Collection({
    owner: {
      uid: '001',
      fullname: 'Uitgeverij Van In',
      isPublisher: true,
    },
  }),
  new Collection({
    owner: {
      uid: '002',
      fullname: 'Uitgeverij De Boeck',
      isPublisher: true,
    },
    targetAudiences: TargetAudience.createMultiple(),
  }),
];

const collectionType = CollectionType.MY_COLLECTIONS;

let wrapper;
let collectionOverviewStore;
let piwikProStore;

describe('CollectionsOverviewFilters', () => {
  beforeEach(() => {
    moxios.install(axios);
    moxios.stubRequest('/target-audiences', {
      status: 200,
      response: TargetAudience.createMultiple(),
    });
  });

  it('renders the component correctly', async () => {
    wrapper = await mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('returns showFilters as truthy when there are collections', async () => {
    wrapper = await mountWrapper();
    expect(wrapper.vm.showFilters).toBeTruthy();
  });

  it('returns showFilters as falsy when there are no collections', async () => {
    wrapper = await mountWrapper({ collections: [] });
    expect(wrapper.vm.showFilters).toBeFalsy();
  });

  it('shows favourite filter when in my materials view', async () => {
    wrapper = await mountWrapper();
    expect(wrapper.vm.showFavourite).toBeTruthy();
  });

  it('hides favourite filter when in library view', async () => {
    wrapper = await mountWrapper({ collectionType: CollectionType.LIBRARY });
    expect(wrapper.vm.showFavourite).toBeFalsy();
  });

  it('shows favourite filter when in archived view', async () => {
    wrapper = await mountWrapper({ collectionType: CollectionType.ARCHIVED });
    expect(wrapper.vm.showFavourite).toBeTruthy();
  });

  it('shows the collection owner count correctly', async () => {
    wrapper = await mountWrapper();
    expect(wrapper.html()).toContain(users[0].fullname + '&nbsp;(1)');
    expect(wrapper.html()).toContain(users[1].fullname + '&nbsp;(1)');
    expect(wrapper.html()).not.toContain(users[2].fullname + '&nbsp;(1)');
  });

  it('shows the correct number of target audiences', async () => {
    wrapper = await mountWrapper();

    const targetAudiencesElement = wrapper
      .findAll('.v-list-item-title')
      .filter((wrapper) => wrapper.text() === 'Target-audiences')
      .at(0);
    expect(targetAudiencesElement.exists()).toBe(true);

    const vChipAudiencesContainer =
      targetAudiencesElement.element.closest('.v-list-item').nextElementSibling;

    const vChips = wrapper
      .findAllComponents({ name: 'VChip' })
      .filter((chip) => chip.element.closest('.v-list-item') === vChipAudiencesContainer);
    expect(vChips).toHaveLength(8);
  });

  it('records an event the favourite filter is used', async () => {
    wrapper = await mountWrapper();

    wrapper.findAllComponents({ name: 'VCheckbox' }).at(0).get('input').setChecked();

    expect(piwikProStore.trackEvent).toHaveBeenCalledTimes(1);
    expect(piwikProStore.trackEvent).toHaveBeenCalledWith({
      action: 'Favourite',
      category: 'Collection filter usage',
    });
  });

  it('records an event the collaboration filter is used', async () => {
    wrapper = await mountWrapper();

    wrapper.findAllComponents({ name: 'VCheckbox' }).at(1).get('input').setChecked();

    expect(piwikProStore.trackEvent).toHaveBeenCalledTimes(1);
    expect(piwikProStore.trackEvent).toHaveBeenCalledWith({
      action: 'In collaboration',
      category: 'Collection filter usage',
    });
  });

  it('records an event the owned by colleague filter is used', async () => {
    wrapper = await mountWrapper();

    wrapper.findAllComponents({ name: 'VCheckbox' }).at(2).get('input').setChecked();

    expect(piwikProStore.trackEvent).toHaveBeenCalledTimes(1);
    expect(piwikProStore.trackEvent).toHaveBeenCalledWith({
      action: 'Owned by collague',
      category: 'Collection filter usage',
    });
  });

  it('records an event the target group filter is used', async () => {
    wrapper = await mountWrapper();

    const specificChip = wrapper
      .findAllComponents({ name: 'VChip' })
      .find((chip) => chip.text() === 'enums.target-audience-type.ko_2');
    specificChip.trigger('click');

    expect(piwikProStore.trackEvent).toHaveBeenCalledTimes(1);
    expect(piwikProStore.trackEvent).toHaveBeenCalledWith({
      action: 'Target group',
      category: 'Collection filter usage',
    });
  });
});

async function mountWrapper(data = {}) {
  const mountedWrapper = Promise.resolve(
    mount(CollectionsOverviewFilters, {
      global: {
        plugins: [
          createTestingPinia({
            stubActions: false,
            initialState: {
              collectionOverview: {
                library: new CollectionLibrary({
                  collections: collections,
                  user: user,
                }),
                collectionType: data.collectionType || collectionType,
              },
            },
          }),
          createTestingVuetify(),
        ],
      },
    })
  );
  piwikProStore = usePiwikProStore();
  piwikProStore.trackEvent = jest.fn();

  collectionOverviewStore = useCollectionOverviewStore();

  collectionOverviewStore.setLibrary({
    collections: data.collections || collections,
    user: data.user || users[0],
  });
  await collectionOverviewStore.setTargetAudiences();
  collectionOverviewStore.setIsLoading(false);
  collectionOverviewStore.resetFilters();

  return mountedWrapper;
}
