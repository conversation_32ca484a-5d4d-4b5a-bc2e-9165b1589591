import CollectionSettingsModal from '../../../../../components/Planner/Collections/components/CollectionSettingsModal.vue';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing';
import lodashDefaults from 'lodash/defaults';
import TargetAudience from '../../../../mocks/TargetAudience';
import DialogMock from '../../../../mocks/components/DialogMock.vue';
import Collection from '../../../../mocks/planner/Collection';
import { default as CollectionDataModel } from '../../../../../models/dataModels/Collection';
import { cloneDeep } from 'lodash';
import moxios from 'moxios';
import { useCollectionDetailStore } from '../../../../../stores/collectionDetailStore';
import { useCollectionOverviewStore } from '../../../../../stores/collectionOverviewStore';
import CollectionLibrary from '../../../../../models/dataModels/CollectionLibrary';
import User from '../../../../mocks/User';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';

let collectionDetailStore;
let collectionOverviewStore;

const user = new User();
const targetAudiences = TargetAudience.createMultiple();
const collection = new CollectionDataModel({ url: 'parentUrl' }, new Collection());

let wrapper;

describe('CollectionSettingsModal', () => {
  beforeEach(() => {
    moxios.install(axios);
  });
  afterEach(() => {
    moxios.uninstall(axios);
  });
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the modal correctly', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.showModal(collection);
    expect(wrapper.vm.collection).toEqual(collection);
  });

  it('handles settingsModalCollection correctly when collection is given', async () => {
    wrapper = mountWrapper();
    const showModalSpy = jest.spyOn(wrapper.vm, 'showModal');
    collectionOverviewStore.openSettingsModal(collection);
    await wrapper.vm.$nextTick();
    expect(showModalSpy).toHaveBeenCalledWith(collection);
    expect(wrapper.vm.isOpen).toBeTruthy();
  });

  it('handles closeModal correctly', async () => {
    wrapper = mountWrapper();
    const closeSettingsModalSpy = jest.spyOn(wrapper.vm, 'closeSettingsModal');

    await wrapper.vm.showModal(collection);
    await wrapper.vm.$nextTick();
    wrapper.vm.closeModal();
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isOpen).toBeFalsy();
    expect(closeSettingsModalSpy).toHaveBeenCalled();
  });

  it('handles opening the modal a second time correctly', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.showModal(collection);
    wrapper.vm.formData.name = 'my updated test name';
    await wrapper.vm.$nextTick();
    wrapper.vm.closeModal();
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.formData.name).toBe('my updated test name');

    await wrapper.vm.showModal(collection);
    expect(wrapper.vm.formData.name).toBe(collection.name);
    expect(wrapper.vm.formData.name).not.toBe('my updated test name');
  });

  it('updates collection correctly', async () => {
    moxios.stubRequest(collection.url, {
      status: 200,
      response: cloneDeep(collection),
    });
    wrapper = mountWrapper();
    const setTargetAudiencesSpy = jest.spyOn(collectionDetailStore, 'setTargetAudiences');
    const closeModalSpy = jest.spyOn(wrapper.vm, 'closeModal');
    await wrapper.vm.showModal(collection);
    const collectionUpdateSpy = jest.spyOn(wrapper.vm.collection, 'update');

    wrapper.vm.formData.name = 'new name';
    wrapper.vm.formData.targetAudiences = [targetAudiences[2]];

    await wrapper.vm.updateCollection();

    expect(setTargetAudiencesSpy).toHaveBeenCalledWith([targetAudiences[2]]);
    expect(collectionUpdateSpy).toHaveBeenCalledWith({
      name: 'new name',
      targetAudienceIds: [targetAudiences[2].id],
      targetAudiences: [targetAudiences[2]],
      template: collection.template,
    });

    expect(closeModalSpy).toHaveBeenCalled();
  });
});

function mountWrapper(data = {}) {
  const mountedWrapper = mount(CollectionSettingsModal, {
    props: lodashDefaults(data, {
      targetAudiences: targetAudiences,
    }),
    global: {
      plugins: [
        createTestingPinia({
          stubActions: false,
          initialState: {
            collectionOverview: {
              library: new CollectionLibrary({
                collections: [collection],
                user: user,
              }),
            },
          },
        }),
        createTestingVuetify(),
      ],
      stubs: {
        VDialog: DialogMock,
      },
    },
  });

  collectionDetailStore = useCollectionDetailStore();
  collectionOverviewStore = useCollectionOverviewStore();

  return mountedWrapper;
}
