import ShareCollectionModal from '../../../../../IPages/Planner/Collections/partials/ShareCollectionModal';
import { mount } from '@vue/test-utils';
import User from '../../../../mocks/User';
import CollectionShareMode from '../../../../../models/enums/CollectionShareMode';
import lodashKeyBy from 'lodash/keyBy';
import lodashDefaults from 'lodash/defaults';
import lodashCloneDeep from 'lodash/cloneDeep';
import { default as CollectionDataModel } from '../../../../../models/dataModels/Collection';
import Collection from '../../../../mocks/planner/Collection';
import TargetAudience from '../../../../mocks/TargetAudience';
import DialogMock from '../../../../mocks/components/DialogMock';
import TooltipMock from '../../../../mocks/components/TooltipMock';
import moxios from 'moxios';
import { events } from '../../../../../events';
import CollectionWithPreviewModal from '../../../../../IPages/Planner/Collections/partials/CollectionWithPreviewModal';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';
import TransferCollectionModal from '../../../../../IPages/Planner/Collections/partials/TransferCollectionModal.vue';
import { createTestingPinia } from '@pinia/testing';
import { usePiwikProStore } from '../../../../../stores/piwikProStore';

const targetAudiences = TargetAudience.createMultiple(5, { activated: false });
const teachers = User.createMultiple();
teachers[0].access = CollectionShareMode.READ.value;
teachers[0].activated = true;
teachers[1].access = CollectionShareMode.WRITE.value;
teachers[2].access = CollectionShareMode.NO_ACCESS.value;
teachers[3].access = CollectionShareMode.NO_ACCESS.value;
teachers[4].access = CollectionShareMode.NO_ACCESS.value;

const collection = createCollection({ owner: teachers[0].uid });

const mappedTeachers = lodashKeyBy(lodashCloneDeep(teachers), 'uid');
const teachersWithAccessEnum = lodashCloneDeep(teachers).map((teacher) => {
  teacher.access = CollectionShareMode.getByValue(teacher.access);
  return teacher;
});

let wrapper;
let store;

describe('ShareCollectionModal', () => {
  beforeEach(() => {
    moxios.install(axios);
    moxios.stubRequest(collection.url + '/share-status', {
      status: 200,
      response: { users: lodashCloneDeep(mappedTeachers) },
    });
  });
  afterEach(() => {
    moxios.uninstall(axios);
    // Destroy wrapper each time so we don't have a lot of event listeners floating around
    wrapper.unmount();
  });

  it('renders the component correctly', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm).toBeTruthy();
  });

  it('renders nothing when dialog is closed', () => {
    wrapper = mountWrapper();
    expect(wrapper.findAll('*').length).toStrictEqual(1);
  });

  it('calls the collectionPreviewModal closeDialog function when closing', () => {
    wrapper = mountWrapper();
    const collectionPreviewCloseSpy = jest.spyOn(
      wrapper.findComponent(CollectionWithPreviewModal).vm,
      'closeDialog'
    );
    const updateBackendDataSpy = jest.spyOn(wrapper.vm, 'updateBackendData');

    wrapper.vm.closeDialog();
    expect(collectionPreviewCloseSpy).toHaveBeenCalled();
    expect(updateBackendDataSpy).toHaveBeenCalled();
  });

  it('sets the data correctly when triggering openDialog', async () => {
    wrapper = mountWrapper();
    const resetDefaultStateSpy = jest.spyOn(wrapper.vm, 'resetDefaultState');
    const resetTeacherDataSpy = jest.spyOn(wrapper.vm, 'resetTeacherData');
    const resetSelectedTeachersSpy = jest.spyOn(wrapper.vm, 'resetSelectedTeachers');
    await wrapper.vm.openDialog(collection);
    expect(wrapper.findAll('*').length).toBeGreaterThan(1);

    expect(resetDefaultStateSpy).toHaveBeenCalled();
    expect(resetTeacherDataSpy).toHaveBeenCalled();
    expect(resetSelectedTeachersSpy).toHaveBeenCalled();
    expect(wrapper.vm.collection).toStrictEqual(collection);
    expect(wrapper.vm.loading).toBeFalsy();
    expect(wrapper.vm.teachersWithAccessUpdated).toBeFalsy();
    expect(wrapper.vm.selectedAccessType).toBe(CollectionShareMode.READ.value);
    expect(wrapper.vm.selectedTeachers).toEqual([]);
    expect(wrapper.vm.teachersWithAccess.length).toBe(2);
    expect(wrapper.vm.teachersWithAccessOriginal).toEqual(wrapper.vm.teachersWithAccess);
    expect(wrapper.vm.teachers).toEqual(teachersWithAccessEnum);
  });

  it('filters the active teachers correctly', () => {
    wrapper = mountWrapper();
    wrapper.vm.teachers = lodashCloneDeep(teachersWithAccessEnum);
    const filteredTeachers = wrapper.vm.getTeachersWithAccess();
    expect(filteredTeachers.length).toBe(2);
    expect(filteredTeachers[0].uid).toBe(teachersWithAccessEnum[0].uid);
    expect(filteredTeachers[1].uid).toBe(teachersWithAccessEnum[1].uid);
  });

  describe('event listeners', () => {
    it('listens to the collections-openShare event correctly', async () => {
      // Spy on openDialog doesn't work for some reason, so we check if the first function in openDialog is called instead.
      wrapper = mountWrapper();
      const resetDefaultStateSpy = jest.spyOn(wrapper.vm, 'resetDefaultState');
      events.$emit('collections-openShare', collection);
      await wrapper.vm.$nextTick();
      expect(resetDefaultStateSpy).toHaveBeenCalled();
    });

    it('removes the event listener when the component is destroyed', () => {
      wrapper = mountWrapper();
      const eventsOffSpy = jest.spyOn(events, '$off');
      wrapper.unmount();
      expect(eventsOffSpy).toHaveBeenCalledWith('collections-openShare', wrapper.vm.openDialog);
    });
  });

  describe('add new users through search bar', () => {
    it('renders the autocompleteChips with the correct data', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      const contentElement = wrapper.find('.v-col-8 .d-flex');

      const autocompleteElement = contentElement.findComponent({ name: 'AutocompleteChips' });
      expect(autocompleteElement.exists()).toBeTruthy();
      expect(autocompleteElement.props().options).toEqual(teachersWithAccessEnum);
      expect(autocompleteElement.props().modelValue).toEqual([]);
    });

    it('shows the access type buttons when a new teacher is selected', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      wrapper.vm.selectedTeachers = [teachersWithAccessEnum[0]];
      await wrapper.vm.$nextTick();

      const contentElement = wrapper.find('.v-col-8 .d-flex');

      expect(contentElement.text().toLowerCase()).toContain(
        i18n.t('labels.modules.planner.collections.overview.share-modal.access-type')
      );
      expect(contentElement.find('.v-btn-group').exists()).toBeTruthy();
      const accessButtons = contentElement.find('.v-btn-group').findAll('.v-btn');
      expect(accessButtons.length).toBe(2);
      expect(accessButtons[0].text().toLowerCase()).toBe(
        i18n.t('labels.modules.planner.collections.overview.share-modal.can-view')
      );
      expect(accessButtons[0].classes()).toContain('v-btn--active');
      expect(accessButtons[1].text().toLowerCase()).toBe(
        i18n.t('labels.modules.planner.collections.overview.share-modal.can-edit')
      );
      expect(accessButtons[1].classes()).not.toContain('v-btn--active');
    });

    it('shows the save and cancel buttons when a new teacher is selected', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      wrapper.vm.selectedTeachers = [teachersWithAccessEnum[0]];
      await wrapper.vm.$nextTick();

      const actionButtons = wrapper.findAll('.v-col-8 > div').at(-1).findAll('button');

      expect(actionButtons.length).toBe(2);
      expect(actionButtons[0].text()).toBe(i18n.t('labels.cancel'));
      expect(actionButtons[1].text()).toBe(i18n.t('labels.save'));
    });

    it('saves the selected teachers correctly', async () => {
      moxios.stubRequest(collection.url + '/share/', {
        status: 200,
        response: {},
      });
      wrapper = mountWrapper();
      const sendDataToBackendSpy = jest.spyOn(wrapper.vm, 'sendDataToBackend');
      const updatedTeachers = lodashCloneDeep(teachersWithAccessEnum);
      updatedTeachers[0].access = CollectionShareMode.WRITE;
      updatedTeachers.map((teacher) => {
        teacher.access = teacher.access.value;
      });
      await wrapper.vm.openDialog(collection);

      wrapper.vm.schoolHasAccess = false;
      wrapper.vm.selectedTeachers = [teachersWithAccessEnum[0]];
      wrapper.vm.selectedAccessType = CollectionShareMode.WRITE.value;
      await wrapper.vm.$nextTick();

      const promise = wrapper.vm.saveNewTeacherAccess();
      expect(wrapper.vm.saving).toBeTruthy();
      await promise;
      expect(wrapper.vm.collection.isCoAuthor).toStrictEqual(true);
      expect(sendDataToBackendSpy).toHaveBeenCalledWith(updatedTeachers);
      expect(moxios.requests.mostRecent().url).toBe(collection.url + '/share/');
      expect(moxios.requests.mostRecent().config.data).toEqual(
        JSON.stringify({
          users: updatedTeachers,
          school_access: false,
        })
      );
    });

    it('does not set coAuthor to true for read access', async () => {
      moxios.stubRequest(collection.url + '/share/', {
        status: 200,
        response: {},
      });
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      wrapper.vm.schoolHasAccess = false;
      wrapper.vm.selectedTeachers = [teachersWithAccessEnum[1]];
      wrapper.vm.selectedAccessType = CollectionShareMode.READ.value;
      await wrapper.vm.$nextTick();

      await wrapper.vm.saveNewTeacherAccess();
      expect(wrapper.vm.collection.isCoAuthor).toStrictEqual(false);
    });
  });

  describe('previously selected teachers', () => {
    it('shows the teachers with access', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      const contentElement = wrapper.find('.v-col-8 .d-flex .d-flex');

      expect(contentElement.text()).toContain(teachersWithAccessEnum[0].fullname);

      const tableElement = contentElement.find('.v-table');
      const tableHeaderRows = tableElement.findAll('thead tr');

      expect(tableHeaderRows[0].find('th').text().toLowerCase()).toBe(
        i18n.t('labels.modules.planner.collections.overview.share-modal.have-access')
      );
      expect(tableHeaderRows[1].findAll('th')[0].text()).toContain('2');
      expect(tableHeaderRows[1].findAll('th')[0].text()).toContain('colleagues');
      expect(tableHeaderRows[1].findAll('th')[1].text().toLowerCase()).toBe(
        i18n.t('labels.modules.planner.collections.overview.share-modal.activated')
      );
      expect(tableHeaderRows[1].findAll('th')[2].text().toLowerCase()).toBe(
        i18n.t('labels.modules.planner.collections.overview.share-modal.role')
      );

      const tableBodyRows = tableElement.findAll('tbody tr');
      expect(tableBodyRows.length).toBe(2);
      for (let i = 0; i < tableBodyRows.length; i++) {
        const rowColumns = tableBodyRows[i].findAll('td');
        expect(rowColumns[0].text()).toBe(teachersWithAccessEnum[i].fullname);
        if (teachersWithAccessEnum[i].activated) {
          expect(rowColumns[1].findComponent({ name: 'VIcon' }).vm.icon).toBe('check');
        } else {
          expect(rowColumns[1].findComponent({ name: 'VIcon' }).vm.icon).toBe('remove');
        }
        const selectElement = rowColumns[2].findComponent({ name: 'VSelect' });
        expect(selectElement.props().modelValue).toEqual(teachersWithAccessEnum[i].access);

        const options = lodashCloneDeep(CollectionShareMode.getAll()).map((option) => {
          option.disabled = false;
          return option;
        });
        if (teachersWithAccessEnum[i].activated === true) {
          options[2].disabled = true;
          expect(selectElement.props().items).toEqual(options);
          expect(
            wrapper.vm.shareOptionDisabled(options[2], teachersWithAccessEnum[i])
          ).toBeTruthy();
        } else {
          options[2].disabled = false;
          expect(selectElement.props().items).toEqual(options);
          expect(wrapper.vm.shareOptionDisabled(options[2], teachersWithAccessEnum[i])).toBeFalsy();
        }
      }
    });

    it('shows the action buttons when one teacher with access is changed', async () => {
      wrapper = mountWrapper();
      await wrapper.vm.openDialog(collection);

      expect(wrapper.vm.teachersWithAccessUpdated).toBeFalsy();

      wrapper.vm.teachersWithAccess[0].access = CollectionShareMode.WRITE;
      await wrapper.vm.checkTeachersWithAccessUpdated();
      expect(wrapper.vm.teachersWithAccessUpdated).toBeTruthy();

      const actionButtons = wrapper.findAll('.v-col-8 > div').at(-1).findAll('button');

      expect(actionButtons.length).toBe(2);
      expect(actionButtons[0].text()).toBe(i18n.t('labels.cancel'));
      expect(actionButtons[1].text()).toBe(i18n.t('labels.save'));
    });

    it('saves the updated teachers with access correctly', async () => {
      moxios.stubRequest(collection.url + '/share/', {
        status: 200,
        response: {},
      });
      wrapper = mountWrapper();
      const closeDialogSpy = jest.spyOn(wrapper.vm, 'closeDialog');
      const sendDataToBackendSpy = jest.spyOn(wrapper.vm, 'sendDataToBackend');
      await wrapper.vm.openDialog(collection);
      wrapper.vm.schoolHasAccess = false;

      const updatedTeachers = lodashCloneDeep(teachersWithAccessEnum);
      updatedTeachers[0].access = CollectionShareMode.WRITE;
      updatedTeachers.map((teacher) => {
        teacher.access = teacher.access.value;
      });

      wrapper.vm.teachersWithAccess[0].access = CollectionShareMode.WRITE;
      await wrapper.vm.checkTeachersWithAccessUpdated();
      expect(wrapper.vm.teachersWithAccessUpdated).toBeTruthy();
      wrapper.vm.saveTeachersWithAccess();

      expect(wrapper.vm.saving).toBeTruthy();
      expect(sendDataToBackendSpy).toHaveBeenCalledWith(updatedTeachers);
      expect(moxios.requests.mostRecent().url).toBe(collection.url + '/share/');
      expect(moxios.requests.mostRecent().config.data).toEqual(
        JSON.stringify({
          users: updatedTeachers,
          school_access: false,
        })
      );

      moxios.wait(() => {
        expect(closeDialogSpy).toHaveBeenCalled();
        expect(wrapper.vm.saving).toBeFalsy();
      });
    });

    it('saves the updated school access correctly', async () => {
      moxios.stubRequest(collection.url + '/share/', {
        status: 200,
        response: {},
      });
      wrapper = mountWrapper();
      const closeDialogSpy = jest.spyOn(wrapper.vm, 'closeDialog');
      await wrapper.vm.openDialog(collection);

      wrapper.vm.schoolHasAccess = true;
      wrapper.vm.saveTeachersWithAccess();

      expect(wrapper.vm.saving).toBeTruthy();
      expect(moxios.requests.mostRecent().url).toBe(collection.url + '/share/');
      expect(JSON.parse(moxios.requests.mostRecent().config.data)).toEqual(
        expect.objectContaining({
          school_access: true,
        })
      );

      moxios.wait(() => {
        expect(closeDialogSpy).toHaveBeenCalled();
        expect(wrapper.vm.saving).toBeFalsy();
      });
    });

    it('resets the teachers with access correctly and closes the dialog', async () => {
      wrapper = mountWrapper();
      const closeDialogSpy = jest.spyOn(wrapper.vm, 'closeDialog');
      const checkTeachersWithAccessUpdatedSpy = jest.spyOn(
        wrapper.vm,
        'checkTeachersWithAccessUpdated'
      );
      await wrapper.vm.openDialog(collection);

      wrapper.vm.teachersWithAccess[0].access = CollectionShareMode.WRITE;
      await wrapper.vm.checkTeachersWithAccessUpdated();
      expect(wrapper.vm.teachersWithAccessUpdated).toBeTruthy();

      wrapper.vm.resetTeachersWithAccess();
      expect(wrapper.vm.teachersWithAccess).toEqual(wrapper.vm.teachersWithAccessOriginal);
      expect(checkTeachersWithAccessUpdatedSpy).toHaveBeenCalled();
      expect(wrapper.vm.teachersWithAccessUpdated).toBeFalsy();
      expect(closeDialogSpy).toHaveBeenCalled();
    });

    it('throws an error when the share request fails', async () => {
      moxios.stubRequest(collection.url + '/share/', {
        status: 500,
        response: {},
      });
      wrapper = mountWrapper();
      const notifySpy = jest.spyOn(wrapper.vm, '$notify');
      await wrapper.vm.openDialog(collection);

      const teacherData = lodashCloneDeep(wrapper.vm.teachers);
      teacherData.map((teacher) => {
        teacher.access = teacher.access.value;
      });

      await wrapper.vm.sendDataToBackend(teacherData);

      expect(moxios.requests.mostRecent().url).toBe(collection.url + '/share/');
      expect(notifySpy).toHaveBeenCalled();
    });
  });

  it('shows and hides the TransferCollectionModal component correctly', async () => {
    wrapper = mountWrapper();
    await wrapper.vm.openDialog(collection);
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.showTransferCollection).toBeFalsy();
    expect(wrapper.findComponent(TransferCollectionModal).exists()).toBeFalsy();
    wrapper.vm.initTransferCollection();
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.showTransferCollection).toBeTruthy();
    expect(wrapper.findComponent(TransferCollectionModal).exists()).toBeTruthy();

    wrapper.vm.newOwner = 'test';
    wrapper.vm.closeTransferCollection();
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.showTransferCollection).toBeFalsy();
    expect(wrapper.vm.newOwner).toBeNull();
  });

  it('handles the transferCollection save correctly', async () => {
    wrapper = mountWrapper();
    moxios.stubRequest(collection.url + '/transfer-ownership', {
      status: 200,
    });
    const closeDialogSpy = jest.spyOn(wrapper.vm, 'closeDialog');

    await wrapper.vm.openDialog(collection);
    await wrapper.vm.$nextTick();

    wrapper.vm.newOwner = { co_author: teachers[0] };
    await wrapper.vm.saveTransferCollection();

    expect(moxios.requests.mostRecent().url).toBe(collection.url + '/transfer-ownership');
    expect(JSON.parse(moxios.requests.mostRecent().config.data)).toEqual({
      user: teachers[0].uid,
    });
    expect(store.trackEvent).toHaveBeenCalledTimes(1);
    expect(store.trackEvent).toHaveBeenCalledWith({
      action: 'Transfer ownership',
      category: 'Collection manipulations',
    });
    expect(closeDialogSpy).toHaveBeenCalled();
  });

  it('updates the backend data correctly', () => {
    wrapper = mountWrapper();
    wrapper.vm.updateBackendData();
    expect(wrapper.vm.$inertia.reload).toHaveBeenCalledWith({
      only: [
        'collections',
        'collection',
        'coAuthorsCurrentSchool',
        'coAuthorCountInOtherSchools',
        'readersCurrentSchool',
        'readerCountInOtherSchools',
        'hasBeenActivated',
        'totalColleagues',
      ],
    });
  });
});

function createCollection(data = {}) {
  const collectionData = lodashDefaults(data, {
    targetAudiences: [targetAudiences[0]],
  });
  return new CollectionDataModel({ url: 'parentUrl/' }, new Collection(collectionData));
}

function mountWrapper() {
  const mountedWrapper = mount(ShareCollectionModal, {
    global: {
      stubs: {
        VDialog: DialogMock,
        VTooltip: TooltipMock,
        TransferCollectionModal: { template: '<div></div>' },
      },
      plugins: [createTestingVuetify(), createTestingPinia()],
      mocks: {
        $notify: jest.fn(),
        $inertia: {
          reload: jest.fn(),
        },
      },
    },
  });
  store = usePiwikProStore();
  return mountedWrapper;
}
