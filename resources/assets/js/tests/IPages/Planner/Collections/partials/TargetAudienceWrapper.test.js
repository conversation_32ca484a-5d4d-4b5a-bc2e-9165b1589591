import TargetAudienceWrapper from '../../../../../IPages/Planner/Collections/partials/TargetAudienceWrapper';
import { mount } from '@vue/test-utils';
import Collection from '../../../../mocks/planner/Collection';
import { default as CollectionDataModel } from '../../../../../models/dataModels/Collection';
import TargetAudience from '../../../../mocks/TargetAudience';
import createTestingVuetify from '../../../../helpers/CreateTestingVuetify.js';

const targetAudiences = TargetAudience.createMultiple();
const collection = new CollectionDataModel(
  { url: 'parentUrl' },
  new Collection({
    targetAudiences: faker.helpers.arrayElements(targetAudiences, 3),
  })
);

let wrapper;

describe('TargetAudienceWrapper', () => {
  it('renders the component correctly', () => {
    expect(mountWrapper().vm).toBeTruthy();
  });

  it('renders the component when the collection has target audiences', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm.showComponent).toBeTruthy();
    expect(wrapper.find('.targetAudiences').exists()).toBeTruthy();
  });

  it('does not render the component when the collection does not have target audiences', () => {
    const currentCollection = createCollection();
    delete currentCollection.targetAudiences;

    wrapper = mountWrapper({ collection: currentCollection });
    expect(wrapper.vm.showComponent).toBeFalsy();
    expect(wrapper.find('.targetAudiences').exists()).toBeFalsy();
  });

  it('does not show the tooltip when the collection has less than 3 target audiences', () => {
    const currentCollection = createCollection({
      targetAudiences: [targetAudiences[0], targetAudiences[1]],
    });

    wrapper = mountWrapper({ collection: currentCollection });

    expect(wrapper.vm.showTooltip).toBeFalsy();
    expect(wrapper.findComponent({ name: 'VTooltip' }).props().disabled).toBeTruthy();
  });

  it('displays the collection target audiences when there are less than 3 target audiences', () => {
    const currentCollection = createCollection({
      targetAudiences: faker.helpers.arrayElements(targetAudiences, 2),
    });

    wrapper = mountWrapper({ collection: currentCollection });

    expect(wrapper.vm.targetAudienceText).toBe(currentCollection.getTargetAudienceString());
    expect(wrapper.find('p').text()).toBe(currentCollection.getTargetAudienceString());
  });

  it('shows the fallback text when the collection has 2 or more target audiences', () => {
    wrapper = mountWrapper();
    expect(wrapper.vm.showTooltip).toBeTruthy();
    expect(wrapper.vm.targetAudienceText).toBe(
      i18n.t('MODULE.COLLECTIONS.COLLECTION.MULTIPLE_AUDIENCES')
    );
    expect(wrapper.find('p').text()).toBe(
      i18n.t('MODULE.COLLECTIONS.COLLECTION.MULTIPLE_AUDIENCES')
    );
  });

  it('renders an empty string when the collection has no targetAudiences', () => {
    const currentCollection = createCollection({ targetAudiences: [] });

    wrapper = mountWrapper({ collection: currentCollection });
    expect(wrapper.vm.targetAudienceText).toBe(' ');
    expect(wrapper.find('p').text()).toBe('');
  });
});

function mountWrapper(data = {}) {
  return mount(TargetAudienceWrapper, {
    props: {
      collection: data.collection || collection,
    },
    global: {
      plugins: [createTestingVuetify()],
    },
  });
}

function createCollection(data = {}) {
  return new CollectionDataModel({ url: 'parentUrl' }, new Collection(data));
}
