import Collection from '../../../models/dataModels/Collection';
import axios from 'axios';
import CollectionLibrary from '../../../models/dataModels/CollectionLibrary';
import { createTestingPinia } from '@pinia/testing';
import { usePiwikProStore } from '../../../stores/piwikProStore.js';

let piwikProStore;

jest.mock('axios');
window.plannerConfig.tenant.internalUrls.apiPlannerPrefix = '/sol/planner/rest/v2';

describe('Collection', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('sets the owner name if the user is the authenticated user', () => {
    const collection = new Collection({}, {});
    collection.owner = { fullname: '<PERSON>', isAuthenticatedUser: true };
    collection.setOwnerName();
    expect(collection.owner.fullname).toEqual('<PERSON> (me)');
  });

  it('sets the owner name if the user is not the authenticated user', () => {
    const collection = new Collection({}, {});
    collection.owner = { fullname: '<PERSON>', isAuthenticatedUser: false };
    collection.setOwnerName();
    expect(collection.owner.fullname).toEqual('Jeff Bezos');
  });

  it('does not add the suffix twice to the name of an authenticated user', () => {
    const collection = new Collection({}, {});
    collection.owner = { fullname: 'Jeff Bezos (me)', isAuthenticatedUser: true };
    collection.setOwnerName();
    expect(collection.owner.fullname).toEqual('Jeff Bezos (me)');
  });

  it('reactivates a collection', async () => {
    const user = { fullname: 'Jeff Bezos', isAuthenticatedUser: true };
    const collectionData = { id: 'old uid', isDeactivated: true };
    const library = new CollectionLibrary({ collections: [collectionData], user: user });
    const collection = library.collections[0];

    axios.patch.mockResolvedValueOnce({
      data: { collectionUid: 'new uid', isOwnCollection: true },
    });
    const savedCollection = await collection.reactivate(user);
    expect(axios.patch).toHaveBeenCalledTimes(1);
    expect(axios.patch).toHaveBeenCalledWith('/sol/planner/rest/v2/collections/old uid/reactivate');
    expect(savedCollection.owner.fullname).toEqual('Jeff Bezos (me)');
    expect(savedCollection.id).toEqual('new uid');
    expect(savedCollection.isDeactivated).toEqual(false);
    expect(savedCollection.url).toEqual('/sol/planner/rest/v2/collections/new uid');
    expect(savedCollection.coAuthorCount).toEqual(null);
  });

  it('reactivates a collection without duplication', async () => {
    const user = { fullname: 'Jeff Bezos', isAuthenticatedUser: true };
    const collectionData = {
      id: 'old uid',
      isDeactivated: true,
      owner: { fullname: 'Richard Branson' },
    };
    const library = new CollectionLibrary({ collections: [collectionData], user: user });
    const collection = library.collections[0];

    axios.patch.mockResolvedValueOnce({
      data: { collectionUid: 'new uid', isOwnCollection: false },
    });
    const savedCollection = await collection.reactivate(user);
    expect(savedCollection.owner.fullname).toEqual('Richard Branson');
  });

  it('deletes an unmigrated collection', async () => {
    const library = new CollectionLibrary({
      collections: [{ id: 'old uid', isDeactivated: true, isLegacyCollection: true }],
    });
    const collection = library.collections[0];

    axios.delete.mockResolvedValueOnce({});
    await collection.delete();
    expect(axios.delete).toHaveBeenCalledTimes(1);
    expect(axios.delete).toHaveBeenCalledWith('/sol/planner/rest/collections/old uid');
  });

  it('deletes a collection', async () => {
    const library = new CollectionLibrary({
      collections: [{ id: 'old uid', isDeactivated: true }],
    });
    const collection = library.collections[0];

    axios.delete.mockResolvedValueOnce({});
    await collection.delete();
    expect(axios.delete).toHaveBeenCalledTimes(1);
    expect(axios.delete).toHaveBeenCalledWith('/sol/planner/rest/v2/collections/old uid');
  });

  it('archives a collection', async () => {
    const user = { fullname: 'Jeff Bezos', isAuthenticatedUser: true };
    const collectionData = { id: 'old uid', isDeactivated: true };
    const library = new CollectionLibrary({ collections: [collectionData], user: user });
    const collection = library.collections[0];

    axios.patch.mockResolvedValueOnce({
      data: { collectionUid: 'new uid', archived: false },
    });
    const savedCollection = await collection.archive();
    expect(axios.patch).toHaveBeenCalledTimes(1);
    expect(axios.patch).toHaveBeenCalledWith('/sol/planner/rest/v2/collections/old uid/archive');
    expect(savedCollection.archived).toEqual(true);
  });

  it('unarchives a collection', async () => {
    const user = { fullname: 'Jeff Bezos', isAuthenticatedUser: true };
    const collectionData = { id: 'old uid', isDeactivated: true };
    const library = new CollectionLibrary({ collections: [collectionData], user: user });
    const collection = library.collections[0];
    const pinia = createTestingPinia();

    piwikProStore = usePiwikProStore(pinia);
    piwikProStore.trackEvent = jest.fn();

    axios.patch.mockResolvedValueOnce({
      data: { collectionUid: 'new uid', archived: false },
    });
    const savedCollection = await collection.unarchive();
    expect(axios.patch).toHaveBeenCalledTimes(1);
    expect(axios.patch).toHaveBeenCalledWith('/sol/planner/rest/v2/collections/old uid/dearchive');
    expect(savedCollection.archived).toEqual(false);
    expect(piwikProStore.trackEvent).toHaveBeenCalledTimes(1);
    expect(piwikProStore.trackEvent).toHaveBeenCalledWith({
      action: 'Restore archived collection',
      category: 'Collection manipulations',
    });
  });
});
