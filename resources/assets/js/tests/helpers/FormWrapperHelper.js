import FormWrapper from '../../components/FormWrapper';
import { mount } from '@vue/test-utils';
import lodashDefaults from 'lodash/defaults';
import { createTestingPinia } from '@pinia/testing';
import createTestingVuetify from './CreateTestingVuetify.js';

export default {
  createFormWrapperElement(i18n, data = {}) {
    return mount(FormWrapper, {
      props: lodashDefaults(data, {
        action: 'action',
        model: data.model,
      }),
      global: {
        plugins: [createTestingPinia(), createTestingVuetify()],
      },
    });
  },
};
