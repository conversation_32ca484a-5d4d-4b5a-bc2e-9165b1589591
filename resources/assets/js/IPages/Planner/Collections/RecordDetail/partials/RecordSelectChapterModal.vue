<template>
  <v-dialog
    v-if="isOpen"
    max-width="880"
    v-model="isOpen"
    class="planner overflow-hidden"
    content-class="d-flex planner"
  >
    <v-card class="modal-content overflow-hidden">
      <v-card-title class="modal-header">
        <button type="button" class="close" aria-label="Close" @click="cancelModal">
          <PlannerIcon name="cross"></PlannerIcon>
        </button>
        <h4 class="modal-title">
          {{ $t('MODULE.COLLECTIONS.COLLECTION.CHAPTER.SELECT') }}
        </h4>
      </v-card-title>
      <v-card-text class="overflow-hidden d-flex pa-0" style="flex: auto 1 1">
        <div class="c-spacer text-center text-muted" v-if="availableCollections.length === 0">
          {{ $t('MODULE.COLLECTIONS.COLLECTIONS.EMPTY') }}
        </div>

        <template v-if="availableCollections.length > 0 || archivedCollections.length > 0">
          <v-col cols="3" class="pa-0 overflow-auto">
            <ul class="nav nav-pills nav-stacked pl-0">
              <li
                v-for="(collection, index) in availableCollections"
                :class="{
                  active: collection === selectedCollection,
                }"
                :key="'collection_' + index"
              >
                <a @click="selectCollection(collection)" style="cursor: pointer">
                  {{ collection.name }}
                  <span v-if="collection.target_audience">({{ collection.target_audience }})</span>
                </a>
              </li>
            </ul>
            <template v-if="archivedCollections.length">
              <hr />
              <label
                v-if="archivedCollections.length > 0"
                style="flex: 0 0 auto; margin-left: 12px"
              >
                {{ $t('MODULE.COLLECTIONS.COLLECTIONS.VIEW_MODE.ARCHIVED') }}
              </label>
              <ul class="nav nav-pills nav-stacked pl-0">
                <li
                  v-for="(collection, index) in archivedCollections"
                  :class="{
                    active: collection === selectedCollection,
                  }"
                  :key="'collection_' + index"
                >
                  <a @click="selectCollection(collection)">
                    {{ collection.name }}
                    <span v-if="collection.target_audience"
                      >({{ collection.target_audience }})</span
                    >
                  </a>
                </li>
              </ul>
            </template>
          </v-col>
          <v-col cols="9" class="pa-0 overflow-auto">
            <div v-if="collectionLoading && selectedCollection">
              <TkLoader :promise="collectionLoading">
                <template v-if="selectedCollection && selectedCollection.chapters">
                  <div
                    class="c-spacer text-center text-muted"
                    v-if="selectedCollection.chapters.length === 0"
                  >
                    {{ $t('MODULE.COLLECTIONS.COLLECTION.CHAPTERS.EMPTY') }}
                  </div>
                  <table
                    class="table table-striped table-hover"
                    v-if="selectedCollection.chapters.length > 0"
                    :aria-label="$t('MODULE.COLLECTIONS.COLLECTION.CHAPTER.CHAPTERS')"
                  >
                    <thead>
                      <tr>
                        <th>
                          {{ $t('MODULE.COLLECTIONS.COLLECTION.CHAPTER.CHAPTER') }}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        v-for="(chapter, index) in selectedCollection.chapters"
                        :class="{
                          selected: selectedChapter === chapter,
                        }"
                        :key="'chapter_' + index"
                        @click="selectChapter(chapter)"
                        style="cursor: pointer"
                      >
                        <td>{{ chapter.name }}</td>
                      </tr>
                    </tbody>
                  </table>
                </template>
              </TkLoader>
            </div>
          </v-col>
        </template>
      </v-card-text>
      <v-card-actions class="modal-footer py-0">
        <div class="bc-toolbar">
          <div class="bc-toolbar-left">
            <div class="bc-toolbar-element">
              <v-btn variant="text" dense @click.stop.prevent="cancelModal">
                {{ $t('ACTION.CANCEL') }}
              </v-btn>
            </div>
          </div>
          <div class="bc-toolbar-right">
            <div class="bc-toolbar-element">
              <v-btn
                color="primary"
                variant="elevated"
                dense
                :disabled="!selectedChapter"
                @click="sendUpdate"
                >{{ $t('ACTION.SELECT') }}</v-btn
              >
            </div>
          </div>
        </div>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import lodash from 'lodash';
import TkLoader from '../../../../../components/Planner/common/TkLoader.vue';
import PlannerIcon from '../../../../../components/Planner/common/PlannerIcon.vue';

export default {
  name: 'RecordSelectChapterModal',

  components: {
    TkLoader,
    PlannerIcon,
  },

  props: {
    library: { required: false, type: Object, default: null },
  },

  data() {
    return {
      isOpen: false,
      selectedChapter: null,
      selectedCollection: null,
      collectionLoading: null,
      record: {},
      libraryClone: lodash.cloneDeep(this.library),
    };
  },

  watch: {
    library: {
      immediate: true,
      handler() {
        this.libraryClone = lodash.cloneDeep(this.library);
      },
    },
  },

  computed: {
    availableCollections() {
      return lodash(this.libraryClone.collections)
        .filter((collection) => {
          return !collection.archived;
        })
        .orderBy('name')
        .value();
    },
    archivedCollections() {
      return lodash(this.libraryClone.collections)
        .filter((collection) => {
          return collection.archived;
        })
        .orderBy('name')
        .value();
    },
  },

  methods: {
    showModal(record) {
      this.record = record;
      this.isOpen = true;
      this.selectCollection(this.libraryClone.collections[0]);
    },
    cancelModal() {
      this.closeModal();
      this.$emit('cancel');
    },
    closeModal() {
      this.isOpen = false;
    },
    sendUpdate() {
      this.$emit('confirm', {
        record: this.record,
        collection: this.selectedCollection,
        chapter: this.selectedChapter,
      });
      this.closeModal();
    },
    selectCollection(collection) {
      if (collection !== this.selectedCollection) {
        this.selectedChapter = null;
        this.selectedCollection = collection;
        this.collectionLoading = collection.enrich();
      }
    },
    selectChapter(chapter) {
      if (chapter !== this.selectedChapter) {
        this.selectedChapter = chapter;
      }
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.modal-content) {
  overflow: hidden;
}
:deep(.modal-body) {
  height: calc(100% - 60px * 2);
}

:deep(.v-dialog) {
  height: 100%;
}
</style>
