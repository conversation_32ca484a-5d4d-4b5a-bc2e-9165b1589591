<template>
  <v-alert
    v-if="linkedCalendarItems && linkedCalendarItems.length"
    color="warning"
    variant="outlined"
    text
    border="bottom"
  >
    <v-row>
      <v-col cols="2">
        <label class="font-weight-bold text-grey-darken-3">{{
          $t('MODULE.COLLECTIONS.RECORD.LINKED_CALENDAR_ITEMS')
        }}</label>
      </v-col>
      <v-col cols="10">
        <ul>
          <li v-for="item in linkedCalendarItems" :key="item.uid" class="text-black">
            <a @click="goToCalendarItem(item)">
              <template v-if="hasRepetition(item)">{{
                capitalize(getRepetitionTranslation(item))
              }}</template>
              <template v-else>{{ capitalize(getCalendarFormatDate(item.begin)) }}</template>
            </a>
          </li>
        </ul>
      </v-col>
    </v-row>
  </v-alert>
</template>

<script>
import { mapState } from 'pinia';
import dayjs from 'dayjs';
import { useCollectionRecordStore } from '../../../../../stores/collectionRecordStore';
import { capitalize } from '../../../../../helpers/stringHelpers.js';

export default {
  name: 'RecordDetailCalendarItemList',

  computed: {
    ...mapState(useCollectionRecordStore, ['linkedCalendarItems']),
  },

  methods: {
    capitalize,
    getCalendarFormatDate(date) {
      return dayjs(date).calendar();
    },
    goToCalendarItem(item) {
      window.location.assign(
        '/calendar?item=' + item.uid + '&date=' + dayjs(item.begin).format('YYYY-MM-DD')
      );
    },
    hasRepetition(item) {
      return item.repetition !== '';
    },
    getRepetitionTranslation(item) {
      return this.$i18n.t(
        'labels.modules.planner.record.calendar-item-repetitions.' + item.repetition,
        {
          date: dayjs(item.begin).format('DD/MM HH:mm'),
        }
      );
    },
  },
};
</script>
