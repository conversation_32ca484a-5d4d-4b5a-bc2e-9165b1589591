<template>
  <RecordDetailFormGroup
    v-if="canEditItem || formData.purpose"
    :label="$t('MODULE.COLLECTIONS.RECORD.EVALUATION')"
    label-padding="pt-4"
    name="purpose"
    ref="formGroup"
    :form-data="formData"
  >
    <p v-if="!canEditItem" class="control-label">
      {{ $t('MODULE.COLLECTIONS.RECORD.PURPOSE.' + formData.purpose + '.' + formData.purpose) }}
    </p>
    <v-tooltip v-else location="top" attach>
      <template v-slot:activator="{ props }">
        <div v-bind="props" class="d-inline-block">
          <v-switch
            :model-value="formData.purpose === 'EVALUATION'"
            @click="togglePurpose"
            hide-details
            class="mt-0"
            color="primary"
          ></v-switch>
        </div>
      </template>
      <span>{{ $t('MODULE.COLLECTIONS.RECORD.EVALUATION_DESCRIPTION') }}</span>
    </v-tooltip>
  </RecordDetailFormGroup>
</template>

<script>
import RecordDetailFormGroup from '../RecordDetailFormGroup.vue';
import { mapState } from 'pinia';
import { useCollectionRecordStore } from '../../../../../../stores/collectionRecordStore';

export default {
  name: 'RecordPurpose',
  components: { RecordDetailFormGroup },
  computed: {
    ...mapState(useCollectionRecordStore, ['canEditItem', 'formData', 'validationObserver']),
  },
  methods: {
    togglePurpose() {
      this.formData.purpose = this.formData.purpose === 'EVALUATION' ? 'COURSE' : 'EVALUATION';
    },
  },
};
</script>
