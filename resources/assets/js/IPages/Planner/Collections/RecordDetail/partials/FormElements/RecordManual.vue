<template>
  <RecordDetailFormGroup
    :label="$t('MODULE.COLLECTIONS.RECORD.MANUAL')"
    name="manual"
    v-if="recordModel?.manual || hasInstructionMaterials"
    :form-data="formData"
  >
    <ul style="list-style: none; padding: 0">
      <li v-if="recordModel.manual">
        <v-btn
          color="primary"
          :href="recordModel.manual"
          target="_blank"
          class="text-white mt-2"
          prepend-icon="fa:far fa-file-alt"
        >
          {{ $t('MODULE.COLLECTIONS.RECORD.INSTRUCTIONS_PRACTICE.MANUAL') }}
        </v-btn>
      </li>
      <li v-if="formData.boardbook_url">
        <v-btn
          color="primary"
          :href="formData.boardbook_url"
          target="_blank"
          class="mt-2 text-white mr-2 pl-3"
        >
          <PlannerIcon start name="boardbook" class="mr-2" fill="#FFFFFF"></PlannerIcon>
          {{ $t('MODULE.COLLECTIONS.RECORD.INSTRUCTIONS_PRACTICE.BOARDBOOK') }}
        </v-btn>
      </li>
      <template v-for="index in 3">
        <li v-if="formData[`instruction_movie_url_${index}`]" :key="'instruction_' + index">
          <v-btn
            color="primary"
            :href="formData[`instruction_movie_url_${index}`]"
            target="_blank"
            class="mt-2 text-white"
            prepend-icon="fa:fas fa-video"
          >
            {{ $t(`MODULE.COLLECTIONS.RECORD.INSTRUCTIONS_PRACTICE.INSTRUCTION_MOVIE_${index}`) }}
          </v-btn>
        </li>
      </template>
    </ul>
  </RecordDetailFormGroup>
</template>

<script>
import RecordDetailFormGroup from '../RecordDetailFormGroup.vue';
import PlannerIcon from '../../../../../../components/Planner/common/PlannerIcon.vue';
import { mapState } from 'pinia';
import { useCollectionRecordStore } from '../../../../../../stores/collectionRecordStore';

export default {
  name: 'RecordManual',

  components: { PlannerIcon, RecordDetailFormGroup },

  computed: {
    ...mapState(useCollectionRecordStore, ['formData', 'recordModel', 'hasInstructionMaterials']),
  },
};
</script>
