<template>
  <Field
    v-model="formData[name]"
    :name="name"
    :rules="rules"
    v-slot="{ errors, meta, field, handleBlur, handleChange }"
    ref="validationProvider"
    :detect-input="detectInput"
  >
    <v-row>
      <v-col v-if="Number(inputColumns) !== 0" :cols="labelColumns" :class="labelPadding">
        <span :class="{ 'text-error': !meta.valid && errors.length }">{{ label }}</span>
        <span
          v-if="hasOriginalRecordData"
          class="text-medium-emphasis ml-1"
          style="font-size: 12px"
        >
          ({{ $t('labels.modules.planner.collections.detail.edited') }})</span
        >
      </v-col>
      <v-col :cols="inputColumns">
        <slot
          :errors="errors"
          :field="field"
          :handleChange="handleChange"
          :handleBlur="handleBlur"
        />
      </v-col>
    </v-row>
  </Field>
</template>

<script>
import { Field } from 'vee-validate';
import { mapState } from 'pinia';
import { useCollectionRecordStore } from '../../../../../stores/collectionRecordStore';

export default {
  name: 'RecordDetailFormGroup',

  components: { Field },

  props: {
    name: { required: true, type: String },
    label: { required: true, type: String },
    rules: { required: false, type: String, default: '' },
    labelColumns: { required: false, type: [String, Number], default: 2 },
    inputColumns: { required: false, type: [String, Number], default: 10 },
    labelPadding: { required: false, type: String, default: 'pt-3' },
    detectInput: {
      required: false,
      type: Boolean,
      default: true,
    },
    formData: { required: true, type: Object },
  },

  computed: {
    ...mapState(useCollectionRecordStore, ['overwrittenFields']),

    hasOriginalRecordData() {
      return (
        this.overwrittenFields &&
        this.overwrittenFields.length &&
        this.overwrittenFields.some((f) => f === this.name)
      );
    },
  },
};
</script>
