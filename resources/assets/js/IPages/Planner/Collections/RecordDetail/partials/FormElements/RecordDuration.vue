<template>
  <RecordDetailFormGroup
    v-if="canEditItem || formData[name]"
    :label="$t('MODULE.COLLECTIONS.RECORD.PURPOSE.' + formData.purpose + '.DURATION')"
    :name="name"
    label-padding="pt-4"
    :form-data="formData"
    :rules="'numeric|min_value:' + min + '|max_value:' + max"
    v-slot="{ errors, handleChange, handleBlur }"
  >
    <p v-if="!canEditItem" class="control-label">
      {{ formData[name] }}
      {{ $t('MODEL.TIME.MINUTES') }}
    </p>

    <TkDuration
      v-if="canEditItem"
      v-model="formData[name]"
      :min="min"
      :max="max"
      @update:model-value="handleChange"
      @blur="handleBlur"
      id="duration"
      name="duration"
      :error-messages="errors"
    />
    <a
      v-if="hasOriginalRecordData"
      @click="restoreOriginal"
      class="text-primary"
      style="cursor: pointer"
      >{{ $t('labels.modules.planner.record.restore-original') }} ({{ originalLabel }})</a
    >
  </RecordDetailFormGroup>
</template>

<script>
import TkDuration from '../../../../../../components/Planner/common/TkDuration.vue';
import RecordDetailFormGroup from '../RecordDetailFormGroup.vue';
import { mapState } from 'pinia';
import { isNull } from 'lodash';
import { useCollectionRecordStore } from '../../../../../../stores/collectionRecordStore';

export default {
  name: 'RecordDuration',

  components: { RecordDetailFormGroup, TkDuration },

  data() {
    return {
      min: 5,
      max: 65535,
    };
  },

  computed: {
    ...mapState(useCollectionRecordStore, [
      'canEditItem',
      'formData',
      'overwrittenFields',
      'getOriginalRecord',
    ]),

    name() {
      return 'duration_minutes';
    },

    hasOriginalRecordData() {
      return (
        this.overwrittenFields &&
        this.overwrittenFields.length &&
        this.overwrittenFields.some((f) => f === this.name)
      );
    },

    originalRecordData() {
      return this.getOriginalRecord[this.name];
    },

    originalLabel() {
      return isNull(this.originalRecordData)
        ? this.$i18n.t('labels.modules.planner.record.empty')
        : this.originalRecordData;
    },
  },

  methods: {
    restoreOriginal() {
      this.formData[this.name] = this.originalRecordData;
    },
  },
};
</script>
