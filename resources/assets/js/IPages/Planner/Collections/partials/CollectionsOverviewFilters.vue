<template>
  <div v-if="showFilters">
    <v-divider class="mx-4"></v-divider>
    <v-list-subheader class="d-flex" style="opacity: 1">
      <div class="flex flex-grow-1" style="opacity: 45%">
        {{ capitalize($t('labels.modules.planner.collections.overview.filters.filters')) }}
      </div>
      <a
        v-if="filtersActive"
        @click="resetFilters"
        style="text-transform: none; font-weight: normal"
        class="text-primary"
        >{{ capitalize($t('labels.modules.planner.collections.overview.filters.reset-all')) }}</a
      >
    </v-list-subheader>

    <v-list-item class="list-filter-type-title">
      <v-list-item-title>{{
        capitalize($t('labels.modules.planner.collections.overview.filters.type'))
      }}</v-list-item-title>
    </v-list-item>

    <v-list-item v-if="showFavourite">
      <template v-slot:prepend>
        <v-list-item-action :start="true">
          <v-checkbox
            density="compact"
            hide-details
            v-model="filters.favourited"
            @update:modelValue="trackFiltersEvent(TrackingMessages.COLLECTION_FILTER_FAVOURITE)"
          ></v-checkbox>
        </v-list-item-action>
      </template>

      <v-list-item-title
        >{{ capitalize($t('labels.modules.planner.collections.overview.filters.favourite'))
        }}<v-icon size="13" class="ml-2" style="vertical-align: baseline" icon="fa:fas fa-star" />
      </v-list-item-title>
    </v-list-item>

    <v-list-item>
      <template v-slot:prepend>
        <v-list-item-action :start="true">
          <v-checkbox
            density="compact"
            hide-details
            v-model="filters.isCoAuthor"
            @update:modelValue="trackFiltersEvent(TrackingMessages.COLLECTION_FILTER_COLLABORATION)"
          ></v-checkbox>
        </v-list-item-action>
      </template>

      <v-list-item-title>{{
        capitalize($t('labels.modules.planner.collections.overview.filters.collaborative'))
      }}</v-list-item-title>
    </v-list-item>

    <template v-if="collectionOwners.length">
      <CollectionsOverviewFiltersHolder :item-count="collectionOwners.length">
        <v-list-item class="list-filter-type-title">
          <v-list-item-title
            >{{ capitalize($t('labels.modules.planner.collections.overview.filters.teacher')) }}
          </v-list-item-title>

          <template v-slot:append>
            <v-list-item-action v-if="filters.owners.length">
              <a @click="resetSelectedOwners" class="text-primary">{{
                capitalize($t('labels.modules.planner.collections.overview.filters.reset'))
              }}</a>
            </v-list-item-action>
          </template>
        </v-list-item>
        <v-list-item v-for="owner in collectionOwners" :key="owner.uid">
          <template v-slot:prepend>
            <v-list-item-action :start="true">
              <v-checkbox
                density="compact"
                hide-details
                v-model="filters.owners"
                :value="owner.uid"
                @update:modelValue="trackFiltersEvent(TrackingMessages.COLLECTION_FILTER_OWNED)"
              ></v-checkbox>
            </v-list-item-action>
          </template>

          <v-list-item-title>
            {{ owner.fullname }}&nbsp;({{ ownerFrequencyMap[owner.uid] }})
          </v-list-item-title>
        </v-list-item>
      </CollectionsOverviewFiltersHolder>
    </template>

    <template v-if="collectionPublishers.length">
      <v-list-item class="list-filter-type-title">
        <v-list-item-title
          >{{ capitalize($t('labels.modules.planner.collections.overview.filters.publishers')) }}
        </v-list-item-title>

        <v-list-item-action v-if="filters.publishers.length">
          <a @click="resetSelectedPublishers">{{
            capitalize($t('labels.modules.planner.collections.overview.filters.reset'))
          }}</a>
        </v-list-item-action>
      </v-list-item>
      <v-list-item>
        <v-chip-group
          selected-class="text-primary"
          :column="true"
          :multiple="true"
          v-model="filters.publishers"
        >
          <v-chip
            v-for="publisher in collectionPublishers"
            :key="publisher.uid"
            :value="publisher.uid"
            :label="true"
            size="large"
            variant="outlined"
          >
            {{ publisher.fullname }}
          </v-chip>
        </v-chip-group>
      </v-list-item>
    </template>

    <template v-if="collectionTargetAudiences.length">
      <v-list-item class="list-filter-type-title">
        <v-list-item-title
          >{{
            capitalize($t('labels.modules.planner.collections.overview.filters.target-audiences'))
          }}
        </v-list-item-title>

        <v-list-item-action v-if="filters.targetAudiences.length">
          <a @click="resetSelectedTargetAudiences">{{
            capitalize($t('labels.modules.planner.collections.overview.filters.reset'))
          }}</a>
        </v-list-item-action>
      </v-list-item>
      <v-list-item>
        <v-chip-group
          selected-class="text-primary"
          :column="true"
          :multiple="true"
          v-model="filters.targetAudiences"
        >
          <v-chip
            v-for="targetAudience in collectionTargetAudiences"
            :value="targetAudience.id"
            :key="targetAudience.id"
            :label="true"
            size="large"
            variant="outlined"
            @click="trackFiltersEvent(TrackingMessages.COLLECTION_FILTER_TARGET)"
          >
            {{ targetAudience.name }}
          </v-chip>
        </v-chip-group>
      </v-list-item>
    </template>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia';
import CollectionType from '../dataModels/CollectionType';
import CollectionsOverviewFiltersHolder from './CollectionsOverviewFiltersHolder.vue';
import { useCollectionOverviewStore } from '../../../../stores/collectionOverviewStore';
import { usePiwikProStore } from '../../../../stores/piwikProStore.js';
import TrackingMessages from '../../../../models/constants/TrackingMessages.js';
import { capitalize } from '../../../../helpers/stringHelpers.js';

export default {
  name: 'CollectionsOverviewFilters',

  components: { CollectionsOverviewFiltersHolder },

  data() {
    return {
      TrackingMessages,
    };
  },

  computed: {
    ...mapState(useCollectionOverviewStore, [
      'filters',
      'library',
      'filteredCollections',
      'collectionOwners',
      'collectionPublishers',
      'collectionTargetAudiences',
      'ownerFrequencyMap',
      'filtersActive',
      'collectionType',
    ]),
    showFilters() {
      return this.library.collections.length > 0;
    },
    showFavourite() {
      return this.collectionType.name !== CollectionType.LIBRARY.name;
    },
  },

  methods: {
    ...mapActions(useCollectionOverviewStore, [
      'resetSelectedOwners',
      'resetSelectedPublishers',
      'resetSelectedTargetAudiences',
      'resetFilters',
    ]),

    ...mapActions(usePiwikProStore, ['trackEvent']),

    capitalize,

    trackFiltersEvent(trackingMessage) {
      this.trackEvent(trackingMessage);
    },
  },
};
</script>

<style scoped lang="scss">
.v-list-subheader {
  text-transform: uppercase;
  font-weight: bold;
  color: #333333;
  opacity: 45%;
  height: auto;
  padding-top: 16px;

  :deep(.v-list-subheader__text) {
    display: flex !important;
    width: 100%;

    a {
      cursor: pointer;
    }
  }
}

.list-filter-type-title {
  .v-list-item-title {
    font-weight: bold;
    color: black;
  }

  .v-list-item-action {
    align-self: start;

    a {
      font-size: 0.75em;
      text-decoration: none;
      cursor: pointer;
    }
  }
}
</style>
