<template>
  <v-card-text
    class="collectionCoverBody flex pa-2 pt-4"
    style="position: relative"
    :class="isDeactivated && 'disabled'"
  >
    <EllipsisTooltip class="collectionName" :class="isDeactivated && 'disabled'">
      {{ collection.name }}
    </EllipsisTooltip>

    <CollectionCoverSharedStatus
      :collection="collection"
      :hover="hover"
      class="collectionPreviewSharedStatus"
    ></CollectionCoverSharedStatus>

    <div>
      <div class="collectionOwner">
        <div class="flex-grow-0 flex-shrink-1" style="min-width: 20px">
          <EllipsisTooltip>
            {{ collection.owner?.fullname }}
          </EllipsisTooltip>
        </div>
        <div v-if="!!collection.coAuthorCount" class="coAuthorCount">
          +{{ collection.coAuthorCount }}
        </div>
      </div>
    </div>

    <div class="d-flex align-center test-collectionMetaData" style="min-height: 30px">
      <div class="d-flex align-center text-truncate">
        <TargetAudienceWrapper :collection="collection" />
      </div>

      <v-spacer />

      <CollectionCoverLibraryButtons
        :collection="collection"
        :collectionType="collectionType"
        :hover="hover"
        style="position: relative; right: 5px; margin-left: 5px"
      ></CollectionCoverLibraryButtons>
    </div>
  </v-card-text>
</template>

<script>
import Collection from '../../../../models/dataModels/Collection';
import EllipsisTooltip from '../../../../components/EllipsisTooltip';
import CollectionCoverSharedStatus from './CollectionCoverSharedStatus.vue';
import TargetAudienceWrapper from './TargetAudienceWrapper';
import CollectionCoverLibraryButtons from './CollectionCoverLibraryButtons.vue';

export default {
  name: 'CollectionCoverBody',

  components: {
    EllipsisTooltip,
    CollectionCoverSharedStatus,
    TargetAudienceWrapper,
    CollectionCoverLibraryButtons,
  },

  props: {
    collection: { type: Collection, required: true },
    collectionType: { type: Object, required: true },
    hover: { type: Boolean, required: true },
  },

  computed: {
    isDeactivated() {
      return this.collection.isDeactivated;
    },
  },
};
</script>

<style scoped lang="scss">
.collectionPreviewSharedStatus {
  position: absolute;
  justify-content: center;
  top: -15px;
  right: 8px;

  :deep(.spacer) {
    flex-grow: 0 !important;
  }
}

.collectionOwner {
  display: flex;
  align-items: center;
}

.coAuthorCount {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 6px;
  font-size: 12px;
  background-color: rgba(141, 156, 169, 0.1);
  border-radius: 100%;
}

:deep(.collectionName) {
  color: black;
  font-weight: bold;
  font-size: 16px;
}

.collectionCoverBody.disabled,
.collectionName.disabled {
  color: rgba(0, 0, 0, 0.2);
}
</style>
