<template>
  <FollowUpSystemWrapper>
    <template v-slot:toolbar>
      <v-spacer></v-spacer>
      <SelectorBar
        :report-periods="reportPeriods"
        :selected-report-period="reportPeriod"
      ></SelectorBar>
    </template>
    <template v-slot:toolbarButton>
      <v-btn
        ref="buttonElement"
        color="success"
        @click="openInputMomentOptions(null)"
        :disabled="periodIsExpired"
      >
        {{ $t('labels.follow-up-systems.add-input-moment') }}
      </v-btn>
    </template>
    <v-container :fluid="true" class="pa-0" :key="followUpSystem.uid + '_' + reportPeriod.uid">
      <v-row>
        <v-col cols="12" class="pt-0">
          <v-row>
            <v-col cols="12" class="py-0">
              <v-card id="maxHeightContainer">
                <v-card-text class="pa-0">
                  <v-data-table
                    :items="pupils"
                    ref="tableElement"
                    v-table-vertical-hover="'hover'"
                    :items-per-page="-1"
                  >
                    <template v-slot:headers>
                      <tr>
                        <th
                          scope="col"
                          class="sticky-column no-hover sticky-header v-border-bottom v-border-right py-3"
                          style="z-index: 2"
                        ></th>
                        <template v-if="!loading">
                          <th
                            v-for="inputMoment in sortedInputMoments"
                            :key="'inputMoment' + inputMoment.uid"
                            class="sticky-header v-border-bottom v-border-right test text-left py-3"
                            :id="'testHeader_' + inputMoment.uid"
                          >
                            <div>
                              <strong style="font-size: 13px; color: black">
                                {{ inputMoment.name }}
                              </strong>
                            </div>
                            <div>
                              <span>
                                {{ getFormattedDate(inputMoment.date, 'D MMM YYYY') }}
                              </span>
                            </div>
                          </th>
                        </template>
                        <th
                          v-if="!periodIsExpired"
                          scope="col"
                          style="border-bottom: none"
                          class="v-border-right addtest"
                          @click="openInputMomentOptions(null)"
                        ></th>
                      </tr>
                      <tr>
                        <td class="v-border-bottom v-border-right sticky-column no-hover"></td>
                        <template v-if="!loading">
                          <td
                            v-for="inputMoment in sortedInputMoments"
                            :key="'editTest' + inputMoment.uid"
                            class="v-border-bottom v-border-right test text-center"
                          >
                            <v-btn
                              icon="edit"
                              variant="text"
                              color="primary"
                              class="ma-0"
                              @click="openInputMomentOptions(inputMoment)"
                              :disabled="periodIsExpired"
                            >
                            </v-btn>
                            <ConfirmButton
                              :title="$t('titles.confirm-delete')"
                              :confirm-text="$t('labels.delete')"
                              :cancel-text="$t('labels.cancel')"
                              :content="$t('labels.confirm-delete')"
                              @md-confirm="removeInputMoment(inputMoment)"
                              :disabled="periodIsExpired"
                            >
                              <template v-slot="{ toggle }">
                                <v-btn
                                  icon="remove_circle"
                                  variant="text"
                                  color="primary"
                                  class="ma-0"
                                  @click="toggle"
                                  :disabled="periodIsExpired"
                                >
                                </v-btn>
                              </template>
                            </ConfirmButton>
                          </td>
                        </template>
                        <td
                          v-if="!periodIsExpired"
                          class="v-border-right addtest"
                          @click="openInputMomentOptions(null)"
                        ></td>
                      </tr>
                    </template>

                    <template v-slot:item="props">
                      <tr>
                        <td class="no-hover sticky-column v-border-bottom v-border-right">
                          <strong>{{ props.item.fullname }}</strong>
                        </td>
                        <td
                          v-for="inputMoment in sortedInputMoments"
                          :key="'score' + inputMoment.uid"
                          class="v-border-bottom v-border-right test"
                        >
                          <div
                            v-if="props.item.disabled"
                            class="d-flex align-center justify-center"
                          >
                            <v-icon style="width: 24px" icon="warning" />
                          </div>
                          <div v-else>
                            <v-btn
                              icon="playlist_add_check"
                              variant="text"
                              :class="isUsed(props.item, inputMoment) ? 'text-primary' : ''"
                              @click.stop.prevent="openQuotationDialog(props.item, inputMoment)"
                            >
                            </v-btn>
                            <v-btn
                              icon="comment"
                              variant="text"
                              :class="
                                itemHasComments(props.item, inputMoment) ? 'text-primary' : ''
                              "
                              @click.stop.prevent="openQuotationDialog(props.item, inputMoment)"
                            >
                            </v-btn>
                          </div>
                        </td>
                        <td
                          v-if="props.index === 0 && !periodIsExpired"
                          :rowspan="pupils.length"
                          @click="openInputMomentOptions(null)"
                          class="addtest"
                        >
                          <v-icon size="40" color="white" icon="add_circle" />
                          <span style="display: block; width: 100%" class="text-white">
                            {{ $t('labels.follow-up-systems.add-input-moment') }}
                          </span>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
    <TestOptionsDialog
      ref="inputMomentOptionsDialog"
      @modal-closed="saveInputMoment"
      :schoolyear="schoolyear"
      :quotation-settings="quotationSettings"
      :show-on-report="true"
      modal-title="labels.follow-up-systems.add-input-moment"
      on-report-label="labels.follow-up-systems.on-report"
    >
    </TestOptionsDialog>
    <FollowUpSystemQuotationDialog
      ref="quotationDialog"
      @hasSelections="setHasSelections"
      @hasComments="setHasComments"
      :fetch-base-url="fetchByPupilBaseUrl"
      :quotation-system="quotationSystem"
      :comment-base-url="commentBaseUrl"
      :quotation-base-url="quotationBaseUrl"
      :quotation-sync-url="quotationSyncUrl"
      :comment-validation-rules="commentValidationRules"
      :report-period="reportPeriod"
      :get-fetch-url="getFetchUrl"
      :period-is-expired="periodIsExpired"
      :comment-sync-url="commentSyncUrl"
      :target-audiences="targetAudiences"
      v-model:target-audience="internalTargetAudience"
      @update:target-audience="setTargetAudience"
    >
    </FollowUpSystemQuotationDialog>
  </FollowUpSystemWrapper>
</template>

<script>
import Layout from '../../Common/Layout';
import SelectorBar from './Partials/SelectorBar';
import dayjs from 'dayjs';
import tableVerticalHover from '../../../directives/table-vertical-hover';
import ConfirmButton from '../../../components/ConfirmButton';
import axios from 'axios';
import TestOptionsDialog from '../../../components/TestOptionsDialog.vue';
import FollowUpSystemWrapper from '../FollowUpSystems/FollowUpSystemWrapper';
import FollowUpSystemQuotationDialog from './Partials/FollowUpSystemQuotationDialog';
import { mapActions } from 'pinia';
import { useTenantConfig } from '../../../stores/tenantConfig';
import { useDateFormat } from '../../../composables/useDateFormat.js';
import * as inertia from '@inertiajs/vue3';

export default {
  name: 'ByPupil',

  components: {
    FollowUpSystemWrapper,
    TestOptionsDialog,
    ConfirmButton,
    SelectorBar,
    FollowUpSystemQuotationDialog,
  },

  directives: {
    tableVerticalHover,
  },

  props: {
    quotationSystem: Object,
    reportPeriods: Array,
    inputMoments: Array,
    reportPeriod: Object,
    commentBaseUrl: String,
    quotationBaseUrl: String,
    quotationSyncUrl: String,
    fetchByPupilBaseUrl: String,
    followUpSystem: Object,
    followUpSystemGoals: Array,
    commentValidationRules: Object,
    pupils: Array,
    pupilsWithQuotations: [Object, Array],
    pupilsWithComments: [Object, Array],
    periodIsExpired: Boolean,
    schoolyear: Object,
    inputMomentSaveBaseUrl: String,
    commentSyncUrl: String,
    targetAudiences: Array,
    targetAudience: Object,
  },

  setup() {
    const { getFormattedDate } = useDateFormat();

    return { getFormattedDate };
  },

  data() {
    return {
      quotationSettings: {
        mayEvaluateUsingQuotations: true,
        mayEvaluateUsingComments: false,
        mayEvaluateUsingScores: false,
      },
      inputMomentsData: [],
      flatItems: [],
      internalTargetAudience: null,
      setTargetAudienceCancelToken: null,
    };
  },

  computed: {
    selectedSubjectAndTestUids() {
      return [
        this.followUpSystem.uid,
        this.reportPeriod.uid,
        this.pupils.map((pupil) => pupil.uid),
      ].join();
    },
    sortedInputMoments() {
      return this.inputMomentsData.slice(0).sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));
    },
  },

  layout: (h, page) => h(Layout, () => [page]),

  watch: {
    pupils: {
      immediate: true,
      handler: function () {
        this.pupils.forEach((value, index) => {
          value.index = index;
        });
      },
    },
    selectedSubjectAndTestUids: {
      immediate: true,
      handler: function () {
        this.loading = true;
        this.inputMomentsData = this.inputMoments;
        this.loading = false;
      },
    },
    followUpSystemGoals: {
      immediate: true,
      handler: function () {
        const cloned = JSON.parse(JSON.stringify(this.followUpSystemGoals || []));
        this.flatItems = this.getFlatItems(cloned);
      },
    },
    targetAudience: {
      immediate: true,
      handler: function (value) {
        this.internalTargetAudience = value;
      },
    },
  },

  mounted() {
    this.setPageTitle(this.$t('titles.evaluation') + ' - ' + this.followUpSystem.name);
  },

  methods: {
    ...mapActions(useTenantConfig, ['setPageTitle']),
    getAmountOfChildren(dataArray) {
      let itemCount = 0;
      function getRecursiveElements(itemArray) {
        for (const item of itemArray) {
          if (item.children && item.children.length > 0) {
            itemCount += item.children.length;
            getRecursiveElements(item.children);
          }
        }
      }
      getRecursiveElements(dataArray);
      return itemCount;
    },
    getFlatItems(dataArray) {
      const self = this;
      const itemAmount = this.getAmountOfChildren(dataArray);
      const flatItems = [];
      const numberOfVisibleGoals = 20;
      function getRecursiveElements(itemArray, parent, level) {
        let i = 0;
        for (const item of itemArray) {
          item.parent = parent;
          item.index = i;
          item.level = level;
          //make open value reactive
          if (itemAmount > numberOfVisibleGoals) {
            item.open = false;
          } else {
            item.open = true;
          }
          flatItems.push(item);
          i++;
          if (item.children && item.children.length > 0) {
            getRecursiveElements(item.children, item, level + 1);
          }
        }
      }
      getRecursiveElements(dataArray, null, 1);
      return flatItems;
    },
    openInputMomentOptions(inputMoment) {
      this.$refs.inputMomentOptionsDialog.openModal(inputMoment);
    },
    removeInputMoment(inputMoment) {
      const self = this;
      return axios
        .delete(this.inputMomentSaveBaseUrl.replace('#followUpSystemInputMoment#', inputMoment.uid))
        .then(() => {
          self.inputMomentsData = self.inputMomentsData.filter(
            (item) => item.uid !== inputMoment.uid
          );
        });
    },
    saveInputMoment(inputMoment) {
      const self = this;
      const inputMomentToUpdate = this.getItemByUid(this.inputMomentsData, inputMoment.uid);

      if (inputMoment.uid && inputMomentToUpdate) {
        return axios
          .post(
            this.inputMomentSaveBaseUrl.replace('#followUpSystemInputMoment#', inputMoment.uid),
            inputMoment
          )
          .then((response) => {
            Object.assign(inputMomentToUpdate, response.data);
          });
      }

      return axios.post(this.inputMomentSaveBaseUrl, inputMoment).then((response) => {
        inputMoment = response.data;

        self.inputMomentsData.push(inputMoment);

        self.$nextTick(() => {
          const scrollElement =
            self.$refs.tableElement.$el.getElementsByClassName('v-table__wrapper')[0];
          const newElement = document.getElementById('testHeader_' + response.data.uid);
          scrollElement.scrollLeft =
            newElement.offsetLeft - scrollElement.offsetWidth + newElement.offsetWidth;
        });
      });
    },
    getItemByUid(array, uid) {
      return array.find((item) => item.uid === uid);
    },
    openQuotationDialog(pupil, inputMoment) {
      this.$refs.quotationDialog.openDialog(
        this.flatItems,
        [pupil],
        inputMoment,
        this.pupils,
        true
      );
    },
    setHasSelections(event) {
      if (this.pupilsWithQuotations[event.itemUid] === undefined) {
        this.pupilsWithQuotations[event.itemUid] = {};
      }
      this.pupilsWithQuotations[event.itemUid][event.inputMomentUid] = event.hasSelections;
    },
    setHasComments(event) {
      if (this.pupilsWithComments[event.itemUid] === undefined) {
        this.pupilsWithComments[event.itemUid] = {};
      }
      this.pupilsWithComments[event.itemUid][event.inputMomentUid] = event.hasComments;
    },
    isUsed(pupil, inputMoment) {
      const inputMomentsForPupil = this.pupilsWithQuotations[pupil.uid];

      return inputMomentsForPupil !== undefined && (inputMomentsForPupil[inputMoment.uid] || false);
    },
    getFetchUrl(fetchBaseUrl, selectedItem, inputMoment) {
      return fetchBaseUrl
        .replace('#pupilUid#', selectedItem.uid)
        .replace('#followUpSystemInputMoment#', inputMoment.uid);
    },
    itemHasComments(goal, inputMoment) {
      const inputMomentsForGoal = this.pupilsWithComments[goal.uid];

      return inputMomentsForGoal !== undefined && (inputMomentsForGoal[inputMoment.uid] || false);
    },
    setTargetAudience(targetAudience) {
      if (this.setTargetAudienceCancelToken) {
        this.setTargetAudienceCancelToken.cancel();
      }

      inertia.router.reload({
        data: {
          target_audience: targetAudience?.id,
        },
        only: ['followUpSystemGoals', 'targetAudience', 'reportPeriods', 'tabs'],
        onCancelToken: (cancelToken) => {
          this.setTargetAudienceCancelToken = cancelToken;
        },
        onSuccess: () => {
          this.$nextTick(() => {
            this.$refs.quotationDialog?.updateGoals(this.flatItems);
          });
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.theme--light.v-tabs__bar) {
  background: transparent;
}

:deep(.v-progress-linear) {
  display: none;
}

:deep(.v-table__wrapper) {
  scroll-behavior: smooth;
  max-height: calc(100vh - 60px - 60px - 60px - 16px - 8px);
  overflow: auto;
}

:deep(.v-data-table > .v-table__wrapper > table) {
  width: auto;
  border-collapse: separate;

  .test {
    width: 200px;
    max-width: 200px;
    min-width: 200px;
    text-align: center;
  }

  .addtest {
    width: 100px;
    max-width: 100px;
    min-width: 100px;
    background-color: rgb(var(--v-theme-success));
    vertical-align: top;
    text-align: center;
    cursor: pointer;
    padding: 0 !important;

    &.hover {
      background-color: rgb(var(--v-theme-success));
    }
  }
}
</style>
