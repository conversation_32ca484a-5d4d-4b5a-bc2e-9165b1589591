<template>
  <v-container :fluid="true" class="min-width fill-height">
    <PupilListCollapseWrapper>
      <template v-slot="{ isOpen, toggleIsOpen }">
        <div style="width: 100%">
          <v-row>
            <v-col
              class="full-height overflow-y-auto pt-0 animated fillheight"
              :class="[isOpen ? 'v-col-4 v-col-md-3' : 'v-col-0']"
            >
              <div class="pb-3">
                <PupilList
                  :pupils="pupils"
                  :title="$t('titles.pupil-list')"
                  :autocomplete-url="pupilSearchAutocompleteUrl"
                  :selected-pupil-uid="pupil.uid"
                  redirect-route="web.evaluation.report.synthesis"
                ></PupilList>
              </div>
            </v-col>
            <v-col
              class="full-height overflow-y-auto animated fillheight"
              :class="[isOpen ? 'v-col-8 v-col-md-9' : 'v-col-12']"
            >
              <v-container :fluid="true" class="pa-0" style="margin-bottom: 150px">
                <v-row style="height: 60px" class="align-center">
                  <v-btn
                    :icon="isOpen ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"
                    variant="flat"
                    @click="toggleIsOpen"
                    class="mr-2"
                  >
                  </v-btn>
                </v-row>
                <v-row>
                  <v-col cols="12" lg="6" class="pt-0">
                    <h1>{{ pupil.fullname }}</h1>
                    <template v-if="showAverageAndMedianFeature">
                      <v-spacer></v-spacer>
                      <v-switch
                        :model-value="showMedianAndAverage"
                        @update:model-value="updateShowMedianAndAverage"
                        :loading="showMedianAndAverageLoading"
                        :label="$t('labels.show-median-and-average')"
                      ></v-switch>
                    </template>
                  </v-col>
                  <SynthesisReportPreview
                    :report="report"
                    :pupil="pupil"
                    :show-median-and-average="showMedianAndAverage"
                    :show-median-and-average-loading="showMedianAndAverageLoading"
                    :fetch-averages-url="fetchAveragesUrl"
                    @update:updateShowMedianAndAverageLoading="updateShowMedianAndAverageLoading"
                  >
                  </SynthesisReportPreview>
                </v-row>
              </v-container>
            </v-col>
          </v-row>
        </div>
      </template>
    </PupilListCollapseWrapper>
  </v-container>
</template>

<script>
import Layout from '../../Common/Layout.vue';
import PupilList from '../../../components/PupilList.vue';
import PupilListCollapseWrapper from '../../../components/PupilListCollapseWrapper.vue';
import SynthesisReportPreview from './Partials/SynthesisReportPreview';
import { mapActions } from 'pinia';
import { useTenantConfig } from '../../../stores/tenantConfig';

export default {
  name: 'Synthesis',

  components: {
    PupilList,
    PupilListCollapseWrapper,
    SynthesisReportPreview,
  },

  layout: (h, page) => h(Layout, () => [page]),

  props: {
    pupils: Array,
    pupil: Object,
    report: Array,
    pupilSearchAutocompleteUrl: String,
    subjects: Array,
    reportPeriods: Array,
    fetchAveragesUrl: {
      required: true,
      type: String,
    },
    showAverageAndMedianFeature: Boolean,
  },

  data() {
    return {
      showMedianAndAverage: false,
      showMedianAndAverageLoading: false,
    };
  },

  mounted() {
    this.showMedianAndAverage = this.getShowMedianAndAverage();
    this.setPageTitle(this.$t('titles.evaluation'));
  },

  methods: {
    ...mapActions(useTenantConfig, ['setPageTitle']),
    updateShowMedianAndAverage(newValue) {
      localStorage.setItem('synthesis-showMedianAndAverage', newValue);
      this.showMedianAndAverage = this.getShowMedianAndAverage();
    },
    getShowMedianAndAverage() {
      if (!this.showAverageAndMedianFeature) {
        return false;
      }
      return localStorage.getItem('synthesis-showMedianAndAverage') === 'true' || false;
    },
    updateShowMedianAndAverageLoading(loadingValue) {
      this.showMedianAndAverageLoading = loadingValue;
    },
  },
};
</script>
