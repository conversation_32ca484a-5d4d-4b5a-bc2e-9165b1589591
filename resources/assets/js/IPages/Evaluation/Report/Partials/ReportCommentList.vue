<template>
  <div v-if="componentToRender">
    <v-row v-if="!!title && (canComment || comments.length > 0)">
      <v-col cols="12" class="mb-0 pb-0"
        ><strong>{{ title }}</strong></v-col
      >
    </v-row>
    <component
      v-bind:is="componentToRender"
      v-for="(comment, index) in comments"
      :key="index + '_' + _uid + '_' + pupil.uid"
      :base-url="baseUrl"
      :name="modelValue.name"
      :can-comment="comment.can_save"
      :creator="comment.creator"
      :modelValue="comment.comment"
      :comment-type="commentType"
    >
    </component>
    <template v-if="canComment && !hasOwnComment">
      <component
        v-bind:is="componentToRender"
        :key="'newComment_' + _uid + '_' + pupil.uid"
        :base-url="baseUrl"
        :name="modelValue.name"
        :can-comment="true"
        :modelValue="newComment"
        :comment-type="commentType"
      ></component>
    </template>
  </div>
</template>

<script>
import ReportComment from './ReportComment';
import OpenReportComment from './OpenReportComment';

export default {
  name: 'ReportCommentList',

  components: { ReportComment, OpenReportComment },

  props: {
    modelValue: { required: true, type: Object },
    baseUrl: { required: true, type: String },
    pupil: { required: true, type: Object },
    commentType: { required: false, type: String, default: '' },
    title: { required: false, type: String },
  },

  data() {
    return {
      comments: [],
      newComment: { comment: null, uid: null },
      canComment: false,
    };
  },

  watch: {
    modelValue: {
      handler() {
        this.isOpen = !!this.modelValue;
        this.comments = this.modelValue.comments;
        this.canComment = this.modelValue.can_comment;
        this.newComment.comment = null;
        this.newComment.uid = null;
      },
      immediate: true,
    },
  },

  computed: {
    _uid() {
      //return component uid;
      return this.$.uid;
    },
    hasOwnComment() {
      return (
        this.comments.filter((comment) => {
          return comment.can_save;
        }).length > 0
      );
    },
    componentToRender() {
      return !!this.commentType ? OpenReportComment : ReportComment;
    },
  },
};
</script>
