<template>
  <v-row>
    <v-col cols="12" :id="domain.uid">
      <div class="text-h6">
        <v-icon
          v-if="domain.icon !== null"
          size="18"
          class="ml-2 mr-2"
          style="line-height: 2rem"
          :icon="getIconHandle(domain.icon.handle)"
        />
        <span>{{ domain.name }}</span>
        <span v-if="domain.recalculated_weight !== null"
          >&nbsp;-&nbsp;<small>{{ roundedWeight(domain.recalculated_weight) }}%</small></span
        >
      </div>
      <v-card variant="elevated">
        <v-card-text class="pa-0 text-black">
          <TestTable
            :tests="domain.tests"
            :total="domain.total"
            :total-max="domain.total_max"
            :domain-name="domain.name"
            :show-total="true"
            :pupil="pupil"
            :is-class-teacher="isClassTeacher"
            :can-comment="domain.can_comment"
            :score-comment-base-url="scoreCommentUrl"
            :score-comment-toggle-base-url="scoreCommentToggleUrl"
            :quotations="quotations"
            :quotation-systems="quotationSystems"
          ></TestTable>
        </v-card-text>
        <RedicodiList
          v-model:redicodis="domain.redicodis"
          :redicodi-toggle-url="redicodiToggleUrl"
          :is-class-teacher="isClassTeacher"
          :report-item="domain"
          report-item-type="subject"
          :show-redicodis-on-report="showRedicodisOnReport"
        ></RedicodiList>
      </v-card>
      <ReportCommentList
        :base-url="domainCommentUrl"
        :model-value="domain"
        :pupil="pupil"
      ></ReportCommentList>
    </v-col>
  </v-row>
</template>

<script>
import ReportCommentList from './ReportCommentList.vue';
import TestTable from './TestTable.vue';
import RedicodiList from './RedicodiList.vue';
import { useNumberFilters } from '../../../../composables/useNumberFilters.js';
import getIconHandle from '../../../../helpers/getIconHandle.js';

export default {
  name: 'Domain',
  components: { RedicodiList, ReportCommentList, TestTable },
  props: {
    pupil: Object,
    domain: Object,
    isClassTeacher: Boolean,
    commentBaseUrl: String,
    scoreCommentBaseUrl: String,
    scoreCommentToggleBaseUrl: String,
    quotations: Object,
    quotationSystems: Object,
    redicodiToggleUrl: String,
    showRedicodisOnReport: Boolean,
  },
  setup() {
    const { roundedWeight } = useNumberFilters();

    return { roundedWeight };
  },
  computed: {
    domainCommentUrl() {
      return this.commentBaseUrl.replace('#subject#', this.domain.uid);
    },
    scoreCommentUrl() {
      return this.scoreCommentBaseUrl.replace('#subject#', this.domain.uid);
    },
    scoreCommentToggleUrl() {
      return this.scoreCommentToggleBaseUrl.replace('#subject#', this.domain.uid);
    },
  },
  methods: {
    getIconHandle,
  },
};
</script>
