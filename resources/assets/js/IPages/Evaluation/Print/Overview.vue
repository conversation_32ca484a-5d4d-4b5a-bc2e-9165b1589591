<template>
  <v-container :fluid="true" class="pt-2 min-width fill-height">
    <PupilListCollapseWrapper>
      <template v-slot="{ isOpen, toggleIsOpen }">
        <div style="width: 100%">
          <v-row>
            <v-col
              class="full-height overflow-y-auto pt-0 animated fillheight"
              :class="[isOpen ? 'v-col-4 v-col-md-3' : 'v-col-0']"
            >
              <div class="pb-3">
                <v-card class="mt-5">
                  <v-list-item
                    :href="overviewPageRoute"
                    ripple
                    active-class="bg-primary text-white"
                    :active="true"
                  >
                    <v-list-item-title>
                      {{ $t('labels.all-pupils') }}
                    </v-list-item-title>

                    <template v-slot:append>
                      <v-list-item-action>
                        <v-icon color="white" icon="keyboard_arrow_right" />
                      </v-list-item-action>
                    </template>
                  </v-list-item>
                </v-card>
                <PupilList
                  :pupils="pupils"
                  :title="$t('titles.pupil-list')"
                  :autocomplete-url="pupilSearchAutocompleteUrl"
                  :selected-pupil-uid="null"
                  redirect-route="web.evaluation.print.pupil-print-reports"
                  :redirect-parameters="{
                    reportPeriod: reportPeriod.uid,
                  }"
                ></PupilList>
              </div>
            </v-col>

            <v-col
              class="full-height overflow-y-auto animated fillheight"
              :class="[isOpen ? 'v-col-8 v-col-md-9' : 'v-col-12']"
            >
              <v-container :fluid="true" class="pa-0">
                <v-row style="height: 60px" class="align-center nowrap">
                  <v-btn
                    :icon="isOpen ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"
                    variant="flat"
                    @click="toggleIsOpen"
                    class="mr-2"
                  >
                  </v-btn>
                  <div class="py-0 my-0" style="max-width: calc(100% - 56px)">
                    <Tabs
                      ref="tabs"
                      :tabs="periodTabs"
                      v-model="selectedTab"
                      :mobile-breakpoint="20"
                    ></Tabs>
                  </div>
                </v-row>

                <v-row v-if="!!warning">
                  <v-col cols="12">
                    <v-alert type="error" variant="outlined">
                      {{ warning }}
                    </v-alert>
                  </v-col>
                </v-row>

                <v-row>
                  <v-card class="ma-5">
                    <v-card-text class="pa-0">
                      <v-data-table :items="pupilStatuses" :key="componentKey">
                        <template v-slot:headers>
                          <tr class="v-no-border">
                            <th scope="col" class="text-center no-hover v-border-bottom">
                              {{ $t('labels.pupils') }}
                            </th>
                            <th
                              scope="col"
                              class="text-center no-hover v-border-bottom v-border-left"
                            >
                              {{ $t('labels.generate-pdf') }}
                            </th>
                            <th
                              scope="col"
                              class="text-center no-hover v-border-bottom v-border-left"
                            >
                              {{ $t('labels.download-pdf') }}
                            </th>
                          </tr>
                        </template>

                        <template v-slot:body.prepend>
                          <tr v-if="groupOverviewPrintIsActive">
                            <td style="background: white" class="v-border-bottom">
                              {{ $t('labels.group-overview') }}
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-btn
                                icon="fa:fas fa-print"
                                size="x-small"
                                color="primary"
                                variant="text"
                                @click="printGroupOverviewReport()"
                              >
                              </v-btn>
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-progress-circular
                                v-if="
                                  isPending('groupOverview') || showSpinner(groupOverviewStatus)
                                "
                                :width="5"
                                size="20"
                                indeterminate
                              ></v-progress-circular>
                              <v-btn
                                icon
                                color="primary"
                                :href="groupOverviewStatus.file"
                                v-else-if="showDownload(groupOverviewStatus)"
                                target="_blank"
                                size="x-small"
                                variant="text"
                              >
                                <v-icon class="mb-1" icon="fa:fas fa-download" />
                              </v-btn>
                            </td>
                          </tr>
                          <tr>
                            <td style="background: white" class="v-border-bottom">
                              {{ groupStatus.groupName }}
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-btn
                                icon="fa:fas fa-print"
                                color="primary"
                                variant="text"
                                size="x-small"
                                @click="printReportForGroup()"
                              >
                              </v-btn>
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-progress-circular
                                v-if="isPending('group') || showSpinner(groupStatus)"
                                :width="5"
                                size="20"
                                :indeterminate="true"
                              ></v-progress-circular>
                              <v-btn
                                v-else-if="showDownload(groupStatus)"
                                icon
                                color="primary"
                                variant="text"
                                size="x-small"
                                :href="groupStatus.file"
                                target="_blank"
                              >
                                <v-icon class="mb-1" icon="fa:fas fa-download" />
                              </v-btn>
                            </td>
                          </tr>
                        </template>

                        <template v-slot:item="props">
                          <tr
                            :key="componentKey + '-' + reportPeriod.uid + '-' + props.item.pupilUid"
                          >
                            <td style="background: white" class="v-border-bottom">
                              {{ props.item.pupilName }}
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-icon
                                v-if="props.item.disabled"
                                color="grey-darken-1"
                                icon="warning"
                              />
                              <v-btn
                                v-else
                                icon="fa:fas fa-print"
                                color="primary"
                                variant="text"
                                size="x-small"
                                @click="printReport(props.item)"
                              >
                              </v-btn>
                            </td>
                            <td class="v-border-bottom v-border-left no-hover text-center">
                              <v-icon
                                v-if="props.item.disabled"
                                color="grey-darken-1"
                                icon="warning"
                              />
                              <template v-else>
                                <v-progress-circular
                                  v-if="isPending(props.item.pupilUid) || showSpinner(props.item)"
                                  :width="5"
                                  size="20"
                                  :indeterminate="true"
                                ></v-progress-circular>
                                <v-btn
                                  v-else-if="showDownload(props.item)"
                                  icon
                                  color="primary"
                                  variant="text"
                                  size="x-small"
                                  :href="props.item.file"
                                  target="_blank"
                                >
                                  <v-icon class="mb-1" icon="fa:fas fa-download" />
                                </v-btn>
                              </template>
                            </td>
                          </tr>
                        </template>
                      </v-data-table>
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-container>
            </v-col>
          </v-row>
        </div>
      </template>
    </PupilListCollapseWrapper>
  </v-container>
</template>

<script>
import Layout from '../../Common/Layout.vue';
import PupilList from '../../../components/PupilList.vue';
import PupilListCollapseWrapper from '../../../components/PupilListCollapseWrapper.vue';
import Tabs from '../../../components/Tabs.vue';
import axios from 'axios';
import { mapActions } from 'pinia';
import { useTenantConfig } from '../../../stores/tenantConfig';

export default {
  name: 'Overview',

  components: {
    PupilList,
    PupilListCollapseWrapper,
    Tabs,
  },

  props: {
    reportPeriod: Object,
    pupils: Array,
    pupilSearchAutocompleteUrl: String,
    periodTabs: Array,
    statusUrl: String,
    printOverviewUrl: String,
    groupPrintUrl: String,
    groupOverviewPrintUrl: String,
    groupOverviewPrintIsActive: Boolean,
    pupilPrintUrl: String,
    warning: String,
    overviewPageRoute: String,
  },

  data() {
    return {
      selectedTab: null,
      pupilStatuses: [],
      groupStatus: {},
      groupOverviewStatus: {},
      // Keep spinners in sync with each other with a componentKey.
      componentKey: 0,
      interval: null,
      pendingRequests: [],
    };
  },

  computed: {
    thereArePendingRequests() {
      return (
        this.pendingRequests.length ||
        [...this.pupilStatuses, this.groupStatus, this.groupOverviewStatus].some((status) => {
          return status.status === 'inProgress' || status.status === 'queued';
        })
      );
    },
  },

  layout: (h, page) => h(Layout, () => [page]),

  mounted() {
    this.setPageTitle(this.$t('titles.evaluation'));
  },

  watch: {
    reportPeriod: {
      handler() {
        this.selectedTab = this.reportPeriod.name;
        this.fetchProgress();
        this.startIntervalCheck();
      },
      immediate: true,
    },
  },

  beforeUnmount() {
    clearInterval(this.interval);
  },

  methods: {
    ...mapActions(useTenantConfig, ['setPageTitle']),
    showDownload(item) {
      return item.file !== null;
    },
    isPending(uid) {
      return this.pendingRequests.indexOf(uid) >= 0;
    },
    showSpinner(item) {
      return item.status === 'queued' || item.status === 'inProgress';
    },
    fetchProgress: function () {
      const self = this;

      return axios.get(this.statusUrl).then((response) => {
        self.pupilStatuses = response.data.pupils;
        self.groupStatus = response.data.group;
        self.groupOverviewStatus = response.data.groupOverview;
        self.componentKey += 1;
        if (!self.thereArePendingRequests) {
          self.stopIntervalCheck();
        }
        self.checkStatusOfPendingRequests();
      });
    },
    printReport(pupil) {
      if (this.hasPendingRequest(pupil.pupilUid)) {
        return;
      }
      this.addToPendingRequests(pupil.pupilUid);
      this.startIntervalCheck();

      axios.get(this.pupilPrintUrl.replace('pupilUid', pupil.pupilUid));
    },
    printReportForGroup() {
      const self = this;
      if (this.hasPendingRequest('group')) {
        return;
      }
      this.addToPendingRequests('group');
      this.pupils.forEach((pupil) => {
        if (!pupil.disabled) {
          self.addToPendingRequests(pupil.uid);
        }
      });
      this.startIntervalCheck();
      axios.get(this.groupPrintUrl);
    },
    printGroupOverviewReport() {
      if (this.hasPendingRequest('groupOverview')) {
        return;
      }
      this.addToPendingRequests('groupOverview');
      this.startIntervalCheck();
      axios.get(this.groupOverviewPrintUrl);
    },
    startIntervalCheck() {
      clearInterval(this.interval);
      this.interval = setInterval(() => {
        this.fetchProgress();
      }, 5000);
    },
    stopIntervalCheck() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },
    hasPendingRequest(uid) {
      return this.pendingRequests.some((pendingUid) => pendingUid === uid);
    },
    addToPendingRequests(uid) {
      this.pendingRequests.push(uid);
    },
    removeFromPendingRequests(uid) {
      const index = this.pendingRequests.indexOf(uid);
      if (index > -1) {
        this.pendingRequests.splice(index, 1);
      }
    },
    checkStatusOfPendingRequests() {
      const self = this;
      const finishedRequests = this.pupilStatuses.filter((pupilStatus) => {
        return pupilStatus.status === 'completed' || pupilStatus.status === 'failed';
      });
      finishedRequests.forEach((pupil) => {
        self.removeFromPendingRequests(pupil.pupilUid);
      });
      if (this.groupStatus.status === 'completed' || this.groupStatus.status === 'failed') {
        self.removeFromPendingRequests('group');
      }
      if (
        this.groupOverviewStatus.status === 'completed' ||
        this.groupOverviewStatus.status === 'failed'
      ) {
        self.removeFromPendingRequests('groupOverview');
      }
    },
  },
};
</script>
