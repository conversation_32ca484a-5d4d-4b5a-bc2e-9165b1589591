import lodash from 'lodash';
import axios from 'axios';
import School from './School';
import config from '../core/config';

let schoolId = null;

export default class User {
  constructor(data) {
    lodash.assign(this, data);
  }

  static getCurrent() {
    return axios
      .get(config.getConfig().internalUrls.apiPlannerPrefix + '/users/user-profile')
      .then((result) => {
        return new User(result.data);
      });
  }

  static search(schoolId, groupId) {
    const url =
      config.getConfig().internalUrls.apiPlannerPrefix + '/schools/' + schoolId + '/staff';
    const filter = groupId
      ? {
          groupId: groupId,
        }
      : {};
    return axios.post(url, filter).then((response) => {
      return lodash.map(response.data, (user) => {
        return new User(user);
      });
    });
  }

  getFullName() {
    return this.firstname + ' ' + this.lastname;
  }

  hasPermission(permissionName, schoolId) {
    return lodash.some(this.permissions, {
      permission: permissionName,
      schoolId: schoolId,
    });
  }

  setSchoolId(value) {
    schoolId = value;
  }

  getSchoolIds() {
    return [schoolId];
  }

  getSchools() {
    return School.getAll(this.getSchoolIds());
  }

  isSchoolCalendarAdmin(schoolId) {
    return this.hasPermission(
      'CAN_MANAGE_SCHOOLCALENDAR',
      schoolId || lodash.first(this.getSchoolIds())
    );
  }

  isTeacherForPreSchoolers(schoolId) {
    return this.hasPermission(
      'HAS_ACCESS_TO_PRESCHOOL',
      schoolId || lodash.first(this.getSchoolIds())
    );
  }

  hasAccessToAllGroups(schoolId) {
    return this.hasPermission(
      'HAS_ACCESS_TO_ALL_GROUPS',
      schoolId || lodash.first(this.getSchoolIds())
    );
  }

  canCopyCalendarFromUsers(schoolId) {
    return this.hasPermission(
      'CAN_COPY_CALENDAR_FROM_USERS',
      schoolId || lodash.first(this.getSchoolIds())
    );
  }

  getGroups(schoolId, currentYear) {
    const promises = lodash(this.getSchoolIds())
      .map((schoolId) => {
        return School.getGroups(schoolId, currentYear);
      })
      .value();
    return axios.all(promises).then((allGroups) => {
      return lodash.flatten(allGroups);
    });
  }

  getGroupIndex(currentYear) {
    const self = this;
    return axios
      .all(
        lodash.map(this.getSchoolIds(), (schoolId) => {
          return School.getGroups(schoolId, currentYear).then((groups) => {
            return [schoolId, lodash.keyBy(groups, 'id')];
          });
        })
      )
      .then((pairs) => {
        return lodash.fromPairs(pairs);
      });
  }
}
