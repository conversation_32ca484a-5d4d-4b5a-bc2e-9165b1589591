<template>
  <v-list class="crud-list">
    <template v-if="!!this.$slots['header']">
      <v-list-item class="mx-0 px-0">
        <slot name="header"></slot>
      </v-list-item>
      <v-divider></v-divider>
    </template>

    <template v-for="(item, index) in localItems" :key="item.internalId">
      <v-list-item class="px-0 mr-0" height="62">
        <v-container :fluid="true">
          <v-row class="d-flex align-center">
            <slot :element="item" :index="index"></slot>
            <v-col cols="1">
              <v-btn
                theme="dark"
                icon="clear"
                size="x-small"
                elevation="0"
                :disabled="isItemDisabled(item, index)"
                @click="removeItem(index)"
                color="red"
              >
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-list-item>
      <v-divider></v-divider>
    </template>

    <v-row v-if="localItems.length < maximum || maximum === 0" justify="end" class="my-0 w-100">
      <v-list-item class="px-0">
        <v-btn icon="add" size="small" elevation="0" theme="dark" @click="addItem()" color="green">
        </v-btn>
      </v-list-item>
    </v-row>
  </v-list>
</template>

<script>
import generateUuid from '../../helpers/generateUuid.js';

export default {
  name: 'ListCrudComponent',

  props: {
    items: { required: false, type: Array, default: () => [] },
    emptyElement: { required: true, type: Object },
    minimum: { required: false, type: Number, default: 1 },
    maximum: { required: false, type: Number, default: 0 },
  },

  data() {
    return {
      emptyItem: this.cloneObject(this.emptyElement),
    };
  },

  watch: {
    emptyElement: {
      handler() {
        this.emptyItem = this.cloneObject(this.emptyElement);
      },
      immediate: true,
    },
  },

  computed: {
    localItems: {
      get() {
        return this.items;
      },
      set() {
        this.emitChanges();
      },
    },
  },

  beforeMount() {
    while (this.localItems.length < this.minimum) {
      this.addItem();
    }
    for (const item of this.localItems) {
      item.internalId = item.uid || item.internalId || this.generateUuid();
    }
  },

  methods: {
    generateUuid,
    cloneObject: function (object) {
      return JSON.parse(JSON.stringify(object));
    },
    addItem: function () {
      this.emptyItem.internalId = this.generateUuid();
      this.localItems.push(this.emptyItem);
      this.emitChanges();
      this.$emit('list-crud:add', this.emptyItem);
      this.emptyItem = this.cloneObject(this.emptyElement);
    },
    removeItem: function (index) {
      const removedItems = this.localItems.splice(index, 1);
      this.emitChanges();
      this.$emit('list-crud:remove', removedItems[0]);
    },
    isItemDisabled(item, index) {
      return (
        item.has_relations ||
        item.disabled ||
        (index <= this.minimum - 1 && this.localItems.length <= this.minimum)
      );
    },
    emitChanges() {
      this.$emit('update:items', this.localItems);
    },
  },
};
</script>

<style>
.crud-list .v-input .v-messages {
  display: block;
}

.crud-list .v-list-item {
  padding: 0;
}
</style>
