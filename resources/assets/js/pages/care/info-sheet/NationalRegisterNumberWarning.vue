<template>
  <v-alert v-model="showWarning" border="bottom" type="warning" class="elevation-min">
    {{ $t('labels.national-register-number-warning') }}
  </v-alert>
</template>

<script>
import dayjs from 'dayjs';
import { mapState } from 'pinia';
import { useFormButtons } from '../../../stores/formButtons';

export default {
  name: 'NationalRegisterNumberWarning',
  props: {
    parentFields: {
      required: true,
      type: Object,
    },
    parentModel: {
      required: true,
      type: Object,
    },
    hasNationalRegisterNumber: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      genderDirty: false,
      dateOfBirthDirty: false,
      originalGender: null,
      originalDateOfBirth: null,
      changedGender: false,
      changedDateOfBirth: false,
    };
  },
  computed: {
    ...mapState(useFormButtons, { formErrors: 'errors' }),
    showWarning() {
      return (
        this.hasNationalRegisterNumber &&
        ((this.genderDirty && this.changedGender) ||
          (this.dateOfBirthDirty && this.changedDateOfBirth))
      );
    },
  },
  watch: {
    formErrors() {
      this.handleUpdatedErrors();
    },
  },
  mounted() {
    this.originalGender = JSON.parse(JSON.stringify(this.parentModel.gender.value));
    this.originalDateOfBirth = this.parentModel.date_of_birth_string;
  },
  methods: {
    handleUpdatedErrors() {
      this.$nextTick(() => {
        this.genderDirty = this.parentFields.gender ? this.parentFields.gender.meta.dirty : false;
        this.dateOfBirthDirty = this.parentFields.date_of_birth_string
          ? this.parentFields.date_of_birth_string.meta.dirty
          : false;
        this.changedGender = this.originalGender !== this.parentModel.gender.value;
        this.changedDateOfBirth = !dayjs(this.originalDateOfBirth, 'DD/MM/YYYY').isSame(
          this.parentModel.date_of_birth_string
        );
      });
    },
  },
};
</script>
