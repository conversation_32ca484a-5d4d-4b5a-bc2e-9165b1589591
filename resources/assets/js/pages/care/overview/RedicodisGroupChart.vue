<template>
  <InteractionObserver
    @on-change="handleChartVisibility"
    :threshold="[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]"
    v-if="options.series.length"
  >
    <v-card>
      <v-card-title class="pb-2">
        <div class="subtitle font-weight-bold text-truncate">
          {{ options.title.text }}
        </div>
      </v-card-title>
      <v-card-text>
        <div
          ref="graph"
          :style="{ opacity: isVisible ? 1 : 0 }"
          style="transition: opacity 200ms ease"
        ></div>
      </v-card-text>
    </v-card>
  </InteractionObserver>
</template>

<script>
import axios from 'axios';
import 'highcharts/modules/drilldown';
import { events } from '../../../events';
import Highcharts from 'highcharts';
import InteractionObserver from '../../../components/InteractionObserver';

export default {
  name: 'RedicodisGroupChart',

  components: {
    InteractionObserver,
  },

  props: {
    options: { type: Object, required: true },
    drillDownUrl: { type: String, required: true },
  },

  data() {
    return {
      chart: null,
      chartData: JSON.parse(JSON.stringify(this.options)), // Create clone
      isVisible: false,
    };
  },

  mounted() {
    const self = this;
    this.chartData.chart.height = 250;
    this.chartData.chart.events = {
      drilldown: function (e) {
        axios
          .get(self.drillDownUrl + '?parent_redicodi_id=' + e.point.drilldown)
          .then((response) => {
            if (response.data.length > 0) {
              self.chart.addSingleSeriesAsDrilldown(e.point, {
                type: 'pie',
                name: e.point.name,
                data: response.data,
                colors: self.getChartColors(response.data.length),
              });
              self.chart.applyDrilldown();
            }
          });
      },
    };
    this.chartData.credits = { enabled: false };
    if (this.options.series[0].data.length > 0) {
      this.$nextTick(function () {
        // Init highcharts with the given options.
        this.chartData.colors = this.getChartColors(this.options.series[0].data.length);
        this.chartData.title.text = ''; //Disable highcharts title display;
        if (this.chartData.tooltip && typeof self.chartData.tooltip.formatter !== 'undefined') {
          self.chartData.tooltip.formatter = new Function(self.chartData.tooltip.formatter);
        }
      });
    }
  },

  beforeUnmount() {
    this.chart.destroy();
  },

  methods: {
    handleChartVisibility(entry) {
      if (entry.intersectionRatio >= 0.6 && !this.isVisible) {
        this.chart = Highcharts.chart(this.$refs.graph, this.chartData);
        events.$emit('resize');
        this.isVisible = true;
      } else if (entry.intersectionRatio <= 0.1) {
        this.isVisible = false;
      }
    },
    getChartColors: function (maxDepth) {
      // Make monochrome colors
      const maxItems = Math.max(this.options.series.length, maxDepth);
      const colors = [];
      const base = this.$vuetify.theme.themes.light.colors.primary;

      for (let i = 0; i < maxItems; i += 1) {
        colors.push(
          Highcharts.color(base)
            .brighten((0.5 / maxItems) * i)
            .get()
        );
      }
      return colors;
    },
  },
};
</script>
