<template>
  <div>
    <v-row style="height: 60px" class="align-center">
      <v-spacer></v-spacer>
      <v-col cols="3" class="py-0">
        <v-select
          v-model="selectedSchoolyearModel"
          :items="schoolyears"
          :label="$t('labels.schoolyear')"
          item-title="description"
          item-value="start"
          hide-details
          variant="underlined"
        ></v-select>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-alert v-if="schoolExportLink" border="bottom" type="info" class="elevation-min">
          {{ $t('labels.history-export-available')
          }}<a :href="schoolExportLink">{{ $t('labels.history-click-to-download') }}</a>
        </v-alert>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" v-show="!this.tableDataLoaded" class="text-center">
        <v-progress-circular color="primary" :indeterminate="true"></v-progress-circular>
      </v-col>
      <v-slide-y-reverse-transition>
        <v-col cols="12" v-show="this.tableDataLoaded">
          <v-data-table
            ref="tableElement"
            :class="showFooter ? 'withFooter' : 'withoutFooter'"
            :items-per-page="100"
            disable-filtering
            v-model:sort-by="sortBy"
            :hide-default-footer="!showFooter"
            :headers="headers"
            :items="filteredTableData"
            class="elevation-min"
            v-table-vertical-hover="'hover'"
            :loading="loading"
            v-model:page="page"
            :footer-props="{
              'disable-items-per-page': true,
            }"
            :fixed-header="true"
          >
            <template v-slot:headers="{ columns, isSorted, getSortIcon, toggleSort }">
              <tr>
                <th
                  v-for="header in columns"
                  :key="header.key"
                  scope="col"
                  style="vertical-align: top"
                  class="v-no-border"
                >
                  <div
                    style="display: flex; align-items: center; width: 100%"
                    :style="header.sortable ? 'cursor: pointer' : ''"
                    class="pt-3 pb-4"
                    @click="() => toggleSort(header)"
                  >
                    <span>{{ header.text }}</span>
                    <v-icon
                      v-if="header.sortable && isSorted(header)"
                      size="small"
                      class="ml-2"
                      :icon="getSortIcon(header)"
                    />
                  </div>
                </th>
              </tr>
              <tr>
                <th
                  v-for="header in columns"
                  :key="header.key"
                  class="v-no-border v-border-bottom"
                  scope="col"
                >
                  <div v-if="header.value === 'firstname'">
                    <v-text-field
                      v-model="firstNameFilter"
                      hide-details
                      :label="$t('labels.search')"
                      density="compact"
                    >
                    </v-text-field>
                  </div>
                  <div v-if="header.value === 'lastname'">
                    <v-text-field
                      v-model="lastNameFilter"
                      hide-details
                      :label="$t('labels.search')"
                      density="compact"
                    >
                    </v-text-field>
                  </div>
                  <div
                    v-if="
                      filters.hasOwnProperty(header.value) &&
                      columnValueList[header.value].length > 1
                    "
                    class="pb-3"
                    @click.prevent.stop
                  >
                    <v-select
                      variant="solo"
                      hide-details
                      density="compact"
                      :chips="true"
                      closable-chips
                      :multiple="true"
                      :items="columnValueList[header.value]"
                      v-model="filters[header.value]"
                      @update:model-value="updateSelectedGroups(header.value)"
                    >
                    </v-select>
                  </div>
                </th>
              </tr>
            </template>

            <template v-slot:item.export="{ item }">
              <v-progress-circular
                class="ml-2"
                :key="componentKey + item.uid"
                v-if="isPending(item.uid) || showSpinner(item)"
                :width="4"
                size="16"
                :indeterminate="true"
              ></v-progress-circular>
              <v-btn
                icon="fa:fas fa-print"
                color="primary"
                @click="initExport(item)"
                :disabled="!item.hasExportAccess"
                v-else
                size="x-small"
                variant="text"
              ></v-btn>

              <v-btn
                icon="fa:fas fa-download"
                size="x-small"
                color="primary"
                :href="getFile(item)"
                v-if="showDownload(item)"
                target="_blank"
                variant="text"
              >
              </v-btn>
            </template>

            <template v-slot:item.timestamp="{ item }">
              {{ getTimestamp(item) }}
            </template>

            <template v-slot:item.links="{ item }">
              <div style="min-width: 90px">
                <v-btn
                  icon="dashboard"
                  size="small"
                  color="primary"
                  :href="getDashboardUrl(item)"
                  target="_blank"
                  variant="text"
                  :disabled="!item.hasDashboardAccess"
                ></v-btn>
                <v-btn
                  v-if="getCareInfoUrl(item)"
                  icon="group"
                  size="small"
                  color="primary"
                  :href="getCareInfoUrl(item)"
                  target="_blank"
                  class="ml-2"
                  variant="text"
                  :disabled="!item.hasInfoSheetAccess"
                ></v-btn>
              </div>
            </template>
          </v-data-table>
        </v-col>
      </v-slide-y-reverse-transition>
    </v-row>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia';
import lodashDebounce from 'lodash/debounce';
import axios from 'axios';
import dayjs from 'dayjs';
import lodashZipObject from 'lodash/zipObject';
import tableVerticalHover from '../../../directives/table-vertical-hover';
import { useHistoryOverviewFilterStore } from '../../../stores/historyOverviewFilterStore';

const currentGroupKey = 'currentGroup';
export default {
  name: 'HistoryOverviewTable',

  directives: { tableVerticalHover },

  props: {
    currentSchool: { required: true, type: String },
    currentSchoolyear: { required: true, type: String },
    schoolyears: { required: true, type: Array },
    fetchBaseUrl: { required: true, type: String },
    dashboardBaseUrl: { type: String },
    careInfoBaseUrl: { required: true, type: String },
    exportBaseUrl: { required: true, type: String },
    exportStatusBaseUrl: { required: true, type: String },
    schoolExportLink: { required: false, type: String },
  },

  data() {
    return {
      loading: false,
      tableData: [],
      tableDataLoaded: false,
      firstNameFilter: '',
      lastNameFilter: '',
      headers: [
        {
          text: this.$i18n.t('labels.firstname'),
          key: 'firstname',
          sortable: true,
        },
        {
          text: this.$i18n.t('labels.lastname'),
          key: 'lastname',
          sortable: true,
        },
        {
          text: this.$i18n.t('labels.pupil-management.class'),
          key: 'group',
          sortable: true,
        },
        {
          text: this.$i18n.t('labels.pupil-management.current-class'),
          key: currentGroupKey,
          sortable: true,
        },
        {
          text: this.$i18n.t('labels.export'),
          key: 'export',
          sortable: false,
        },
        {
          text: this.$i18n.t('labels.pupil-management.timestamp'),
          key: 'timestamp',
          sortable: true,
        },
        { text: '', key: 'links', sortable: false, align: 'center' },
      ],
      sortBy: [{ key: 'lastname', order: 'asc' }],
      filters: {
        group: [],
        currentGroup: [],
        status: [],
      },
      debouncedGetTableData: lodashDebounce(this.getTableData, 500),
      interval: null,
      pendingRequests: [],
      // Keep spinners in sync with each other with a componentKey.
      componentKey: 0,
      pupilStatuses: [],
      page: 0,
    };
  },

  computed: {
    ...mapState(useHistoryOverviewFilterStore, ['selectedSchoolyear', 'selectedGroups']),
    selectedSchoolyearModel: {
      get() {
        return this.selectedSchoolyear;
      },
      set(selectedSchoolyear) {
        this.setSelectedSchoolyear(selectedSchoolyear);
        this.debouncedGetTableData();
      },
    },
    filteredTableData() {
      if (!this.tableData) {
        return [];
      }
      this.goToFirstPage();
      let tableData = this.tableData.slice();
      tableData = this.filterByFirstName(tableData);
      tableData = this.filterByLastName(tableData);
      return tableData.filter((data) => {
        return Object.keys(this.filters).every((filter) => {
          return this.filters[filter].length < 1 || this.filters[filter].includes(data[filter]);
        });
      });
    },
    showFooter() {
      return this.filteredTableData.length >= 100;
    },
    columnValueList() {
      const self = this;
      const keys = Object.keys(this.filters);
      return lodashZipObject(
        keys,
        keys.map((key) => [...new Set(this.tableData.map((data) => data[key]))].sort())
      );
    },
    thereArePendingRequests() {
      const pendingPupilStatuses = [];
      for (const [pupilUid, pupilStatus] of Object.entries(this.pupilStatuses)) {
        if (pupilStatus.status === 'in_progress' || pupilStatus.status === 'queued') {
          pendingPupilStatuses.push(pupilUid);
        }
      }

      return this.pendingRequests.length || pendingPupilStatuses.length;
    },
  },

  methods: {
    ...mapActions(useHistoryOverviewFilterStore, [
      'setSelectedSchoolyear',
      'setCurrentSchool',
      'setSelectedGroups',
    ]),
    getTableData() {
      const self = this;
      this.loading = true;

      return axios
        .post(this.fetchBaseUrl, {
          schoolyearStartYear: dayjs(this.selectedSchoolyear).format('YYYY'),
        })
        .then((response) => {
          self.setTableData(response.data);
          self.fetchProgress();
          self.startIntervalCheck();
        });
    },
    updateSelectedGroups(key) {
      if (key === currentGroupKey) {
        this.setSelectedGroups(this.filters[key]);
      }
    },
    filterByFirstName(data) {
      return data.filter(
        (pupil) =>
          this.firstNameFilter.length === 0 ||
          pupil.firstname.toLowerCase().includes(this.firstNameFilter.toLowerCase())
      );
    },
    filterByLastName(data) {
      return data.filter(
        (pupil) =>
          this.lastNameFilter.length === 0 ||
          pupil.lastname.toLowerCase().includes(this.lastNameFilter.toLowerCase())
      );
    },
    setTableData(data) {
      this.tableData = data;
      this.resetFilters();
      this.$nextTick(function () {
        if (this.$refs.tableElement && this.$refs.tableElement.$el) {
          this.$refs.tableElement.$el.dispatchEvent(new Event('recalculate'));
        }
        this.loading = false;
        this.tableDataLoaded = true;
        this.goToFirstPage();
      });
    },
    changeSort(column) {
      if (this.sortBy === column) {
        this.sortDesc = !this.sortDesc;
      } else {
        this.sortBy = column;
        this.sortDesc = false;
      }
      this.goToFirstPage();
    },
    goToFirstPage() {
      this.page = 1;
    },
    getDefaultGroups() {
      const groups = [];
      for (const currentGroupName of this.columnValueList[currentGroupKey]) {
        if (
          typeof currentGroupName === 'string' &&
          ((Array.isArray(this.selectedGroups) && this.selectedGroups.includes(currentGroupName)) ||
            (!Array.isArray(this.selectedGroups) &&
              (currentGroupName.toLowerCase() === 'schoolverlaters' ||
                currentGroupName === this.$i18n.t('labels.has-no-group'))))
        ) {
          groups.push(currentGroupName);
        }
      }
      return groups;
    },
    resetFilters() {
      Object.keys(this.filters).forEach((filterKey) => {
        this.filters[filterKey] =
          filterKey === currentGroupKey
            ? this.getDefaultGroups(this.currentSchool, this.currentSchoolyear)
            : [];
      });
      this.goToFirstPage();
    },
    getDashboardUrl(pupil) {
      return this.dashboardBaseUrl.replace('#pupilUid#', pupil.uid);
    },
    getCareInfoUrl(pupil) {
      return this.careInfoBaseUrl.replace('#pupilUid#', pupil.uid);
    },
    getExportUrl(pupil) {
      return this.exportBaseUrl.replace('#pupilUid#', pupil.uid);
    },
    showDownload(item) {
      return this.isPending(item.uid) === false && this.getFile(item) !== null;
    },
    isPending(uid) {
      return this.pendingRequests.indexOf(uid) >= 0;
    },
    showSpinner(item) {
      const pupilStatus = this.pupilStatuses[item.uid];
      return pupilStatus
        ? pupilStatus.status === 'queued' || pupilStatus.status === 'in_progress'
        : false;
    },
    getFile(item) {
      const pupilStatus = this.pupilStatuses[item.uid];
      return pupilStatus ? pupilStatus.file : null;
    },
    getTimestamp(item) {
      const pupilStatus = this.pupilStatuses[item.uid];
      return pupilStatus ? pupilStatus.timestamp : null;
    },
    fetchProgress: function () {
      const self = this;

      return axios.get(this.exportStatusBaseUrl).then((response) => {
        self.pupilStatuses = response.data;
        self.componentKey++;
        if (!self.thereArePendingRequests) {
          self.stopIntervalCheck();
        }
        self.checkStatusOfPendingRequests();
      });
    },
    initExport(pupil) {
      this.addToPendingRequests(pupil.uid);
      this.startIntervalCheck();
      axios.get(this.getExportUrl(pupil));
    },
    startIntervalCheck() {
      clearInterval(this.interval);
      this.interval = setInterval(() => {
        this.fetchProgress();
      }, 5000);
    },
    stopIntervalCheck() {
      clearInterval(this.interval);
    },
    addToPendingRequests(uid) {
      this.pendingRequests.push(uid);
      this.componentKey++;
    },
    removeFromPendingRequests(uid) {
      const index = this.pendingRequests.indexOf(uid);
      if (index > -1) {
        this.pendingRequests.splice(index, 1);
      }
    },
    checkStatusOfPendingRequests() {
      const self = this;
      for (const [pupilUid, pupilStatus] of Object.entries(this.pupilStatuses)) {
        if (pupilStatus.status === 'completed' || pupilStatus.status === 'failed') {
          self.removeFromPendingRequests(pupilUid);
        }
      }
    },
  },
  mounted() {
    if (!this.selectedSchoolyear) {
      this.setSelectedSchoolyear(this.currentSchoolyear);
    }
    this.setCurrentSchool(this.currentSchool);
    this.getTableData();
  },
  beforeUnmount() {
    clearInterval(this.interval);
  },
};
</script>

<style scoped lang="scss">
:deep(.v-table__wrapper) {
  scroll-behavior: smooth;
  overflow: auto;
}
:deep(.withFooter .v-table__wrapper) {
  max-height: calc(100vh - 60px - 60px - 60px - 16px - 20px - 24px - 40px - 16px);
}

:deep(.withoutFooter .v-table__wrapper) {
  max-height: calc(100vh - 60px - 60px - 60px - 16px - 20px - 24px - 2px);
}

:deep(.v-data-footer__select) {
  display: none;
}
:deep(.v-data-footer__pagination) {
  margin-left: auto;
}
:deep(.v-table) {
  .v-no-border {
    box-shadow: none !important;
  }
  .v-border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12) !important;
  }
  .v-border-left {
    border-left: 1px solid rgba(0, 0, 0, 0.12) !important;
  }
}
</style>
