<template>
  <v-dialog v-model="isOpen" width="500">
    <v-card>
      <v-card-title>
        {{ $t('titles.pupil-management.manage-pupils.pupil.confirm-no-national-register-number') }}
      </v-card-title>

      <v-card-text class="text-medium-emphasis">
        {{ $t('labels.pupil-management.help.confirm-no-national-register-number') }}
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn variant="text" @click.stop.prevent="close">
          {{ $t('labels.cancel') }}
        </v-btn>
        <v-btn color="success" class="ml-2" variant="elevated" @click="confirm">
          {{ $t('labels.confirm') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'PupilHasNoNationalRegisterNumberDialog',

  data() {
    return {
      isOpen: false,
    };
  },

  methods: {
    show() {
      this.isOpen = true;
      return Promise.resolve();
    },

    close() {
      this.isOpen = false;
    },

    confirm() {
      this.$emit('confirm');
      this.close();
    },
  },
};
</script>
