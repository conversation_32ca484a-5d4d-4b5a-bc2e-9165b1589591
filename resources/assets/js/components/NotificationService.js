import { useNotificationStore } from '../stores/notificationStore';

/**
 * Install plugin.
 */

const NotificationService = {
  install(app, params = {}) {
    if (this.installed) {
      return;
    }

    this.installed = true;
    this.params = params;

    app.config.globalProperties.$notify = (params) => {
      const notificationStore = useNotificationStore();
      if (typeof params === 'string') {
        params = { text: params };
      }

      if (typeof params === 'object') {
        notificationStore.showNotification(params);
      }
    };
  },
};

export default NotificationService;
