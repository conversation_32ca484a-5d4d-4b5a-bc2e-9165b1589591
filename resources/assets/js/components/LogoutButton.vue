<template>
  <v-btn color="primary" class="mt-4" @click="logout">
    {{ label }}
  </v-btn>
</template>

<script>
import axios from 'axios';

export default {
  name: 'LogoutButton',
  props: {
    label: {
      type: String,
      required: true,
    },
    beforeLogout: {
      type: Function,
      default: () => Promise.resolve(),
    },
  },
  methods: {
    logout() {
      const self = this;
      return this.beforeLogout().then(() => {
        return axios.get(self.$page.props.routes['logout']).then((response) => {
          if (response.data.redirect) {
            window.location = response.data.redirect;
          }
        });
      });
    },
  },
};
</script>
