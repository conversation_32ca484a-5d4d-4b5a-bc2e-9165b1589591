<template>
  <div class="mt-1">
    <FormTitle
      v-if="label"
      :class="{ 'text-error': errorMessages.length > 0 }"
      style="font-size: 12px"
      >{{ label }}</FormTitle
    >
    <v-divider v-if="label" :class="{ error: errorMessages.length > 0 }"></v-divider>

    <textarea class="fr-view" ref="froala"></textarea>

    <div class="input-group__details" :class="{ closed: errorMessages.length === 0 }">
      <div
        class="input-group__messages input-group__error text-error"
        v-if="errorMessages.length > 0"
      >
        {{ errorMessages[0] }}
      </div>
    </div>
  </div>
</template>

<script>
import FroalaEditor from 'froala-editor';
import 'froala-editor/css/froala_editor.pkgd.min.css';
import 'froala-editor/css/froala_style.min.css';
import 'froala-editor/js/plugins/align.min.js';
import 'froala-editor/js/plugins/colors.min.js';
import 'froala-editor/css/plugins/colors.min.css';
import 'froala-editor/js/plugins/emoticons.min.js';
import 'froala-editor/css/plugins/emoticons.min.css';
import 'froala-editor/js/plugins/entities.min.js';
import 'froala-editor/js/plugins/font_family.min.js';
import 'froala-editor/js/plugins/font_size.min.js';
import 'froala-editor/js/plugins/fullscreen.min.js';
import 'froala-editor/css/plugins/fullscreen.min.css';
import 'froala-editor/js/plugins/help.min.js';
import 'froala-editor/css/plugins/help.min.css';
import 'froala-editor/js/plugins/inline_class.min.js';
import 'froala-editor/js/plugins/inline_style.min.js';
import 'froala-editor/js/plugins/image.min.js';
import 'froala-editor/js/plugins/line_breaker.min.js';
import 'froala-editor/css/plugins/line_breaker.min.css';
import 'froala-editor/js/plugins/line_height.min.js';
import 'froala-editor/js/plugins/link.min.js';
import 'froala-editor/js/plugins/lists.min.js';
import 'froala-editor/js/plugins/paragraph_format.min.js';
import 'froala-editor/js/plugins/paragraph_style.min.js';
import 'froala-editor/js/plugins/quick_insert.min.js';
import 'froala-editor/css/plugins/quick_insert.min.css';
import 'froala-editor/js/plugins/quote.min.js';
import 'froala-editor/js/plugins/special_characters.min.js';
import 'froala-editor/css/plugins/special_characters.min.css';
import 'froala-editor/js/plugins/table.min';
import 'froala-editor/css/plugins/table.min.css';
import 'froala-editor/js/plugins/url.min.js';
import 'froala-editor/js/plugins/word_paste.min.js';
import 'froala-editor/js/third_party/font_awesome.min.js';
import '../plugins/froala-nl';
import { activateCheckboxes } from '../plugins/froala-checklist.js';
import { useS3Upload } from '../composables/useS3Upload.js';
import FormTitle from './FormTitle';
import { events } from '../events';
import axios from 'axios';
import { useTenantConfig } from '../stores/tenantConfig';
import { useAllVuetifyColors } from '../composables/useAllVuetifyColors.js';

export default {
  name: 'VEditorFroala',

  components: {
    FormTitle,
  },

  props: {
    modelValue: { required: false, default: '' },
    label: { required: false },
    errorMessages: {
      required: false,
      default() {
        return [];
      },
    },
    small: { required: false, type: Boolean, default: false },
    heightMin: {
      required: false,
      default(props) {
        return props.small ? 82 : 200;
      },
    },
    heightMax: {
      required: false,
      default(props) {
        return props.small ? 82 : 500;
      },
    },
  },

  mounted() {
    if (this.tenantConfigStore.locale === 'nl-be') {
      this.config.language = 'nl';
    }
    this.initializeEditor();
  },

  watch: {
    modelValue: function (newValue) {
      if (newValue !== this.currentEditorValue) {
        this.updateEditorContent();
      }
    },
  },
  setup() {
    const { getAllColorsByValue } = useAllVuetifyColors();
    const { uploadFile } = useS3Upload();
    const tenantConfigStore = useTenantConfig();

    return {
      getAllColorsByValue,
      uploadFile,
      tenantConfigStore,
    };
  },
  data() {
    let colors = [
      '#E9252D',
      '#ED7928',
      '#F79739',
      '#F6BD16',
      '#B1D03D',
      '#8CB83A',
      '#46BC95',
      '#7CCDCF',
      '#0A82BE',
      '#5B54A4',
    ];
    colors.push(
      ...this.getAllColorsByValue(['lighten2', 'lighten4', 'darken2', 'darken4', 'black', 'white'])
    );
    colors.push('REMOVE');

    return {
      editor: null,
      currentEditorValue: null,
      config: {
        key: 'QFF4nB12A7B7A6D5E4eMRPYa1c1REe1BGQOQIc1CDBREJImD6F5F4F4E1B9B5C3B4F5==',
        attribution: false,
        quickInsertEnabled: false,
        imageInsertButtons: ['imageBack', '|', 'imageUpload', 'imageByURL'],
        tableEditButtons: [
          'tableHeader',
          'tableRemove',
          'tableRows',
          'tableColumns',
          'tableStyle',
          '-',
          'tableCells',
          'tableCellBackground',
          'tableCellVerticalAlign',
          'tableCellHorizontalAlign',
          'tableCellStyle',
        ],
        videoUpload: false,
        htmlUntouched: true,
        imageUpload: true,
        heightMin: this.heightMin,
        heightMax: this.heightMax,
        iconsTemplate: 'font_awesome_5r',
        fontAwesomeTemplate: '<i class="far fa-[NAME] fr-deletable" aria-hidden="true">&nbsp;</i>',
        toolbarButtons: {},
        toolbarSticky: false,
        tableStyles: {
          'fr-dashed-borders': this.$t('froala.table-styles.dashed-borders'),
          'fr-alternate-rows': this.$t('froala.table-styles.alternate-rows'),
          'hide-borders': this.$t('froala.table-styles.hide-borders'),
          'table-padding': this.$t('froala.table-styles.table-padding'),
        },
        tableCellStyles: {
          'fr-highlighted': this.$t('froala.table-cell-styles.highlighted'),
          'fr-thick': this.$t('froala.table-cell-styles.thick'),
        },
        inlineClasses: {
          'fr-class-code': this.$t('froala.inline-classes.code'),
          'fr-class-highlighted': this.$t('froala.inline-classes.highlighted'),
          'fr-class-transparency': this.$t('froala.inline-classes.transparent'),
        },
        paragraphStyles: {
          'fr-text-gray': this.$t('froala.paragraph-styles.gray'),
          'fr-text-bordered': this.$t('froala.paragraph-styles.bordered'),
          'fr-text-spaced': this.$t('froala.paragraph-styles.spaced'),
          'fr-text-uppercase': this.$t('froala.paragraph-styles.uppercase'),
        },
        fontFamily: {
          "'proximanova', 'Roboto', sans-serif": 'Proxima Nova',
          'Arial,Helvetica,sans-serif': 'Arial',
          'Georgia,serif': 'Georgia',
          'Impact,Charcoal,sans-serif': 'Impact',
          'Tahoma,Geneva,sans-serif': 'Tahoma',
          "'Times New Roman',Times,serif": 'Times New Roman',
          'Verdana,Geneva,sans-serif': 'Verdana',
        },
        fontAwesomeSets: [
          {
            title: 'Bingel',
            icon: 'users',
            list: [
              'star',
              'smile',
              'smile-beam',
              'laugh',
              'frown',
              'meh',
              'grin',
              'grin-stars',
              'grin-hearts',
              'thumbs-up',
              'heart',
              'chart-line',
              'hiking',
              'plus',
              'minus',
              'check',
              'hand-spock',
              'feather',
              'thumbs-up',
              'thumbs-down',
              'exclamation-circle',
              'feather-alt',
              'crown',
              'hand-holding-seedling',
              'tree',
              'hand-holding-heart',
              'comment',
              'comment-alt-smile',
              'book-reader',
              'user-graduate',
              'comment-edit',
              'file-minus',
              'bell-slash',
              'ballot',
              'eye',
              'paw-alt',
              'chair',
              'keynote',
              'map-marker-plus',
              'eye-slash',
              'headphones',
              'user-friends',
              'tennis-ball',
              'hourglass-half',
              'history',
              'alarm-clock',
              'alarm-plus',
              'sticky-note',
              'sitemap',
              'users-class',
              'user-edit',
              'compress-arrows-alt',
              'arrow-alt-circle-up',
              'font',
              'font-case',
              'pencil-alt',
              'pen-alt',
              'pen-fancy',
              'signature',
              'address-book',
              'copy',
              'keyboard',
              'photo-video',
              'inbox',
              'palette',
              'briefcase',
              'book-open',
              'calculator',
              'underline',
              'chart-network',
              'question-circle',
              'users',
              'border-style',
              'dumbbell',
              'volume-mute',
              'ear',
              'hand-heart',
              'undo',
              'comment-plus',
              'less-than-equal',
              'alarm-plus',
              'copy',
              'cheese-swiss',
              'crop-alt',
              'copyright',
              'notes-medical',
              'book-medical',
              'music',
              'volume-down',
              'book-user',
              'lips',
              'image',
              'laptop-medical',
              'users-cog',
              'location-circle',
              'abacus',
              'shoe-prints',
              'file-times',
              'sort-numeric-down-alt',
              'calculator',
              'circle',
              'border-all',
              'ruler-horizontal',
              'comments',
              'fist-raised',
              'exclamation-circle',
              'comment-check',
              'life-ring',
              'head-side-brain',
              'question',
              'eye-slash',
              'theater-masks',
              'sign-out',
              'compress-arrows-alt',
              'comment-dots',
              'comment-medical',
              'hands-helping',
              'calculator-alt',
              'function',
              'pen',
              'cut',
              'ruler',
              'ruler-triangle',
              'laptop',
              'pencil-ruler',
              'volume-slash',
              'list-ol',
              'map-signs',
              'repeat',
              'files-medical',
              'books-medical',
              'paste',
              'draw-circle',
              'microphone',
              'american-sign-language-interpreting',
              'swimmer',
              'swimming-pool',
              'map-signs',
              'suitcase',
              'microphone-stand',
              'theater-masks',
              'globe-europe',
              'child',
              'palette',
              'pen',
              'tools',
              'shoe-prints',
              'desktop',
              'hands-helping',
              'running',
              'custom-icons icon-activiteit',
              'custom-icons icon-actualiteit',
              'custom-icons icon-attitude',
              'custom-icons icon-bewegingsopvoeding',
              'custom-icons icon-blok',
              'custom-icons icon-contract',
              'custom-icons icon-dag_afsluiten',
              'custom-icons icon-frans',
              'custom-icons icon-godsdienst',
              'custom-icons icon-hoeken',
              'custom-icons icon-hoofd1',
              'custom-icons icon-hoofd2',
              'custom-icons icon-ict',
              'custom-icons icon-leren_leren',
              'custom-icons icon-lezen',
              'custom-icons icon-media',
              'custom-icons icon-mens_en_maatschappij',
              'custom-icons icon-muzische',
              'custom-icons icon-nederlands',
              'custom-icons icon-project',
              'custom-icons icon-schrijven',
              'custom-icons icon-sociaal_emotioneel',
              'custom-icons icon-sociale_vaardigheden',
              'custom-icons icon-taal',
              'custom-icons icon-welbevinden',
              'custom-icons icon-wereldorientatie',
              'custom-icons icon-wetenschap',
              'custom-icons icon-wiskunde',
              'custom-icons icon-zelfevaluatie',
              'custom-icons icon-zelfst-werken',
              'custom-icons icon-zorg',
              'custom-icons icon-groei-1',
              'custom-icons icon-groei-2',
              'custom-icons icon-groei-3',
              'custom-icons icon-groei-4',
              'custom-icons icon-groei-5',
              'award',
              'backpack',
              'bell-school',
              'cookie-bite',
              'dice-five',
              'dove',
              'hands-heart',
              'lightbulb-on',
              'medal',
              'pennant',
              'people-carry',
              'fish',
              'flower-daffodil',
              'leaf-oak',
              'acorn',
              'tree-alt',
              'house-user',
              'life-ring',
              'custom-icons icon-vla-aandacht',
              'custom-icons icon-vla-creatief',
              'custom-icons icon-vla-doorzetting',
              'custom-icons icon-vla-mezelfzien',
              'custom-icons icon-vla-plannen',
              'custom-icons icon-vla-reflectie',
              'custom-icons icon-vla-samenwerken',
              'custom-icons icon-vla-zelfkennis',
              'custom-icons icon-vla-zelfstandig',
              'custom-icons icon-vla-groei-a1',
              'custom-icons icon-vla-groei-a1-zwart',
              'custom-icons icon-vla-groei-a2',
              'custom-icons icon-vla-groei-a2-zwart',
              'custom-icons icon-vla-groei-a3',
              'custom-icons icon-vla-groei-a3-zwart',
              'custom-icons icon-vla-groei-a4',
              'custom-icons icon-vla-groei-a4-zwart',
              'custom-icons icon-vla-groei-a5',
              'custom-icons icon-vla-groei-a5-zwart',
              'custom-icons icon-vla-groei-a6',
              'custom-icons icon-vla-groei-a6-zwart',
              'custom-icons icon-vla-groei-b2',
              'custom-icons icon-vla-groei-b3',
              'custom-icons icon-vla-groei-b4',
              'custom-icons icon-vla-groei-b5',
              'thermometer',
              'balloon',
              'clover',
              'face-frown-open',
              'face-thinking',
              'face-smile-plus',
              'square',
              'rectangle',
              'rectangle-wide',
              'battery-quarter',
              'battery-three-quarters',
              'battery-full',
              'star-sharp-half-stroke',
              'cloud',
              'cloud-sun',
              'sun',
              'face-anxious-sweat',
              'child-reaching',
              'traffic-light-go',
              'traffic-light-slow',
              'traffic-light-stop',
              'gauge-min',
              'gauge-low',
              'gauge',
              'gauge-high',
              'gauge-max',
              'square-b',
              'square-d',
              'square-g',
              'square-e',
              'apple-whole',
              'solid fa-apple-whole',
              'solid fa-circle-o',
              'solid fa-circle-m',
              'solid fa-circle-g',
              'solid fa-circle-v',
              'solid fa-circle-r',
              'solid fa-stairs',
              'face-grin',
              'face-tongue-sweat',
              'face-relieved',
              'croissant',
              'treasure-chest',
            ],
          },
          {
            title: 'ZILL',
            icon: 'custom-icons icon-zill',
            list: [
              'custom-icons icon-zill-1',
              'custom-icons icon-zill-1-outline',
              'custom-icons icon-zill-2',
              'custom-icons icon-zill-2-outline',
              'custom-icons icon-zill-3',
              'custom-icons icon-zill-3-outline',
              'custom-icons icon-zill-4',
              'custom-icons icon-zill-4-outline',
              'custom-icons icon-zill-5',
              'custom-icons icon-zill-5-outline',
              'custom-icons icon-zill-6',
              'custom-icons icon-zill-6-outline',
              'custom-icons icon-zill-7',
              'custom-icons icon-zill-7-outline',
              'custom-icons icon-zill-8',
              'custom-icons icon-zill-8-outline',
              'custom-icons icon-zill-9',
              'custom-icons icon-zill-9-outline',
              'custom-icons icon-zill-10',
              'custom-icons icon-zill-10-outline',
            ],
          },
          {
            title: 'Web Application Icons',
            icon: 'address-book',
            list: [
              'address-book',
              'address-card',
              'adjust',
              'american-sign-language-interpreting',
              'anchor',
              'archive',
              'chart-area',
              'arrows',
              'arrows-h',
              'arrows-v',
              'american-sign-language-interpreting',
              'assistive-listening-systems',
              'asterisk',
              'at',
              'audio-description',
              'car',
              'balance-scale',
              'ban',
              'university',
              'chart-bar',
              'barcode',
              'bars',
              'bath',
              'battery-empty',
              'battery-quarter',
              'battery-half',
              'battery-three-quarters',
              'battery-full',
              'bed',
              'beer',
              'bell',
              'bell-slash',
              'bicycle',
              'binoculars',
              'birthday-cake',
              'blind',
              'bolt',
              'bomb',
              'book',
              'bookmark',
              'braille',
              'briefcase',
              'bug',
              'building',
              'bullhorn',
              'bullseye',
              'bus',
              'taxi',
              'calculator',
              'calendar',
              'calendar-check',
              'calendar-minus',
              'calendar-plus',
              'calendar-times',
              'camera',
              'camera-retro',
              'car',
              'caret-square-down',
              'caret-square-left',
              'caret-square-right',
              'caret-square-up',
              'cart-arrow-down',
              'cart-plus',
              'closed-captioning',
              'certificate',
              'check',
              'check-circle',
              'check-square',
              'child',
              'circle',
              'circle-notch',
              'clock',
              'clone',
              'times',
              'cloud',
              'cloud-download',
              'cloud-upload',
              'code',
              'code-branch',
              'coffee',
              'cog',
              'cogs',
              'comment',
              'comment-dots',
              'comments',
              'compass',
              'copyright',
              'credit-card',
              'crop',
              'crosshairs',
              'cube',
              'cubes',
              'utensils',
              'tachometer',
              'database',
              'deaf',
              'desktop',
              'diamond',
              'dot-circle',
              'download',
              'id-card',
              'id-card-alt',
              'edit ',
              'ellipsis-h',
              'ellipsis-v',
              'envelope',
              'envelope-open',
              'envelope-square',
              'eraser',
              'exchange',
              'exclamation',
              'exclamation-circle',
              'exclamation-triangle',
              'external-link',
              'external-link-square',
              'eye',
              'eye-slash',
              'eye-dropper',
              'fax',
              'rss',
              'female',
              'fighter-jet',
              'file-archive',
              'file-audio',
              'file-code',
              'file-excel',
              'file-image',
              'file-video',
              'file-pdf',
              'file-powerpoint',
              'file-video',
              'film',
              'filter',
              'fire',
              'fire-extinguisher',
              'flag',
              'flag-checkered',
              'flag',
              'bolt',
              'flask',
              'folder',
              'folder',
              'folder-open',
              'frown',
              'futbol',
              'gamepad',
              'gavel',
              'cog',
              'cogs',
              'gift',
              'glass',
              'globe',
              'graduation-cap',
              'users',
              'hand-lizard',
              'hand-paper',
              'hand-peace',
              'hand-pointer',
              'hand-rock',
              'hand-scissors',
              'hand-spock',
              'hand-paper ',
              'handshake',
              'hashtag',
              'hdd',
              'headphones',
              'heart',
              'heartbeat',
              'history',
              'home',
              'hotel',
              'hourglass',
              'hourglass-half',
              'hourglass-end',
              'hourglass-start',
              'i-cursor',
              'id-badge',
              'id-card',
              'image ',
              'inbox',
              'industry',
              'info',
              'info-circle',
              'key',
              'keyboard',
              'language',
              'laptop',
              'leaf',
              'gavel',
              'lemon',
              'level-down',
              'level-up',
              'life-ring',
              'lightbulb',
              'chart-line',
              'location-arrow',
              'lock',
              'low-vision',
              'magic',
              'magnet',
              'share',
              'reply',
              'reply-all',
              'male',
              'map',
              'map-marker',
              'map-pin',
              'map-signs',
              'meh',
              'microchip',
              'microphone',
              'microphone-slash',
              'minus',
              'minus-circle',
              'minus-square',
              'mobile',
              'mobile-alt',
              'money-bill',
              'money-bill-wave',
              'moon',
              'graduation-cap',
              'motorcycle',
              'mouse-pointer',
              'music',
              'bars',
              'newspaper',
              'object-group',
              'object-ungroup',
              'paint-brush',
              'paper-plane',
              'paw',
              'pencil',
              'pen-square',
              'percent',
              'phone',
              'phone-square',
              'image',
              'images',
              'chart-pie',
              'plane',
              'plug',
              'plus',
              'plus-circle',
              'plus-square',
              'podcast',
              'power-off',
              'print',
              'puzzle-piece',
              'qrcode',
              'question',
              'question-circle',
              'quote-left',
              'quote-right',
              'random',
              'recycle',
              'sync-all',
              'registered',
              'sort',
              'reply',
              'reply-all',
              'retweet',
              'road',
              'rocket',
              'rss',
              'rss-square',
              'search',
              'search-minus',
              'search-plus',
              'server',
              'share',
              'share-alt',
              'share-alt-square',
              'share-square',
              'shield',
              'ship',
              'shopping-bag',
              'shopping-basket',
              'shopping-cart',
              'shower',
              'sign-in',
              'sign-language',
              'sign-out',
              'signal',
              'sitemap',
              'sliders-v',
              'sliders-h',
              'smile',
              'snowflake',
              'futbol',
              'sort',
              'sort-alpha-up',
              'sort-alpha-down',
              'sort-amount-up',
              'sort-amount-down',
              'sort-up',
              'sort-down',
              'sort-numeric-up',
              'sort-numeric-down',
              'space-shuttle',
              'spinner',
              'balance-scale',
              'square',
              'star',
              'star-half',
              'sticky-note',
              'street-view',
              'suitcase',
              'sun',
              'tablet',
              'tachometer',
              'tag',
              'tags',
              'tasks',
              'taxi',
              'tv',
              'terminal',
              'thermometer',
              'thermometer-empty',
              'thermometer-quarter',
              'thermometer-half',
              'thermometer-three-quarters',
              'thermometer-full',
              'thumbtack',
              'thumbs-down',
              'thumbs-up',
              'ticket',
              'times',
              'times-circle',
              'times-square',
              'tint',
              'toggle-off',
              'toggle-on',
              'trademark',
              'trash',
              'tree',
              'trophy',
              'truck',
              'tty',
              'tv ',
              'umbrella',
              'universal-access',
              'university',
              'unlock',
              'unlock-alt',
              'upload',
              'user',
              'user-circle',
              'user-plus',
              'user-secret',
              'user-times',
              'users',
              'vcard ',
              'video',
              'volume-down',
              'volume-off',
              'volume-up',
              'exclamation-circle',
              'wheelchair',
              'wifi',
              'window-close',
              'window-maximize',
              'window-minimize',
              'window-restore',
              'wrench',
            ],
          },
          {
            title: 'Accessibility Icons',
            icon: 'american-sign-language-interpreting',
            list: [
              'american-sign-language-interpreting',
              'assistive-listening-systems',
              'audio-description',
              'blind',
              'braille',
              'closed-captioning',
              'deaf',
              'low-vision',
              'question-circle',
              'sign-language',
              'tty',
              'universal-access',
              'wheelchair',
            ],
          },
          {
            title: 'Hand Icons',
            icon: 'thumbs-up',
            list: [
              'thumbs-down',
              'thumbs-up',
              'hands',
              'handshake ',
              'hand-lizard',
              'hand-point-down',
              'hand-point-left',
              'hand-point-right',
              'hand-point-up',
              'hand-peace',
              'hand-pointer',
              'hand-paper',
              'hand-rock',
              'hand-scissors',
              'hand-spock',
              'hands-helping',
              'hands-wash',
            ],
          },
          {
            title: 'Transportation Icons',
            icon: 'car',
            list: [
              'car',
              'bicycle',
              'bus',
              'taxi',
              'fighter-jet',
              'motorcycle',
              'plane',
              'rocket',
              'ship',
              'space-shuttle',
              'subway',
              'train',
              'truck',
              'ambulance',
            ],
          },
          {
            title: 'Gender Icons',
            icon: 'venus-mars',
            list: [
              'genderless',
              'venus-mars ',
              'mars',
              'mars-double',
              'mars-stroke',
              'mars-stroke-h',
              'mars-stroke-v',
              'mercury',
              'neuter',
              'transgender',
              'transgender-alt',
              'venus',
              'venus-double',
              'venus-mars',
            ],
          },
          {
            title: 'Form Control Icons',
            icon: 'check-square',
            list: ['check-square', 'circle', 'dot-circle', 'minus-square', 'plus-square', 'square'],
          },
          {
            title: 'Chart Icons',
            icon: 'chart-area',
            list: ['chart-area', 'chart-bar', 'chart-line', 'chart-pie'],
          },
          {
            title: 'Text Editor Icons',
            icon: 'align-center',
            list: [
              'align-center',
              'align-justify',
              'align-left',
              'align-right',
              'bold',
              'link ',
              'unlink',
              'clipboard',
              'columns',
              'copy',
              'cut',
              'eraser',
              'file',
              'file-alt',
              'copy',
              'save',
              'font',
              'heading',
              'indent',
              'outdent',
              'italic',
              'link',
              'list',
              'list-alt',
              'list-ol',
              'list-ul',
              'outdent',
              'paperclip',
              'paragraph',
              'paste ',
              'repeat',
              'redo',
              'undo',
              'strikethrough',
              'subscript',
              'superscript',
              'table',
              'text-height',
              'text-width',
              'th',
              'th-large',
              'th-list',
              'underline',
              'undo',
              'unlink',
            ],
          },
        ],
        colorsText: colors,
        colorsBackground: colors,
        tableColors: colors,
        tableColorsStep: 10,
        colorsStep: 10,
      },
      defaultButtons: {
        moreText: {
          buttons: [
            'bold',
            'italic',
            'underline',
            'strikeThrough',
            'subscript',
            'superscript',
            'fontFamily',
            'fontSize',
            'textColor',
            'backgroundColor',
            'inlineClass',
            'inlineStyle',
            'clearFormatting',
          ],
        },
        moreParagraph: {
          buttons: [
            'formatOL',
            'formatUL',
            'insertChecklist',
            'alignLeft',
            'alignCenter',
            'alignRight',
            'alignJustify',
            'paragraphFormat',
            'paragraphStyle',
            'lineHeight',
            'outdent',
            'indent',
            'quote',
          ],
        },
        moreRich: {
          buttons: [
            'insertImage',
            'insertLink',
            'insertTable',
            'emoticons',
            'fontAwesome',
            'specialCharacters',
            'insertHR',
          ],
          buttonsVisible: 4,
        },
        moreMisc: {
          buttons: ['undo', 'redo', 'fullscreen', 'selectAll', 'help'],
          align: 'right',
          buttonsVisible: 3,
        },
      },
      smallButtons: {
        moreText: {
          buttons: [
            'bold',
            'italic',
            'underline',
            'strikeThrough',
            'textColor',
            'backgroundColor',
            'clearFormatting',
          ],
        },
        moreParagraph: {
          buttons: ['formatOL', 'formatUL', 'insertChecklist'],
        },
        moreRich: {
          buttons: ['insertLink', 'insertTable'],
        },
        moreMisc: {
          buttons: ['undo', 'redo', 'help'],
          align: 'right',
        },
      },
    };
  },

  methods: {
    getButtonConfig() {
      return this.small ? this.smallButtons : this.defaultButtons;
    },
    initializeEditor() {
      const self = this;
      const tenantConfigApiPlannerPrefix = this.tenantConfigStore.internalUrls.apiPlannerPrefix;
      self.config.events = {
        'image.beforeUpload': async function (images) {
          let s3File;
          if (images.length) {
            const file = images[0];
            file.name ||=
              new Date().getTime() +
              '.' +
              (file.type || 'image/jpeg').replace(/image\//g, '').replace(/;.*/g, '');
            await self.uploadFile(file, file.name, 'froala').then((uploadedFile) => {
              s3File = uploadedFile;
            });
          }
          const froalaImage = this.image;
          axios
            .get(tenantConfigApiPlannerPrefix + '/system/froala-image-upload', {
              params: { path: s3File.file },
            })
            .then((imageUploadResponse) => {
              froalaImage.insert(
                imageUploadResponse.data.url,
                false,
                null,
                froalaImage.get(),
                s3File
              );
            });

          return false;
        },
        'commands.after': function (cmd, param1, param2) {
          if (cmd === 'tableStyle') {
            const table = this.selection.element().closest('table');
            const padding = table.classList.contains('table-padding') ? '4px 16px' : null;
            table.querySelectorAll('td, th').forEach((node) => (node.style['padding'] = padding));
          }

          if (cmd === 'imageAlign') {
            const td = this.image.getEl().closest('td')[0];

            if (td) {
              this.image.getEl()[0].style['float'] = 'none';
              td.style['text-align'] = param1;
            }
          }
        },
      };

      self.config.toolbarButtons = self.getButtonConfig();
      self.$nextTick(function () {
        this.createEditor();
        setTimeout(activateCheckboxes, 300);
        events.$emit('resize');
      });
    },
    createEditor() {
      this.editor = new FroalaEditor(this.$refs.froala, this.config, () => {
        this.updateEditorContent();
        this.editor.events.on('contentChanged', () => {
          this.currentEditorValue = this.editor.html.get();
          this.$emit('update:modelValue', this.currentEditorValue);
        });
      });
    },
    updateEditorContent() {
      if (typeof this.modelValue === 'string' && this.modelValue !== this.currentEditorValue) {
        this.editor?.html.set(this.modelValue);
      }
    },
  },
};
</script>

<style lang="scss">
//custom froala styling
.fr-box {
  .fr-toolbar,
  .fr-wrapper,
  .fr-second-toolbar {
    border: none !important;
  }

  .fr-newline {
    margin: 0;
  }

  .fr-second-toolbar {
    display: none;
  }

  .fr-wrapper {
    transition: background-color 500ms ease;

    &:focus,
    &:hover {
      background-color: darken(white, 4);
    }
  }

  .fr-insert-helper {
    .fr-floating-btn {
      margin-left: 50px;
    }
  }
}

.fr-popup .fr-table-colors,
.fr-popup .fr-color-set {
  max-height: 200px;
  overflow-y: auto;
  width: 380px;
}

.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list {
  padding-top: 0;
  padding-bottom: 0;
}

.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a,
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a.fr-active {
  padding-bottom: 5px;
  padding-top: 5px;
}

.v-input-group__details {
  transition: all 300ms ease;

  &.closed {
    min-height: 0;
  }
}
</style>
