<template>
  <v-treeview
    :activatable="false"
    :selectable="false"
    open-on-click
    expand-icon=""
    collapse-icon=""
    :items="internalItems"
    :item-value="itemKey"
    variant="flat"
    class="panelTree elevation-min py-0"
    :opened="openItems"
    :load-children="loadChildren"
    @update:opened="updateOpened"
  >
    <template v-slot:prepend="{ item }">
      <slot name="prepend" :item="item"> </slot>
    </template>

    <template v-slot:title="{ item }">
      <div v-if="!item.edit" :id="'recursive_item_' + item[itemKey]">
        <slot
          name="itemHeader"
          :item="item"
          :index="item.order"
          :parent="parent"
          :max-level="maxLevel"
          :current-level="currentLevel"
        ></slot>
      </div>
      <div v-else>
        <slot
          name="newField"
          :item="item"
          :index="item.order"
          :parent="parent"
          :max-level="maxLevel"
          :current-level="currentLevel"
        ></slot>
      </div>
    </template>

    <template v-slot:append="{ isActive, item }">
      <slot
        name="append"
        :item="item"
        :index="item.order"
        :parent="parent"
        :max-level="maxLevel"
        :current-level="currentLevel"
      ></slot>
      <v-icon
        v-if="!isDeepestChild(item)"
        :icon="isActive ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
        class="node-toggle"
      ></v-icon>
    </template>
  </v-treeview>
</template>

<script>
export default {
  name: 'RecursiveExpansionPanel',

  props: {
    items: { required: true, type: Array },
    parent: { required: true },
    maxLevel: { required: false, default: 5 },
    currentLevel: { required: false, default: 1 },
    openChildren: {
      required: false,
      type: Array,
      default: function () {
        return [];
      },
    },
    itemKey: { required: false, default: 'uid' },
  },

  data() {
    return {
      openItems: [],
      firstUpdate: true,
      internalItems: [],
    };
  },

  watch: {
    items: {
      immediate: true,
      handler() {
        this.internalItems = this.items.map((item) => {
          item.children = item.originalChildren?.length > 0 ? item.children || [] : null;
          item.originalChildren = item.originalChildren?.length > 0 ? item.originalChildren : null;

          return item;
        });
      },
    },
    openChildren: {
      immediate: true,
      handler(newValue) {
        this.loadItemsByKeys([...newValue]);
        this.$nextTick(() => {
          this.openItems = newValue;
        });
      },
    },
  },

  methods: {
    loadItemsByKeys(keys) {
      const searchQueue = [...this.internalItems];
      while (searchQueue.length > 0 && keys.length > 0) {
        const item = searchQueue.shift();
        const keyIndex = keys.indexOf(item[this.itemKey]);

        if (keyIndex >= 0) {
          keys.splice(keyIndex, 1);
          this.loadChildren(item);

          searchQueue.push(...item.children);
        }
      }
    },
    isDeepestChild(item) {
      return !item.originalChildren || item.originalChildren.length === 0;
    },
    updateOpened(newOpened) {
      this.openItems = newOpened;
      this.$emit('update:openChildren', this.openItems);
    },
    // TODO: v-tree-view issue, all elements are loaded into the DOM now by default, causing performance issues
    loadChildren(item) {
      item.children =
        item.originalChildren?.map((child) => {
          child.children = child.children || (child.originalChildren?.length > 0 ? [] : null);
          return child;
        }) || null;

      return Promise.resolve();
    },
  },
};
</script>

<style lang="scss" scoped>
.v-expansion-panels :deep(.v-expansion-panels-container--active.selectable) {
  background-color: #f0f0f0;

  > .v-expansion-panel-title {
    font-weight: bold;
  }
}

:deep(.v-list-item__prepend) {
  min-width: 0;
  margin: 0;
}

:deep(.v-list-item--active > .v-list-item__overlay) {
  opacity: 0;
}

.v-input--selection-controls {
  margin-top: 0px;
}

.v-input {
  flex: initial;
}

.archived {
  color: #9e9e9e;
}
</style>
