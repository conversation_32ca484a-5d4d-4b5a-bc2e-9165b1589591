<template>
  <v-dialog
    class="appSwitcherDialog"
    content-class="appSwitcher"
    v-model="localOpenValue"
    :fullscreen="true"
    :persistent="true"
    style="top: 60px"
    :scrim="false"
    transition="dialog-bottom-transition"
    :scrollable="true"
    origin="top start"
  >
    <slot></slot>
  </v-dialog>
</template>

<script>
export default {
  name: 'AppSwitcher',
  props: {
    open: {
      required: true,
      type: Boolean,
    },
  },
  computed: {
    localOpenValue: {
      get() {
        return this.open;
      },
      set() {
        this.$emit('update:open', this.localOpenValue);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.appSwitcher) {
  box-shadow: none;
  border-radius: 0;
}

:deep(svg.sol-icon) {
  height: 20px !important;
  width: 20px !important;
}

:deep(.app-switcher-title) {
  font-weight: 400;
  font-size: 32px !important;
  line-height: 48px !important;
  padding: 24px 0;
}

@media only screen and (min-width: 640px) {
  :deep(.v-flex.alternative-grid) {
    flex-basis: 50% !important;
    max-width: 50% !important;
  }
}

@media only screen and (min-width: 768px) {
  :deep(.v-col.alternative-grid) {
    flex-basis: 33.3333% !important;
    max-width: 33.3333% !important;
    margin-bottom: 50px;
  }

  :deep(.app-switcher-title) {
    padding: 84px 0 24px 0;
  }
}

@media only screen and (min-width: 1024px) {
  :deep(.v-container) {
    width: auto;
    max-width: 960px;
  }

  :deep(.v-col.alternative-grid) {
    flex-basis: 25% !important;
    max-width: 25% !important;
    margin-bottom: 70px;
  }

  :deep(.app-switcher-title) {
    padding: 94px 0 36px 0;
  }
}
</style>
