<template>
  <div data-test="shareStatusChip">
    <v-tooltip location="bottom">
      <template v-slot:activator="{ props }">
        <v-chip
          v-bind="props"
          :size="small ? 'small' : 'default'"
          :disabled="disabled"
          class="flex-shrink-0"
          :class="[boxShadow ? 'boxShadow' : '', 'bg-' + backgroundColor, 'text-' + textColor]"
          :style="{ fontWeight: fontWeight }"
        >
          {{ label }}
        </v-chip>
      </template>
      <span>{{ tooltip }}</span>
    </v-tooltip>
  </div>
</template>

<script>
export default {
  name: 'ShareStatusChip',

  props: {
    label: { type: String, default: '' },
    tooltip: { type: String, default: '' },
    textColor: { type: String, default: '' },
    backgroundColor: { type: String, default: '' },
    fontWeight: { type: String, default: 'bold' },
    small: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    boxShadow: { type: Boolean, default: false },
  },
};
</script>

<style lang="scss" scoped>
.v-chip:hover::before {
  opacity: 0;
}

.v-chip--disabled {
  opacity: 1;
}

.boxShadow {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);
}
</style>
