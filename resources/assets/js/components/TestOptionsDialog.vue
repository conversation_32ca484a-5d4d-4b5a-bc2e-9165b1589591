<template>
  <Form v-slot="{ handleSubmit }">
    <v-dialog v-if="isOpen" v-model="isOpen" max-width="600" :scrollable="true" :persistent="true">
      <v-card>
        <v-card-title class="pb-0">
          <h2 class="text-h5">{{ $t(modalTitle) }}</h2>
        </v-card-title>

        <v-divider></v-divider>

        <v-card-text v-if="formData" class="pt-0">
          <v-container>
            <v-row v-if="subject !== null">
              <v-col md="4" cols="6">
                <div>
                  <strong>{{ $t('labels.subject', 1) }}</strong>
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <strong style="font-size: large">{{ subject.name }}</strong>
              </v-col>
            </v-row>

            <v-row>
              <v-col md="4" cols="6">
                <div class="formLabel">
                  {{ $t('labels.name') }}
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <Field
                  v-model="formData.name"
                  name="name"
                  rules="required|max:255"
                  v-slot="{ field, errors }"
                >
                  <v-text-field
                    v-bind="field"
                    :error-messages="errors"
                    :autofocus="true"
                    hide-details="auto"
                  ></v-text-field>
                </Field>
              </v-col>
            </v-row>

            <v-row v-if="showDescription">
              <v-col md="4" cols="6">
                <div class="formLabel">
                  {{ $t('labels.description') }}
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <Field
                  name="description"
                  rules="max:65535"
                  v-slot="{ field, errors }"
                  v-model="formData.description"
                >
                  <v-textarea
                    v-bind="field"
                    ref="textarea"
                    rows="1"
                    no-resize
                    auto-grow
                    hide-details="auto"
                    :error-messages="errors"
                  ></v-textarea>
                </Field>
              </v-col>
            </v-row>

            <v-row>
              <v-col md="4" cols="6">
                <div class="formLabel">
                  {{ $t('labels.date') }}
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <Field v-model="formData.date" name="date" rules="required" v-slot="{ errors }">
                  <DatePicker
                    v-model="formData.date"
                    :error-messages="errors"
                    :min="schoolyear.start"
                    :max="schoolyear.end"
                    :no-title="true"
                    hide-details="auto"
                    :disabled="isImportedTest(formData)"
                  >
                  </DatePicker>
                </Field>
              </v-col>
            </v-row>

            <v-row v-if="quotationSettings.mayEvaluateUsingScores">
              <v-col md="4" cols="6">
                <div class="formLabel">
                  {{ $t('labels.evaluation-table.min-max') }}
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <v-container class="pa-0">
                  <v-row>
                    <v-col cols="6">
                      <NumericVTextField
                        v-model="formData.min"
                        name="minimum"
                        :disabled="isImportedTest(formData)"
                        hide-details="auto"
                        :min="0"
                        :max="formData.max || 999999"
                        @update:model-value="setMinValue"
                      >
                      </NumericVTextField>
                    </v-col>
                    <v-col cols="6">
                      <NumericVTextField
                        v-model="formData.max"
                        name="maximum"
                        :disabled="isImportedTest(formData)"
                        hide-details="auto"
                        :min="minValueOfMaxField"
                        :max="999999"
                        required
                      >
                      </NumericVTextField>
                    </v-col>
                  </v-row>
                </v-container>
              </v-col>
            </v-row>

            <v-row v-if="showOnReport">
              <v-col md="4" cols="6">
                <div class="formLabel mt-4">
                  {{ $t(onReportLabel) }}
                </div>
              </v-col>
              <v-col md="8" cols="6">
                <v-switch
                  v-model="formData.on_report"
                  color="primary"
                  class="d-inline-block"
                ></v-switch>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="isOpen = false">{{ $t('labels.cancel') }}</v-btn>
          <v-btn color="primary" variant="elevated" @click="handleSubmit(closeModal)">
            {{ $t('labels.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </Form>
</template>

<script>
import DatePicker from '../components/DatePicker';
import lodashCloneDeep from 'lodash/cloneDeep';
import dayjs from 'dayjs';
import { Field, Form } from 'vee-validate';
import NumericVTextField from './NumericVTextField';
import { useDateFormat } from '../composables/useDateFormat.js';

export default {
  name: 'TestOptionsDialog',

  components: {
    NumericVTextField,
    DatePicker,
    Form,
    Field,
  },

  props: {
    schoolyear: { required: true, type: Object },
    quotationSettings: { required: true, type: Object },
    showOnReport: { required: false, type: Boolean, default: true },
    modalTitle: { required: false, type: String, default: 'labels.evaluation-table.add-test' },
    onReportLabel: {
      required: false,
      type: String,
      default: 'labels.evaluation-table.counts-for-report',
    },
    subject: { required: false, type: Object, default: () => null },
    isImportedTest: { required: false, type: Function, default: () => false },
    showDescription: { required: false, type: Boolean, default: false },
  },

  setup() {
    const { getFormattedDate } = useDateFormat();

    return { getFormattedDate };
  },

  data() {
    return {
      isOpen: false,
      formData: {},
      minValueOfMaxField: 1,
      defaultFormData: {
        uid: null,
        name: '',
        date: this.getFormattedDate(dayjs(), 'YYYY-MM-DD'),
        min: null,
        max: null,
        on_report: true,
        scores: {},
        imported_at: null,
        description: '',
      },
    };
  },

  methods: {
    openModal(test) {
      this.setFormData(test ? test : this.defaultFormData);
      this.isOpen = true;
    },

    closeModal() {
      this.isOpen = false;
      this.$emit('modal-closed', this.formData);
    },

    setFormData(data) {
      Object.keys(data).forEach((key) => {
        this.formData[key] = lodashCloneDeep(data[key]);
      });
      this.setMinValue();
    },

    getKey(name, element) {
      return name + element.uid;
    },

    setMinValue() {
      this.minValueOfMaxField = this.formData.min > 0 ? this.formData.min : 1;
    },
  },
};
</script>

<style lang="scss" scoped>
.formLabel {
  margin-top: 10px;
  font-weight: bold;
}
</style>
