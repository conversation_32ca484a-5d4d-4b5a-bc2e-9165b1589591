<template>
  <v-container :fluid="true">
    <v-row v-if="hasDisabledPupils">
      <v-col cols="12" class="pa-0 pt-3">
        <v-alert type="error" variant="outlined">
          {{ $t('labels.follow-up-systems.predefined.inactive-pupils-found') }}
        </v-alert>
      </v-col>
    </v-row>

    <v-row style="height: 60px" ref="rowElement" class="mb-0" @vue:mounted="initRowElement">
      <div ref="slotElement" @vue:mounted="initSlotElement"><slot></slot></div>
      <div class="py-0 my-0" :style="{ width: maxTabWidth }">
        <Tabs ref="tabs" :tabs="tabs" v-model="selectedTab" :mobile-breakpoint="20"></Tabs>
      </div>
      <v-spacer></v-spacer>
      <v-menu location="bottom" light>
        <template v-slot:activator="{ props }">
          <v-btn
            v-show="printIsActive"
            class="mt-3 ml-2 mr-0"
            id="exportButton"
            v-bind="props"
            @vue:mounted="initExportButton"
          >
            {{ $t('labels.print') }}
            <v-progress-circular
              class="ml-1"
              v-if="isSubjectPrintPending"
              :width="3"
              size="17"
              indeterminate
            />
            <v-icon v-else end icon="keyboard_arrow_down" />
          </v-btn>
        </template>
        <v-list class="py-0">
          <v-list-item class="px-0">
            <v-list-item-title>
              <v-btn variant="text" @click.stop.prevent="exportPdf" prepend-icon="fa:fas fa-print">
                {{ $t('labels.generate-pdf') }}
              </v-btn>
            </v-list-item-title>
          </v-list-item>
          <v-list-item class="px-0">
            <v-list-item-title>
              <v-btn
                :disabled="!subjectPrintFileUrl"
                block
                variant="text"
                :href="subjectPrintFileUrl"
                target="_blank"
              >
                <v-progress-circular
                  class="mr-1"
                  v-if="isSubjectPrintPending"
                  :width="3"
                  size="17"
                  indeterminate
                />
                <v-icon v-else start size="small" icon="fa:fas fa-download" />
                {{ $t('labels.download-pdf') }}
              </v-btn>
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-tooltip :disabled="!isExpired" location="bottom">
        <template v-slot:activator="{ props }">
          <div v-bind="props">
            <v-btn
              data-test="addBtn"
              ref="addBtn"
              class="mt-3 ml-2 mr-0"
              color="success"
              @click.prevent="openTestOptions(null)"
              :disabled="isExpired"
              prepend-icon="add"
              @vue:mounted="initAddButton"
            >
              {{ $t('labels.evaluation-table.add-test') }}
            </v-btn>
          </div>
        </template>
        <span>{{ $t('tooltips.period-expired-evaluation-test') }}</span>
      </v-tooltip>
    </v-row>

    <v-row class="mt-0">
      <v-col cols="12" class="pa-0">
        <v-card id="maxHeightContainer">
          <v-card-text class="pa-0">
            <v-data-table
              :itemValue="'uid'"
              :items="pupils"
              ref="tableElement"
              id="evaluationTable"
              v-table-vertical-hover="'hover'"
            >
              <template v-slot:headers>
                <tr>
                  <th
                    scope="col"
                    class="sticky-column no-hover sticky-header v-border-bottom v-border-right py-3"
                    style="z-index: 2"
                  ></th>
                  <template v-if="!loading">
                    <th
                      v-for="test in sortedTests"
                      :key="'test' + test.uid"
                      class="align-content-start sticky-header v-border-bottom v-border-right test text-left py-3"
                      :id="'testHeader_' + test.uid"
                    >
                      <v-tooltip :disabled="!test.description && test.on_report" location="bottom">
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <div>
                              <span
                                style="font-size: 13px; color: black; font-weight: bold"
                                class="d-flex align-center"
                              >
                                {{ test.name }}
                                <v-icon
                                  v-if="!test.on_report"
                                  icon="visibility_off"
                                  size="small"
                                  color="grey-darken-1"
                                  class="ml-2"
                                />
                              </span>
                            </div>
                            <div>
                              <span>
                                {{ getFormattedDate(test.date, 'D MMM YYYY') }}
                              </span>
                            </div>
                            <div v-if="!isTestOfCurrentGroup(test.group.group_uid)">
                              ({{ $t('labels.group-selector.group') }} {{ test.group.name }})
                            </div>
                          </div>
                        </template>
                        <span>
                          <div v-if="test.description" style="white-space: pre-wrap">
                            {{ test.description }}
                          </div>
                          <div v-if="test.description && !test.on_report"><br /></div>
                          <div v-if="!test.on_report">
                            {{ $t('labels.evaluation-table.does-not-count-for-report') }}
                          </div>
                        </span>
                      </v-tooltip>
                    </th>
                  </template>
                  <th
                    v-if="!isExpired"
                    scope="col"
                    style="border-bottom: none"
                    class="v-border-right addtest"
                    @click="openTestOptions(null)"
                  ></th>
                </tr>
                <tr>
                  <td class="v-border-bottom v-border-right sticky-column no-hover"></td>
                  <template v-if="!loading">
                    <td
                      v-for="test in sortedTests"
                      :key="'editTest' + test.uid"
                      class="v-border-bottom v-border-right test text-center"
                    >
                      <v-btn
                        variant="text"
                        color="primary"
                        class="ma-0"
                        :disabled="isExpired"
                        icon="edit"
                        @click="openTestOptions(test)"
                      >
                      </v-btn>
                      <v-btn
                        variant="text"
                        color="primary"
                        class="ma-0"
                        :disabled="isExpired"
                        :icon="'mdi:' + mdiFormatListChecks"
                        @click="openTestEvaluation(test)"
                      >
                      </v-btn>

                      <v-btn
                        v-if="isImportedTest(test)"
                        variant="text"
                        color="primary"
                        class="ma-0"
                        icon="remove_circle"
                        @click="importedTestDeleteDialog = true"
                      >
                      </v-btn>

                      <ConfirmButton
                        v-else
                        :title="$t('titles.confirm-delete')"
                        :confirm-text="$t('labels.delete')"
                        :cancel-text="$t('labels.cancel')"
                        :content="$t('labels.confirm-delete')"
                        @md-confirm="removeElement(test)"
                      >
                        <template v-slot="{ toggle }">
                          <v-btn
                            variant="text"
                            color="primary"
                            class="ma-0"
                            icon="remove_circle"
                            :disabled="!isTestOfCurrentGroup(test.group.group_uid)"
                            @click="toggle"
                          >
                          </v-btn>
                        </template>
                      </ConfirmButton>
                    </td>
                  </template>
                  <td
                    v-if="!isExpired"
                    class="v-border-right addtest"
                    @click="openTestOptions(null)"
                  ></td>
                </tr>
                <tr class="bg-grey-lighten-2" v-if="showAggregateRows">
                  <td class="v-border-bottom sticky-column v-border-right no-hover">
                    <strong>{{ $t('labels.evaluation-table.maximum') }}</strong>
                  </td>
                  <td
                    v-for="test in sortedTests"
                    :key="'maximum' + test.uid"
                    class="v-border-bottom v-border-right test text-center"
                  >
                    <strong>{{ test.max }}</strong>
                  </td>
                  <td
                    v-if="!isExpired"
                    class="v-border-right addtest"
                    @click="openTestOptions(null)"
                  ></td>
                </tr>
                <tr
                  class="bg-grey-lighten-2 border-dark"
                  v-if="showAverageAndMedian && showAggregateRows"
                >
                  <td class="v-border-bottom sticky-column v-border-right no-hover">
                    <strong>{{ $t('labels.average.full') }}</strong>
                  </td>
                  <td
                    v-for="test in sortedTests"
                    :key="'average' + test.uid"
                    class="v-border-bottom v-border-right test text-center"
                  >
                    <strong>{{ score(test.average) }} </strong>
                  </td>
                  <td
                    v-if="!isExpired"
                    class="v-border-right addtest"
                    @click="openTestOptions(null)"
                  ></td>
                </tr>
                <tr
                  class="bg-grey-lighten-2 border-dark"
                  v-if="showAverageAndMedian && showAggregateRows"
                >
                  <td class="v-border-bottom sticky-column v-border-right no-hover">
                    <strong>{{ $t('labels.median.full') }}</strong>
                  </td>
                  <td
                    v-for="test in sortedTests"
                    :key="'median' + test.uid"
                    class="v-border-bottom v-border-right test text-center"
                  >
                    <strong>{{ score(test.median) }} </strong>
                  </td>
                  <td
                    v-if="!isExpired"
                    class="v-border-right addtest"
                    @click="openTestOptions(null)"
                  ></td>
                </tr>
              </template>

              <template v-slot:item="props">
                <tr>
                  <td class="no-hover sticky-column v-border-bottom v-border-right">
                    <strong>{{ props.item.fullname }}</strong>
                  </td>
                  <td
                    v-for="test in sortedTests"
                    :key="'score' + test.uid"
                    class="v-border-bottom v-border-right test pa-0"
                  >
                    <div v-if="props.item.disabled" class="d-flex align-center justify-center">
                      <v-icon style="width: 24px" icon="warning" />
                    </div>
                    <div
                      v-else
                      style="background: none; width: 100%"
                      class="d-flex align-center justify-center"
                    >
                      <div
                        v-if="testHasScores(test)"
                        class="px-2"
                        style="width: auto; min-width: 40px"
                      >
                        <span
                          :class="scoreTextColor(test, props.item)"
                          v-if="getScoreForPupilInTest(props.item, test) !== null"
                        >
                          {{ score(getScoreForPupilInTest(props.item, test)) }}
                        </span>
                      </div>
                      <div
                        v-if="testHasQuotations(test)"
                        :style="{
                          width: getQuotationDisplayWidth(test.quotation_system) + 'px',
                        }"
                      >
                        <QuotationDisplay
                          v-if="getQuotationUidForPupilInTest(props.item, test) !== null"
                          :quotation-system="test.quotation_system"
                          :quotation="
                            mappedQuotations[getQuotationUidForPupilInTest(props.item, test)]
                          "
                        >
                        </QuotationDisplay>
                      </div>
                      <div v-if="testHasComments(test)" style="min-width: 48px">
                        <QuotationCommentDialog
                          v-if="getCommentForPupilInTest(props.item, test) !== null"
                          v-model="
                            test.evaluation_test_scores[getPupilIndexInTest(props.item.uid, test)]
                              .comment
                          "
                          :element-uid="props.item.uid"
                          :rules="commentRules"
                          :show-on-hover="true"
                        ></QuotationCommentDialog>
                      </div>
                    </div>
                  </td>
                  <td
                    v-if="props.index === 0 && !isExpired"
                    :rowspan="pupils.length"
                    class="addtest"
                    @click="openTestOptions(null)"
                  >
                    <v-icon size="40" color="white" icon="add_circle" />
                    <span style="display: block; width: 100%" class="text-white">
                      {{ $t('labels.evaluation-table.add-test') }}
                    </span>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <TestOptionsDialog
      ref="testOptionsDialog"
      @modal-closed="saveTest"
      :subject="selectedSubject"
      :schoolyear="schoolyear"
      :quotation-settings="quotationSettings"
      :is-imported-test="isImportedTest"
      :show-description="true"
    ></TestOptionsDialog>

    <TestEvaluationDialog
      ref="testEvaluationDialog"
      :pupils="pupils"
      :save-scores-url="saveScoresUrl"
      :comment-rules="commentRules"
      @update="saveEvaluations"
      :is-imported-test="isImportedTest"
    ></TestEvaluationDialog>

    <v-dialog max-width="300" v-model="importedTestDeleteDialog">
      <v-card>
        <v-card-text class="pt-5">
          {{ $t('labels.bingel-max-delete-warning') }}
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn primary @click="importedTestDeleteDialog = false">{{ $t('labels.close') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios';
import ConfirmButton from './ConfirmButton.vue';
import dayjs from 'dayjs';
import QuotationCommentDialog from '../pages/follow-up-systems/components/QuotationCommentDialog.vue';
import QuotationDisplay from './QuotationDisplay.vue';
import tableVerticalHover from '../directives/table-vertical-hover';
import Tabs from './Tabs.vue';
import TestEvaluationDialog from '../pages/evaluation/testEvaluationDialog.vue';
import TestOptionsDialog from './TestOptionsDialog.vue';
import { mdiFormatListChecks } from '@mdi/js';
import { isNil } from 'lodash';
import { useDateFormat } from '../composables/useDateFormat.js';
import { useNumberFilters } from '../composables/useNumberFilters.js';

export default {
  name: 'EvaluationTable',

  components: {
    QuotationCommentDialog,
    QuotationDisplay,
    TestEvaluationDialog,
    ConfirmButton,
    Tabs,
    TestOptionsDialog,
  },

  directives: {
    tableVerticalHover,
  },

  props: {
    selectedSubject: { required: true, type: Object },
    tests: { required: true, type: Array },
    pupils: { required: true, type: Array },
    baseUrl: { required: true, type: String },
    deleteTestUrl: { required: true, type: String },
    saveScoresUrl: { required: true, type: String },
    tabs: { required: true, type: Array },
    initialTab: { required: true, type: String },
    schoolyear: { required: true, type: Object },
    quotationSettings: { required: true, type: Object },
    commentRules: { required: true, type: Object },
    isExpired: { required: true, type: Boolean },
    showAverageAndMedian: { required: true, type: Boolean },
    subjectOverviewPrintIsActive: { required: true, type: Boolean },
    subjectOverviewPrintUrl: { required: true, type: String },
    subjectPrintStatusUrl: { required: true, type: String },
  },

  setup() {
    const { getFormattedDate } = useDateFormat();
    const { score } = useNumberFilters();

    return { getFormattedDate, score };
  },

  data() {
    return {
      loading: false,
      selectedTab: null,
      testsData: [],
      maxTabWidth: null,
      testScoreIndexByPupilUid: [],
      mdiFormatListChecks: mdiFormatListChecks,
      importedTestDeleteDialog: false,
      interval: null,
      isSubjectPrintPending: false,
      subjectPrintFileUrl: null,
    };
  },

  computed: {
    selectedSubjectAndTestUids() {
      return [this.selectedSubject.uid, this.pupils.map((pupil) => pupil.uid)].join();
    },
    sortedTests() {
      return this.testsData.slice(0).sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));
    },
    printIsActive: function () {
      return (
        this.subjectOverviewPrintIsActive &&
        this.testsData.some(
          (testData) =>
            testData.on_report &&
            (testData.mayEvaluateUsingScores || testData.mayEvaluateUsingQuotations) &&
            testData.evaluation_test_scores.some(
              (testScore) =>
                (testScore.score !== null && testScore.score !== '') ||
                testScore.quotation_uid !== null
            )
        )
      );
    },
    showAggregateRows: function () {
      return this.sortedTests.some((testData) => testData.mayEvaluateUsingScores);
    },
    mappedQuotations() {
      const mapping = {};
      this.testsData.forEach((testData) => {
        const quotationSystem = testData.quotation_system;

        if (!quotationSystem) {
          return;
        }

        quotationSystem.quotations.forEach((quotation) => {
          mapping[quotation.uid] = quotation;
        });
      });

      return mapping;
    },
    hasDisabledPupils() {
      return this.pupils.some((pupil) => pupil.disabled);
    },
  },

  mounted() {
    window.addEventListener('resize', this.setMaxTabWidth);
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.setMaxTabWidth);
    clearInterval(this.interval);
  },

  watch: {
    selectedSubjectAndTestUids: {
      immediate: true,
      handler() {
        this.loading = true;
        this.selectedTab = this.initialTab;
        this.testsData = this.tests;
        this.testScoreIndexByPupilUid = [];
        this.fetchProgress();
        this.startIntervalCheck();
        this.pupils.forEach((pupil) => {
          this.testsData.forEach((testData) => {
            if (this.getPupilIndexInTest(pupil.uid, testData) !== null) {
              return;
            }

            testData.evaluation_test_scores.push({
              comment: null,
              score: null,
              quotation_uid: null,
              pupil_uid: pupil.uid,
            });
          });
        });
        // Reset the mapping again because we pushed new pupils.
        this.testScoreIndexByPupilUid = [];
        this.loading = false;
      },
    },
    printIsActive: {
      handler: function () {
        this.$nextTick(this.setMaxTabWidth);
      },
    },
  },

  methods: {
    getPupilIndexInTest(pupilUid, test) {
      if (this.testScoreIndexByPupilUid.length === 0) {
        const testMapping = {};
        this.testsData.forEach((testData) => {
          const mapping = {};
          testData.evaluation_test_scores.forEach((testScore, key) => {
            mapping[testScore.pupil_uid] = key;
          });
          testMapping[testData.uid] = mapping;
        });
        this.testScoreIndexByPupilUid = testMapping;
      }
      if (
        !this.testScoreIndexByPupilUid.hasOwnProperty(test.uid) ||
        !this.testScoreIndexByPupilUid[test.uid].hasOwnProperty(pupilUid)
      ) {
        return null;
      }

      return this.testScoreIndexByPupilUid[test.uid][pupilUid];
    },
    getAttributeForPupilInTest(attribute, pupil, test) {
      return test.evaluation_test_scores[this.getPupilIndexInTest(pupil.uid, test)][attribute];
    },
    getScoreForPupilInTest(pupil, test) {
      return this.getAttributeForPupilInTest('score', pupil, test);
    },
    getCommentForPupilInTest(pupil, test) {
      return this.getAttributeForPupilInTest('comment', pupil, test);
    },
    getQuotationUidForPupilInTest(pupil, test) {
      return this.getAttributeForPupilInTest('quotation_uid', pupil, test);
    },
    removeElement(testData) {
      return axios
        .delete(this.deleteTestUrl.replace('#evaluationTestUid#', testData.uid))
        .then(() => (this.testsData = this.testsData.filter((item) => item.uid !== testData.uid)));
    },
    openTestOptions(test) {
      this.$refs.testOptionsDialog.openModal(test);
    },
    openTestEvaluation(test) {
      this.$refs.testEvaluationDialog.openModal(test);
    },
    saveTest(testData) {
      const testToUpdate = this.getItemByUid(this.testsData, testData.uid);

      if (testData.uid && testToUpdate) {
        return axios.post(this.baseUrl + '/' + testData.uid, testData).then((response) => {
          Object.assign(testToUpdate, response.data);
        });
      }

      return axios.post(this.baseUrl, testData).then((response) => {
        testData = response.data;
        testData.evaluation_test_scores = [];
        this.testScoreIndexByPupilUid[testData.uid] = [];
        this.pupils.forEach((pupil, index) => {
          testData.evaluation_test_scores.push({
            comment: null,
            score: null,
            quotation_uid: null,
            pupil_uid: pupil.uid,
          });
          this.testScoreIndexByPupilUid[testData.uid][pupil.uid] = index;
        });

        this.testsData.push(testData);

        this.$nextTick(() => {
          const scrollElement =
            this.$refs.tableElement.$el.getElementsByClassName('v-table__wrapper')[0];
          const newElement = document.getElementById('testHeader_' + response.data.uid);
          scrollElement.scrollLeft =
            newElement.offsetLeft - scrollElement.offsetWidth + newElement.offsetWidth;
        });
      });
    },
    saveEvaluations(evaluationData) {
      const testIndex = this.testsData.findIndex((test) => test.uid === evaluationData.uid);
      this.testsData[testIndex] = evaluationData;
    },
    getItemByUid(array, uid) {
      return array.find((item) => item.uid === uid);
    },
    setMaxTabWidth() {
      const slotElement = this.$refs.slotElement;
      const button = this.$refs.addBtn;
      const exportBtn = document.getElementById('exportButton');
      const row = this.$refs.rowElement;

      if (isNil(slotElement) || isNil(button) || isNil(row)) {
        return;
      }
      const slotElementWidth = slotElement.clientWidth;
      const buttonWidth = button.$el.clientWidth;
      const exportBtnWidth = exportBtn?.clientWidth ?? 0;
      const rowWidth = row.$el.clientWidth;
      this.maxTabWidth = rowWidth - buttonWidth - exportBtnWidth - slotElementWidth - 24 + 'px';
      this.$nextTick(() => {
        this.$refs.tabs.$forceUpdate();
      });
    },
    scoreTextColor(test, pupil) {
      const score = this.getScoreForPupilInTest(pupil, test);
      if (!test.min && test.min !== 0) {
        return '';
      }
      return score >= test.min ? 'text-success-darken-1' : 'text-warning-darken-1';
    },
    testHasScores(test) {
      return test.evaluation_test_scores.some((testScore) => testScore.score !== null);
    },
    testHasQuotations(test) {
      return test.evaluation_test_scores.some((testScore) => testScore.quotation_uid !== null);
    },
    testHasComments(test) {
      return test.evaluation_test_scores.some((testScore) => testScore.comment !== null);
    },
    getQuotationDisplayWidth(quotationSystem) {
      const quotationSystemHasMultipleIcons = quotationSystem.quotations.some(
        (quotation) => quotation.icon_count > 1
      );

      if (quotationSystemHasMultipleIcons || quotationSystem.type.value === 'named') {
        return 27 * 5;
      }
      return 27;
    },
    isImportedTest(test) {
      return test.imported_at !== undefined && test.imported_at !== null;
    },
    startIntervalCheck() {
      clearInterval(this.interval);
      this.interval = setInterval(() => {
        this.fetchProgress();
      }, 5000);
    },
    stopIntervalCheck() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },
    fetchProgress() {
      return axios.get(this.subjectPrintStatusUrl).then((response) => {
        this.isSubjectPrintPending =
          response.data.status === 'queued' || response.data.status === 'inProgress';
        this.subjectPrintFileUrl = response.data.file;
        if (!this.isSubjectPrintPending) {
          this.stopIntervalCheck();
        }
      });
    },
    exportPdf() {
      if (this.isSubjectPrintPending) {
        return;
      }
      this.subjectPrintFileUrl = null;
      this.isSubjectPrintPending = true;
      this.startIntervalCheck();
      axios.get(this.subjectOverviewPrintUrl);
    },
    initAddButton() {
      this.setMaxTabWidth();
    },
    initExportButton() {
      this.setMaxTabWidth();
    },
    initRowElement() {
      this.setMaxTabWidth();
    },
    initSlotElement() {
      this.setMaxTabWidth();
    },
    isTestOfCurrentGroup(testUid) {
      const selectedUid = this.$page.props.groupSelector.selected.uid;
      return testUid === selectedUid;
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.theme--light.v-tabs__bar) {
  background: transparent;
}

:deep(.v-progress-linear) {
  display: none;
}

:deep(#evaluationTable .v-table__wrapper) {
  scroll-behavior: smooth;
  max-height: calc(100vh - 60px - 60px - 60px - 16px - 8px);
  overflow: auto;
}

:deep(#evaluationTable > .v-table__wrapper > table) {
  width: auto;
  border-collapse: separate;

  .test {
    width: 210px;
    max-width: 210px;
    min-width: 210px;
    text-align: center;
  }

  .addtest {
    width: 80px;
    max-width: 80px;
    min-width: 80px;
    background-color: rgb(var(--v-theme-success));
    vertical-align: top;
    text-align: center;
    cursor: pointer;
    padding: 0 !important;
    border-radius: 0;

    &.hover {
      background-color: rgb(var(--v-theme-success-lighten-1));
    }
  }
}
</style>
