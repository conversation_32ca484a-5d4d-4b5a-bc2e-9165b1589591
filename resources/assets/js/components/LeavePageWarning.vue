<template><div style="display: none"></div></template>
<script>
export default {
  name: 'LeavePageWarning',

  props: {
    showMessage: {
      required: false,
      type: Boolean,
      default: true,
    },
  },

  watch: {
    showMessage() {
      this.setOnBeforeUnload();
    },
  },

  methods: {
    setOnBeforeUnload() {
      if (!this.showMessage) {
        window.onbeforeunload = null;
      } else {
        window.onbeforeunload = () => {
          return true;
        };
      }
    },
  },
};
</script>
