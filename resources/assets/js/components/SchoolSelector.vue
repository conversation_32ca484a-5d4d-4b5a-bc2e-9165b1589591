<template>
  <v-menu
    v-if="schools && schools.length > 1"
    location="bottom right"
    theme="light"
    :max-height="maximumHeight"
    ref="menu"
    z-index="1000"
    class="ellipsis-menu"
  >
    <template v-slot:activator="{ props }">
      <v-btn variant="text" class="no-focus mx-0 toolbarBtn" v-bind="props">
        {{ selectedSchoolName }}
        <v-icon :end="true" icon="keyboard_arrow_down" size="x-large" />
      </v-btn>
    </template>

    <v-list theme="light">
      <v-list-item :href="school.redirectLink" v-for="school in schools" :key="school.uid">
        <v-list-item-title>{{ getSchoolName(school) }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
  <div v-else class="selected-school d-inline-block">
    {{ selectedSchoolName }}
  </div>
</template>

<script>
import { mapActions } from 'pinia';
import { usePiwikProStore } from '../stores/piwikProStore';

export default {
  name: 'SchoolSelector',
  computed: {
    schools: function () {
      return this.$page.props.schools.all;
    },
    selectedSchoolName: function () {
      const selectedSchool = this.$page.props.schools.selected;

      return selectedSchool ? this.getSchoolName(selectedSchool) : null;
    },
    maximumHeight: function () {
      let headerHeight = 60;
      if (this.$refs.menu) headerHeight = this.$refs.menu.absoluteY;
      return 'calc(100vh - ' + headerHeight + 'px)';
    },
  },
  watch: {
    '$page.props.schools.selected': {
      immediate: true,
      handler: function () {
        this.setSchoolDimensionValue(this.getSchoolName(this.$page.props.schools.selected));
      },
    },
  },
  methods: {
    ...mapActions(usePiwikProStore, ['setSchoolDimensionValue']),
    getSchoolName(school) {
      return school.school_number !== null && school.school_number.length > 0
        ? school.school_number + ' - ' + school.name
        : school.name;
    },
  },
};
</script>

<style scoped lang="scss">
.md-list-item-content * {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.md-menu-content {
  max-width: calc(100vw - 30px);
}

.selected-school {
  padding: 0 16px;
}
</style>
