<template>
  <v-dialog
    max-width="660"
    :scrollable="true"
    v-model="isOpen"
    class="planner"
    :persistent="loading"
  >
    <div class="modal-content planner">
      <Form slim ref="validationObserver" v-slot="{ meta, handleSubmit }" :initial-values="roster">
        <div class="modal-header">
          <button
            type="button"
            class="close"
            aria-label="Close"
            dismiss="modal"
            @click="closeModal"
          >
            <PlannerIcon name="cross"></PlannerIcon>
          </button>
          <h4 class="modal-title">{{ $t(titleText) }} ({{ school.name }})</h4>
        </div>
        <div class="modal-body">
          <TkContent spaceContent :loading="false">
            <TkFormGroup
              :label="$t('MODULE.ROSTERS.ROSTER.NAME.NAME')"
              label-columns="v-col-3"
              input-columns="v-col-9"
              :is-minimal="true"
            >
              <Field
                name="name"
                :rules="rosterNameRule"
                v-slot="{ field, errors }"
                v-model="roster.name"
              >
                <v-text-field
                  id="rosterName"
                  v-bind="field"
                  :error-messages="errors"
                  hide-details="auto"
                  variant="outlined"
                  class="tiny-input"
                  density="compact"
                  :placeholder="$t('MODULE.ROSTERS.ROSTER.NAME.PLACEHOLDER')"
                ></v-text-field>
              </Field>
            </TkFormGroup>
            <TkFormGroup
              :label="$t('MODULE.ROSTERS.ROSTER.PERIOD.UNLIMITED')"
              label-columns="v-col-3"
              input-columns="v-col-9"
              :is-minimal="true"
            >
              <v-checkbox v-model="roster.unlimited" class="tiny-input mt-0" hide-details />
            </TkFormGroup>
            <TkFormGroup
              :label="$t('MODULE.ROSTERS.ROSTER.PERIOD.PERIOD')"
              v-if="!roster.unlimited"
              label-columns="v-col-3"
              input-columns="v-col-9"
              :is-minimal="true"
            >
              <TkPeriod
                id="rosterDuration"
                name="rosterDuration"
                v-model:from="roster.applicable_from"
                v-model:to="roster.applicable_to"
                :min="schoolyear.start"
                :max="schoolyear.end"
                :help-text="$t('MODULE.ROSTERS.ROSTER.PERIOD.INFO')"
              >
              </TkPeriod>
            </TkFormGroup>
          </TkContent>
        </div>
        <div class="modal-footer py-0">
          <div class="bc-toolbar">
            <div class="bc-toolbar-left">
              <div class="bc-toolbar-element">
                <v-btn variant="text" :disabled="loading" @click.stop.prevent="closeModal">
                  {{ $t('ACTION.CANCEL') }}
                </v-btn>
              </div>
            </div>
            <div class="bc-toolbar-right">
              <div class="bc-toolbar-element">
                <template v-if="isEditing">
                  <v-btn
                    color="primary"
                    :disabled="loading || !meta.valid"
                    :loading="loading"
                    @click="handleSubmit(triggerHandler)"
                    >{{ $t(saveButtonText) }}</v-btn
                  >
                </template>
                <template v-else>
                  <v-btn color="success" :disabled="!meta.valid" @click="handleSubmit(createRoster)"
                    ><PlannerIcon name="add" fill="#FFFFFF" class="mr-2"></PlannerIcon
                    >{{ $t('ACTION.ADD') }}</v-btn
                  >
                </template>
              </div>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </v-dialog>
</template>

<script>
import lodash from 'lodash';
import dayjs from 'dayjs';
import Roster from '../../../models/dataModels/Roster';
import PlannerIcon from '../common/PlannerIcon';
import TkContent from '../common/TkContent';
import TkPeriod from './TkPeriod';
import TkFormGroup from '../common/TkFormGroup.vue';
import { Field, Form } from 'vee-validate';

export default {
  name: 'PlrRosterCreateModal',
  components: {
    Form,
    Field,
    TkFormGroup,
    TkPeriod,
    TkContent,
    PlannerIcon,
  },
  props: {
    rosters: {
      required: true,
      type: Array,
    },
    school: {
      required: true,
      type: Object,
    },
    schoolyear: {
      required: true,
      type: Object,
    },
    user: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      isOpen: false,
      defaultRosterName: this.$i18n.t('MODULE.ROSTERS.ROSTER.NAME.DEFAULT'),
      roster: {
        name: null,
        schoolId: this.school.id,
        defaultGroupId: null,
        applicable_from: dayjs(this.schoolyear.start).clone(),
        applicable_to: dayjs(this.schoolyear.end).clone(),
        unlimited: false,
      },
      isEditing: false,
      saveButtonText: '',
      titleText: '',
      loading: false,
      isDuplicate: false,
    };
  },
  computed: {
    existingNames() {
      return lodash(this.rosters)
        .filter((roster) => {
          return (
            roster.schoolId === this.school.id && (roster.id !== this.roster.id || this.isDuplicate)
          );
        })
        .map('name')
        .value();
    },

    rosterNameRule() {
      if (this.existingNames.length) {
        return 'required|max:255|not_one_of:' + this.existingNames.join(',');
      }

      return 'required|max:255';
    },
  },
  methods: {
    showModal() {
      this.isEditing = false;
      this.resetDefaultRoster();
      this.titleText = 'MODULE.ROSTERS.ROSTER.ADD';
      this.isOpen = true;
    },
    showEditModal(roster, saveButtonText, titleText, handler, isDuplicate = false) {
      this.isEditing = true;
      this.setRoster(roster);
      this.saveButtonText = saveButtonText;
      this.titleText = titleText;
      this.handler = handler;
      this.isOpen = true;
      this.isDuplicate = isDuplicate;
    },
    closeModal() {
      this.loading = false;
      this.isOpen = false;
    },
    createRoster() {
      this.loading = true;
      Roster.create(this.roster).then((roster) => {
        this.$emit('create', roster);
        this.closeModal();
      });
    },
    triggerHandler() {
      this.loading = true;
      this.handler(this.roster);
    },
    getRosterName() {
      if (lodash.some(this.rosters, { schoolId: this.roster.schoolId })) {
        if (this.roster.name === this.defaultRosterName) {
          return null;
        }
      } else if (!this.roster.name) {
        return this.defaultRosterName;
      }
      return null;
    },
    setRoster(rosterToEdit) {
      this.roster = {
        id: rosterToEdit.id,
        name: rosterToEdit.name,
        schoolId: rosterToEdit.schoolId,
        defaultGroupId: rosterToEdit.defaultGroupId,
        applicable_from: rosterToEdit.applicable_from?.clone(),
        applicable_to: rosterToEdit.applicable_to?.clone(),
        unlimited: rosterToEdit.unlimited,
      };
    },
    resetDefaultRoster() {
      this.roster = {
        name: this.getRosterName(),
        schoolId: this.school.id,
        defaultGroupId: null,
        applicable_from: dayjs(this.schoolyear.start).clone(),
        applicable_to: dayjs(this.schoolyear.end).clone(),
        unlimited: false,
      };
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.v-input.tiny-input .v-input__control) {
  min-height: 28px;
}

:deep(.v-input.tiny-input.v-field--variant-outlined) {
  &.v-select {
    .v-input__slot {
      background: white;
    }
    .v-input__control {
      height: 0;
    }
  }

  .v-input__control {
    background: white;
    min-height: 32px;

    .v-text-field__slot input {
      padding: 4px 0;
    }
    .v-input__append-inner {
      margin-top: 6px;
    }
  }
}
</style>
