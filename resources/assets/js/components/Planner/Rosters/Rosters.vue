<template>
  <TkPage style="overflow-y: hidden; min-width: 620px" :is-fetching="isFetching">
    <PlrRosterCreateModal
      ref="plrRosterCreateModal"
      :rosters="rosters"
      :school="schoolData"
      :schoolyear="schoolyear"
      :user="user"
      @create="rosterCreated"
    ></PlrRosterCreateModal>
    <div
      class="bc-navbar bc-navbar-fixed-top bc-toolbar-light-gray c-util-gray-light-bottom-border"
    >
      <div class="bc-toolbar bingel-toolbar">
        <div class="bc-toolbar-left">
          <div class="bc-toolbar-element">
            <h3 class="bc-toolbar-title">
              {{ $t('MODULE.ROSTERS.MODULE') }}
            </h3>
          </div>
          <div class="bc-toolbar-element">
            <v-btn
              :class="contextCollapsed ? '' : 'v-btn--active'"
              variant="outlined"
              class="toolbarButton"
              @click="contextCollapsed = !contextCollapsed"
            >
              <PlannerIcon name="sidebar-collapse"></PlannerIcon>
            </v-btn>
          </div>
          <div class="bc-toolbar-element"></div>
        </div>
        <div class="bc-toolbar-right">
          <v-menu location="bottom start" density="compact">
            <template v-slot:activator="{ props }">
              <v-btn v-bind="props" class="toolbarButton"
                >{{ $t('MODULE.CALENDARS.CALENDAR.OPTIONS.OPTIONS') }} <span class="caret"></span
              ></v-btn>
            </template>
            <v-list density="compact">
              <template v-if="timeslots && timeslots.length > 0">
                <v-list-item @click="selectAll">
                  <v-list-item-title
                    >{{ $t('MODULE.ROSTERS.BULK.SELECT_ALL')
                    }}<small class="text-grey ml-2">(ctrl+a, ⌘+a)</small></v-list-item-title
                  >
                </v-list-item>
                <v-divider></v-divider>
              </template>
              <v-list-item @click="print">
                <v-list-item-title
                  >{{ $t('MODULE.CALENDARS.CALENDAR.OPTIONS.PRINT')
                  }}<small class="text-grey ml-2">(ctrl+p, ⌘+p)</small></v-list-item-title
                >
              </v-list-item>
            </v-list>
          </v-menu>
          <v-btn @click="createRoster" id="addRosterButton" color="success"
            >{{ $t('MODULE.ROSTERS.ROSTER.ADD')
            }}<PlannerIcon name="add" fill="#FFFFFF" class="ml-2"></PlannerIcon
          ></v-btn>
        </div>
      </div>
    </div>

    <TkPageMain class="large" :is-fetching="isLoading">
      <TkWorkspace>
        <TkWorkspaceToolbox
          v-if="rosters.length > 0"
          :collapsed="contextCollapsed"
          class="bc-sidebar"
        >
          <div class="bc-sidebar-header">{{ user.fullname }}</div>
          <div class="bc-sidebar-section">
            <div class="form-group form-group-block-labels form-group-block-labels-with-options">
              <div
                v-if="schoolRosters.length === 0"
                class="c-util-font-medium text-muted c-spacer-left c-spacer-right"
              >
                {{ $t('MODULE.ROSTERS.ROSTERS.EMPTY') }}
              </div>
              <div
                v-for="roster in orderedSchoolRosters"
                :key="roster.id"
                class="radio"
                :class="{
                  'active-highlight': isSelectedRoster(roster),
                }"
              >
                <label>
                  <input
                    type="radio"
                    :value="roster"
                    v-model="selectedRoster"
                    @change="selectedRosterChanged"
                  />
                  <span>{{ roster.name }}</span
                  ><br />
                  <small v-show="roster.applicable_from" class="text-muted">
                    {{ $t('MODULE.ROSTERS.ROSTER.PERIOD.FROM') }}
                    {{ formatDate(roster.applicable_from, 'DD/MM/YYYY') }}
                  </small>
                  <small v-show="roster.applicable_to" class="text-muted">
                    {{ $t('MODULE.ROSTERS.ROSTER.PERIOD.TO') }}
                    {{ formatDate(roster.applicable_to, 'DD/MM/YYYY') }}
                  </small>
                  <v-menu density="compact">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        size="x-small"
                        class="py-3"
                        v-bind="props"
                        variant="text"
                        style="position: absolute; top: 50%; transform: translateY(-50%); right: 0"
                      >
                        <PlannerIcon
                          name="more"
                          :key="roster.id + '_whiteIcon'"
                          v-if="isSelectedRoster(roster)"
                          fill="#FFFFFF"
                        ></PlannerIcon>
                        <PlannerIcon
                          name="more"
                          v-else
                          :key="roster.id + '_blackIcon'"
                        ></PlannerIcon>
                      </v-btn>
                    </template>
                    <v-list density="compact">
                      <v-list-item @click="editRoster(roster)">
                        <template v-slot:append>
                          <PlannerIcon name="edit"></PlannerIcon>
                        </template>
                        <v-list-item-title>{{
                          $t('MODULE.ROSTERS.ROSTER.ACTIONS.EDIT')
                        }}</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="duplicateRoster(roster)">
                        <template v-slot:append>
                          <PlannerIcon name="duplicate"></PlannerIcon>
                        </template>
                        <v-list-item-title>{{
                          $t('MODULE.ROSTERS.ROSTER.ACTIONS.DUPLICATE')
                        }}</v-list-item-title>
                      </v-list-item>
                      <v-list-item @click="deleteRoster(roster)">
                        <template v-slot:append>
                          <PlannerIcon name="delete"></PlannerIcon>
                        </template>
                        <v-list-item-title>{{
                          $t('MODULE.ROSTERS.ROSTER.ACTIONS.DELETE')
                        }}</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </label>
              </div>
            </div>
          </div>
        </TkWorkspaceToolbox>
        <TkWorkspaceMain>
          <TkContent>
            <template v-if="!rosters.length">
              <div class="c-util-flex-block c-util-maximize-height">
                <div class="c-util-flex-vertical-centered-element c-util-maximize-width">
                  <div class="container-fluid">
                    <div class="row">
                      <v-col cols="6" lg="4" offset="3" offset-lg="4">
                        <div class="c-spacer-top-l-at-md c-spacer-top-at-sm">
                          <p class="text-center text-muted">
                            {{ $t('MODULE.ROSTERS.ROSTERS.EMPTY') }}
                          </p>
                        </div>
                      </v-col>
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="selectedRoster !== null">
              <TkSinglePrint>
                <TkSinglePrintHeader>
                  <span>
                    {{ user.fullname }} -- {{ selectedRoster.name }} --
                    <strong>{{ schoolData.name }}</strong>
                    <em v-if="selectedRoster.applicable_from">
                      {{ $t('MODULE.ROSTERS.ROSTER.PERIOD.FROM') }}
                      {{ formatDate(selectedRoster.applicable_from, 'DD/MM/YYYY') }}
                    </em>
                    <em v-if="selectedRoster.applicable_to">
                      {{ $t('MODULE.ROSTERS.ROSTER.PERIOD.TO') }}
                      {{ formatDate(selectedRoster.applicable_to, 'DD/MM/YYYY') }})
                    </em>
                  </span>
                </TkSinglePrintHeader>
                <TkSinglePrintMain>
                  <PlrPlanner
                    :num-days="5"
                    :snap="5"
                    :items="timeslots"
                    :subject-index="subjectIndex"
                    :group-index="groupIndex"
                    v-model:selected-items="selectedTimeslots"
                    :user="user"
                    :on-item-created="createTimeslot"
                    :color-mode="colorMode"
                    :selected-school="schoolData"
                    :zoomed="false"
                    :available-groups="availableGroups"
                    :schoolyear="schoolyear"
                    :networks="networks"
                    module="ROSTERS"
                    :minimum-planning-date="new Date(selectedRoster.applicable_from)"
                    :maximum-planning-date="new Date(selectedRoster.applicable_to)"
                  >
                  </PlrPlanner>

                  <PlrPlannerBulkActions
                    v-if="selectedTimeslots.length > 0"
                    module="ROSTERS"
                    :groups="availableGroups"
                    :user="user"
                    :items="timeslots"
                    v-model:selected-items="selectedTimeslots"
                  >
                  </PlrPlannerBulkActions>
                </TkSinglePrintMain>
              </TkSinglePrint>

              <div class="alert-floating-container float-bottom visible" v-if="showNoGroupsMessage">
                <div class="alert alert-default alert-floating" style="position: relative">
                  <button
                    class="close"
                    style="position: absolute; top: 5px; right: 10px"
                    @click="showNoGroupsMessage = false"
                  >
                    <span aria-hidden="true">&times;</span>
                  </button>
                  <div class="row" style="padding-right: 15px">
                    <div class="v-col-sm-12">
                      <i18n-t
                        scope="global"
                        keypath="MODULE.ROSTERS.NO_GROUP_IN_ROSTER.MESSAGE"
                        tag="p"
                      >
                        <template #action>
                          <a
                            href="https://www.vanin.be/help/hoe-kan-ik-de-doelenrapportage-raadplegen-in-bingel-planner/"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {{ $t('MODULE.ROSTERS.NO_GROUP_IN_ROSTER.ACTION') }}
                          </a>
                        </template>
                      </i18n-t>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </TkContent>
        </TkWorkspaceMain>
      </TkWorkspace>
    </TkPageMain>
  </TkPage>
</template>

<script>
import lodash from 'lodash';
import dayjs from 'dayjs';
import User from '../../../models/dataModels/User';
import School from '../../../models/dataModels/School';
import Subjects from '../../../models/dataModels/Subjects';
import Roster from '../../../models/dataModels/Roster';
import Schoolyear from '../../../models/dataModels/Schoolyear';
import TkPage from '../common/TkPage';
import PlannerIcon from '../common/PlannerIcon';
import TkPageMain from '../common/TkPageMain';
import TkWorkspace from '../common/TkWorkspace';
import TkWorkspaceToolbox from '../common/TkWorkspaceToolbox';
import TkWorkspaceMain from '../common/TkWorkspaceMain';
import TkContent from '../common/TkContent';
import TkSinglePrint from '../common/TkSinglePrint';
import TkSinglePrintHeader from '../common/TkSinglePrintHeader';
import TkSinglePrintMain from '../common/TkSinglePrintMain';
import PlrPlanner from '../Calendar/components/PlrPlanner';
import PlrPlannerBulkActions from '../Calendar/components/PlrPlannerBulkActions';
import ColorMode from '../../../models/dataModels/ColorMode';
import PlrRosterCreateModal from './PlrRosterCreateModal';
import { mapActions } from 'pinia';
import { useVueTourStore } from '../../../stores/vueTourStore';

export default {
  name: 'Rosters',

  components: {
    PlrRosterCreateModal,
    PlrPlannerBulkActions,
    PlrPlanner,
    TkSinglePrintMain,
    TkSinglePrintHeader,
    TkSinglePrint,
    TkContent,
    TkWorkspaceMain,
    TkWorkspaceToolbox,
    TkWorkspace,
    TkPageMain,
    PlannerIcon,
    TkPage,
  },

  props: {
    schoolId: { required: true, type: String },
  },

  data() {
    return {
      isFetching: true,
      isLoading: true,
      stateParams: null,
      user: {},
      schoolData: {},
      subjectIndex: {},
      groupIndex: {},
      rosters: [],
      selectedRoster: null,
      context: {},
      contextCollapsed: false,
      showNoGroupsMessage: false,
      timeslots: [],
      selectedTimeslots: [],
      colorMode: ColorMode.COURSE,
      schoolyear: {},
      availableGroups: [],
      networks: [],
    };
  },

  computed: {
    schoolRosters() {
      return lodash(this.rosters).filter({ schoolId: this.schoolData.id }).sortBy('name').value();
    },
    orderedSchoolRosters() {
      return lodash.orderBy(this.schoolRosters, [(roster) => roster.name.toLowerCase()], ['asc']);
    },
  },

  mounted() {
    this.updateStateParams();
    this.getUser();
    this.contextCollapsed = window.innerWidth < 1200;
  },

  methods: {
    ...mapActions(useVueTourStore, ['startTour', 'stopTour']),

    updateStateParams() {
      this.stateParams = new URLSearchParams(window.location.search);
    },
    getUser() {
      User.getCurrent().then((user) => {
        this.user = user;
        this.user.setSchoolId(this.schoolId);
        this.getSchoolyear();
      });
    },
    getSchoolyear() {
      Schoolyear.getCurrent().then((schoolyear) => {
        this.schoolyear = schoolyear;
        this.getSchoolData();
      });
    },
    getSchoolData() {
      School.get(this.schoolId).then((data) => {
        this.schoolData = data;
        this.getSubjectIndex();
      });
    },
    getSubjectIndex() {
      const type = this.user.isTeacherForPreSchoolers() ? null : 'primary';
      return Subjects.getIndexForSchools([this.schoolId], type).then((data) => {
        this.subjectIndex = data;
        this.getGroupIndex();
      });
    },
    getGroupIndex() {
      this.user.getGroupIndex().then((groupIndex) => {
        this.groupIndex = groupIndex;
        this.getNetworks();
      });
    },
    getNetworks() {
      const self = this;
      School.getOwnNetworks().then((data) => {
        self.networks = data;
        self.getRosters();
      });
    },
    getRosters() {
      Roster.loadAllForUser(this.user.id).then((rosters) => {
        this.rosters = rosters;
        this.context = lodash.wrap({});
        this.initiate();
      });
    },
    initiate() {
      if (this.schoolRosters.length === 0) {
        setTimeout(() => {
          this.startTour();
        }, 500);

        this.isLoading = false;
        this.isFetching = false;

        return;
      }

      if (!this.stateParams.has('rosterId')) {
        this.selectFirstRoster();

        return;
      }

      const rosterToSelect = lodash.find(this.rosters, {
        id: this.stateParams.get('rosterId'),
      });
      if (rosterToSelect) {
        this.selectRoster(rosterToSelect);
      } else {
        this.selectFirstRoster();
      }
    },
    selectFirstRoster() {
      this.toRosterDetail(this.schoolRosters[0]);
    },
    selectedRosterChanged() {
      this.isLoading = true;
      this.isFetching = true;
      this.toRosterDetail(this.selectedRoster);
    },
    createRoster() {
      this.stopTour();
      this.$refs.plrRosterCreateModal.showModal();
    },
    rosterCreated(roster) {
      this.rosters.push(roster);
      this.selectRoster(roster);
    },
    duplicateRoster(roster) {
      const duplicateRoster = new Roster(roster);
      duplicateRoster.name = '';
      this.$refs.plrRosterCreateModal.showEditModal(
        duplicateRoster,
        'ACTION.DUPLICATE',
        'MODULE.ROSTERS.ROSTER.DUPLICATE',
        (newRoster) => {
          roster.duplicate(newRoster).then((duplicatedRoster) => {
            this.rosterCreated(duplicatedRoster);
            this.$refs.plrRosterCreateModal.closeModal();
          });
        },
        true
      );
    },
    editRoster(roster) {
      this.$refs.plrRosterCreateModal.showEditModal(
        roster,
        'ACTION.SAVE.CHANGES',
        'MODULE.ROSTERS.ROSTER.EDIT',
        (editedRoster) => {
          Object.assign(roster, editedRoster);
          roster.update().then(() => {
            this.$refs.plrRosterCreateModal.closeModal();
          });
        }
      );
    },
    deleteRoster(roster) {
      this.$confirm({
        title: this.$t('MODULE.ROSTERS.ROSTER.DELETE.CONFIRM.TITLE', { name: roster.name }),
        content: this.$t('MODULE.ROSTERS.ROSTER.DELETE.CONFIRM.MESSAGE', {
          name: roster.name,
        }),
        confirmationText: this.$t('ACTION.DELETE.CONFIRM.ACTION'),
        confirmationButtonProps: { color: 'error' },
        cancellationText: this.$t('ACTION.CANCEL'),
        dialogProps: { color: 'error' },
      }).then((response) => {
        if (response) {
          roster.delete().then(() => {
            const itemIndex = this.rosters.indexOf(roster);
            if (itemIndex >= 0) {
              this.rosters.splice(itemIndex, 1);
              if (this.rosters.length) {
                this.selectFirstRoster();
              }
            }
          });
        }
      });
    },
    toRosterDetail(roster) {
      if (roster) {
        this.stateParams.set('rosterId', roster.id);
        this.selectRoster(lodash.find(this.rosters, { id: roster.id }));
      } else {
        this.stateParams.delete('rosterId');
        this.selectedRoster = null;
      }
      history.replaceState(null, null, '?' + this.stateParams.toString());
    },
    print() {
      window.print();
      return false;
    },
    checkIfTimeSlotsHaveGroups() {
      this.showNoGroupsMessage =
        this.timeslots.length > 0 &&
        !lodash(this.timeslots).some((timeslots) => {
          return timeslots.groupIds.length >= 1;
        });
    },
    selectRoster(roster) {
      return roster.enrich().then((rosterData) => {
        this.selectedRoster = rosterData;
        this.timeslots = roster.timeslots;
        this.selectedTimeslots = [];
        this.checkIfTimeSlotsHaveGroups();
        this.isFetching = false;
        this.isLoading = false;
      });
    },
    selectAll() {
      this.selectedTimeslots.splice(0, this.selectedTimeslots.length);

      lodash(this.timeslots).each((item) => {
        if (item.show) {
          this.selectedTimeslots.push(item);
        }
      });
      window.dispatchEvent(new Event('plrPlannerItemsSelected'));
    },
    formatDate(date, format) {
      return dayjs(date).format(format);
    },
    createTimeslot(begin, end) {
      return this.selectedRoster.createTimeslot(begin, end);
    },
    isSelectedRoster(roster) {
      return this.selectedRoster && this.selectedRoster.id === roster.id;
    },
  },
};
</script>
