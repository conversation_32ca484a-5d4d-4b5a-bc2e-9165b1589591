<template>
  <TkLoader :small="true" :promise="!isLoading">
    <TkPopoverContent>
      <form id="timeslotPropertiesForm" class="bc-section form-horizontal minimal">
        <PlrTimeslotProperties
          :item="item"
          :available-subjects="availableSubjects"
          :with-blocks="isTeacherForPreSchoolers"
          :available-groups="availableGroups"
          @change="saveItem"
          @update:time="updateTime"
          @update:groups="updateGroups"
          @update:subject="updateDetails"
        >
        </PlrTimeslotProperties>
      </form>
      <section>
        <hr />
        <v-btn variant="text" @click="deleteItem"
          ><PlannerIcon class="mr-2" name="delete"></PlannerIcon
          >{{ $t('ACTION.DELETE.DELETE') }}</v-btn
        >
      </section>
    </TkPopoverContent>
  </TkLoader>
</template>

<script>
import lodash from 'lodash';
import Subjects from '../../../models/dataModels/Subjects';
import Schoolyear from '../../../models/dataModels/Schoolyear';
import TkLoader from '../common/TkLoader';
import TkPopoverContent from '../Calendar/components/TkPopoverContent';
import User from '../../../models/dataModels/User';
import PlrTimeslotProperties from './PlrTimeslotProperties';
import PlannerIcon from '../common/PlannerIcon';

export default {
  name: 'PlrTimeslot',

  components: {
    PlannerIcon,
    PlrTimeslotProperties,
    TkPopoverContent,
    TkLoader,
  },

  props: {
    item: { required: true, type: Object },
  },

  data() {
    return {
      isLoading: true,
      user: {},
      isTeacherForPreSchoolers: false,
      availableSubjects: [],
      availableGroups: [],
      schoolyear: {},
    };
  },

  mounted() {
    this.getUser();
  },

  methods: {
    getUser() {
      User.getCurrent().then((user) => {
        this.user = user;
        this.isTeacherForPreSchoolers = user.isTeacherForPreSchoolers();
        if (this.item.schoolId) {
          this.getAvailableSubjects();
        } else {
          this.getSchoolyear();
        }
      });
    },

    getAvailableSubjects() {
      const self = this;
      const subjectType = this.isTeacherForPreSchoolers ? null : 'primary';

      Subjects.get(this.item.schoolId, subjectType).then((subjects) => {
        self.availableSubjects = lodash.filter(subjects.subjects, (subject) => {
          return subject.id === self.item.subjectId || !subject.is_archived;
        });
        self.getAvailableGroups();
      });
    },

    getAvailableGroups() {
      this.user.getGroups(this.item.schoolId).then((availableGroups) => {
        this.availableGroups = availableGroups;
        this.getSchoolyear();
      });
    },

    getSchoolyear() {
      Schoolyear.getCurrent().then((schoolyear) => {
        this.schoolyear = schoolyear;
        this.initiate();
      });
    },

    initiate() {
      this.isLoading = false;
    },

    deleteItem() {
      this.item.delete();
      window.dispatchEvent(
        new CustomEvent('plrPlannerItemsDeleted', {
          detail: [this.item],
        })
      );
    },

    saveItem() {
      this.item.save();
    },

    updateTime() {
      this.item.timeUpdated.fire();
    },

    updateDetails() {
      this.item.detailsUpdated.fire();
    },

    updateGroups() {
      localStorage.setItem('ngStorage-rosterLastGroups', JSON.stringify(this.item.groupIds));
      this.updateDetails();
    },
  },
};
</script>
