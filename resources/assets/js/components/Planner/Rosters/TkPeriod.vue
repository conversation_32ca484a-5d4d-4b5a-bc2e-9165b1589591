<template>
  <div>
    <label style="width: 10%; vertical-align: middle">{{ $t('MODEL.DURATION.FROM') }}</label>
    <DatePicker
      v-model="beginDate"
      :min="min"
      :max="max"
      outlined
      input-classes="tiny-input"
      @close="updateDates"
      hide-details
      style="width: calc(40% - 8px); margin-right: 8px; display: inline-block"
      :return-format="returnFormat"
    ></DatePicker>
    <label style="width: 10%; vertical-align: middle">{{ $t('MODEL.DURATION.TO') }}</label>
    <DatePicker
      v-model="endDate"
      :min="from"
      :max="max"
      outlined
      input-classes="tiny-input mr-2"
      style="width: calc(40% - 8px); margin-right: 8px; display: inline-block"
      @close="updateDates"
      hide-details
      :return-format="returnFormat"
    ></DatePicker>
  </div>
</template>

<script>
import DatePicker from '../../DatePicker';
import dayjs from 'dayjs';
export default {
  name: 'TkPeriod',
  components: { DatePicker },
  props: {
    from: {
      required: true,
      type: [Object, null],
    },
    to: {
      required: true,
      type: [Object, null],
    },
    min: {
      required: true,
      type: [Object, null],
    },
    max: {
      required: true,
      type: [Object, null],
    },
    helpText: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      beginDate: null,
      endDate: null,
    };
  },
  watch: {
    from: {
      immediate: true,
      handler() {
        this.beginDate = this.from ? dayjs(this.from).clone() : null;
        if (this.beginDate && this.endDate && dayjs(this.endDate).isBefore(this.beginDate)) {
          this.endDate = dayjs(this.beginDate).clone();
          this.updateDates();
        }
      },
    },
    to: {
      immediate: true,
      handler() {
        this.endDate = this.to ? dayjs(this.to).clone() : null;
      },
    },
  },
  methods: {
    updateDates() {
      this.$emit('update:from', this.beginDate ? dayjs(this.beginDate) : null);
      this.$emit('update:to', this.endDate ? dayjs(this.endDate) : null);
    },
    returnFormat(date) {
      return date ? dayjs(date) : null;
    },
  },
};
</script>
