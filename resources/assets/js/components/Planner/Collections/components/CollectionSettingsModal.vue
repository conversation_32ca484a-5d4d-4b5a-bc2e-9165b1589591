<template>
  <v-dialog v-model="isOpen" class="planner" max-width="660">
    <Form v-slot="{ handleSubmit, meta }" slim ref="validationObserver">
      <form @submit.prevent="handleSubmit(updateCollection)" class="planner">
        <v-card v-if="Object.keys(collection).length">
          <v-card-title class="d-flex">
            <div>
              {{ $t('MODULE.COLLECTIONS.COLLECTION.SETTINGS') }}
            </div>
            <v-spacer></v-spacer>
            <v-btn icon="close" @click="closeModal" size="small" variant="text"></v-btn>
          </v-card-title>
          <v-card-text>
            <Field
              name="name"
              rules="required"
              v-slot="{ errors, field }"
              slim
              v-model="formData.name"
            >
              <label class="d-block">{{ $t('MODULE.COLLECTIONS.COLLECTION.NAME') }}*</label>
              <v-text-field
                v-bind="field"
                :error-messages="errors"
                :autofocus="true"
                variant="solo"
                density="compact"
              ></v-text-field>
            </Field>
            <label class="d-block">{{ $t('MODULE.COLLECTIONS.COLLECTION.TARGET_AUDIENCE') }}</label>
            <AutocompleteChips
              :options="targetAudiences"
              label=""
              v-model="formData.targetAudiences"
              item-value="id"
              item-text="name"
              hide-details
              variant="solo"
              density="compact"
            ></AutocompleteChips>
          </v-card-text>
          <v-card-actions>
            <v-btn variant="text" @click.stop.prevent="closeModal">
              {{ $t('ACTION.CANCEL') }}
            </v-btn>
            <v-spacer />
            <v-btn color="success" type="submit" variant="elevated" :disabled="!meta.valid">
              {{ $t('ACTION.SAVE.SAVE') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </form>
    </Form>
  </v-dialog>
</template>

<script>
import lodash from 'lodash';
import AutocompleteChips from '../../../AutocompleteChips';
import { mapActions, mapState } from 'pinia';
import { useCollectionDetailStore } from '../../../../stores/collectionDetailStore';
import { useCollectionOverviewStore } from '../../../../stores/collectionOverviewStore';
import { Field, Form } from 'vee-validate';

export default {
  name: 'CollectionSettingsModal',

  components: {
    AutocompleteChips,
    Field,
    Form,
  },

  props: {
    targetAudiences: { required: true, type: Array },
  },

  data() {
    return {
      isOpen: false,
      collection: {},
      formData: {},
    };
  },

  computed: {
    ...mapState(useCollectionOverviewStore, ['settingsModalCollection']),
  },

  watch: {
    settingsModalCollection(collection) {
      if (!lodash.isEmpty(collection)) {
        this.showModal(collection);
      }
    },
  },

  methods: {
    ...mapActions(useCollectionOverviewStore, ['closeSettingsModal']),
    ...mapActions(useCollectionDetailStore, ['setTargetAudiences']),

    showModal(collection) {
      this.collection = collection;
      this.formData = lodash.cloneDeep(collection);
      this.isOpen = true;
      return Promise.resolve();
    },

    closeModal() {
      this.closeSettingsModal();
      this.isOpen = false;
    },

    async updateCollection() {
      this.setTargetAudiences(this.formData.targetAudiences);
      await this.collection.update({
        name: this.formData.name,
        targetAudienceIds: lodash(this.formData.targetAudiences).map('id').value(),
        targetAudiences: this.formData.targetAudiences,
        template: this.formData.template,
      });
      this.closeModal();
      return Promise.resolve();
    },
  },
};
</script>

<style lang="scss" scoped>
.form-group {
  float: left;
  width: 100%;
}
</style>
