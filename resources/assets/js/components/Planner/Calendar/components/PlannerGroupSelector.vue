<template>
  <v-menu
    v-if="showGroupSelector"
    location="bottom"
    :max-height="maxHeight"
    density="compact"
    :disabled="!showDropdown"
  >
    <template v-slot:activator="{ props, isActive }">
      <div
        :id="componentUid + '_activator'"
        v-bind="props"
        class="d-inline-block"
        style="cursor: pointer"
        v-resize="debouncedGetMaxHeight"
      >
        -&nbsp;{{ selectedGroup.name }}&nbsp;
        <template v-if="showDropdown">
          <v-icon size="20" :icon="isActive ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" />
        </template>
      </div>
    </template>
    <v-list density="compact">
      <v-list-item>
        <v-list-item-subtitle>{{
          $t('MODULE.CALENDARS.CALENDAR.GROUPS.RECENT_GROUPS')
        }}</v-list-item-subtitle>
      </v-list-item>
      <v-list-item
        v-for="group in recentGroups"
        :key="'recent_' + group.uid"
        @click="selectGroup(group)"
      >
        <v-list-item-title>{{ group.name }}</v-list-item-title>
      </v-list-item>
      <v-divider></v-divider>
      <v-list-item>
        <v-list-item-subtitle>{{
          $t('MODULE.CALENDARS.CALENDAR.GROUPS.ALL_GROUPS')
        }}</v-list-item-subtitle>
      </v-list-item>
      <v-list-item v-for="group in groups" @click="selectGroup(group)" :key="group.uid">
        <v-list-item-title>{{ group.name }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
import lodash from 'lodash';

export default {
  name: 'PlannerGroupSelector',

  props: {
    plannerMode: { required: true, type: Object },
    groups: { required: true, type: Array },
    selectedGroup: { required: true, type: Object },
  },

  emits: ['update', 'update:groups'],

  data() {
    return {
      maxHeight: 50,
      debouncedGetMaxHeight: lodash.debounce(this.getMaxHeight, 500),
    };
  },

  computed: {
    componentUid() {
      return this.$.uid;
    },
    showGroupSelector() {
      return this.plannerMode.name === 'MY_GROUPS';
    },
    showDropdown() {
      return this.groups.length > 1;
    },
    recentGroups() {
      return this.groups.filter((group) => {
        return group.recently_accessed === true;
      });
    },
  },

  mounted() {
    if (this.showGroupSelector && this.showDropdown) {
      //use timeout because getBoundingClientRect is empty otherwise
      setTimeout(this.getMaxHeight);
    }
  },

  methods: {
    selectGroup(group) {
      const stateParams = new URLSearchParams(window.location.search);
      stateParams.set('groupId', group.uid);
      history.replaceState(null, null, '?' + stateParams.toString());
      const newGroups = [...this.groups];
      newGroups.forEach((g) => {
        if (g.uid === group.uid) {
          g.recently_accessed = true;
        }
      });
      this.$emit('update');
      this.$emit('update:groups', newGroups);
    },
    async getMaxHeight() {
      await this.$nextTick();
      const activator = document.getElementById(this.componentUid + '_activator');
      const elementBox = activator.getBoundingClientRect();
      this.maxHeight = window.innerHeight - elementBox.top - elementBox.height - 20;
    },
  },
};
</script>
