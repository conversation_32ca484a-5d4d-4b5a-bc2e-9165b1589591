<template>
  <div v-if="initialized">
    <TkFormGroup
      v-if="!item.edit"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.FROM')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <p class="form-control-static mb-0">
        {{ formatDate(item.begin, 'DD/MM/YYYY') }}
        {{ item.allDayItem ? '' : formatDate(item.begin, 'HH:mm') }}
      </p>
    </TkFormGroup>

    <TkFormGroup
      v-if="!item.edit"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.TO')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <p class="form-control-static mb-0">
        {{ formatDate(item.allDayItem ? item.end : item.begin, 'DD/MM/YYYY') }}
        {{
          item.allDayItem ? '' : formatDate(addToDate(item.begin, item.duration, 'minute'), 'HH:mm')
        }}
      </p>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.TYPE.ACTIVITY.ALL_DAY')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <v-checkbox
        v-model="item.allDayItem"
        @update:model-value="allDayChanged"
        class="mt-0"
        hide-details="auto"
      ></v-checkbox>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && !item.allDayItem"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.DATE')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <DatePicker
        :modelValue="beginDate"
        :min="this.minimumDate"
        :max="this.maximumDate"
        outlined
        input-classes="tiny-input"
        @close="saveItemDate"
        hide-details="auto"
      ></DatePicker>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && item.allDayItem"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.FROM')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <DatePicker
        v-model="beginDate"
        :min="this.minimumDate"
        :max="this.maximumDate"
        outlined
        hide-details="auto"
        input-classes="tiny-input"
        @close="updateItemTimeData"
      ></DatePicker>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && item.allDayItem"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.TO')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <DatePicker
        v-model="endDate"
        outlined
        :min="beginDate"
        :max="maxEndDate"
        input-classes="tiny-input"
        hide-details="auto"
        @close="updateItemTimeData"
      ></DatePicker>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && !item.allDayItem"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.BEGIN')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <TkTimeInput
        id="calendarItemBegin"
        name="calendarItemBegin"
        v-model:model="item.begin"
        :change="debouncedSaveItem"
        :duration="item.duration"
        :required="true"
      ></TkTimeInput>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && !item.allDayItem"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.DURATION')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <TkDuration
        id="calendarItemDuration"
        name="calendarItemDuration"
        v-model="item.duration"
        @update:model-value="debouncedSaveItem"
      ></TkDuration>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.REPEAT.REPEAT')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <v-select
        v-model="item.repetition_period"
        id="calendar_item_repetition_period"
        :disabled="item.repetition_period !== 'NEVER'"
        @update:model-value="saveItem"
        :items="filteredRepetitionPeriods"
        variant="outlined"
        density="compact"
        class="tiny-input"
        hide-details="auto"
        item-title="name"
        item-value="name"
        :menu-props="{ dense: true }"
      >
        <template v-slot:selection="data">
          <span class="v-select__selection-text">
            {{ translateRepetitionKey(data.item.title) }}
          </span>
        </template>
        <template v-slot:item="data">
          <v-list-item v-bind="data.props" :title="translateRepetitionKey(data.item.title)">
          </v-list-item>
        </template>
      </v-select>
    </TkFormGroup>

    <TkFormGroup
      v-if="item.edit && item.repetition_period !== 'NEVER'"
      :label="$t('MODULE.CALENDARS.CALENDAR.ITEM.REPEAT.REPEAT_END')"
      label-columns="v-col-3"
      input-columns="v-col-9"
      class="d-flex align-center"
    >
      <DatePicker
        v-model="itemrepetition_end"
        id="calendar_item_repetition_end"
        name="calendar_item_repetition_end"
        :min="beginDate"
        :max="this.maximumDate"
        outlined
        input-classes="tiny-input"
        hide-details="auto"
        @close="updateItemTimeData"
      ></DatePicker>
    </TkFormGroup>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import lodashDebouce from 'lodash/debounce';
import TkFormGroup from '../../common/TkFormGroup.vue';
import DatePicker from '../../../DatePicker';
import TkTimeInput from '../../common/TkTimeInput';
import TkDuration from '../../common/TkDuration';
import { useDateFormat } from '../../../../composables/useDateFormat.js';

const availableRepetitionPeriods = [
  { name: 'NEVER' },
  { name: 'WEEKDAY' },
  { name: 'WEEK' },
  { name: 'BIWEEKLY' },
];

export default {
  name: 'PlrCalendarItemTimeProperties',
  components: { TkDuration, TkTimeInput, DatePicker, TkFormGroup },
  props: {
    item: {
      required: true,
      type: Object,
    },
    schoolyear: {
      required: true,
      type: Object,
    },
    minimumPlanningDate: {
      required: true,
      type: Date,
    },
    maximumPlanningDate: {
      required: true,
      type: Date,
    },
    plannerVisibleDates: {
      required: true,
      type: Array,
    },
    plannerVisibleDatesFormat: {
      required: true,
      type: String,
    },
  },
  setup() {
    const { isDateInArray } = useDateFormat();
    return { isDateInArray };
  },
  data() {
    return {
      itemBegin: null,
      itemEnd: null,
      itemrepetition_end: null,
      debouncedSaveItem: lodashDebouce(this.saveItem, 400),
      beginDate: null,
      endDate: null,
      initialized: false,
    };
  },
  mounted() {
    if (!this.item.repetition_end) {
      this.item.resetDefaultRepetitionEnd(this.schoolyear);
    }
    this.updateScopeTimeData();
    window.addEventListener('plrPlannerItemsUpdated', this.updateScopeTimeData);
    this.initialized = true;
  },
  unmounted() {
    window.removeEventListener('plrPlannerItemsUpdated', this.updateScopeTimeData);
  },
  computed: {
    filteredRepetitionPeriods() {
      const repetitionPeriods = cloneDeep(availableRepetitionPeriods);
      const timeDiff = dayjs(this.item.end).diff(dayjs(this.item.begin), 'day');

      if (timeDiff > 0) {
        repetitionPeriods[1].props = { disabled: true };
      }
      if (timeDiff > 7) {
        repetitionPeriods[2].props = { disabled: true };
      }
      if (timeDiff > 14) {
        repetitionPeriods[3].props = { disabled: true };
      }

      return repetitionPeriods;
    },
    maxEndDate() {
      switch (this.item.repetition_period) {
        case 'NEVER':
          return this.maximumDate;
        case 'WEEKDAY':
          return this.item.begin;
        case 'WEEK':
          return dayjs.min([dayjs(this.item.begin).add(6, 'day'), this.maximumDate]);
        case 'BIWEEKLY':
          return dayjs.min([dayjs(this.item.begin).add(13, 'day'), this.maximumDate]);
        default:
          return null;
      }
    },
    minimumDate() {
      return dayjs(this.minimumPlanningDate);
    },
    maximumDate() {
      return dayjs(this.maximumPlanningDate);
    },
  },
  methods: {
    saveItem() {
      this.item.save();
      this.item.timeUpdated.fire();
      this.$emit('plrPlannerItemsUpdated');
    },
    allDayChanged() {
      this.item.updateAllDayItemProperties();
      this.$emit('plrPlannerItemsUpdated');
      this.debouncedSaveItem();
    },
    updateItemTimeData() {
      const timeHasChanged =
        this.item.begin.format('YYYY-MM-DD HH:mm') !==
          this.beginDate + ' ' + this.itemBegin.format('HH:mm') ||
        (this.endDate !== null &&
          this.item.end.format('YYYY-MM-DD HH:mm') !==
            this.endDate + ' ' + this.itemEnd.format('HH:mm')) ||
        this.item.repetition_end.valueOf() !== this.itemrepetition_end.valueOf();

      if (timeHasChanged) {
        this.item.begin = dayjs(this.beginDate + ' ' + this.itemBegin.format('HH:mm'));
        this.item.end = this.endDate
          ? dayjs(this.endDate + ' ' + this.itemEnd.format('HH:mm'))
          : null;
        this.item.repetition_end = this.itemrepetition_end;
        this.saveItem();
      }
    },
    updateScopeTimeData() {
      this.itemBegin = this.item.begin.clone();
      this.beginDate = this.itemBegin.format('YYYY-MM-DD');
      this.itemEnd = this.item.end ? this.item.end.clone() : null;
      this.endDate = this.itemEnd ? this.itemEnd.format('YYYY-MM-D') : null;
      this.itemrepetition_end = this.item.repetition_end
        ? dayjs(this.item.repetition_end).clone()
        : null;
    },
    translateRepetitionKey(key) {
      return this.$i18n.t('MODULE.CALENDARS.CALENDAR.ITEM.REPEAT.' + (key || 'NEVER'));
    },
    formatDate(date, format) {
      return dayjs(date).format(format);
    },
    addToDate(date, timeToAdd, timeString) {
      return dayjs(date).add(timeToAdd, timeString);
    },
    saveItemDate(date) {
      const beginTime = this.item.begin.format('HH:mm');
      this.item.begin = dayjs(date + ' ' + beginTime);

      const isBeginDateInViewRange = this.isDateInArray(
        this.item.begin,
        this.plannerVisibleDates,
        this.plannerVisibleDatesFormat
      );

      this.item.show = true;

      if (!isBeginDateInViewRange) {
        this.item.show = false;

        this.$notify({
          color: 'warning',
          text: this.$i18n.t(
            'labels.modules.planner.collections.calendar.item.moved-out-of-calendar-range'
          ),
        });
      }

      this.saveItem();
    },
  },
};
</script>
