<template>
  <div
    class="plr-planner-day-item"
    :class="{ editable: item.edit }"
    :id="'planner-item-' + item.localId"
  >
    <div class="c-calendar-event" :class="classes">
      <div :class="'c-calendar-owner-color-' + item.ownerColor"></div>
      <div class="c-calendar-event-header">
        <div class="c-calendar-event-title" v-if="item.type === 'ACTIVITY'">
          {{ item.typeData.title }}
        </div>
        <div class="c-calendar-event-title" v-if="item.type !== 'ACTIVITY'">
          <PlrPlannerItemSubtitle :item="item" />
        </div>
        <div class="c-calendar-event-group" v-if="groupLabel && groupLabel.length <= 12">
          {{ groupLabel }}
        </div>
      </div>
      <div class="c-calendar-event-group-long" v-if="groupLabel && groupLabel.length > 12">
        {{ groupLabel }}
      </div>
      <div class="c-calendar-event-subtitle"></div>
    </div>
    <div class="plr-planner-item-duration-handle"></div>
  </div>
</template>

<script>
import lodash from 'lodash';
import ColorMode from '../../../../models/dataModels/ColorMode';
import PlrPlannerItemSubtitle from './PlrPlannerItemSubtitle.vue';

export default {
  name: 'PlrPlannerDayItem',
  components: { PlrPlannerItemSubtitle },
  props: {
    item: {
      required: true,
      type: Object,
    },
    colorMode: {
      required: true,
      type: Object,
    },
    isItemSelected: {
      required: true,
      type: Function,
    },
    groupIndex: {
      required: true,
      type: Object,
    },
    subjectIndex: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      selected: false,
      subject: null,
      classes: [],
      groupLabel: null,
    };
  },
  mounted() {
    this.selected = this.isItemSelected(this.item);
    this.updateDetails();
    this.item.timeUpdated.on(this.timeUpdated);
    this.item.detailsUpdated.on(this.updateDetails);

    window.addEventListener('plrPlannerItemsSelected', this.updateSelected);
    window.addEventListener('plrPlannerItemColorUpdated', this.updateClasses);
  },
  unmounted() {
    window.removeEventListener('plrPlannerItemsSelected', this.updateSelected);
    window.removeEventListener('plrPlannerItemColorUpdated', this.updateClasses);
    this.item.detailsUpdated.off(this.updateDetails);
    this.item.timeUpdated.off(this.timeUpdated);
  },
  methods: {
    timeUpdated() {
      window.dispatchEvent(new Event('plrPlannerItemsUpdated'));
    },
    updateDetails() {
      this.groupLabel = lodash(this.item.groupIds)
        .map((groupId) => {
          const group = this.groupIndex[this.item.schoolId][groupId];
          return group ? group.name : '';
        })
        .sortBy()
        .join(' + ');

      const subjectId = this.item.subjectId || (this.item.typeData && this.item.typeData.subjectId);
      this.subject = subjectId && this.subjectIndex[this.item.schoolId][subjectId];

      this.updateClasses();
    },
    updateSelected() {
      const shouldBeSelected = this.isItemSelected(this.item);
      if (this.selected !== shouldBeSelected) {
        this.selected = shouldBeSelected;
        this.updateClasses();
      }
    },
    updateClasses() {
      const classes = [];
      if (this.selected) {
        classes.push('c-calendar-event-selected');
      }
      if (this.item.hasContent()) {
        classes.push('c-calendar-event-has-content');
      }
      if (this.item.type === 'ACTIVITY') {
        classes.push('c-calendar-event-activity');
        classes.push('c-calendar-event-color-' + this.item.color);
      } else {
        classes.push('c-calendar-event-course');
        if (this.subject && this.colorMode.name === ColorMode.COURSE.name) {
          classes.push('c-calendar-event-color-' + this.subject.color_index);
        } else {
          classes.push('c-calendar-event-color-' + this.item.color);
        }
      }

      this.classes = classes;
    },
  },
};
</script>
