<template>
  <TkContent :scrollable="true" spaceContent>
    <PlrCalendarPrintHeader
      :user="user"
      :calendar-context-tree="calendarContext.tree"
      class="visible-print-block"
    >
    </PlrCalendarPrintHeader>
    <h5>
      {{
        $t('MODULE.CALENDARS.CALENDAR.VIEW.LIST.HEADER', {
          from: formatDate(days[0], 'dddd D MMMM'),
          to: formatDate(days[6], 'dddd D MMMM'),
        })
      }}
    </h5>
    <div v-for="(day, index) in days" style="margin-bottom: 30px" :key="'day_' + index">
      <h3>
        {{
          toSentenceCase(
            formatDate(day, $t('MODULE.CALENDARS.CALENDAR.PLANNER.LIST_DATE_HEADER_FORMAT'))
          )
        }}
      </h3>
      <PlrCalendarItemDetail
        v-for="item in getItemsForDay(day)"
        :key="item.localId"
        :item="item"
        :subject-index="subjectIndex"
        :group-index="groupIndex"
        :networks="networks"
        :options="options"
        :user="user"
        :users="users"
      >
      </PlrCalendarItemDetail>
    </div>
  </TkContent>
</template>

<script>
import lodash from 'lodash';
import dayjs from 'dayjs';
import TkContent from '../../common/TkContent';
import PlrCalendarPrintHeader from './PlrCalendarPrintHeader';
import PlrCalendarItemDetail from './ListView/PlrCalendarItemDetail';

export default {
  name: 'PlannerListView',

  components: { PlrCalendarItemDetail, PlrCalendarPrintHeader, TkContent },

  props: {
    user: { required: true, type: Object },
    users: { required: true, type: Array },
    calendarContext: { required: true, type: Object },
    days: { required: true, type: Array },
    items: { required: true, type: Array },
    options: { required: true, type: Object },
    subjectIndex: { required: true, type: Object },
    groupIndex: { required: true, type: Object },
    networks: { required: true, type: Array },
    groups: { required: true, type: Array },
  },

  mounted() {
    this.calendarContext.setGroups(this.groups);
    this.calendarContext.initItems(this.items, this);
  },

  methods: {
    formatDate(date, format) {
      return dayjs(date).format(format);
    },
    toSentenceCase(value) {
      return this.isString(value) ? value.slice(0, 1).toUpperCase() + value.slice(1) : value;
    },
    isString(value) {
      return typeof value === 'string' || value instanceof String;
    },
    getItemsForDay(date) {
      return lodash(this.items)
        .filter((item) => {
          return item.allDayItem
            ? !(
                dayjs(date).isBefore(dayjs(item.begin), 'day') ||
                dayjs(date).isAfter(dayjs(item.end), 'day')
              )
            : dayjs(date).isSame(dayjs(item.begin), 'day');
        })
        .orderBy(
          [
            (item) => {
              return item.allDayItem ? '00:00' : dayjs(item.begin).format('HH:mm');
            },
            (item) => {
              return item.allDayItem ? dayjs(item.end).format('Y/MM/DD') : item.duration;
            },
          ],
          ['asc', 'desc']
        )
        .filter((item) => item.show)
        .value();
    },
  },
};
</script>
