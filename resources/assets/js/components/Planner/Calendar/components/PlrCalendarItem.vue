<template>
  <TkLoader :small="numDays !== 1" :promise="!loading">
    <TkPopoverContent
      :style="{ 'max-height': numDays !== 1 ? maximumHeight + 'px' : 'auto' }"
      :scrollable="numDays !== 1"
      v-resize="resizeHandlerDebounced"
    >
      <form class="bc-section form-horizontal">
        <fieldset :disabled="!item.edit">
          <legend class="visually-hidden">
            {{ $t('MODULE.CALENDARS.CALENDAR.ITEM.TYPE.CHOICE') }}
          </legend>
          <PlrCalendarItemSpecification
            :item="item"
            :networks="networks"
            :is-teacher-for-pre-schoolers="isTeacherForPreSchoolers"
          >
          </PlrCalendarItemSpecification>
        </fieldset>
      </form>

      <form class="minimal form-horizontal bc-section" v-show="item.type === 'COURSE'">
        <fieldset v-if="item.type === 'COURSE'" :disabled="!item.edit">
          <legend class="visually-hidden">
            {{ $t('MODULE.CALENDARS.CALENDAR.ITEM.TYPE.COURSE.ROWS') }}
          </legend>
          <PlrCalendarItemCourseRecords :item="item" :visibility="visibility">
          </PlrCalendarItemCourseRecords>
        </fieldset>
      </form>

      <form
        id="itemActivitySpecificationForm"
        class="form-horizontal minimal bc-section"
        v-show="item.type === 'ACTIVITY'"
      >
        <fieldset v-if="item.type === 'ACTIVITY'" :disabled="!item.edit">
          <legend class="visually-hidden">
            {{ $t('MODULE.CALENDARS.CALENDAR.ITEM.TYPE.ACTIVITY.TITLE') }}
          </legend>
          <PlrCalendarItemActivitySpecification :item="item" :visibility="visibility">
          </PlrCalendarItemActivitySpecification>
        </fieldset>
      </form>

      <form name="propertiesForm" class="form-horizontal bc-section minimal">
        <fieldset :disabled="!item.edit">
          <legend class="visually-hidden">
            {{ $t('MODULE.CALENDARS.CALENDAR.ITEM.DATE.DATE') }}
          </legend>
          <PlrCalendarItemTimeProperties
            :item="item"
            :schoolyear="schoolyear"
            :minimum-planning-date="minimumPlanningDate"
            :maximum-planning-date="maximumPlanningDate"
            :planner-visible-dates="plannerVisibleDates"
            :planner-visible-dates-format="plannerVisibleDatesFormat"
          >
          </PlrCalendarItemTimeProperties>
        </fieldset>
      </form>

      <form class="form-horizontal bc-section minimal">
        <fieldset :disabled="!item.edit">
          <legend class="visually-hidden">
            {{ $t('MODULE.CALENDARS.CALENDAR.ITEM.TYPE.COURSE.SUBJECT') }}
          </legend>
          <PlrCalendarItemProperties
            :item="item"
            :available-subjects="availableSubjects"
            :with-blocks="isTeacherForPreSchoolers"
            :available-groups="availableGroups"
          >
          </PlrCalendarItemProperties>
        </fieldset>
      </form>

      <div class="text-right">
        <fieldset v-show="item.edit" :disabled="!item.edit" class="bc-section">
          <legend class="visually-hidden">{{ $t('ACTION.DELETE.DELETE') }}</legend>
          <v-btn variant="text" @click="cancelEdit">{{ $t('ACTION.CANCEL') }}</v-btn>
          <v-btn
            variant="tonal"
            color="error"
            prepend-icon="delete"
            class="ml-2"
            @click="deleteItem"
          >
            {{ $t('ACTION.DELETE.DELETE') }}</v-btn
          >
        </fieldset>

        <fieldset
          v-show="!item.edit && !!permissions['CAN_COPY_CALENDAR_FROM_USERS']"
          class="bc-section"
        >
          <legend class="visually-hidden">{{ $t('ACTION.DUPLICATE') }}</legend>
          <v-btn variant="text" @click="copyItem" :loading="copyLoading"
            ><PlannerIcon name="duplicate"></PlannerIcon> {{ $t('ACTION.DUPLICATE') }}</v-btn
          >
        </fieldset>
      </div>
    </TkPopoverContent>
  </TkLoader>
</template>

<script>
import lodash from 'lodash';
import TkLoader from '../../common/TkLoader';
import VisibilitySettings from '../../../../models/dataModels/VisibilitySettings';
import Subjects from '../../../../models/dataModels/Subjects';
import PlrCalendarItemSpecification from './PlrCalendarItemSpecification';
import TkPopoverContent from './TkPopoverContent';
import PlrCalendarItemCourseRecords from './PlrCalendarItemCourseRecords';
import PlrCalendarItemActivitySpecification from './PlrCalendarItemActivitySpecification';
import PlrCalendarItemTimeProperties from './PlrCalendarItemTimeProperties';
import PlrCalendarItemProperties from './PlrCalendarItemProperties';
import PlannerIcon from '../../common/PlannerIcon';

export default {
  name: 'PlrCalendarItem',

  components: {
    PlannerIcon,
    PlrCalendarItemProperties,
    PlrCalendarItemTimeProperties,
    PlrCalendarItemActivitySpecification,
    PlrCalendarItemCourseRecords,
    TkPopoverContent,
    PlrCalendarItemSpecification,
    TkLoader,
  },

  props: {
    numDays: { required: true, type: Number },
    item: { required: true, type: Object },
    edit: { required: true, type: Boolean },
    permissions: { required: true, type: Object },
    user: { required: true, type: Object },
    schoolyear: { required: true, type: Object },
    networks: { required: true, type: Array },
    minimumPlanningDate: { required: true, type: Date },
    maximumPlanningDate: { required: true, type: Date },
    plannerVisibleDates: { required: true, type: Array },
    plannerVisibleDatesFormat: { required: true, type: String },
  },

  data() {
    return {
      loading: true,
      promises: [],
      visibility: null,
      isTeacherForPreSchoolers: false,
      availableSubjects: [],
      availableGroups: [],
      maximumHeight: 500,
      resizeHandlerDebounced: lodash.debounce(this.resizeHandler, 500),
      copyLoading: false,
    };
  },

  mounted() {
    this.loading = true;
    this.isTeacherForPreSchoolers = this.user.isTeacherForPreSchoolers();
    this.getVisibility();
    this.resizeHandlerDebounced();
  },

  methods: {
    getVisibility() {
      const self = this;
      if (this.item.schoolId) {
        VisibilitySettings.getPersonal(this.item.schoolId).then((data) => {
          self.visibility = data;
          self.getAvailableSubjects();
        });
      } else {
        this.getAvailableGroups();
      }
    },
    getAvailableSubjects() {
      const self = this;
      const subjectType = this.isTeacherForPreSchoolers ? null : 'primary';

      Subjects.get(this.item.schoolId, subjectType).then((subjects) => {
        self.availableSubjects = lodash.filter(subjects.subjects, (subject) => {
          return subject.id === self.item.subjectId || !subject.is_archived;
        });
        self.getAvailableGroups();
      });
    },
    getAvailableGroups() {
      const self = this;
      if (this.item.schoolId) {
        this.user.getGroups(this.item.schoolId, this.schoolyear.startYear).then((data) => {
          self.availableGroups = data;
          self.loading = false;
          self.resizeHandlerDebounced();
        });
      } else {
        self.loading = false;
        self.resizeHandlerDebounced();
      }
    },
    resizeHandler() {
      this.$nextTick().then(() => {
        const padding = 2 * 12;
        const spacing = 20;

        if (this.item.allDayItem) {
          const offsetTop = this.$el.getBoundingClientRect().top + window.scrollY;
          this.maximumHeight = window.innerHeight - offsetTop - spacing;
          return;
        }

        if (this.numDays === 1) {
          this.maximumHeight = document.getElementsByClassName('master-detail')[0].offsetHeight;
          return;
        }

        this.maximumHeight =
          document.getElementsByClassName('plr-planner-items-body-container')[0].offsetHeight -
          padding -
          spacing;
      });
    },
    deleteItem() {
      this.item.delete();
      window.dispatchEvent(new CustomEvent('plrPlannerItemsDeleted', { detail: [this.item] }));
    },
    copyItem() {
      this.copyLoading = true;
      this.item.copy(this.item.begin, this.item.end).then((result) => {
        window.dispatchEvent(new CustomEvent('plrPlannerItemAdded', { detail: result }));
        this.copyLoading = false;
        this.cancelEdit();
      });
    },
    cancelEdit() {
      window.dispatchEvent(new CustomEvent('plrPlannerItemDeselected', { detail: this.item }));
    },
  },
};
</script>

<style scoped lang="scss">
:deep(.v-input.tiny-input .v-input__control) {
  min-height: 28px;
}

:deep(.v-input.tiny-input.v-field--variant-outlined) {
  &.v-select {
    .v-input__slot {
      background: white;
    }
    .v-input__control {
      height: 0;
    }
  }

  .v-input__slot {
    background: white;
    min-height: 32px;

    .v-text-field__slot input {
      padding: 4px 0;
    }
    .v-input__append-inner {
      margin-top: 6px;
    }
  }
}
</style>
