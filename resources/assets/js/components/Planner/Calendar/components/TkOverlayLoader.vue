<template>
  <v-fade-transition>
    <div class="tk-overlay-loader" v-if="loading">
      <div class="bc-spinner" v-if="isSmall"></div>
      <div class="spinner-container-big" v-else>
        <div class="bc-spinner bc-spinner-big"></div>
      </div>
    </div>
  </v-fade-transition>
</template>

<script>
export default {
  name: 'TkOverlayLoader',
  props: {
    loading: {
      required: true,
      type: Boolean,
    },
  },
  computed: {
    isSmall() {
      return this.$attrs.small !== undefined;
    },
  },
};
</script>

<style lang="scss" scoped>
.tk-overlay-loader {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10000;
}

.tk-overlay-loader > *:first-child {
  position: relative;
  top: 0;
  left: calc(50% - 15px);
  margin: 10px;
}

.tk-overlay-loader > .spinner-container-big {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
