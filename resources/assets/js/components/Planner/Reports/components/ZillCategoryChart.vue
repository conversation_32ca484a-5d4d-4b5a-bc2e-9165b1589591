<template>
  <InteractionObserver
    @on-change="handleChartVisibility"
    :threshold="[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]"
    v-if="options.data.length"
    v-resize="setChartHeight"
  >
    <div
      ref="graph"
      :style="{ opacity: isVisible ? 1 : 0, minHeight: chartHeight + 'px' }"
      style="transition: opacity 200ms ease"
      class="overflow-visible"
    ></div>
  </InteractionObserver>
</template>

<script>
import InteractionObserver from '../../../InteractionObserver';
import Highcharts from 'highcharts';
import { events } from '../../../../events';
export default {
  name: 'ZillCategoryChart',
  components: { InteractionObserver },
  props: {
    options: {
      type: Object,
      required: true,
    },
    title: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      chart: null,
      chartData: {},
      isVisible: false,
      chartHeight: 0,
    };
  },
  methods: {
    handleChartVisibility(entry) {
      if (entry.intersectionRatio >= 0.6 && !this.isVisible) {
        this.chart = Highcharts.chart(this.$refs.graph, this.chartData);
        events.$emit('resize');
        this.isVisible = true;
        this.setChartHeight();
      } else if (entry.intersectionRatio <= 0.1) {
        this.isVisible = false;
      }
    },
    setChartHeight() {
      if (this.$refs.graph?.clientHeight > 0) {
        this.chartHeight = this.$refs.graph.clientHeight;
      }
    },
  },
  mounted() {
    const self = this;
    this.chartData = {
      colors: ['#FDD89F', '#A8DCF8', '#AA9AFB'],
      credits: {
        enabled: false,
      },
      chart: {
        type: 'pie',
        height: '300px',
        spacing: [0, 0, 0, 0],
        animation: {
          duration: 10000,
        },
      },
      title: {
        text: self.title,
        style: {
          fontSize: '12px',
        },
      },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.percentage:.0f}%</b>',
      },
      legend: {
        borderWidth: 1,
      },
      plotOptions: {
        pie: {
          allowPointSelect: false,
          dataLabels: {
            enabled: false,
          },
          showInLegend: true,
          point: {
            events: {
              legendItemClick: function (e) {
                return false;
              },
            },
          },
        },
      },
      series: [self.options],
      exporting: {
        buttons: {
          contextButton: {
            menuItems: ['downloadPNG', 'downloadJPEG'],
          },
        },
        chartOptions: {
          chart: {
            spacing: [10, 10, 15, 10],
          },
          plotOptions: {
            pie: {
              allowPointSelect: false,
              dataLabels: {
                enabled: true,
                formatter: function () {
                  return (
                    '<span style="color: ' +
                    this.color +
                    '">' +
                    this.key +
                    ' (' +
                    this.percentage.toFixed(0) +
                    '%)</span>'
                  );
                },
              },
              showInLegend: false,
            },
          },
        },
      },
    };
  },
};
</script>

<style lang="scss" scoped>
:deep(.highcharts-container) {
  margin: auto;
}
</style>
