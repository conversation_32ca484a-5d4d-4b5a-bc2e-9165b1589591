<template>
  <TkPage :is-fetching="isFetching" v-resize="resizeEvent">
    <div
      class="bc-navbar bc-navbar-fixed-top bc-toolbar-light-gray c-util-gray-light-bottom-border"
    >
      <div class="bc-toolbar bingel-toolbar">
        <div class="bc-toolbar-left">
          <div class="bc-toolbar-element">
            <h3 class="bc-toolbar-title">
              {{ $t('titles.planner-reports') }}
            </h3>
          </div>
          <button
            @click="contextCollapsed = !contextCollapsed"
            class="btn btn-default ng-not-empty"
            :class="{ active: !contextCollapsed }"
          >
            <PlannerIcon name="sidebar-collapse"></PlannerIcon>
          </button>
        </div>
        <div class="bc-toolbar-right"></div>
      </div>
    </div>
    <TkPageMain class="large" v-if="!isFetching">
      <TkWorkspace>
        <TkWorkspaceToolbox :collapsed="contextCollapsed">
          <section v-show="schoolyears.length > 1">
            <div class="c-spacer-top-s">
              <div class="c-spacer-left c-spacer-right c-spacer-bottom-s">
                <strong>{{ $t('MODULE.REPORTS.DASHBOARD.SCHOOLYEAR') }}</strong>
              </div>
              <div class="c-spacer-left c-spacer-right">
                <v-select
                  @update:model-value="selectSchoolyear"
                  v-model="schoolyear"
                  :items="schoolyears"
                  item-value="startYear"
                  item-title="description"
                  variant="solo"
                  density="compact"
                  hide-details
                >
                </v-select>
              </div>
            </div>
          </section>
          <section v-show="schoolHasZill">
            <form>
              <div class="c-spacer-top">
                <div class="c-spacer-left c-spacer-right c-spacer-bottom-s">
                  <strong>{{ $t('MODULE.REPORTS.REPORT.DATE.DATE') }}</strong>
                </div>
                <div class="container-fluid">
                  <div class="row">
                    <div class="v-col-12 c-spacer-bottom-s">
                      <label>{{ $t('MODULE.REPORTS.REPORT.DATE.FROM') }}</label>
                      <DatePicker
                        v-model="beginDate"
                        :min="minDate"
                        :max="maxDate"
                        density="compact"
                        hide-details
                        solo
                      ></DatePicker>
                    </div>
                    <div class="v-col-12">
                      <label>{{ $t('MODULE.REPORTS.REPORT.DATE.TO') }}</label>
                      <DatePicker
                        v-model="endDate"
                        :min="beginDate"
                        :max="maxDate"
                        density="compact"
                        hide-details
                        solo
                      ></DatePicker>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </section>
          <section>
            <header class="text-muted">
              {{ $t('MODULE.REPORTS.DASHBOARD.MY_SCHOOLS') }}
            </header>
            <div>
              <h4 class="c-spacer-left c-spacer-top-s">
                {{ schoolWithGroups.school.name }}
              </h4>

              <div v-if="schoolWithGroups.hasHiddenGroups" style="padding: 4px 12px">
                <label style="margin: 0" class="text-muted" :disabled="isFetching">
                  <input
                    type="checkbox"
                    v-model="schoolWithGroups.showAll"
                    :disabled="isFetching"
                  />
                  {{ $t('MODULE.REPORTS.DASHBOARD.SHOW_ALL_GROUPS') }}
                </label>
              </div>

              <div
                v-for="(group, index) in schoolWithGroups.groups"
                :key="index"
                style="padding: 4px 12px"
                v-show="group.own || schoolWithGroups.showAll"
              >
                <label style="font-weight: normal; margin: 0" :disabled="isFetching">
                  <input
                    type="checkbox"
                    v-model="group.selected"
                    @change="updateTrigger"
                    :disabled="isFetching"
                  />
                  {{ group.name }}
                </label>
              </div>

              <div class="text-muted c-spacer-left" v-if="schoolWithGroups.groups.length == 0">
                {{ $t('MODULE.REPORTS.DASHBOARD.NO_GROUPS') }}
              </div>
            </div>
          </section>
        </TkWorkspaceToolbox>
        <TkWorkspaceMain>
          <TkContent :scrollable="true">
            <ul class="nav nav-tabs nav-justified c-spacer-top" v-if="showLeerLokaal">
              <li
                :class="{
                  active: selectedGoalType === 'default',
                }"
              >
                <a @click="selectedGoalType = 'default'">
                  {{ defaultCurriculumTitle }}
                </a>
              </li>
              <li :class="{ active: selectedGoalType === 'LEERLOKAAL' }">
                <a @click="selectedGoalType = 'LEERLOKAAL'">
                  {{ $t('enums.curriculum-type.leer_lokaal') }}
                </a>
              </li>
            </ul>
            <div class="c-spacer" style="max-width: 1200px; margin: auto">
              <ZillReportsDashboard
                v-if="schoolHasZill"
                :school-with-groups="schoolWithGroups"
                :begin-date="beginDate"
                :end-date="endDate"
                :schoolyear="schoolyear"
                :loading="isFetching"
                :zill-detail-base-url="zillDetailBaseUrl"
              >
              </ZillReportsDashboard>
              <GoalReportsDashboard
                v-else
                :school-with-groups="schoolWithGroups"
                :schoolyear="schoolyear"
                :report-view-mode="reportViewMode"
                :trigger-update="triggerUpdate"
                :loading="isFetching"
                :detail-base-url="detailBaseUrl"
                :goal-type="selectedGoalType"
                :key="selectedGoalType"
              >
              </GoalReportsDashboard>
            </div>
          </TkContent>
        </TkWorkspaceMain>
      </TkWorkspace>
    </TkPageMain>
  </TkPage>
</template>

<script>
import dayjs from 'dayjs';
import lodash from 'lodash';
import PlannerIcon from '../common/PlannerIcon';
import TkPageMain from '../common/TkPageMain';
import TkWorkspace from '../common/TkWorkspace';
import TkWorkspaceToolbox from '../common/TkWorkspaceToolbox';
import TkPage from '../common/TkPage';
import User from '../../../models/dataModels/User';
import School from '../../../models/dataModels/School';
import Schoolyear from '../../../models/dataModels/Schoolyear';
import TkWorkspaceMain from '../common/TkWorkspaceMain';
import TkContent from '../common/TkContent';
import GoalReportsDashboard from './components/GoalReportsDashboard';
import DatePicker from '../../DatePicker';
import ZillReportsDashboard from './components/ZillReportsDashboard';
export default {
  name: 'ReportsDashboard',
  components: {
    ZillReportsDashboard,
    DatePicker,
    GoalReportsDashboard,
    TkContent,
    TkWorkspaceMain,
    TkPage,
    TkWorkspaceToolbox,
    TkWorkspace,
    TkPageMain,
    PlannerIcon,
  },
  props: {
    school: {
      required: true,
      type: Object,
    },
    detailBaseUrl: {
      required: true,
      type: String,
    },
    zillDetailBaseUrl: {
      required: true,
      type: String,
    },
    hideOldOvsgCurriculum: {
      default: false,
      type: Boolean,
    },
    leerlokaalIsActive: {
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      contextCollapsed: false,
      isFetching: true,
      user: null,
      schoolData: [],
      schoolyears: [],
      schoolyear: null,
      schoolWithGroups: {},
      showOldKathOndVlaCurriculum: false,
      selectedGoalType: 'default',
      beginDate: null,
      minDate: null,
      maxDate: null,
      endDate: null,
      triggerUpdate: dayjs().valueOf(),
      reportViewMode: null,
    };
  },
  mounted() {
    User.getCurrent().then((data) => {
      this.user = data;
      this.getSchoolData();
    });
  },
  watch: {
    beginDate: {
      handler() {
        if (dayjs(this.endDate).isBefore(this.beginDate)) {
          this.endDate = dayjs(this.beginDate).clone();
        }
      },
      deep: true,
    },
  },
  computed: {
    showLeerLokaal() {
      const hasLeerlokaal =
        this.schoolWithGroups.school.network.curriculumTypes.includes('LEERLOKAAL');
      return this.leerlokaalIsActive && !this.hideOldOvsgCurriculum && hasLeerlokaal;
    },
    defaultCurriculumTitle() {
      return this.showLeerLokaal
        ? this.$i18n.t('enums.follow-up-system-type.ovsg')
        : this.$i18n.t('MODULE.REPORTS.DASHBOARD.GOALS');
    },
    schoolHasZill() {
      return this.schoolWithGroups.school.network.curriculumTypes.includes('ZILL');
    },
  },
  methods: {
    getSchoolData() {
      const self = this;
      School.get(this.school.uid).then((data) => {
        self.schoolData = data;
        self.getSchoolyears();
      });
    },
    getSchoolyears() {
      const self = this;
      Schoolyear.getSchoolyears(this.school.uid).then((data) => {
        self.schoolyears = lodash(data).orderBy('startYear', 'desc').value();
        self.getCurrentSchoolyear();
      });
    },
    getCurrentSchoolyear() {
      const stateParams = new URLSearchParams(window.location.search);
      this.schoolyear =
        lodash(this.schoolyears)
          .filter({
            startYear: parseInt(stateParams.get('schoolyear')),
          })
          .value()[0] || this.schoolyears[0];
      this.loadSchoolWithGroupsData();
    },
    loadSchoolWithGroupsData() {
      this.loadSchoolWithGroups().then((data) => {
        this.schoolWithGroups = data;
        this.initPage();
      });
    },
    loadSchoolWithGroups() {
      const self = this;
      const gotAllGroups = School.getGroups(this.school.uid, this.schoolyear.startYear);

      return gotAllGroups.then((data) => {
        const groups = lodash.unionBy(self.transformGroups(data), 'id');

        return self.schoolData.getNetwork().then((network) => {
          self.schoolData.network = network;
          return {
            school: self.schoolData,
            hasHiddenGroups: !!lodash.find(groups, ['own', false]),
            groups: groups,
          };
        });
      });
    },
    transformGroups(groups) {
      return lodash.map(groups, (group) => {
        return {
          id: group.id,
          name: group.name,
          own: group.is_own_group,
          schoolyear: group.schoolyear,
          selected: group.is_own_group && group.is_classgroup,
        };
      });
    },
    initPage() {
      this.setInitialGoalType();
      this.beginDate = dayjs([this.schoolyear.startYear, 8, 1, 0, 0, 0]);
      this.minDate = dayjs(this.schoolyear.start);
      this.maxDate = dayjs(this.schoolyear.end);
      // The default end date of the report is always today
      this.endDate = dayjs.min(this.maxDate, dayjs());

      this.resizeEvent();
      this.isFetching = false;
    },
    setInitialGoalType() {
      if (!this.leerlokaalIsActive) {
        this.selectedGoalType = 'default';
        return;
      }
      this.selectedGoalType = this.hideOldOvsgCurriculum ? 'LEERLOKAAL' : 'default';
    },
    selectSchoolyear(data) {
      const stateParams = new URLSearchParams(window.location.search);
      stateParams.set('schoolyear', data);
      window.location.search = stateParams.toString();
    },
    updateTrigger() {
      this.triggerUpdate = dayjs().valueOf();
    },
    resizeEvent() {
      const isSmallScreen = window.innerWidth < 767;
      this.contextCollapsed = isSmallScreen;
      this.reportViewMode = isSmallScreen ? 'list' : 'grid';
    },
  },
};
</script>
