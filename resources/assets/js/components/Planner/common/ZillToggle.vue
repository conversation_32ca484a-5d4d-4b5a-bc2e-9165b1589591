<template>
  <v-tooltip v-if="showZill" location="bottom">
    <template v-slot:activator="{ props }">
      <div class="zill-toggle-element" :class="{ 'zill-toggle-inline': inline }">
        <span class="zill-toggle zill-purple" v-if="isZill === true" v-bind="props">Zill!</span>
        <span class="zill-toggle text-muted" v-if="isZill === false" v-bind="props">Zill?</span>
      </div>
    </template>
    <span>{{ tooltipText }}</span>
  </v-tooltip>
</template>

<script>
import lodashFilter from 'lodash/filter';
import School from '../../../models/dataModels/School';

export default {
  name: 'ZillToggle',
  props: {
    isZill: {
      required: false,
      type: Boolean,
    },
    inline: {
      required: false,
      type: Boolean,
      default: false,
    },
  },
  computed: {
    showZill() {
      return this.isZill !== null && lodashFilter(this.networks, { id: this.vvkbaoUid }).length > 0;
    },
    tooltipText() {
      return this.isZill === true
        ? this.$i18n.t('MODULE.COLLECTIONS.COLLECTION.ZILL_PERSONAL')
        : this.$i18n.t('MODULE.COLLECTIONS.COLLECTION.ZILL_GENERAL');
    },
  },
  data() {
    return {
      vvkbaoUid: '160f5535-1b2d-4404-8bf7-a438610556ad',
      networks: [],
    };
  },
  mounted() {
    const self = this;
    School.getOwnNetworks().then((networks) => {
      self.networks = networks;
    });
  },
};
</script>

<style scoped lang="scss">
/*  zill specific */
.zill-toggle-element {
  padding-top: 3.5px;
  padding-bottom: 5.5px;
  margin-bottom: 0;
  display: inline-block;

  &.zill-toggle-inline {
    padding: 0;
    font-size: 14pt;
    margin-top: -3px;

    .zill-toggle {
      font-size: 14pt;
    }
  }
}
p zill-toggle-element {
  padding: 0;
  .zill-toggle {
    margin-top: -3px;
  }
}
.zill-toggle {
  font-family: 'ShadowsIntoLight', 'Roboto', sans-serif;
  font-weight: 400;
  font-size: 16px;
  display: inline-block;
  color: #ccc;
  width: 25px;
  vertical-align: middle;
  margin-left: 5px;
  margin-right: 5px;
  word-break: keep-all;
  overflow-wrap: normal;

  &.zill-purple {
    color: #ae1f80;
  }
}
</style>
