<template>
  <span :style="{ height: height + 'px', width: width + 'px' }" ref="icon"></span>
</template>

<script>
export default {
  name: 'smartSvg',
  props: {
    src: {
      required: true,
      type: String,
    },
    width: {
      required: false,
      type: [Number, String],
      default: 16,
    },
    height: {
      required: false,
      type: [Number, String],
      default: 16,
    },
    iconWidth: {
      required: false,
      type: [Number, String],
      default: 16,
    },
    iconHeight: {
      required: false,
      type: [Number, String],
      default: 16,
    },
    fill: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      rendered: false,
      svg: null,
    };
  },
  mounted() {
    this.renderIcon();
  },
  updated() {
    if (!this.rendered && this.svg !== null) {
      this.createIcon();
    }
  },
  watch: {
    src() {
      this.renderIcon();
    },
  },
  methods: {
    async renderIcon() {
      const svg = await this.getInlineIcon(this.src);

      setTimeout(() => {
        svg.setAttribute('height', this.iconHeight + 'px');
        svg.setAttribute('width', this.iconWidth + 'px');
        svg.querySelectorAll('path, polygon').forEach((path) => {
          path.setAttribute('fill', this.fill);
          path.style.fill = this.fill;
        });

        this.svg = svg;
        this.createIcon();
      });
    },
    createIcon() {
      this.$nextTick().then(() => {
        if (this.$refs.icon) {
          this.$refs.icon.innerHTML = this.svg.outerHTML;
          this.rendered = true;
        }
      });
    },
    async getInlineIcon(imageLink) {
      const svgString = imageLink.startsWith('data:image/svg+xml')
        ? decodeURIComponent(imageLink.substr(19))
        : await fetch(imageLink).then((r) => r.text());

      const span = document.createElement('span');
      span.innerHTML = svgString;
      return span.getElementsByTagName('svg')[0];
    },
  },
};
</script>
