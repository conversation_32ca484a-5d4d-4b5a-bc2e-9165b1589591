<script>
export default {
  name: 'notification',
  render() {
    return null;
  },
  props: {
    text: {
      required: true,
    },
    color: {
      default: '',
    },
    mode: {
      default: '',
    },
    timeout: {},
    position: {},
    closeText: {},
    callback: {},
  },
  computed: {
    options: function () {
      return {
        color: this.color,
        mode: this.mode,
        timeout: this.timeout,
        text: this.text,
        closeText: this.closeText,
        position: this.position,
        callback: this.callback,
      };
    },
  },
  mounted: function () {
    this.$notify(this.options);
  },
};
</script>
