<template>
  <Form
    v-slot="{ handleSubmit, meta, errors }"
    ref="validationObserver"
    @vue:mounted="initValidationProvider"
  >
    <form @submit.prevent="handleSubmit(validateAndSubmitForm)">
      <v-card class="ma-0 mb-2">
        <v-progress-linear
          color="primary"
          :indeterminate="true"
          :active="sending"
          class="ma-0"
        ></v-progress-linear>
        <v-card-title v-if="title"
          ><h1 class="text-h5">{{ title }}</h1></v-card-title
        >
        <v-card-text>
          <v-row>
            <v-col cols="12" v-for="(element, index) in schema" :key="index">
              <component
                :key="index"
                :is="
                  componentsByName['./FormBuilder/' + getComponentName(element.type) + '.vue']
                    .default
                "
                :element="element"
                :parent-errors="errors"
                :parent-model="modelData"
                :validations="getValidationRules(element.model)"
                @change="parseFrontendErrors"
                @file-change="fileChange"
                color="primary"
                variant="underlined"
              >
              </component>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions v-if="renderButtons">
          <v-spacer></v-spacer>
          <v-btn color="primary" variant="text" @click.prevent="cancelEvent" v-if="cancelUrl">{{
            cancelText
          }}</v-btn>
          <ConfirmButton
            v-if="confirmToSaveMessage"
            :label="submitText"
            :title="submitText"
            :confirm-text="submitText"
            :cancel-text="cancelText"
            content="confirmToSaveMessage"
            :disabled="!meta.valid"
            @md-confirm="handleSubmit(validateAndSubmitForm)"
            :text="false"
          />
          <v-btn v-else color="primary" type="submit" :disabled="!meta.valid">
            {{ submitText }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </form>
  </Form>
</template>

<script>
import ConfirmButton from './ConfirmButton';
import FormBuilder from '../mixins/FormBuilder';
import { Form } from 'vee-validate';
import { mapActions } from 'pinia';
import { useFormStore } from '../stores/FormStore.js';

const componentsByName = import.meta.glob('./FormBuilder/*.vue', { eager: true });

export default {
  name: 'FormBuilder',
  components: { Form, ConfirmButton },
  mixins: [FormBuilder],
  props: {
    // Object/Array defaults should be returned from a factory function
    schema: {
      required: true,
      type: Array,
    },
    validate: {
      type: Object,
      default: function () {
        return {};
      },
    },
    title: {
      type: String,
    },
    submitText: {
      type: String,
      required: true,
    },
    cancelText: {
      type: String,
    },
    confirmToSaveMessage: {
      type: String,
    },
    renderButtons: {
      required: false,
      default: false,
      type: Boolean,
    },
  },
  data() {
    return {
      componentsByName: componentsByName,
      disabled: false,
    };
  },
  methods: {
    ...mapActions(useFormStore, ['setValidator']),
    getComponentName: function (type) {
      switch (type) {
        case 'textarea':
          return 'TextareaElement';
        case 'checkbox':
          return 'CheckboxElement';
        case 'radio':
          return 'RadioElement';
        case 'switch':
          return 'SwitchElement';
        case 'chips':
          return 'ChipsElement';
        case 'select':
          return 'SelectElement';
        case 'importTypeSelect':
          return 'ImportTypeSelectElement';
        case 'autocompleteChips':
          return 'AutocompleteChipsElement';
        case 'file':
          return 'FileElement';
        case 's3file':
          return 'S3FileElement';
        case 'text':
        case 'number':
        case 'email':
        case 'password':
        default:
          return 'InputElement';
      }
    },
    getValidationRules: function (model) {
      if (this.validate[model]) {
        return this.validate[model];
      }
      return '';
    },
    initValidationProvider() {
      this.setValidator(this.$refs.validationObserver);
    },
  },
  computed: {
    buttonDisabled() {
      return !this.$validator.getMeta().valid || this.disabled;
    },
  },
};
</script>
