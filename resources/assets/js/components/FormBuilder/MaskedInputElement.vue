<template>
  <Field
    :slim="true"
    :name="element.model"
    :vid="name"
    :rules="validations"
    v-model="parentModel[element.model]"
    v-slot="{ field, errors }"
    ref="validationProvider"
  >
    <v-text-field
      :type="element.type"
      :label="element.label"
      v-model="parentModel[element.model]"
      @update:model-value="sendUpdate"
      :disabled="element.disabled || disabled"
      :placeholder="element.placeholder || $attrs.placeholder"
      :error-messages="errors"
      v-bind="{ ...field, ...$attrs }"
      autocomplete="off"
      v-maska="options"
    >
    </v-text-field>
  </Field>
</template>

<script>
import { Field } from 'vee-validate';
import { vMaska } from 'maska/vue';
import FormBuilderElement from './FormBuilderElement.js';

export default {
  name: 'MaskedInputElement',

  components: { Field },

  directives: {
    maska: vMaska,
  },

  mixins: [FormBuilderElement],

  props: {
    mask: { required: true, type: String },
    disabled: { required: false, type: Boolean, default: false },
  },

  data() {
    return {
      options: {
        mask: this.mask,
      },
    };
  },
};
</script>
