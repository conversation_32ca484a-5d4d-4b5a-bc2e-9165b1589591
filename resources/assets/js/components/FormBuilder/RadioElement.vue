<template>
  <Field
    :slim="true"
    :name="element.model"
    :rules="validations"
    v-slot="{ field, errors }"
    ref="validationProvider"
    @vue:mounted="initValidationProvider"
  >
    <v-radio-group v-model="parentModel[element.model]" :mandatory="false" :error-messages="errors">
      <v-radio
        :label="element.label"
        :value="element.value"
        @update:model-value="sendUpdate"
        :disabled="element.disabled"
        :placeholder="element.placeholder"
        v-bind="{ ...field, ...$attrs }"
      >
      </v-radio>
    </v-radio-group>
  </Field>
</template>

<script>
import FormBuilderElement from './FormBuilderElement';
import { Field } from 'vee-validate';

export default {
  name: 'RadioElement',
  components: { Field },
  mixins: [FormBuilderElement],
};
</script>
