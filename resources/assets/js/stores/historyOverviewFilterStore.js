import { defineStore } from 'pinia';

export const useHistoryOverviewFilterStore = defineStore('historyOverviewFilterStore', {
  state() {
    return {
      selectedSchoolyear: null,
      currentSchool: null,
      groups: {},
    };
  },
  getters: {
    selectedGroups() {
      return this.groups[this.currentSchool]?.[this.selectedSchoolyear]?.selectedGroups || null;
    },
  },
  actions: {
    setSelectedSchoolyear(selectedSchoolyear) {
      this.selectedSchoolyear = selectedSchoolyear;
    },
    setCurrentSchool(currentSchool) {
      this.currentSchool = currentSchool;
    },
    setSelectedGroups(groups) {
      if (!this.groups[this.currentSchool]) {
        this.groups[this.currentSchool] = {};
      }
      if (!this.groups[this.currentSchool][this.selectedSchoolyear]) {
        this.groups[this.currentSchool][this.selectedSchoolyear] = {};
      }
      this.groups[this.currentSchool][this.selectedSchoolyear].selectedGroups = groups;
    },
  },
  persist: true,
});
