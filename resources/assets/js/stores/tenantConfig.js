import { defineStore } from 'pinia';

export const useTenantConfig = defineStore('tenantConfig', {
  state() {
    return {
      awsConfig: '',
      locale: null,
      permissions: {},
      securityExtensionWhitelist: {},
      internalUrls: {},
      features: {},
      pageTitle: '',
    };
  },
  actions: {
    setAwsConfig(newConfig) {
      this.awsConfig = newConfig;
    },
    setLocale(locale) {
      this.locale = locale;
    },
    setPermissions(permissions) {
      this.permissions = permissions;
    },
    setFeatures(features) {
      this.features = features;
    },
    setSecurityExtensionWhitelist(securityExtensionWhitelist) {
      this.securityExtensionWhitelist = securityExtensionWhitelist;
    },
    setInternalUrls(internalUrls) {
      this.internalUrls = internalUrls;
    },
    setPageTitle(pageTitle) {
      this.pageTitle = pageTitle;
    },
  },
  persist: true,
});
