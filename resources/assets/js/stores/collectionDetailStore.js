import { defineStore } from 'pinia';
import { i18n } from '../plugins/vue-i18n';
import lodash from 'lodash';
import CollectionLibrary from '../models/dataModels/CollectionLibrary';

export const useCollectionDetailStore = defineStore('collectionDetail', {
  state() {
    return {
      loading: true,
      userObject: {},
      collectionData: {},
      chapters: [],
      targetAudiencesData: [],
      edit: null,
      name: '',
      validationObserver: null,
      scrollContainer: null,
      chaptersOpen: [],
      library: null,
      recordSelectModal: null,
      coAuthorsCurrentSchool: [],
      coAuthorCountInOtherSchools: 0,
      readersCurrentSchool: [],
      readerCountInOtherSchools: 0,
      canTakeOwnership: false,
      hasBeenActivated: false,
      totalColleagues: 0,
      lastShownRecord: {
        record: null,
        chapter: null,
      },
    };
  },

  getters: {
    coAuthorsCurrentSchoolAvatars() {
      const avatars = [...this.coAuthorsCurrentSchool];

      if (this.coAuthorCountInOtherSchools) {
        avatars.push({
          uid: 'coAuthorCountInOtherSchools',
          fullname: i18n.t('labels.co-author-different-school', this.coAuthorCountInOtherSchools),
          initials: this.coAuthorCountInOtherSchools,
          customColor: 'grey',
        });
      }

      return avatars;
    },

    readersCurrentSchoolAvatars() {
      const avatars = [...this.readersCurrentSchool];

      if (this.readerCountInOtherSchools) {
        avatars.push({
          uid: 'readerCountInOtherSchools',
          fullname: i18n.t('labels.viewer-different-school', this.readerCountInOtherSchools),
          initials: this.readerCountInOtherSchools,
          customColor: 'grey',
        });
      }

      return avatars;
    },

    isReaderCollection() {
      return this.collectionData.shareStatus === 'reader';
    },

    lastShownRecordExists() {
      if (!this.lastShownRecord.record) {
        return false;
      }

      const chapterInStore = this.findChapter({ id: this.lastShownRecord.chapter });

      return chapterInStore.chapter
        ? chapterInStore.chapter.records.some((record) => record.id !== null)
        : false;
    },
  },

  actions: {
    setValidationObserver(validationObserver) {
      this.validationObserver = validationObserver;
    },
    setScrollContainer(scrollContainer) {
      this.scrollContainer = scrollContainer;
    },
    setLoading(loading) {
      this.loading = loading;
    },
    setUserObject(userObject) {
      this.userObject = userObject;
    },
    setCollectionDataProperty(collectionData) {
      this.collectionData = collectionData;
    },
    setCollectionChapters(chapters) {
      this.collectionData.chapters = chapters;
    },
    setTargetAudiencesData(targetAudiences) {
      this.targetAudiencesData = targetAudiences;
    },
    setTargetAudiences(targetAudiences) {
      this.collectionData.targetAudiences = targetAudiences;
    },
    setEdit(edit) {
      this.edit = edit;
    },
    setName(name) {
      this.name = name;
    },
    updateChapters() {
      this.chapters = this.collectionData.chapters.filter((chapter) => {
        return !chapter.is_template;
      });
    },
    updateChaptersOpen() {
      this.chaptersOpen = Object.keys(lodash.pickBy(this.chapters, 'open')).map(Number);
    },
    changeCover(fileData) {
      this.collectionData.cover = URL.createObjectURL(fileData.blob);
      this.collectionData.cover_large = URL.createObjectURL(fileData.blob);
      this.collectionData.update({ cover: fileData.file }, false);
    },
    setCoAuthorsCurrentSchool(coAuthorsCurrentSchool) {
      this.coAuthorsCurrentSchool = coAuthorsCurrentSchool || [];
    },
    setCoAuthorCountInOtherSchools(coAuthorCountInOtherSchools) {
      this.coAuthorCountInOtherSchools = coAuthorCountInOtherSchools || 0;
    },
    setReadersCurrentSchool(readersCurrentSchool) {
      this.readersCurrentSchool = readersCurrentSchool || [];
    },
    setReaderCountInOtherSchools(readerCountInOtherSchools) {
      this.readerCountInOtherSchools = readerCountInOtherSchools || 0;
    },
    setHasBeenActivated(hasBeenActivated) {
      this.hasBeenActivated = hasBeenActivated;
    },
    setCanTakeOwnership(canTakeOwnership) {
      this.canTakeOwnership = canTakeOwnership;
    },
    setTotalColleagues(totalColleagues) {
      this.totalColleagues = totalColleagues || 0;
    },

    // actions
    async setCollectionData({ collectionData, isInit }) {
      this.setCollectionDataProperty(collectionData);
      this.updateChapters();
      if (isInit && !this.lastShownRecordExists) {
        await Promise.all(
          this.chapters.map((chapter, index) => {
            return this.setChapterOpen({ chapter: chapter, openValue: index === 0 });
          })
        );
      } else {
        this.setLoading(false);
      }
    },

    addChapter() {
      const chapter = this.collectionData.addChapter();
      this.updateChapters();
      this.beginEdit(chapter);
      return chapter;
    },

    async beginEdit(target) {
      await this.confirmEdit();

      this.setEdit(target);
      this.setName(target.name);
    },

    confirmEdit() {
      if (this.edit) {
        const triggerStructureSave = !this.edit.id;
        return this.validationObserver.validate().then((valid) => {
          if (valid) {
            return this.edit.save({ name: this.name }).then(() => {
              if (triggerStructureSave) {
                this.collectionData.saveStructure();
              } else if (this.isReaderCollection && !this.edit.is_own_record) {
                this.edit.is_user_edit = true;
              }
              this.setEdit(null);
            });
          } else {
            return this.edit.delete().then(() => {
              this.setEdit(null);
            });
          }
        });
      }
      return Promise.resolve();
    },

    async cancelEdit() {
      if (this.edit) {
        if (!this.edit.id) {
          await this.edit.delete();
          this.updateChapters();
        }
        this.setEdit(null);
      }
    },

    blurEdit() {
      const target = this.edit;

      // delay is necessary as it would otherwise interfere with toggleEdit
      // because clicking the edit button also triggers a blur
      setTimeout(() => {
        if (target === this.edit) {
          this.confirmEdit();
        }
      }, 250);
    },

    async toggleEdit(target) {
      if (this.edit === target) {
        await this.confirmEdit();
      } else if (target) {
        await this.beginEdit(target);
      }
    },

    duplicateRecordToCollection({ record, chapter }) {
      return record.duplicateToCollection(chapter).then((responseRecord) => {
        if (record?.parent?.parent && record.parent.parent.id === responseRecord.parent.parent.id) {
          lodash(this.collectionData.chapters).filter({ id: chapter.id }).first().records =
            chapter.records;
          lodash(this.chapters).filter({ id: chapter.id }).first().records = chapter.records;
        } else {
          const message = translate.t(
            'MODULE.COLLECTIONS.COLLECTION.DUPLICATE_TO_COLLECTION_SUCCESS',
            {
              record: record.name,
              chapter: chapter.name,
              collection: chapter.parent.name,
            }
          );
          $notify({
            text: message,
            color: 'success',
          });
        }
      });
    },

    deleteChapter(chapter) {
      this.cancelEdit();
      return chapter.delete().then(() => {
        this.updateChapters();
      });
    },

    addRecord({ chapter, purpose }) {
      const record = chapter.addRecord(purpose);
      record.is_own_record = true;
      record.hasBeenActivated = this.collectionData.hasBeenActivated;
      this.beginEdit(record);
      return record;
    },

    async setChapterOpen({ chapter, openValue }) {
      const linkedChapters = await this.findChapter(chapter);
      linkedChapters.collectionChapter.open = openValue;
      linkedChapters.chapter.open = openValue;
      this.updateChaptersOpen();
      return Promise.resolve();
    },

    findChapter(chapter) {
      const linkedChapters = {};
      linkedChapters.collectionChapter = this.collectionData.chapters.find((collectionChapter) => {
        return collectionChapter.id === chapter.id;
      });
      linkedChapters.chapter = this.chapters.find((collectionChapter) => {
        return collectionChapter.id === chapter.id;
      });

      return linkedChapters;
    },

    async duplicateRecord(record) {
      this.cancelEdit();
      const newRecord = await record.duplicate();
      await this.beginEdit(newRecord);
      return newRecord;
    },

    deleteRecord(record) {
      this.cancelEdit();
      return record.delete();
    },

    async fetchLibrary() {
      this.library = await CollectionLibrary.load();
    },

    setRecordSelectModal(recordSelectModal) {
      this.recordSelectModal = recordSelectModal;
    },

    openRecordSelectModal({ record, deleteOriginal }) {
      this.recordSelectModal.showModal(record, deleteOriginal);
    },

    copyRecordToCollection(data) {
      this.duplicateRecordToCollection({
        record: data.record,
        chapter: data.chapter,
      });
    },

    setLastShownRecord(recordId, chapterId) {
      this.lastShownRecord.record = recordId;
      this.lastShownRecord.chapter = chapterId;
    },
  },

  persist: {
    pick: ['lastShownRecord'],
  },
});
