import './plugins/datadog';
import '../css/vendors.scss';
import NotificationService from './components/NotificationService';
import { createApp, h } from 'vue';
import './plugins/vee-validate';
import i18n from './plugins/vue-i18n.js';
import { initVuetify } from './plugins/vuetify';
import Dayjs from './plugins/dayjs';
import NProgress from 'nprogress';
import plannerTranslations from './plugins/translations/plannerTranslations';
import { createInertiaApp, router } from '@inertiajs/vue3';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import VuetifyUseDialog from 'vuetify-use-dialog';
// app.scss must be loaded last so the CSS overrides work correctly.
import '../css/app.scss';

window._paq = window._paq || [];
plannerTranslations(i18n);

/*
Progress indicator configuration with NProgress.
Progress bar will be rendered inside #loaderContainer element
and automatically trigger on navigation start/finish.
https://inertiajs.com/progress-indicators
*/
NProgress.configure({ parent: '#loaderContainer' });
router.on('start', () => NProgress.start());
router.on('finish', () => NProgress.done());

// Add datalayer data for Piwik Pro.
router.on('navigate', (event) => {
  window.dataLayer = window.dataLayer || [];
  // prettier-ignore
  window.dataLayer.push({
    'url': event.detail.page.url,
    'event': 'pageview',
  });
});

createInertiaApp({
  resolve: (name) => {
    const pages = import.meta.glob('./IPages/**/*.vue', { eager: true });
    return pages[`./IPages/${name}.vue`];
  },
  setup({ el, App, props, plugin }) {
    const app = createApp(
      { render: () => h(App, props) },
      {
        mounted() {
          window.addEventListener('popstate', this.forceReloadOnBrowserNavigation);
        },
        beforeUnmount() {
          window.removeEventListener('popstate', this.forceReloadOnBrowserNavigation);
        },
        methods: {
          forceReloadOnBrowserNavigation(event) {
            // Default browser behaviour is to use cache when you navigate history.
            // This can be prevented by setting the this.$page.forceReload flag in the mounted method.
            // https://github.com/inertiajs/inertia/issues/565

            if (!this.$page.forceReload) {
              return;
            }

            event.stopImmediatePropagation();

            router.reload({
              preserveState: false,
              preserveScroll: false,
              replace: true,
              onSuccess: (page) => {
                router.setPage(page);
              },
              onError: () => {
                window.location.href = event.state.url;
              },
            });
          },
        },
      }
    );
    app.use(plugin);
    app.use(NotificationService);
    app.use(Dayjs);
    app.use(i18n);

    const pinia = createPinia();
    pinia.use(piniaPluginPersistedstate);
    pinia.use(({ store }) => {
      store.$notify = app.config.globalProperties.$notify;
    });

    const vuetify = initVuetify();
    app.use(vuetify);

    app.use(pinia);

    app.use(VuetifyUseDialog, {
      confirmDialog: {
        dialogProps: { width: 450 },
        theme: 'light',
        confirmationButtonProps: {
          color: 'primary',
          variant: 'elevated',
        },
        cancellationButtonProps: {
          variant: 'text',
        },
      },
    });

    app.mount(el);
  },
  progress: false,
});
