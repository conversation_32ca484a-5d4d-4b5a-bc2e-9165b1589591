import $ from 'jquery';

export default function () {
  $(function () {
    let destinationUser = false;
    let sourceUser = false;

    function selectSourceUser(uid, tableRow) {
      tableRow.addClass('source-user');
      sourceUser = uid;

      $('#source-user').val(uid);
    }

    function selectDestinationUser(uid, tableRow) {
      tableRow.addClass('destination-user');
      destinationUser = uid;

      $('#destination-user').val(uid);

      $('.transfer-accounts-btn').prop('disabled', false);
    }

    function resetSelection() {
      $('tr.transfer-accounts').removeClass('destination-user').removeClass('source-user');

      destinationUser = false;
      sourceUser = false;

      $('#source-user').val(null);
      $('#destination-user').val(null);

      $('.transfer-accounts-btn').prop('disabled', true);
    }

    $('.transfer-accounts').on('click', function () {
      const tableRow = $(this);
      const uid = tableRow.data('uid');

      if (!sourceUser) {
        return selectSourceUser(uid, tableRow);
      }

      if (!destinationUser) {
        return selectDestinationUser(uid, tableRow);
      }

      return resetSelection();
    });
  });
}
