import $ from 'jquery';

export default function () {
  $(function () {
    let originalUser = false;
    let userToMerge = false;

    function handleOriginalUserSelect(uid, tableRow) {
      tableRow.addClass('original-user');
      originalUser = uid;

      $('#original-user').val(uid);
    }

    function handleUserToMergeSelect(uid, tableRow) {
      if (uid === originalUser) {
        return false;
      }
      tableRow.addClass('user-to-merge');
      userToMerge = uid;

      $('#user-to-merge').val(uid);

      // Show the merge button.
      $('.original-user .submit-btn').removeClass('hidden');
    }

    function resetSelection() {
      $('.users tr').removeClass('original-user').removeClass('user-to-merge');

      $('.submit-btn').addClass('hidden');

      originalUser = false;
      userToMerge = false;
    }

    $('.user').on('click', function () {
      const tableRow = $(this);
      const uid = tableRow.data('uid');

      if (!originalUser) {
        return handleOriginalUserSelect(uid, tableRow);
      }

      if (!userToMerge) {
        return handleUserToMergeSelect(uid, tableRow);
      }

      return resetSelection();
    });
  });
}
