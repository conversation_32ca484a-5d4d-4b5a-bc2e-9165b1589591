{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"uid": {"$ref": "uid-definition.json#/definitions/uid"}, "personUid": {"$ref": "uid-definition.json#/definitions/uid"}, "schoolUid": {"$ref": "uid-definition.json#/definitions/uid"}, "groupUid": {"$ref": "uid-definition.json#/definitions/uid"}, "groupType": {"$ref": "grouptype-definition.json#/definitions/groupType"}, "groupNumber": {"type": "integer"}, "startDate": {"$ref": "date-definition.json#/definitions/date"}, "endDate": {"$ref": "date-definition.json#/definitions/date"}, "email": {"$ref": "email-definition.json#/definitions/email"}, "person": {"$ref": "person-payload-definition.json#/definitions/personPayload"}}, "required": ["uid", "personUid", "schoolUid", "startDate"]}