{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "definitions": {"pupilDataPayload": {"type": "object", "properties": {"birthplace": {"type": "string"}, "motherland": {"type": "string"}, "nationality": {"type": "string"}, "email": {"$ref": "email-definition.json#/definitions/email"}, "dateOfBirth": {"$ref": "date-definition.json#/definitions/date"}, "phone": {"type": "string"}, "cellphone": {"type": "string"}, "emergencyPhone": {"type": "string"}, "doctorName": {"type": "string"}, "doctorPhone": {"type": "string"}, "motherTongue": {"type": "string"}, "homeLanguage": {"type": "string"}, "homeless": {"type": "boolean"}, "dateOfRegistration": {"$ref": "date-definition.json#/definitions/date"}, "dateOfDeregistration": {"$ref": "date-definition.json#/definitions/date"}, "numberOfAbsences": {"type": "integer"}, "religiousEducation": {"type": "string"}, "address": {"$ref": "address-definition.json#/definitions/address"}}, "required": []}}}