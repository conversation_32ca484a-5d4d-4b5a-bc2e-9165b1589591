{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"type": {"type": "string", "enum": ["TEST_RESULTS"]}, "version": {"type": "string", "enum": ["1.0"]}, "classGroupId": {"$ref": "uid-definition.json#/definitions/uid"}, "testId": {"$ref": "uid-definition.json#/definitions/uid"}, "testName": {"type": "string"}, "dateTaken": {"type": "string", "format": "date"}, "cmsReference": {"type": "string"}, "contentStructure": {"$ref": "content-structure-definition.json#/definitions/contentStructure"}, "domains": {"type": "array", "items": {"anyOf": [{"$ref": "domain-definition.json#/definitions/domain"}]}}, "pupilResults": {"type": "array", "items": {"anyOf": [{"$ref": "pupil-result-definition.json#/definitions/pupilResult"}]}}}, "required": ["classGroupId", "testId", "testName", "dateTaken", "cmsReference", "contentStructure"]}