@extends('common.report-print-template')
@section('content')

    <x-report::document.paginator :pupil-index="$pupilIndex"></x-report::document.paginator>
    <x-report::document.footer :date="$date" :pupil="$pupil" :report-settings="$reportSettings" :pupil-index="$pupilIndex"></x-report::document.footer>

    <div class="report">
        <x-report::report.header
            :report-settings="$reportSettings"
            :date="$date"
            :report-period="$reportPeriod"
            :schoolyear="$schoolyear"
            :care-info="$careInfo"
            :pupil="$pupil"
            :group="$group"
        >
        </x-report::report.header>
        @if($isSynthesis)
            <x-report::report.synthesisContent
                :report-settings="$reportSettings"
                :report="$report"
            >
            </x-report::report.synthesisContent>
        @else
            <x-report::report.content
                :report-settings="$reportSettings"
                :report-group-settings="$reportGroupSettings"
                :report="$report"
            >
            </x-report::report.content>
        @endif

        @if(!$isSynthesis)
            <x-report::report.footer
                :report-settings="$reportSettings"
                :report-group-settings="$reportGroupSettings"
                :report="$report"
            >
            </x-report::report.footer>
        @endif
    </div>
@endsection
