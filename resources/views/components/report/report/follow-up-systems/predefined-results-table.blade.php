<table class="{{ $attributes['class'] }}" style="{{ $attributes['style'] }}" autosize="{{ $attributes['autosize'] }}">
    @if($showTableHeader)
        <thead>
            <tr>
                <td colspan="{{$numberOfColumns}}" class="no-border-top {{$secondaryBackgroundColor}} {{$primaryBorderColor}} border-whiten-4 bold">
                    {{ $followUpSystemName }}
                </td>
            </tr>
            @if($hasRedicodis)
                <x-report::report.redicodis-list :redicodis="$redicodis"
                                                 :number-of-columns="$numberOfColumns"
                                                 :primary-border-color="$primaryBorderColor"
                                                 :hide-border="isset($comments)"
                ></x-report::report.redicodis-list>
            @endif
            @if($showComments)
                <x-report::report.results-table-comments
                    :report-settings="$reportSettings"
                    :comments="$comments"
                    :number-of-columns="$numberOfColumns"
                >
                </x-report::report.results-table-comments>
            @endif
        </thead>
    @endif
    <tbody>
        @foreach($testMoments as $testMoment)
            <tr>
                <td class="{{$primaryBorderColor}} border-whiten-4">{{ $testMoment['testMoment']['name'] }}</td>
                @if($hasScores)
                    <td class="input-moment-column {{$primaryBorderColor}} border-whiten-4">{{$testMoment['score']}}</td>
                @endif
                @if($hasPercentiles)
                    <td class="input-moment-column {{$primaryBorderColor}} border-whiten-4">{{$testMoment['percentile']}}</td>
                @endif
                <td class="input-moment-column {{$primaryBorderColor}} border-whiten-4">{{$testMoment['zone']}}</td>
            </tr>
            @if($testMoment['comment'] !== null)
                <tr class="comment-row">
                    <td class="{{$primaryBorderColor}} border-whiten-4" colspan="{{$numberOfColumns}}">
                        <x-report::report.comments :report-settings="$reportSettings" :comments="$getCommentsForTestMoment($testMoment)">
                            @php($comment = $component->comments[0] ?? null)
                            @isset($comment)
                                <table class="grid">
                                    <tr class="row-with-icon with-padding">
                                        <td class="icon-column" style="border: none; vertical-align: top;"><span class="far fa-lg margin-right-4 padding-right-4" style="display: inline-block; color: #4d4d4d">&#xf075</span></td>
                                        <td class="data-column-with-author" style="border: none; vertical-align: top; color: #4d4d4d">
                                            {!! $comment['comment'] !!}
                                        </td>
                                        <td class="author-column text-grey" style="border: none; vertical-align: top; text-align: right;">
                                            @isset($comment['author'])
                                                <i style="font-size: 8pt;color: #4d4d4d">- {{ $comment['author'] }}</i>
                                            @endisset
                                        </td>
                                    </tr>
                                </table>
                            @endisset
                        </x-report::report.comments>
                    </td>
                </tr>
            @endif
        @endforeach
    </tbody>
</table>
