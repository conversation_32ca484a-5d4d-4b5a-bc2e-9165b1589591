<div class="no-break">
    <x-report::section :report-settings="$reportSettings" icon-code="e900">
        <table class="grid {{$primaryBorderColor}} section-content" autosize="0">
            <tr>
                <td class="grid-12 {{$primaryBorderColor}}" style="border-top-width: 1px; border-top-style: solid;">
                    <table class="grid" autosize="0">
                        <tr>
                            @isset($logo)
                                <td class="grid-{{ isset($schoolName) ? '4' : '8' }}">
                                    <img src="{{$logo}}" alt="logo" class="image-holder" style="max-height: 140px; max-width: {{isset($schoolName) ? '185' : '370'}}px"/>
                                </td>
                            @endisset
                            @isset($schoolName)
                                <td class="grid-4 text-left" style="height: 70px;">
                                    <h2 class="school-name {{$secondaryColor}}">{{ $schoolName }}</h2>
                                </td>
                            @endisset
                            <td class="grid-4 text-right" style="height: 70px; min-height: 70px;">
                                <h2 class="report-title {{$secondaryColor}}">{{ $reportPeriodName }}</h2>
                            </td>
                        </tr>
                    </table>
                    <div style="height: 150px;"></div>
                </td>
            </tr>
        </table>
    </x-report::section>

    @php($labelClass = 'grid-5 text-vertical-center padding-v-2 text-left ' . $primaryColor . ' bold')
    @php($valueClass = 'grid-7 text-vertical-center padding-v-2 text-left ' . $secondaryColor)
    @php($sectionContentClass = 'grid margin-bottom-2 ' . $primaryBorderColor . ' section-content')

    <table class="grid margin-bottom-4" autosize="0">
        <tr>
            <td class="grid-5">
                <x-report::section :report-settings="$reportSettings" icon-code="e901">
                    <table class="{{ $sectionContentClass }}" autosize="0">
                        <tr>
                            <td class="grid-12 {{$primaryBorderColor}}" style="border-top-width: 1px; border-top-style: solid;">
                                <table class="grid" autosize="0">
                                    <tr>
                                        <td class="{{ $labelClass }}">{{trans('labels.schoolyear')}}:</td>
                                        <td class="{{ $valueClass }}">{{$schoolyearDescription}}</td>
                                    </tr>
                                    @if(empty($date))
                                        <tr>
                                            <td class="{{ $labelClass }}"></td>
                                            <td class="{{ $valueClass }}">&nbsp;</td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td class="{{ $labelClass }}">{{trans('labels.date')}}:</td>
                                            <td class="{{ $valueClass }}">{{$date}}</td>
                                        </tr>
                                    @endif
                                </table>
                            </td>
                        </tr>
                    </table>
                </x-report::section>
            </td>
            <td class="grid-6 grid-offset-1">
                <x-report::section :report-settings="$reportSettings" :image-path="$profilePicturePath" icon-code="e902">
                    <table class="{{ $sectionContentClass }}" autosize="0">
                        <tr>
                            <td class="grid-12 {{$primaryBorderColor}}" style="border-top-width: 1px; border-top-style: solid;">
                                <table class="grid" autosize="0">
                                    <tr>
                                        <td class="{{ $labelClass }}">{{trans('labels.name')}}:</td>
                                        <td class="{{ $valueClass }}">{{$pupilName}}</td>
                                    </tr>
                                    <tr>
                                        <td class="{{ $labelClass }}">{{trans('labels.class')}}:</td>
                                        <td class="{{ $valueClass }}">{{$groupName}}</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </x-report::section>
            </td>
        </tr>
    </table>
</div>
