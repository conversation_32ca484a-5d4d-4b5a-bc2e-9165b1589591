@foreach($tables as $table)
    <div @if(!$fitsOnASinglePage && !$loop->last) style="page-break-after: always"@endif >
        @foreach($table['subjects'] as $subject)
            @php($isFirstSection = $loop->first)
            @php($hideTopMargin = !$isFirstSection)
            @php($isSubject = $subject['level'] === 0)
            <x-report::section :report-settings="$reportSettings"
                               :icon-code="$isFirstSection ? 'e903' : null" :hide-top-margin="!$isFirstSection">
                <table class="grid section-content {{$isFirstSection ? 'margin-top-2' : ''}}" autosize="0">
                    <tr>
                        <td class="grid-12 {{$isFirstSection ? $primaryBorderColor : ''}}"
                            style="{{$isFirstSection ? 'border-top-width: 1px; border-top-style: solid;' : ''}}">
                            <table class="scoreTable synthesisTable" @if($loop->first)style="margin-top: 20px;"
                                   @endif autosize="0">
                                @if($loop->first)
                                    <thead>
                                    <tr>
                                        <td class="{{$headerStyle}} bold"></td>
                                        @foreach($table['periods'] as $period)
                                            <td class="number-column {{$headerStyle}} bold">{{$period['short_name']}}</td>
                                        @endforeach

                                        @if($table['showSubjectTotal'] || $loop->parent->last)
                                            <td class="number-column {{$headerStyle}} bold">{{$totals['short_name']}}</td>
                                        @endif

                                    </tr>
                                    </thead>
                                @endif
                                <tbody>
                                <tr>
                                    <td class="{{$isSubject ? $subjectStyle : $domainStyle}}">{{$subject['name']}}</td>
                                    @foreach($table['periods'] as $period)
                                        <td class="number-column {{$isSubject ? $subjectStyle : $domainStyle}}">
                                            @if(($isSubject ? $showSynthesisSubjectTotals : $showSynthesisDomainTotals)  && array_key_exists($subject['uid'], $period['subjects']))
                                                <x-report::report.score
                                                    :value="$period['subjects'][$subject['uid']]['total']" />
                                            @endif
                                        </td>
                                    @endforeach
                                    @if($table['showSubjectTotal'] || $loop->parent->last)
                                        <td class="number-column {{$isSubject ? $subjectStyle : $domainStyle}}">
                                            @if(($isSubject ? $showSynthesisSubjectTotals : $showSynthesisDomainTotals)  && array_key_exists($subject['uid'], $totals['subjects']))
                                                <x-report::report.score
                                                    :value="$totals['subjects'][$subject['uid']]['total']"
                                                    :total-max="$totals['subjects'][$subject['uid']]['total_max']" />
                                            @endif
                                        </td>
                                    @endif
                                </tr>
                                </tbody>
                            </table>
                            @if($loop->last && ($table['showReportTotal']) && ($showSynthesisReportTotals || $showSynthesisTotal))
                                <table class="scoreTable synthesisTable" autosize="0">
                                    <tbody>
                                    <tr>
                                        <td class="{{$reportTotalStyle}}">@lang('labels.report.report-total')</td>
                                        @foreach($table['periods'] as $period)
                                            <td class="number-column {{$reportTotalStyle}}">
                                                @if($showSynthesisReportTotals)
                                                    <x-report::report.score :value="$period['total']"
                                                                            :total-max="$period['total_max']" />
                                                @endif
                                            </td>
                                        @endforeach
                                        @if($table['showReportTotal'] && $loop->parent->last)
                                            <td class="number-column {{$reportTotalStyle}}">
                                                @if($showSynthesisTotal)
                                                    <x-report::report.score :value="$totals['total']"
                                                                            :total-max="$totals['total_max']" />
                                                @endif
                                            </td>
                                        @endif
                                    </tr>
                                    </tbody>
                                </table>
                            @endif
                        </td>
                    </tr>
                </table>
            </x-report::section>
        @endforeach
    </div>
@endforeach