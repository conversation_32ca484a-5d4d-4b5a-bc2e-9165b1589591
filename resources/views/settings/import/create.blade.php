<?php

use App\Models\File\FileUsage;

$schema = [
    [
        'type' => 's3file',
        'multiple' => false,
        'label' => trans('labels.file'),
        'model' => 'file',
        'usage' => FileUsage::CareImportFile->value,
    ],
    [
        'type' => 's3file',
        'multiple' => false,
        'label' => trans('labels.attachments'),
        'model' => 'attachments',
        'usage' => FileUsage::CareImportAttachments->value,
    ],
    [
        'type' => 'importTypeSelect',
        'label' => trans('labels.type'),
        'model' => 'type',
        'options' => $careImportTypes,
        'careImportTypesConfig' => $careImportTypesConfig,
        'item-text' => 'name',
    ],
    [
        'type' => 'select',
        'label' => trans('labels.separator'),
        'model' => 'separator',
        'options' => $separators,
    ],
    [
        'type' => 'select',
        'label' => trans('labels.enclosure'),
        'model' => 'enclosure',
        'options' => $enclosures,
    ],
    [
        'type' => 'checkbox',
        'label' => trans('labels.has_headers'),
        'model' => 'has_headers',
        'value' => 'true',
    ],
    [
        'type' => 'checkbox',
        'label' => trans('labels.use_previous_attachments'),
        'model' => 'use_previous_attachments',
        'value' => 'true',
    ],
    [
        'type' => 'checkbox',
        'label' => trans('labels.delete_previous_imports'),
        'model' => 'reset_import',
        'value' => 'true',
    ],
];

?>

@extends('settings.template')
@section('title', $title)
@section('navbar-top-right')
    <form-buttons label="{{ trans('labels.save') }}"
                  :show-cancel="true"
    >
    </form-buttons>
@endsection
@section('settings_content')
    @include('settings.import.breadcrumbs', [
        'currentStep' => 1,
        'model' => $model,
    ])
    <v-row>
        <v-col cols="12">
            <v-alert
                type="info"
                variant="outlined"
                class="mb-2"
            >
                <template v-slot:default>
                    <i18n-t keypath="labels.import.help.intro" scope="global">
                        <template #break>
                            <br />
                        </template>
                    </i18n-t>
                </template>
            </v-alert>
        </v-col>
    </v-row>
    @include('common.components.form', ['submitText' => @trans('labels.next')])
@endsection
