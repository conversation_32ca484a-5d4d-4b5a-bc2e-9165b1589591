@extends('settings.template')

@section('title', @trans('labels.history'))

@section('content')
    <v-container :fluid="true" fill-height class="pt-2 min-width">
        <v-row>

            <v-col class="full-height overflow-y-auto animated"
                   fillheight
                   cols="12">
                <div class="pb-3">
                    <history-overview-table current-schoolyear="{{ $currentSchoolyear->start }}"
                                            :schoolyears="{{ $schoolyears }}"
                                            current-school="{{ school()->uid }}"
                                            fetch-base-url="{{ $fetchBaseUrl }}"
                                            dashboard-base-url="{{ $evaluationDashboardBaseUrl }}"
                                            care-info-base-url="{{ $careInfoBaseUrl }}"
                                            export-base-url="{{ $exportBaseUrl }}"
                                            export-status-base-url="{{ $exportStatusBaseUrl }}"
                                            school-export-link="{{ $schoolExportLink }}"
                    ></history-overview-table>
                </div>
            </v-col>
        </v-row>
    </v-container>
@endsection
