@extends('settings.template')
@section('title', $title)
@section('navbar-top-right')
    <v-btn variant="elevated" color="success" text-color="white" href="{{$baseUrl}}/create" prepend-icon="add">
        {{ trans('labels.create') }}
    </v-btn>
@endsection

@section('settings_content')

    @component('common.components.archivable-crud-table', [
        'title' => $title,
        'data' => $models,
        'baseUrl' => $baseUrl,
        'disabledTooltip' => $disableTooltip,
        'disabledField' => 'has_relations',
        'fields' => [
            [
                'title' => trans('labels.name'),
                'key' => 'name',
                'render' => true,
            ],
            [
                'title' => '',
                'key' => 'actions',
                'render' => false,
                'sortable' => false,
            ],
        ],
        'defaultSort' => 'name',
    ])
    @endcomponent
@endsection
