@extends('settings.template')
@section('title', $title)
@section('navbar-top-right')
    <form-buttons label="{{ trans('labels.save') }}"
                  :show-cancel="true"
                  cancel-url="{{ route('web.settings.common.quotation-systems.index') }}"
    >
    </form-buttons>
@endsection

@section('settings_content')
    <form-wrapper action="{{ $action }}"
                  method="{{ $method }}"
                  :model="{{ $quotationSystem->toJson() }}"
                  :render-buttons="false"
    >
        <template v-slot="{ parentErrors, parentModel, parentChange, parentFields }">
            <v-container>
                <v-row>
                    <v-col cols="12" class="py-0">
                        <form-title>{{trans('labels.name')}}</form-title>
                    </v-col>
                    <v-col cols="12" md="6" lg="4" class="py-0">
                        <input-element
                            :element="{{json_encode([
                                        'model' => 'name',
                                        'label' => trans('labels.name'),
                                    ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $quotationSystem->validation_rules['name'] }}"
                            @change="parentChange"
                            variant="solo"
                            color="primary"
                            :single-line="true"
                        >
                        </input-element>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12" class="py-0">
                        <form-title>{{trans('labels.description')}}</form-title>
                    </v-col>
                    <v-col cols="12" md="6" lg="4" class="py-0">
                        <textarea-element
                            :element="{{json_encode([
                                    'model' => 'description',
                                    'label' => trans('labels.description'),
                                ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $quotationSystem->validation_rules['description'] }}"
                            @change="parentChange"
                            auto-grow
                            rows="3"
                            variant="solo"
                            color="primary"
                            :single-line="true"
                        >
                        </textarea-element>
                    </v-col>
                </v-row>
                <v-row class="mt-6">
                    <v-col cols="12" >
                        <quotation-creator
                            v-model:type="parentModel['type']"
                            v-model:quotations="parentModel['quotations']"
                            v-model:rating-type="parentModel['rating_type']"
                            :minimum="{{ \Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation::MINIMUM }}"
                            :maximum="{{ \Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation::MAXIMUM }}"
                            @change="parentChange"
                            :parent-errors="parentErrors"
                            :parent-change="parentChange"
                            :parent-fields="parentFields"
                            :icons="{{json_encode($icons)}}"
                            :types="{{json_encode($types)}}"
                            :rating-types="{{json_encode($ratingTypes)}}"
                        >
                        </quotation-creator>
                    </v-col>
                </v-row>
            </v-container>
        </template>
    </form-wrapper>


@endsection
