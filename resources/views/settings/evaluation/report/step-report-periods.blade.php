@php
    use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
@endphp

@extends('settings.template')

@section('title', $pageTitle)

@section('navbar-top-right')
    @if($isStandAlone)
        <form-buttons label="{{ trans('labels.save-and-close') }}" redirect-url="{{ route('web.settings.common.reports.report-setting.overview.index') }}"></form-buttons>
    @else
        <form-buttons :inline="true"
                          :primary="false"
                          class="mr-2"
                          label="{{ trans('labels.save-and-close') }}"
                          redirect-url="{{ route('web.settings.common.reports.report-setting.overview.index') }}">
            </form-buttons>
            <form-buttons
                :inline="true"
                label="{{ trans('labels.save-and-next') }}"
                redirect-url="{{ route('web.settings.evaluation.reports.report-setting.quotation-settings.index', Route::current()->parameters())}}"
            >
            </form-buttons>
    @endif
@endsection

@section('settings_content')
    @if(!$isStandAlone)
        @include('settings.evaluation.report.breadcrumbs', ['currentStep' => $currentStep, 'submitOnNavigate' => true])
    @endif

    <report-periods :existing-report-periods="{{ $reportPeriods }}"
                    save-url="{{ $saveUrl }}"
                    start-date="{{ $startDate }}"
                    end-date="{{ $endDate }}"
                    :max-short-name-length="{{ ReportPeriod::MAX_SHORT_NAME_LENGTH }}"
                    @if(!$isStandAlone)
                        redirect-link="{{ route('web.settings.evaluation.reports.report-setting.report-settings.index', Route::current()->parameters())}}"
                    @endif
    ></report-periods>
@endsection
