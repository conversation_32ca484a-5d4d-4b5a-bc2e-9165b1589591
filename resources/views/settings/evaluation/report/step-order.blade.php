@extends('settings.template')
@section('title', $pageTitle)
@section('navbar-top-right')
    <form-buttons
            :primary="false"
            :inline="true"
            label="{{ trans('labels.save-and-close') }}"
            redirect-url="{{ route('web.settings.common.reports.report-setting.overview.index') }}">
        </form-buttons>
        <form-buttons
            :inline="true"
            label="{{ trans('labels.save-and-next') }}"
            redirect-url="{{ route('web.settings.evaluation.reports.report-setting.report-settings.index', Route::current()->parameters())}}"
        >
        </form-buttons>
@endsection
@section('settings_content')
    @include('settings.evaluation.report.breadcrumbs', ['currentStep' => $currentStep, 'submitOnNavigate' => true])
    <order-report-settings save-url="{{ $saveUrl }}" :min="{{ $min }}" :max="{{ $max }}"
                           :report-items="{{ json_encode($reportItems) }}"
                           redirect-link="{{ $redirectLink }}"
    />
@endsection
