@extends('settings.template')

@section('title', trans('titles.settings.care.pupil-management.upload-photos'))

@section('settings_content')
    @include('settings.care.pupil-management.upload-photos.breadcrumbs', ['currentStep' => 1,])

    <v-row>
        <v-col cols="12" class="mt-0">
            <v-alert type="info" variant="outlined">
                {{ trans('labels.photo.help.download-list') }}
            </v-alert>
        </v-col>
    </v-row>

    <v-row class="mt-0">
        <v-col cols="12">
            <v-card class="ma-0 mb-2">
                <v-card-text>
                    <v-container :fluid="true" class="pa-0">
                        <v-row>
                            <v-col cols="12">
                                <v-btn color="primary" href="{{ route('web.settings.care.pupil-management.upload-photos.download') }}">
                                    {{ trans('labels.photo.download') }}
                                </v-btn>
                            </v-col>
                        </v-row>
                    </v-container>
                </v-card-text>
            </v-card>
        </v-col>
    </v-row>
@endsection
