@use(App\Models\Feature\FeatureToggle)
@use(\App\Models\Feature\Feature)
@use(App\Models\File\FileUsage)
@php($pupil = Route::current()->parameter('pupil'))
@php($isException = $pupil ? $pupil->careInfo->pupil_status->isException():false)
@extends('common.care-info.template')

@section('title', trans('titles.info-sheet.edit'))

@section('navbar-top-right')
    <form-buttons label="{{ trans('labels.save') }}"
                  :show-cancel="true"
                  :disable-save="{{ json_encode($isException) }}"
    >
    </form-buttons>
@endsection

@section('group-content')
    <v-row justify-end style="height: 60px;" class="text-right pt-2">
        <v-btn :icon="isOpen ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"
               variant="flat"
               @click="toggleIsOpen"
               class="mr-2">
        </v-btn>
        <v-spacer></v-spacer>
    </v-row>

    @include('care.components.inactive-pupil-alert')
    <v-row>
        <form-wrapper action="{{ route('web.common.care-info.save', ['pupil' => $pupil->uid]) }}"
                      :model="{{ $careInfo->toJson() }}"
                      :render-buttons="false"
                      class="mb-3"
        >

            <template v-slot="{ parentErrors, parentModel, parentChange, parentFields, linkedFields }">
                @component('common.components.info-sheet', compact('pupil', 'pupilAvatar', 'careInfo', 'pupilChange', 'classGroupCareers', 'studyGroupCareers'))
                    @slot('extraInfo')
                        <editor-element
                            :element="{{ json_encode([
                                    'model' => 'extra_info',
                                    ]) }}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInput->validation_rules['extra_info'] ?? '' }}"
                            @change="parentChange"
                        ></editor-element>
                    @endslot

                    @if ($pupil->careInfo->pupil_status->isActive())
                        @slot('profilePicture')
                            <v-spacer></v-spacer>
                            <image-upload-button
                                name="profile_picture"
                                v-model="parentModel.profile_picture_path"
                                :image-url="parentModel.profile_picture_parsed"
                                :width="400"
                                :height="400"
                                :show-delete="true"
                                :mini="true"
                                :display-height="36"
                                :display-width="36"
                            >
                            </image-upload-button>
                        @endslot
                    @endif
                    @slot('gender')
                        @if ($careInfo->has_wisa_connection)
                            {{ optional($careInfo->gender)->getHumanReadableName() }}
                        @else
                            <select-element
                                :element="{{json_encode([
                                    'model' => 'gender',
                                    'options' => $genders->toArray(),
                                ])}}"
                                :linked-items="['national_register_number']"
                                :linked-fields="linkedFields"
                                v-bind="{ parentErrors, parentModel, parentFields }"
                                validations="{{ $careInfo->validation_rules['gender'] ?? '' }}"
                                @change="parentChange"
                                class="mt-0 pt-0"
                                attach
                                variant="underlined"
                                density="compact"
                                item-text="text"

                            ></select-element>
                        @endif
                    @endslot

                    @slot('birthDate')
                        @if ($careInfo->has_wisa_connection)
                            {{ $careInfo->date_of_birth_string }}
                        @else
                            <date-picker-element
                                :element="{{ json_encode([
                                    'model' => 'date_of_birth_string',
                                ]) }}"
                                :linked-items="['national_register_number']"
                                :linked-fields="linkedFields"
                                input-classes="mt-0 pt-0"
                                v-bind="{ parentErrors, parentModel, parentFields }"
                                validations="{{ $careInfo->validation_rules['date_of_birth'] ?? '' }}"
                                @change="parentChange"
                            ></date-picker-element>
                        @endif
                    @endslot

                    @slot('homeLanguage')
                        @if ($careInfo->has_wisa_connection === false)
                            <select-element
                                :element="{{json_encode([
                                    'model' => 'home_language',
                                    'options' => $languages->toArray(),
                                ])}}"
                                item-text="name"
                                item-value="code"
                                v-bind="{ parentErrors, parentModel, parentFields }"
                                validations="{{ $careInfo->validation_rules['home_language'] ?? '' }}"
                                @change="parentChange"
                                autocomplete
                                class="mt-0 pt-0"
                                attach
                                :clearable="true"
                                variant="underlined"
                                density="compact"
                            ></select-element>
                        @else
                            {{ trans('labels.managed-by-wisa') }}
                        @endif
                    @endslot

                    @slot('nationalRegisterNumber')
                        @if ($careInfo->has_wisa_connection)
                            **.**.**-***.**
                        @else
                            <masked-input-element
                                :element="{{json_encode([
                                    'model' => 'national_register_number',
                                ])}}"
                                :linked-items="['date_of_birth_string', 'gender']"
                                :linked-fields="linkedFields"
                                v-bind="{ parentErrors, parentModel, parentFields }"
                                validations="{{ $careInfo->validation_rules['national_register_number'] ?? '' }}"
                                @change="parentChange"
                                class="mt-0 pt-0"
                                color="primary"
                                @if(!empty($careInfo->national_register_number))
                                    placeholder="**.**.**-***.**"
                                @endif
                                mask="##.##.##-###.##">
                            </masked-input-element>
                            <national-register-number-warning
                                :parent-fields="parentFields"
                                :parent-model="parentModel"
                                :has-national-register-number="{{json_encode(!empty($careInfo->national_register_number))}}"
                            ></national-register-number-warning>
                        @endif
                    @endslot

                    @slot('careLevel')
                        <select-element
                            :element="{{json_encode([
                                'model' => 'current_care_level',
                                'options' => $careLevelTypes->toArray(),
                            ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            @change="parentChange"
                            class="mt-0 pt-0"
                            attach
                            variant="underlined"
                            density="compact"
                        ></select-element>
                    @endslot

                    @slot('diagnoses')
                        <autocomplete-chips-element
                            :element="{{json_encode([
                                    'model' => 'diagnoses',
                                    'options' => $diagnoses->toArray(),
                                    'itemText' => 'name',
                                    'itemValue' => 'uid',
                                ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInfo->validation_rules['diagnoses'] ?? '' }}"
                            @change="parentChange"
                            @if(FeatureToggle::isActive(Feature::ShowDiagnosesPage) === false)
                            :allow-new="true"
                            @else
                            :hide-archived="true"
                            no-data-additional-text="labels.diagnoses-not-found"
                            @endif
                            :full-chips="true"
                            :selected="parentModel['diagnoses']"
                            :select-classes="'mt-0 pt-0 my-1 mx-2'"
                            attach
                            variant="underlined"
                            density="compact"
                        >
                        </autocomplete-chips-element>
                        <s3-file-upload-button name="diagnosis_attachments"
                                               v-model="parentModel['diagnosis_attachments']"
                                               usage="{{ FileUsage::DiagnosisAttachments->value }}"
                                               :show-progress="true"
                                               class="mt-2"
                                               v-bind="{ parentErrors, parentFields }"
                        >
                        </s3-file-upload-button>
                    @endslot

                    @slot('homeSituation')
                        <editor-element
                            :element="{{json_encode([
                                    'model' => 'home_situation',
                                ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInfo->validation_rules['home_situation'] }}"
                            @change="parentChange">
                        </editor-element>
                    @endslot

                    @slot('socialEmotional')
                        <editor-element
                            :element="{{json_encode([
                                    'model' => 'social_emotional',
                                ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInfo->validation_rules['social_emotional'] }}"
                            @change="parentChange">
                        </editor-element>
                    @endslot

                    @slot('redicodis')
                        <redicodis-selector :items="{{$redicodis}}"
                                            v-model="parentModel['redicodis']"
                                            save-url="{{ route('web.care.care-info.redicodis.save', ['pupil' => $pupil->uid]) }}"
                                            :report-items="{{ json_encode($reportItems) }}"
                                            :show-redicodis-on-report="{{json_encode($showRedicodisOnReport)}}"
                                            redicodi-link-url="{{route('web.care.care-info.redicodis.redicodi.save', ['pupil' => $pupil->uid, 'redicodi' => '#redicodiUid#'])}}"
                        >
                        </redicodis-selector>
                    @endslot

                    @slot('physicalSituation')
                        <editor-element
                            :element="{{json_encode([
                                'model' => 'physical_situation',
                            ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInfo->validation_rules['physical_situation'] }}"
                            @change="parentChange">
                        </editor-element>
                    @endslot

                    @slot('dominantHand')
                        <dominant-hand-selector
                            :element="{{json_encode([
                                    'model' => 'dominant_hand',
                                    'options' => $dominantHandTypes->toArray(),
                                ])}}"
                            v-bind="{ parentErrors, parentModel, parentFields }"
                            validations="{{ $careInfo->validation_rules['dominant_hand'] }}"
                            @change="parentChange">
                        </dominant-hand-selector>
                    @endslot
                @endcomponent
            </template>
        </form-wrapper>
    </v-row>
@endsection
