@use(\Cfa\Common\Domain\User\Mfa\Status\MfaStatus)

@extends('settings.template')

@section('title', trans('titles.mfa.management'))

@section('content')
    <v-container>
        <v-row>
            <v-col cols="12" sm="6" offset-sm="3" md="6" offset-md="3" xl="4" offset-xl="4">
                <div class="logo">
                    <img src="{{$logoUrl}}" alt='logo' style="max-width: 150px;">
                </div>

                <v-card class="mt-2">
                    <v-card-text>
                        @if ($userMfa === null)
                            <h2 class="mb-6">@lang('titles.mfa.configure.disabled')</h2>
                            <p>@lang('labels.mfa.configure.access')</p>
                            <p>@lang('labels.mfa.configure.logout')</p>
                            <p><a href="{{ $helpUrl }}" target="_blank">@lang('labels.mfa.configure.help')</a></p>

                            <mfa-configure-form configure-url="{{ $configureUrl }}"></mfa-configure-form>
                        @else
                            <h2 class="mb-6">@lang('titles.mfa.configure.enabled')</h2>

                            @can('accessSettings', school())
                                <p>{{ trans('labels.mfa.configure.enabled') }}</p>
                                <p>{{ trans('labels.mfa.reset.ict-admin') }}</p>
                            @else
                                <mfa-reset-form reset-url="{{ $resetUrl }}" :user-mfa="{{ json_encode($userMfa) }}">
                                </mfa-reset-form>
                            @endcan
                        @endif
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
    </v-container>
@endsection
