<?php
$schema = [
    [
        'type' => 'text',
        'label' => trans('labels.user-data-transfer.previous-account'),
        'model' => 'email',
        'placeholder' => trans('labels.user-data-transfer.previous-account'),
    ],
];
$model = ['email' => ''];
?>
@extends('settings.template')

@section('title', trans('titles.user-data-transfer.index'))
@section('navbar-top-right')
    <form-buttons label="{{ trans('labels.save') }}"
                  :show-cancel="true"
    >
    </form-buttons>
@endsection

@section('content')
    <v-container :fluid="true" class="px-10">
        <v-row justify="center" align="center">
            <v-col cols="7" class="mb-4">
                <v-alert type="info" class="elevation-min" outlined>
                    <i18n-t keypath="labels.user-data-transfer.intro" tag="span">
                        <template #break>
                            <br />
                            <br />
                        </template>
                    </i18n-t>
                </v-alert>
            </v-col>
        </v-row>
        <v-row justify="center" align="center">
            <v-col cols="7" class="mb-4">
                <form-builder :schema="{{json_encode($schema)}}"
                              :model="{{json_encode($model)}}"
                              :validate="{{json_encode($emailValidation)}}"
                              action="{{$action}}"
                              method="{{$method}}"
                              submit-text="{{trans('labels.save')}}"
                              :render-buttons="true"
                >
                </form-builder>
            </v-col>
        </v-row>
    </v-container>
@endsection
