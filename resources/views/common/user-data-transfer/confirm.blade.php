@extends('settings.template')

@section('title', trans('titles.user-data-transfer.mail-confirm'))

<?php
/* @var \Cfa\Common\Domain\UserDataTransfer\UserDataTransfer $userDataTransfer */
/* @var \Cfa\Common\Domain\User\User $fromUser */
$fromUser = $userDataTransfer->fromUser;
/* @var \Cfa\Common\Domain\User\User $toUser */
$toUser = $userDataTransfer->toUser;
?>

@section('content')
    <v-container :fluid="true" class="px-10">
        <v-row justify="center" align="center">
            <v-col cols="7" class="mb-4">
                <v-alert type="info" class="elevation-min" outlined>
                    @lang('labels.user-data-transfer.warning')
                </v-alert>
            </v-col>
        </v-row>
        <v-row justify="center" align="center">
            <v-col cols="3">
                <v-card>
                    <v-card-title class="text-h5">{{$fromUser->fullname}}</v-card-title>
                    <v-divider></v-divider>
                    <v-card-text>
                        <div>
                            <form-title>{{ trans('labels.emails') }}</form-title>
                            {{$fromUser->email}} / {{$fromUser->account_name}}
                        </div>
                        <div>
                            <form-title>{{ trans('labels.schools') }}</form-title>
                            <ul style="list-style: none;" class="ma-0 pa-0">
                                @foreach($fromUser->schools() as $school)
                                    <li class="ma-0 pa-0">{{$school->name}}</li>
                                @endforeach
                            </ul>
                        </div>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="1">
                <v-icon color="primary" class="d-flex align-center" style="height:100%;" icon="chevron_right" />
            </v-col>
            <v-col cols="3">
                <v-card>
                    <v-card-title class="text-h5">{{$toUser->fullname}}</v-card-title>
                    <v-divider></v-divider>
                    <v-card-text>
                        <div>
                            <form-title>{{ trans('labels.emails') }}</form-title>
                            {{$toUser->email}} / {{$toUser->account_name}}
                        </div>
                        <div>
                            <form-title>{{ trans('labels.schools') }}</form-title>

                            @foreach($toUser->schools() as $school)
                                <span>{{$school->name}}</span>
                                @endforeach</form>
                        </div>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
        <v-row justify="center" align="center">
            <v-col cols="7">
                <v-row justify="end" no-gutters>
                    <form action="{{route('web.common.users.data-transfer.execute', [
                    'userDataTransfer' => $userDataTransfer
                    ])}}"
                          method="POST" >
                        @csrf()
                        <v-btn class="bg-primary mr-0" type="submit">@lang('labels.confirm')</v-btn>
                    </form>
                </v-row>
            </v-col>
        </v-row>
    </v-container>
@endsection
