@use(App\Constants\Subdomain)
@use(App\Services\Maintenance\MaintenanceService)
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="globalsign-domain-verification" content="2ygmc8JyXBn2yjTDkHhQFA0GwvmdFeCRLcQfFaOWbA"/>
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,minimum-scale=0.5">
    <meta content="width=device-width,initial-scale=1,minimal-ui,maximum-scale=1,minimum-scale=0.5" name="viewport">
    <title>@yield('title')@yield('page-title')</title>
    <link href='https://fonts.googleapis.com/css?family=Material+Icons' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,400,500,700' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="{{ asset('css/outdatedbrowser.9131a0c1.min.css') }}">
    @yield('scripts')
    @include('common.components.fav-icons')
</head>
<body style="min-width: 420px;">
    @include('common.components.piwik-pro')
    <div id="app" v-cloak>
        <layout blade-title="@yield('title')@yield('page-title')"
                :blade-properties="{{json_encode($page = $page)}}"
                :show-navbar-top="{{request()->is('settings/*') || Subdomain::PLANNER !== get_subdomain() ? 'true' : 'false'}}">
            @if(View::hasSection('navbar-top-right'))
            <template v-slot:navbar-top-right>
                @yield('navbar-top-right')
            </template>
            @endif
            @yield('content')
        </layout>
    </div>

    <!-- Outdated browser -->
    <script src="{{ asset('js/outdatedbrowser.fb0ea634.min.js') }}"></script>

    <div id="outdated" style="text-transform: none">
        <h6>{{ trans('exceptions.outdated-browser.action') }}</h6>
        <p> {{ trans('exceptions.outdated-browser.warning') }} <a id="btnUpdateBrowser" href="http://outdatedbrowser.com/nl">{{ trans('exceptions.outdated-browser.action') }}</a></p>
        <p class="last"><a href="#" id="btnCloseUpdateBrowser" title="{{ trans('labels.close') }}">&times;</a></p>
    </div>
</body>
</html>
