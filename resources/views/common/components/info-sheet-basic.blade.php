@use(Cfa\Care\Domain\CareInfo\PupilStatus)
<v-col class="isoElement pupil not-wisa" :class="[isOpen ? 'v-col-12 v-col-sm-12 v-col-md-6': 'v-col-12 v-col-sm-6 v-col-md-4']">
    <info-card :show-divider="true">
        <template #title>
            <v-card-title class="d-flex align-items-center">
                <avatar :avatar="{{ json_encode($pupilAvatar) }}" class="mr-3"></avatar>
                <div class="subtitle font-weight-bold" style="line-height: 2;">
                    {{ $pupil->fullname }}
                </div>
                @if (isset($profilePicture))
                    {{ $profilePicture }}
                @endif
            </v-card-title>
        </template>
        @if ($pupil->careInfo->pupil_status->isDisabled())
            <info-card-element :label="$t('labels.pupil-status')">
                {{ $pupil->careInfo->pupil_status->getHumanReadableName() }}
            </info-card-element>
        @endif
        @if ($pupil->careInfo->pupil_status === PupilStatus::SuspiciousNameChangeException)
            <info-card-element
                :label="$t('labels.pupil-management.name-exception.previous-name')">
                {{ $pupilChange['old_fullname'] }}
            </info-card-element>
        @endif
        @if (!$pupil->careInfo->pupil_status->isException())
            <info-card-element :label="$t('labels.date-of-birth')">
                {{ $birthDate }}
            </info-card-element>
            <info-card-element :label="$t('labels.gender')">
                {{ $gender }}
            </info-card-element>
            @if ($pupil->careInfo->pupil_status->isActive())
                <info-card-element :label="$t('labels.home-language')">
                    <template #tooltip>
                        <i18n-t keypath="labels.home-language-message.message" tag="span">
                            <template #conditions>
                                <ul>
                                    <li>@vue('$t("labels.home-language-message.condition1")')</li>
                                    <li>@vue('$t("labels.home-language-message.condition2")')</li>
                                    <li>@vue('$t("labels.home-language-message.condition3")')</li>
                                </ul>
                            </template>
                        </i18n-t>
                    </template>
                    {{ $homeLanguage }}
                </info-card-element>
            @endif
            @if ($pupil->careInfo->pupil_status !== PupilStatus::StandAlone)
                <info-card-element :label="$t('labels.national-register-number')">
                    {{ $nationalRegisterNumber }}
                </info-card-element>
            @endif
        @endif
    </info-card>
</v-col>

@isset($wisa)
    <v-col class="isoElement wisa" :class="[isOpen ? 'v-col-12 v-col-sm-12 v-col-md-6': 'v-col-12 v-col-sm-6 v-col-md-4']">
        <info-card :show-divider="true">
            <v-card-title slot="title" style="position: relative;">
                <avatar :avatar="{{ json_encode($pupilAvatar) }}"></avatar>

                <div class="subtitle font-weight-bold" style="line-height: 2;">
                    {{$wisa['pupil']['firstname']}} {{$wisa['pupil']['lastname']}}
                </div>
            </v-card-title>

            <info-card-element :label="$t('labels.date-of-birth')">
                {{ $wisa['pupil']['date_of_birth']->format(\App\Constants\DateFormats::DATE) }}
            </info-card-element>
            <info-card-element :label="$t('labels.gender')">
                {{ $wisa['pupil']['gender'] }}
            </info-card-element>
            <info-card-element :label="$t('labels.home-language')">
                <template #tooltip>
                    <i18n-t keypath="labels.home-language-message.message" tag="span">
                        <template #conditions>
                            <ul>
                                <li>@vue('$t("labels.home-language-message.condition1")')</li>
                                <li>@vue('$t("labels.home-language-message.condition2")')</li>
                                <li>@vue('$t("labels.home-language-message.condition3")')</li>
                            </ul>
                        </template>
                    </i18n-t>
                </template>
                {{ $wisa['pupil']['home_language'] }}
            </info-card-element>
            <info-card-element :label="$t('labels.wisa.mother-tongue')">
                {{ $wisa['pupil']['mother_tongue'] }}
            </info-card-element>
            <info-card-element :label="$t('labels.wisa.nationality')">
                {{ $wisa['pupil']['nationality'] }}
            </info-card-element>
            <info-card-element :label="$t('labels.wisa.country_of_birth')">
                {{ $wisa['pupil']['country_of_birth'] }}
            </info-card-element>
            <info-card-element :label="$t('labels.national-register-number')">
                @if($wisa['pupil']['national_register_number'])
                    **.**.**-***.**
                @endif
            </info-card-element>
        </info-card>
    </v-col>
@endisset
