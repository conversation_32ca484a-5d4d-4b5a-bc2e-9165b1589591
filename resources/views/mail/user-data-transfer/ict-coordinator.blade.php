@extends('mail.mail-base')
@section('content')
    <h2>{{ __('emails.user-data-transfer.subject') }}</h2>
    <p>
        {{ __('emails.salutation') }} {{ $ictCoordinator->fullname }},
    </p>

    @yield('content')

    <p>
        <a href="{{ $url }}">{{ $url }}</a>
    </p>
    <p>
        {{ __('emails.user-data-transfer.email-body-ict-coordinator.line1', ['name' => $user->fullname]) }}<br>
        {{ __('emails.user-data-transfer.email-body-ict-coordinator.line2') }}<br>
        {{ __('emails.user-data-transfer.email-body-ict-coordinator.line3', ['email1' => $user->account_name, 'email2' => $user->email]) }}
        <br>
        {{ __('emails.user-data-transfer.email-body-ict-coordinator.line4') }}<br>
        {{ __('emails.user-data-transfer.email-body-ict-coordinator.line5.pre') }}<a href="mailto:<EMAIL>"><EMAIL></a>{{ __('emails.user-data-transfer.email-body-ict-coordinator.line5.post') }}
    </p>
    <p>
        {{ __('emails.closing-salutation') }}<br>
        {{ __('emails.closing-salutation-name') }}
    </p>
@endsection
