<html>
  <head>
    <link
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons"
      rel="stylesheet"
    />
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/vuetify/dist/vuetify.min.css" />
    <style>
      @font-face {
        font-family: 'proximanova';
        src:
          url('fonts/proximanova_regular_macroman/proximanova-regular-webfont.woff2')
            format('woff2'),
          url('fonts/proximanova_regular_macroman/proximanova-regular-webfont.woff') format('woff');
        font-weight: normal;
        font-style: normal;
      }

      @font-face {
        font-family: 'proximanova';
        src:
          url('fonts/proximanova_semibold_macroman/proximanova-semibold-webfont.woff2')
            format('woff2'),
          url('fonts/proximanova_semibold_macroman/proximanova-semibold-webfont.woff')
            format('woff');
        font-weight: bold;
        font-style: normal;
      }

      @font-face {
        font-family: 'londrina_solidregular';
        src: url('fonts/londrina_solid/LondrinaSolid-Regular.ttf');
      }
      * {
        font-family: 'proximanova', 'roboto', sans-serif;
      }
      .v-center {
        height: 100%;
        text-align: center;
        font-size: 0;
        white-space: nowrap;
      }

      .v-center::before,
      .v-center > * {
        display: inline-block;
        vertical-align: middle;
      }

      .v-center::before {
        content: '';
        height: 100%;
      }

      .error-panel {
        font-size: 18px;
        color: #444;
        position: relative;
        white-space: normal;
      }

      .error-panel .error-type {
        position: absolute;
        top: 110px;
        right: 70px;
        width: 140px;
        font-size: 18px;
        color: #999;
        margin-left: auto;
        margin-right: auto;
      }

      .error-panel .error-message {
        position: absolute;
        top: 175px;
        left: 460px;
        right: 80px;
        text-align: left;
      }

      .error-panel .error-message h1 {
        font-family: 'londrina_solidregular', arial, sans-serif;
        font-size: 36px;
        color: #c09;
      }

      .error-panel .error-message a {
        text-decoration: none;
        color: #037caf;
      }

      .logo {
        position: absolute;
        width: 100%;
        padding: 0 0 0 10%;
      }
      [v-cloak] {
        display: none;
      }
    </style>
  </head>
  <body style="min-width: 420px">
    <div id="application" v-cloak>
      <div class="page-container">
        <div class="md-bg-gray">
          <div class="v-center white">
            <div class="error-panel">
              <div class="logo">
                <img src="images/bingel-logo.png" />
              </div>
              <img src="images/404.png" />
              <div class="error-type">{{ $t('error') }} 503</div>
              <div class="error-message">
                <h1>{{ $t('title')}}</h1>
                <div>
                  <p>{{ $t('subtitle') }}</p>
                </div>
                <p>
                  <a href="javascript:history.back()"> <b>‹</b> {{ $t('returnMessage') }}</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="https://unpkg.com/vue/dist/vue.js"></script>
    <script src="https://unpkg.com/vuetify/dist/vuetify.min.js"></script>
    <script src="https://unpkg.com/vue-i18n/dist/vue-i18n.min.js"></script>
    <script>
      import { createApp } from 'vue';

      const translations = {
        en: {
          error: 'Error',
          title: 'Oops, something went wrong!',
          subtitle: 'Nothing to worry about, something went wrong with Bingel.',
          returnMessage: 'Back to the previous page',
        },
        nl: {
          error: 'Fout',
          title: 'Pagina niet gevonden',
          subtitle: 'Niet erg, er is bij Bingel iets fout gegaan.',
          returnMessage: 'Ga terug naar de vorige pagina',
        },
      };

      const app = createApp({
        el: '#application',
        i18n: i18n,
        data() {
          return {
            translations: translations,
          };
        },
      });

      app.use(VueI18n);

      const i18n = new VueI18n({
        locale: navigator.language.split('-')[0], // Get first part of locale string: en from en-US
        fallbackLocale: 'en',
        messages: translations,
      });

      app.use(Vuetify, {
        theme: {
          primary: '#4f80e1',
          secondary: '#4570c3',
          accent: '#2a4985',
          error: '#ff7863',
          info: '#4A90E2',
          success: '#27AE80',
          sidebarBlue: '#4f80e1',
          sidebarSelection: '#4c72bd',
          topheaderBlue: '#2a4985',
        },
      });
    </script>
  </body>
</html>
