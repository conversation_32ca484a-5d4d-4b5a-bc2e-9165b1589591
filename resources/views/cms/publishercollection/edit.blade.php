@extends('cms.template')

@section('title')
    Edit publisher collection
@endsection

@php($isPublished = $collection['published_at'] !== null);
@section('header')
    @include('cms.partials.title', [
        'title' => 'Publisher Collection: ' . $collection['name'],
        'subtitle' => $isPublished ? 'Published at: ' . $collection['published_at'] : 'Not published',
        'url' => $collection['canPublish'] === false ? null : route('web.cms.collections.toggle', $collection['id']),
        'button' => $isPublished ? 'warning' : 'success',
        'text' => $isPublished ? 'Unpublish' : 'Publish'
    ])
@endsection

@section('content')
    <div class="row">
        <div class="v-col-md-12">
            @if ($isPublished)
                @include('cms.publishercollection.partials.details')
            @else
                @include('cms.publishercollection.partials.form')
            @endif

            @include('cms.publishercollection.partials.chapters')
        </div>
    </div>
@endsection
