@isset($collection['id'])
<div class="details">
    <div class="details-group">
        <label>Uid</label>
        {{ $collection['id'] }}
    </div>
</div>
@endisset
@isset($collection['methodIdentifier'])
<div class="details">
    <div class="details-group">
        <label>Method Identifier</label>
        {{ $collection['methodIdentifier'] }}
    </div>
</div>
@endisset

<form method="POST" action="{{ $collection['id'] ? route('web.cms.collections.update', ['collectionv2' => $collection['id']]) : route('web.cms.collections.store') }}" enctype="multipart/form-data">
    @csrf

    @isset($collection['id'])
        @method(\Illuminate\Http\Request::METHOD_PUT)
    @endisset

    <div class="form-group">
        <label for="name" class="control-label">{{ trans('labels.cms.name') }}</label>
        <input type="text" name="name" value="{{ old('name', $collection['name']) }}" class="form-control" />
    </div>

    <div class="form-group">
        <label for="target_audience[]" class="control-label">{{ trans('labels.cms.target-audience') }}</label>
        <select name="target_audience[]" class="form-control" :multiple="true">
            @foreach ($targetAudiences as $id => $name)
                <option value="{{ $id }}" {{ in_array($id, $collectionTargetAudienceIds) ? 'selected' : null }}>
                    {{ $name }}
                </option>
            @endforeach
        </select>
    </div>

    <div class="form-group">
        <label for="publisher_id" class="control-label">{{ trans('labels.cms.publisher') }}</label>

        @if ($publisher = auth()->user()->publisher)
            <input type="text" value="{{ $collection['activatedFrom']['publisherName'] }}" class="form-control" disabled />
            <input type="hidden" name="publisher_id" value="{{ $collection['activatedFrom']['publisherId'] ?? $publisher->uid }}" />
        @else
            <select name="publisher_id" class="form-control">
                @foreach ($publishers as $id => $name)
                    <option value="{{ $id }}" {{ $id === old('publisher_id', $collection['activatedFrom']['publisherId']) ? 'selected' : '' }}>
                        {{ $name }}
                    </option>
                @endforeach
            </select>
        @endif
    </div>

    <div class="form-group">
        <label for="cover" class="control-label">{{ trans('labels.cms.cover') }}</label>
        <input type="file" name="cover" />

        @if(empty($collection['cover_large']) === false)
            <div class="current-file">
                <a href="{{ $collection['cover_large'] }}" target="_blank">
                    {{ trans('labels.cms.view-current-cover') }}
                </a>
            </div>
        @endif
    </div>

    <div class="row">
        <div class="v-col-md-6">
            <button type="submit" class="btn btn-success">{{ $collection['id'] ? trans('labels.cms.update') : trans('labels.cms.create') }}</button>
        </div>
        <div class="v-col-md-6 form-cancel">
            @isset($collection['id'])
                @include('cms.partials.delete-link', [
                    'url' => route('web.cms.collections.delete', $collection['id']),
                    'role' => 'admin',
                    'label' => trans('labels.cms.delete'),
                    'published' => $collection['published_at'] !== null,
                    'small' => false,
                ])
            @else
                <a href="{{route('web.cms.collections')}}"
                   class="btn btn-danger">
                    {{ trans('labels.cms.cancel') }}
                </a>
            @endisset
        </div>
    </div>
</form>
