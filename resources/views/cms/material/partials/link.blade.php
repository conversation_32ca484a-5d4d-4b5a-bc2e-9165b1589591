@use(Illuminate\Support\Str)
<input type="hidden" name="link_type" value="{{ old('link_type', Str::lower($material['link_type']) ?? 'default') }}" />

<div class="form-group">
    <label for="description" class="control-label">{{ trans('labels.cms.name') }}</label>
    <input type="text" name="description" value="{{ old('description', $material['description']) }}" class="form-control" />
</div>

<div class="form-group">
    <label for="url" class="control-label">{{ trans('labels.cms.url') }}</label>
    <input type="text" name="url" value="{{ old('url', $material['url']) }}" class="form-control" />
</div>

<div class="form-group">
    <label for="provider" class="control-label">{{ trans('labels.cms.provider') }}</label>
    <input type="text" name="provider" value="{{ old('provider', $material['provider']) }}" class="form-control" />
</div>
