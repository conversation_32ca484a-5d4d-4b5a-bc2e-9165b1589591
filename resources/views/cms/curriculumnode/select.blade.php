@extends('cms.template')

@section('title')
    Lesson goals - {{ $network->name }}
@endsection

@section('header')
    @include('cms.partials.title', [
        'title' => sprintf('Lesson Goals: %s (%s)', $network->name, $curriculumType->name),
        'subtitle' => $record['name'],
    ])
    @include('cms.partials.breadcrumb', [
        'collection' => $collection,
        'chapter' => $chapter,
        'record' => $record,
    ])
@endsection

@section('content')
    <div class="row">
        <div class="v-col-md-12">
            <form id="curriculumnodeForm" method="GET" action="{{ route('web.cms.records.curriculumnodes.update', ['collectionv2' => $collection['id'], 'chapterv2' => $chapter['id'], 'recordv2' => $record['id'], 'network' => $network->uid]) }}">
                <input type="hidden" name="network_uid" value="{{ $network->uid }}" />

                <div class="form-group">
                    <label for="add_goals_auto_complete" class="control-label">{{ trans('labels.cms.add-goals') }}</label>
                    <input type="text" id="add_goals_auto_complete" autocomplete="off" name="add_goals_auto_complete" value="{{ old('add_goals_auto_complete') }}" class="form-control" />
                </div>

                @include('cms.curriculumnode.partials.submit')

                <table class="table" id="nodeTable">
                    <thead>
                    <tr>
                        <th></th>
                        <th width="20%">Code</th>
                        <th>Title</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($nodes as $node)
                        <tr>
                            @if($node->type == \Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType::Goal)
                                <td>
                                    <div class="checkbox">
                                        <input type="checkbox" name="curriculumnode_uid[]" value="{{ $node->uid }}" checked>
                                    </div>
                                </td>
                                <td class="node-{{ $node->type->name }}" data-sort="{{ $node->sequence }}">
                                    {{ $node->code }}
                                </td>
                                <td>
                                    {!! $node->description ?? $node->name !!}
                                </td>
                            @else
                                <td></td>
                                <td colspan="3" class="node-{{ $node->type->name }}" data-sort="{{ $node->sequence }}">
                                    <label>
                                        {{ $node->name }}
                                    </label>
                                </td>
                                <td class="hidden"></td>
                            @endif
                        </tr>
                    @endforeach
                    </tbody>
                </table>

                @include('cms.curriculumnode.partials.submit')
            </form>
        </div>
    </div>

@endsection
