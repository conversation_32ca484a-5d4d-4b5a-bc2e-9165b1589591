@extends('cms.template')

@section('title')
    Lesson goals
@endsection

@section('header')
    @include('cms.partials.title', [
        'title' => 'Lesson Goals: ' . $record['name'],
        'url' => route('web.cms.records.edit', ['collectionv2' => $collection['id'], 'chapterv2' => $chapter['id'], 'recordv2' => $record['id']]),
        'button' => 'info',
        'text' => $collection['is_published'] ? 'View Record' : 'Edit Record',
    ])
    @include('cms.partials.breadcrumb', [
        'collection' => $collection,
        'chapter' => $chapter,
        'record' => $record,
    ])
@endsection

@section('content')
    <div class="row">
        <div class="v-col-md-12">
            @php ($counter = 0)
            @foreach($networks as $code => $network)
                @foreach($network['curriculum_types'] ?? [] as $curriculumType)
                    @php($nodes = $goals[$network['uid']][$curriculumType->value] ?? [])
                    @include('cms.partials.section', [
                        'title' => sprintf('%s (%s)', $code, $curriculumType->name),
                        'url' => $collection['is_published'] ?
                            null :
                            route('web.cms.records.curriculumnodes.select', [
                                'collectionv2' => $collection['id'],
                                'chapterv2' => $chapter['id'],
                                'recordv2' => $record['id'],
                                'network' => $network['uid'],
                                'curriculum_type' => $curriculumType->name
                            ]),
                        'text' => 'Select Goals',
                        'line' => $counter === 0 ? false : true,
                    ])
                    @if(empty($nodes) === false)
                        <table class="table table-striped curriculumnodes">
                            <thead>
                            <tr>
                                <th width="20%">Code</th>
                                <th>Title</th>
                            </tr>
                            </thead>

                            <tbody>
                            @foreach($nodes as $node)
                                <tr>
                                    <td>{{ $node['code'] }}</td>
                                    <td>{!! $node['description'] ?? $node['name'] !!}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    @else
                        <p>No goals linked.</p>
                    @endif
                    @php ($counter++)
                @endforeach
            @endforeach
        </div>
    </div>
@endsection

@section('styles')
    <style>
        @media (max-width: 992px) {
            table.curriculumnodes tr td:nth-child(1):before {
                content: "Code";
            }

            table.curriculumnodes tr td:nth-child(2):before {
                content: "Title";
            }
        }
    </style>
@endsection
