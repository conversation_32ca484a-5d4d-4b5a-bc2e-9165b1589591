@extends('care.groups.pupils.template')

@section('group-content')
        <v-row justify-end style="height: 60px;" class="text-right pt-2">
            <v-btn :icon="isOpen ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"
                   variant="flat"
                   @click="toggleIsOpen"
                   class="mr-2">
            </v-btn>
            <v-spacer></v-spacer>
        </v-row>
        @include('care.components.inactive-pupil-alert')
        <v-row>
            <v-col cols="12" class="pa-0 pb-3">
                <v-card style="overflow: hidden;">
                    <v-card-text>
                        <timeline item-url="{{route('web.care.overview.pupils.timeline', Route::current()->parameters())}}"
                        ></timeline>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
@endsection

@section('pupil-list')
    @include(
        'common.components.pupil-list',
        ['redirectRoute' => 'web.care.overview.pupils.timeline']
    )
@endsection
