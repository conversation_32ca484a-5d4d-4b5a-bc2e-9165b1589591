@extends('care.groups.dashboard.template')
@section('title', trans('navigation.side.care.dashboard'))
@section('dashboard-list')
    @section('group-list')
        @include('common.components.filtered-list', [
            'selectedUid' => $selectedGroupUidFromRoute->uid ?? null,
            'items' => $page['props']['groupSwitcherGroups'],
            'title' => 'titles.group-list',
            'placeholder' => 'labels.search-groups',
        ])
    @show
@endsection
