@use(App\Models\Feature\FeatureToggle)
@use(App\Models\Feature\Feature)
@extends('care.template')

@isset($pupil)
    @section('navbar-top-right')
        @can('updatePupilCareInfo', $pupil)
            <v-btn color="primary"
                   variant="elevated"
                   text-color="white"
                   href="{{ route('web.common.care-info.edit', ['pupil' => $pupil->uid]) }}" prepend-icon="edit">
                {{ trans('labels.edit') }}
            </v-btn>
        @endcan
    @endsection
@endisset

@section('content')
    @php($isOverviewPage = $isOverviewPage ?? (Route::currentRouteName() === $overviewPageRoute))
    <v-container :fluid="true" fill-height class="pt-2 min-width">
        <pupil-list-collapse-wrapper>
            <template v-slot="{isOpen, toggleIsOpen}" >
                <div style="width: 100%">
                    <v-row>
                        <v-col class="full-height overflow-y-auto pt-0 animated" fillheight :class="[isOpen ? 'v-col-4 v-col-md-3': 'v-col-0']">
                            <div class="pb-3">
                                <button-list title="{{ trans('titles.display') }}">
                                    <v-list-item ripple
                                                 href="{{ route($overviewPageRoute) }}"
                                                 active-class="bg-primary text-white"
                                                 :active="{{ $isOverviewPage ? 'true' : 'false' }}"
                                    >
                                        <template v-slot:prepend>
                                            <v-icon icon="view_module" />
                                        </template>
                                        <v-list-item-title>
                                            <v-list-item-title>
                                                {{ trans('labels.overview') }}
                                            </v-list-item-title>
                                        </v-list-item-title>
                                        <template v-slot:append>
                                            <v-list-item-action>
                                                <v-icon icon="keyboard_arrow_right" />
                                            </v-list-item-action>
                                        </template>
                                    </v-list-item>
                                </button-list>
                                @yield('dashboard-list')
                            </div>
                        </v-col>
                        <v-col class="full-height overflow-y-auto animated" fillheight :class="[isOpen ? 'v-col-8 v-col-md-9': 'v-col-12']">
                            <v-container :fluid="true" class="pa-0 pb-3">
                                <v-row style="height: 60px;" class="align-center mt-2">
                                    <v-btn :icon="isOpen ? 'keyboard_arrow_left' : 'keyboard_arrow_right'"
                                           variant="flat"
                                           @click="toggleIsOpen"
                                           class="mr-2">
                                    </v-btn>
                                    @if(isset($tabs) && count($tabs) > 1)
                                        <div class="py-0 my-0">
                                            <tabs :tabs="{{json_encode($tabs)}}" model-value="{{$currentTab}}" :mobile-breakpoint="20"></tabs>
                                        </div>
                                    @endif
                                </v-row>
                                @isset($pupil)
                                    <info-sheet-filters :wisa-data="{{json_encode($wisa)}}" :without-care-filter="true"></info-sheet-filters>
                                @endisset
                                @hasSection('custom-chart')
                                    @yield('custom-chart')
                                @else
                                <isotope-grid>
                                    <v-row class="isoGrid">
                                        <v-col col="6" sm="6" md="1" class="isoSizer"></v-col>
                                        @isset($pupil)
                                            @include('common.care-info.show-basic-content')
                                        @endisset
                                        @sectionMissing('custom-chart')
                                            @include(
                                                'common.components.charts',
                                                [
                                                    'optionsArray' => $optionsArray,
                                                    'emptyState' => isset($pupil) ? null : $emptyState,
                                                    'fullWidth' => true,
                                                ]
                                            )
                                        @endif
                                    </v-row>
                                    @endif
                                </isotope-grid>
                            </v-container>
                        </v-col>
                    </v-row>
                </div>
            </template>
        </pupil-list-collapse-wrapper>
    </v-container>
@endsection
