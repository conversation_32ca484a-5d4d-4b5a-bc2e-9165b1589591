@extends('care.tasks.template')

@section('navbar-top-right')
    <create-task-button></create-task-button>
@endsection

@section('content')
    <task-sidebar user-search-api="{{route('web.common.teachers.search')}}"></task-sidebar>
    <v-container :fluid="true" class="pt-2">
        <task-filters></task-filters>
        <v-row>
            <v-col cols="12">
                <v-card style="overflow: hidden;">
                    <task-list base-url="{{route('web.care.tasks.store')}}"
                               :user="{{$user}}"
                               tasks-url="{{route('web.care.tasks.follow-up.fetch')}}"
                               :use-filters="true"
                    >
                    </task-list>
                </v-card>
            </v-col>
        </v-row>
    </v-container>

@endsection
