@extends('print.template')

@section('content')
    <h3>{{$title}} - {{$titleData}}</h3>
    @foreach($tables as $table)
    @php($headerGroups = $table['headerGroups'])
    @php($secondaryHeaderGroups = $table['secondaryHeaderGroups'])
    @php($headers = $table['headers'])
    <table
        @if($loop->first)
            style="page-break-inside: initial"
        @endif
    >
        <thead>
            @if($headerGroups->isNotEmpty())
                <tr>
                    <th class="border-right" style="z-index:2;"></th>
                    @foreach($headerGroups as $headerGroup)
                    <th colspan="{{ $headerGroup['count'] }}"
                        class="no-hover @if($loop->index !== 0) group-start @endif text-center">
                            {{ $headerGroup['name'] }}
                    </th>
                    @endforeach
                </tr>
            @endif
            @if($secondaryHeaderGroups->isNotEmpty())
                <tr>
                    <th class="border-right"></th>
                    @foreach($secondaryHeaderGroups as $secondaryHeaderGroup)
                    <th colspan="{{ $secondaryHeaderGroup['count'] }}"
                        class="no-hover @if($secondaryHeaderGroup['startOfGroup'] && $loop->index !== 0) group-start @endif text-center">
                        {{$secondaryHeaderGroup['name']}}
                    </th>
                    @endforeach
                </tr>
            @endif
            <tr>
                @foreach($headers as $header)
                <th class="text-center @if($header['indexInGroup'] === 0 && $loop->index !== 1) group-start @endif ">
                    @if(array_key_exists('title', $header))
                        {{$header['title']}}
                    @endif
                </th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach($items as $item)
                <tr>
                    @foreach($headers as $header)
                        <td class="@if($loop->index !== 0) text-center @else text-left @endif
                            @if($header['indexInGroup'] === 0 && $loop->index !== 1) group-start @else border-right @endif"
                        >
                            @if(array_key_exists($header["key"], $item))
                                {{ $item[$header["key"]] }}
                            @endif
                        </td>
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>
    @endforeach
@endsection

@section('styles')
    <style>
        .container {
            margin-left: initial !important;
            margin-right: initial !important;
            padding: initial !important;
        }
        table {
            border: none;
            width: 100%;
            text-align: left;
            border-collapse: collapse;
            page-break-after: always;
            page-break-inside: avoid;
        }
        table td, table th {
            border: none;
            padding: 8px 8px;
        }
        table tbody td {
            font-size: 13px;
            color: #000000;
            border: none;
            border-bottom: thin solid rgba(0, 0, 0, 0.12);
        }
        table thead th {
            font-size: 15px;
            font-weight: bold;
            color: #000000;
            border: none;
        }
        .group-start {
            border-left: thin solid #BABABA;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left;
        }
        border-right {
            border-right: thin solid #BABABA;
        }
    </style>
@endsection
