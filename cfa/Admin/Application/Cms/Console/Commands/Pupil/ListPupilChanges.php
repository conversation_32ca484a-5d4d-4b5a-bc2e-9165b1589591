<?php

namespace Cfa\Admin\Application\Cms\Console\Commands\Pupil;

use App\Console\Commands\Command;
use App\Constants\CacheStores;
use App\Notifications\PupilChangeNotification;
use Carbon\Carbon;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\PupilChange\PupilChangeHandledStatus;
use Cfa\Common\Domain\User\PupilChange\PupilChangeNotification as PupilChangeNotificationModel;
use Cfa\Common\Domain\User\PupilChange\PupilChangeNotificationType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use stdClass;

class ListPupilChanges extends Command
{
    private const MAX_ERRORS = 5;

    /** @var string */
    protected $description = 'Sends a list of all unhandled pupil_changes to slack grouped by school.';

    /** @var string */
    protected $name = 'tms:db:send-pupil-changes-to-slack';

    private Carbon $now;

    private int $schoolIctAdministratorRoleId;

    public function handle(): void
    {
        $this->now = Carbon::now();
        $this->schoolIctAdministratorRoleId = Role::getRepository()
            ->findRoleByName(RoleName::SchoolIctAdministrator)->id;

        $pupilChanges = DB::table('pupil_changes')
            ->whereNull('handled_at')
            ->whereNull('seen_at')
            ->where('handled_status', PupilChangeHandledStatus::Unhandled)
            ->leftJoin('care_infos', 'care_infos.pupil_id', '=', 'pupil_changes.user_id')
            ->leftJoin('schools', 'care_infos.school_id', '=', 'schools.id')
            ->distinct()
            ->select([
                'care_infos.school_id',
                'schools.name',
                'schools.partner_number',
                'pupil_changes.*',
            ])
            ->get()
            ->groupBy('school_id');

        $schoolToBlacklist = DB::table('pupil_changes')
            ->where('handled_status', PupilChangeHandledStatus::HandledAutomatically)
            ->where('handled_at', '>', Carbon::now()->subDay())
            ->join('care_infos', 'care_infos.pupil_id', '=', 'pupil_changes.user_id')
            ->join('schools', 'care_infos.school_id', '=', 'schools.id')
            ->distinct()
            ->select([
                'care_infos.school_id',
                DB::raw('count(pupil_changes.id) as count'),
                DB::raw('max(handled_at) as handled_at'),
            ])
            ->groupBy('school_id')
            ->having('count', '>', self::MAX_ERRORS)
            ->get()
            ->keyBy('school_id');

        $schoolToBlacklist->each(function (stdClass $schoolObject): void {
            $ictCoordinatorsForSchool = $this->getIdsIctCoordinatorsForSchool($schoolObject->school_id);
            $ictCoordinatorsForSchool->each(function (int $ictCoordinatorId) use ($schoolObject): void {
                $this->flagIctCoordinatorAsBlockedWhenNeeded(
                    $schoolObject->school_id,
                    $ictCoordinatorId,
                    Carbon::createFromFormat('Y-m-d H:i:s', $schoolObject->handled_at),
                    PupilChangeNotificationType::WithoutData,
                );
            });
        });

        $pupilChanges->each(function (Collection $schoolPupilChanges): void {
            $pupilChangeData = $schoolPupilChanges->first();
            $lastPupilChange = $schoolPupilChanges->max('created_at');
            $numberOfPupilChanges = $schoolPupilChanges->count();
            if ($numberOfPupilChanges > self::MAX_ERRORS) {
                $ictCoordinatorsForSchool = $this->getIdsIctCoordinatorsForSchool($pupilChangeData->school_id);
                $ictCoordinatorsForSchool
                    ->each(function (int $ictCoordinatorId) use ($lastPupilChange, $pupilChangeData): void {
                        $this->flagIctCoordinatorAsBlockedWhenNeeded(
                            $pupilChangeData->school_id,
                            $ictCoordinatorId,
                            Carbon::createFromFormat('Y-m-d H:i:s', $lastPupilChange),
                            PupilChangeNotificationType::WithData,
                        );
                    });
            }
            if ($this->createCacheKeyForSchool($pupilChangeData->school_id)) {
                $this->notify(
                    new PupilChangeNotification(
                        [
                            'School' => $pupilChangeData->name . ' (' . $pupilChangeData->partner_number . ')',
                            'Number of unhandled PupilChanges' => $numberOfPupilChanges,
                            'Nova link' => route('web.tms.home') . '/nova/resources/schools/' .
                                $pupilChangeData->school_id,
                        ],
                    ),
                );
            }
        });
    }

    private function flagIctCoordinatorAsBlockedWhenNeeded(
        int $schoolId,
        int $ictCoordinatorId,
        Carbon $lastPupilChange,
        PupilChangeNotificationType $type,
    ): void {
        $pupilChangeNotificationModel = $this->getPupilChangeNotification(
            $schoolId,
            $ictCoordinatorId,
            $type,
        );

        if ($pupilChangeNotificationModel instanceof PupilChangeNotificationModel) {
            if (
                $pupilChangeNotificationModel->trashed() &&
                $pupilChangeNotificationModel->blocked_at > $lastPupilChange
            ) {
                return;
            }
            $pupilChangeNotificationModel->deleted_at = null;
            $pupilChangeNotificationModel->blocked_at = $this->now;
            $pupilChangeNotificationModel->save();
        }
    }

    private function getPupilChangeNotification(
        int $schoolId,
        int $ictCoordinatorId,
        PupilChangeNotificationType $type,
    ): PupilChangeNotificationModel {
        return PupilChangeNotificationModel::unguarded(fn() => PupilChangeNotificationModel::withoutGlobalScopes()
            ->firstOrNew(
                [
                    'school_id' => $schoolId,
                    'user_id' => $ictCoordinatorId,
                    'type' => $type->value,
                ],
            ));
    }

    private function getIdsIctCoordinatorsForSchool(int $schoolId): Collection
    {
        return Career::whereSchoolId($schoolId)
            ->where('startdate', '<=', $this->now)
            ->where(function (Builder $subQuery): void {
                $subQuery->whereNull('enddate')
                    ->orWhere('enddate', '>=', $this->now);
            })
            ->where('role_id', $this->schoolIctAdministratorRoleId)
            ->get('user_id')
            ->pluck('user_id');
    }

    private function createCacheKeyForSchool(int $schoolId): bool
    {
        return Cache::store(CacheStores::MAINTENANCE)
            ->add(
                $this->getCacheKey($schoolId),
                true,
                Carbon::tomorrow()
                    ->setTime(8, 0),
            );
    }

    private function getCacheKey(int $schoolId): string
    {
        return self::class . '-school:' . $schoolId;
    }
}
