<?php

namespace Cfa\Admin\Application\Cms\Services\Auth;

use App\Constants\SessionKeys;
use Cfa\Common\Domain\School\SchoolMfaStatus;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Common\Domain\User\Mfa\Status\MfaStatus;
use Cfa\Common\Domain\User\User;
use Cfa\Common\Domain\User\UserRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

use function app;

class TMSApiAuthService
{
    public function authByUid(string $userUid): void
    {
        $user = app(UserRepositoryInterface::class)->getFullUser($userUid);
        if ($user === null) {
            return;
        }

        $authGuard = Auth::guard('web');

        if ($authGuard->id() === $user->uid) {
            return;
        }

        Session::invalidate();
        $authGuard->login($user);
        // Logout from 2FA when asking for a new session so the user has to reenter 2FA code.
        Session::remove(SessionKeys::MFA_VERIFIED);

        if ($this->shouldSetupMfa($user)) {
            Session::put(SessionKeys::MFA_REQUESTED, true);
        }
    }

    private function shouldSetupMfa(User $user): bool
    {
        if ($user->hasMfaEnabled() === false) {
            return SchoolSettings::whereIn('school_id', $user->activeSchools()->pluck('id'))
                ->where('type', SchoolSettingsType::MfaStatus)
                ->where('value', SchoolMfaStatus::Active->value)
                ->exists();
        }

        return $user->userMfa->status === MfaStatus::MfaRequested;
    }
}
