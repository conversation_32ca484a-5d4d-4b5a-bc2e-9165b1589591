<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Http\Requests\FormRequest;
use Override;

class PlannerCollectionImportRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return [
            'file' => 'required',
            'file.*' => [
                'file',
                'mimes:zip',
                'max:500000',
            ],
            'oldIdentifier' => 'prohibits:v1originalIdentifier',
        ];
    }
}
