<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\CmsPlannerCollectionResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Support\Arr;
use Illuminate\View\View;

use function old;
use function uuid;
use function view;

class PlannerCollectionEditController extends Controller
{
    public function __invoke(
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        FormRequest $request,
        ?PlannerCollection $plannerCollection = null,
    ): View {
        if ($plannerCollection === null) {
            $plannerCollection = new PlannerCollection();
        }

        $plannerCollection = $plannerCollectionRepository->loadAllDataAndRelations($plannerCollection);
        $plannerCollection->setAttribute('collectionUid', $plannerCollection->uid ?? uuid());
        $collection = new CmsPlannerCollectionResource($plannerCollection)
            ->toResponse($request)
            ->getData(true);

        $targetAudiences = TargetAudience::orderBy('type')
            ->orderBy('natural_study_year')
            ->get()
            ->mapWithKeys(fn(TargetAudience $targetAudience): array => [$targetAudience->uid => $targetAudience->name]);

        return view(
            'cms.publishercollection.' . ($plannerCollection->id === null ? 'create' : 'edit'),
            [
                'collection' => $collection,
                'publishers' => [null => '-'] + Publisher::pluck('name', 'uid')->all(),
                'targetAudiences' => $targetAudiences->all(),
                'collectionTargetAudienceIds' => old(
                    'target_audience',
                    Arr::pluck($collection['targetAudiences'], 'id'),
                ),
            ],
        );
    }
}
