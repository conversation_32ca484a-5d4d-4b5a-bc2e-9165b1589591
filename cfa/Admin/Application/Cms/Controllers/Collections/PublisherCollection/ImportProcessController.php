<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use Cfa\Admin\Application\Cms\Controllers\Collections\Import\AbstractImportController;
use Cfa\Planner\Application\Jobs\ProcessImport;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

use function app;

class ImportProcessController extends AbstractImportController
{
    public function __invoke(PlannerCollectionImportRequest $request): RedirectResponse|Redirector
    {
        $paths = $this->handleUpload($request, 'collections', 'tmp');

        $paths->each(function ($path) use ($request): void {
            // Send to queue for processing.
            dispatch(app(ProcessImport::class, [
                'collectionZip' => $path,
                'oldIdentifier' => $request->get('oldIdentifier'),
                'allowDeletingCollectionItems' => $request->exists('allowDeletingCollectionItems'),
                'v1originalIdentifier' => $request->get('v1originalIdentifier'),
            ]));
        });

        $this->flashMessageForQueuedFiles($paths);

        return redirect(route('web.cms.import'));
    }
}
