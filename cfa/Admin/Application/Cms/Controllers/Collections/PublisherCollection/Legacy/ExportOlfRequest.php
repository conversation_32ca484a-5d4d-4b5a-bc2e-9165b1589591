<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection\Legacy;

use App\Http\Requests\FormRequest;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Override;

use function is_numeric;

class ExportOlfRequest extends FormRequest
{
    #[Override]
    public function authorize(): bool
    {
        return $this->user() instanceof CmsUser
            || $this->getCollection()->userIsOwner($this->user());
    }

    public function getCollection(): PlannerCollection
    {
        $collectionId = $this->route('collection');

        if (is_numeric($collectionId)) {
            return PlannerCollection::findOrFail($collectionId);
        }

        return PlannerCollection::where(['uid' => $collectionId])->firstOrFail();
    }
}
