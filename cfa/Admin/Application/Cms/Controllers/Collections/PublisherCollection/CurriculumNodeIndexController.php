<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetworkRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Illuminate\Support\Collection;
use Illuminate\View\View;

use function view;

class CurriculumNodeIndexController extends Controller
{
    public function __construct(private readonly EducationalNetworkRepositoryInterface $educationalNetworkRepository) {}

    public function __invoke(
        FormRequest $request,
        PlannerCollection $plannerCollection,
        Chapter $chapter,
        Record $record,
    ): View {
        return view(
            'cms.curriculumnode.index',
            [
                'collection' => [
                    'id' => $plannerCollection->uid,
                    'name' => $plannerCollection->name,
                    'is_published' => $plannerCollection->published_at !== null,
                ],
                'chapter' => ['id' => $chapter->uid, 'name' => $chapter->name],
                'record' => ['id' => $record->uid, 'name' => $record->name],
                'networks' => $this->getEducationalNetworks(),
                'goals' => $this->getCurriculumNodesByNetworkAndType($record),
            ],
        );
    }

    private function getCurriculumNodesByNetworkAndType(Record $record): array
    {
        $curriculumNodes = CurriculumNode::query()
            ->join('educationalnetworks', 'educationalnetwork_id', 'educationalnetworks.id')
            ->whereIn('curriculumnodes.id', $record->curriculumnodes)
            ->get(['educationalnetworks.uid as educational_network_uid', 'curriculum_type', 'curriculumnodes.*']);

        return $curriculumNodes
            ->groupBy('educational_network_uid')
            ->map
            ->groupBy(fn(CurriculumNode $curriculumNode): int => $curriculumNode->curriculum_type->value)
            ->toArray();
    }

    private function getEducationalNetworks(): Collection
    {
        return $this->educationalNetworkRepository
            ->getUsedEducationalNetworksWithSupportedCurriculumTypes()
            ->map(
                fn(EducationalNetwork $educationalNetwork): array =>
                    $educationalNetwork->only(['uid', 'curriculum_types']),
            );
    }
}
