<?php

namespace Cfa\Admin\Application\Cms\Controllers\Collections\PublisherCollection;

use App\Http\Requests\FormRequest;
use Override;

class PublisherCollectionSaveRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => 'required',
                'publisher_id' => 'required|string|exists:publishers,uid',
                'cover' => 'nullable|image',
            ],
        );
    }
}
