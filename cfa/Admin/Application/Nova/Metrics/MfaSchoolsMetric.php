<?php

namespace Cfa\Admin\Application\Nova\Metrics;

use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

class MfaSchoolsMetric extends StatisticsMetric
{
    /** {@inheritdoc} */
    public function calculate(NovaRequest $request)
    {
        $cachedStatistics = $this->getCachedStatistics();

        if (isset($cachedStatistics['mfaSchools'])) {
            return $this->result($cachedStatistics['mfaSchools'])
                ->allowZeroResult();
        }

        return $this->result(0);
    }

    /** {@inheritdoc} */
    #[Override]
    public function uriKey(): string
    {
        return 'mfa-schools';
    }

    /** {@inheritdoc} */
    #[Override]
    public function name()
    {
        return trans('nova.metrics.mfa-schools');
    }
}
