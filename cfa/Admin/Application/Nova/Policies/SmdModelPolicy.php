<?php

namespace Cfa\Admin\Application\Nova\Policies;

use Cfa\Admin\Application\Nova\Resources\Resource;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Override;

class SmdModelPolicy extends DefaultPolicy
{
    #[Override]
    public function view(CmsUser $cmsUser, Resource $resource): bool
    {
        return $this->viewAny($cmsUser);
    }

    #[Override]
    public function viewAny(CmsUser $cmsUser): bool
    {
        return $cmsUser->checkPermission(CmsUserPermission::Impersonate);
    }
}
