<?php

namespace Cfa\Admin\Application\Nova\Policies;

use Cfa\Admin\Application\Nova\Resources\Resource;
use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Laravel\Nova\Actions\Action;
use Override;

class QueryExportPolicy extends DefaultPolicy
{
    #[Override]
    public function viewAny(CmsUser $cmsUser): bool
    {
        return $cmsUser->checkPermission(CmsUserPermission::DownloadQueryExports);
    }

    #[Override]
    public function runAction(CmsUser $cmsUser, Resource $resource, Action $action): bool
    {
        return $cmsUser->checkPermission(CmsUserPermission::DownloadQueryExports);
    }
}
