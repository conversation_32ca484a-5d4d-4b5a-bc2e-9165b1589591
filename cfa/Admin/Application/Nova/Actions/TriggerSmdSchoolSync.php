<?php

namespace Cfa\Admin\Application\Nova\Actions;

use App\Constants\Queues;
use Cfa\Common\Application\Services\Smd\SmdApiService;
use Cfa\Common\Domain\School\School;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Override;

class TriggerSmdSchoolSync extends Action implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct()
    {
        $this->queue = Queues::MEDIUM_PRIORITY;
    }

    /**
     * Trigger SMD Sync for all schools in collection.
     */
    public function handle(ActionFields $fields, Collection $schools): ActionResponse
    {
        $smdApiService = app(SmdApiService::class);
        $schools->each(function (School $school) use ($smdApiService): void {
            try {
                $smdApiService->publishFullBingelSchool($school)->wait();
                $this->markAsFinished($school);
            } catch (Exception $exception) {
                $this->markAsFailed($school, $exception);
            }
        });

        return Action::message(trans('nova.actions.smd_sync_triggered'));
    }

    /**
     * {@inheritdoc}
     *
     * @return string
     */
    #[Override]
    public function name()
    {
        return trans('nova.actions.trigger_school');
    }

    /** {@inheritdoc} */
    #[Override]
    public function authorizedToSee(Request $request)
    {
        return $request->user()->checkRole('admin');
    }
}
