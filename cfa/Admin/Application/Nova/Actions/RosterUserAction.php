<?php

namespace Cfa\Admin\Application\Nova\Actions;

use App\Constants\Queues;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Services\CalendarItem\RosterUserService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

class RosterUserAction extends Action implements ShouldQueue
{
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Indicates if this action is only available on the resource detail view.
     *
     * @var bool
     */
    public $onlyOnDetail = true;

    /**
     * @var GroupRepositoryInterface
     */
    private $groupRepository;

    public function __construct(GroupRepositoryInterface $groupRepository)
    {
        $this->queue = Queues::LOW_PRIORITY;
        $this->groupRepository = $groupRepository;
    }

    /**
     * Link group to rosters and calendaritems for selected user.
     *
     * @param ActionFields $fields The fields in the action.
     * @param Collection $users The selected users.
     */
    public function handle(ActionFields $fields, Collection $users): ActionResponse
    {
        $rosterUserService = app(RosterUserService::class);
        $users->first(function (User $user) use ($rosterUserService, $fields): void {
            $group = Group::find($fields->get('group'));
            try {
                $rosterUserService->linkGroupToCalendarItems($user, $group);
                $this->markAsFinished($user);
            } catch (Exception $e) {
                $this->markAsFailed($user, $e);
            }
        });

        return Action::message(trans('nova.actions.group_linked_to_roster_for_user'));
    }

    #[Override]
    public function name(): string
    {
        return trans('nova.actions.link_group_to_roster');
    }

    #[Override]
    public function fields(NovaRequest $request): array
    {
        $groups = $this->getGroupsWithSchoolName();

        return [
            Select::make(trans('nova.fields.choose_group'), 'group')->options($groups)->rules('required'),
        ];
    }

    /**
     * Get the groups with schoolname from careers for the selected staff.
     */
    private function getGroupsWithSchoolName(): array
    {
        if ($resourceId = request()->get('resourceId')) {
            $user = User::with([
                'careers.school',
                'careers.role.permissions',
                'schoolUserAccess',
            ])->findOrFail($resourceId);
            $userWithoutSchoolUserAccess = (clone $user)->unsetRelation('schoolUserAccess');
            $user->schoolUserAccess->each->setRelation('user', $userWithoutSchoolUserAccess);
            $groups = $user->schools()->mapWithKeys(function (School $school) use ($user) {
                $groups = $this->groupRepository->getGroupsForUserAndSchool($user, $school);

                return $groups->mapWithKeys(static function (Group $group) use ($school): array {
                    return [$group->id => $school->name . ': ' . $group->name];
                });
            });

            return $groups->toArray();
        }

        return [];
    }

    /** {@inheritdoc} */
    #[Override]
    public function authorizedToSee(Request $request)
    {
        return $request->user()->checkPermission(CmsUserPermission::Impersonate);
    }
}
