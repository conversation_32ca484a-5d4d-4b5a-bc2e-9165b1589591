<?php

namespace Cfa\Admin\Application\Nova\Actions;

use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\PupilChange\PupilChange;
use Cfa\Common\Domain\User\PupilChange\PupilChangeHandledStatus;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Override;

use function trans;

class MarkBlockedPupilAsSeen extends Action
{
    /** {@inheritdoc} */
    #[Override]
    public function name()
    {
        return trans('nova.actions.mark_blocked_pupil_as_seen');
    }

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        $models->transform(fn(Pupil $pupil): ?Pupil => $pupil->fresh());
        $models
            ->filter(function (Pupil $pupil) {
                return $pupil->careInfos->first()->pupil_status === PupilStatus::SuspiciousNameChangeException;
            })
            ->each(function (Pupil $pupil): void {
                PupilChange::whereUserId($pupil->id)
                    ->where('handled_status', PupilChangeHandledStatus::Unhandled)
                    ->update([
                        'seen_at' => Carbon::now(),
                    ]);
            });

        return Action::message(trans('nova.actions.blocked_pupil_marked_as_seen'));
    }
}
