<?php

namespace Cfa\Admin\Application\Nova\Actions;

use Cfa\Common\Application\Services\Wisa\WisaApiService;
use Cfa\Common\Domain\User\Pupil;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Override;
use Throwable;

class TriggerWisaCareDataRequest extends Action
{
    public function handle(ActionFields $fields, Collection $pupils): ActionResponse
    {
        $wisaApiService = app(WisaApiService::class);
        $pupils->transform(fn(Pupil $pupil): ?Pupil => $pupil->fresh());
        $pupils->each(function (Pupil $pupil) use ($wisaApiService): void {
            try {
                $careInfo = $pupil->careInfos->first();
                if ($careInfo->wisa_uid !== null) {
                    $wisaApiService->requestWisaCareDataForUser($careInfo->wisa_uid);
                }
                $this->markAsFinished($pupil);
            } catch (Throwable $throwable) {
                $this->markAsFailed($pupil, $throwable);
            }
        });

        return Action::message(trans('nova.actions.requested_wisa_care_data'));
    }

    /** {@inheritdoc} */
    #[Override]
    public function name()
    {
        return trans('nova.actions.request_wisa_care_data');
    }
}
