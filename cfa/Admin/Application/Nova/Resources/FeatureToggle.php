<?php

namespace Cfa\Admin\Application\Nova\Resources;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle as FeatureToggleModel;
use Cfa\Admin\Application\Nova\Actions\FeatureToggleActive;
use Cfa\Admin\Application\Nova\Policies\FeatureTogglePolicy;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

class FeatureToggle extends Resource
{
    public static string $model = FeatureToggleModel::class;

    public static string $policy = FeatureTogglePolicy::class;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function fields(NovaRequest $request): array
    {
        $rules = $this->resource->rules();

        return [
            Select::make(trans('labels.feature'), 'feature')
                ->displayUsingLabels()
                ->options(Feature::class)
                ->rules($rules['feature']),

            Boolean::make(trans('labels.is-active'), 'is_active')
                ->rules($rules['is_active']),
        ];
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function actions(NovaRequest $request): array
    {
        return [
            app(FeatureToggleActive::class)
                ->canSee(fn(NovaRequest $request): bool => $request->user()->checkRole('admin'))
                ->withoutConfirmation()
                ->sole(),
        ];
    }
}
