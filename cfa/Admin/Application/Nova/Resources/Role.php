<?php

namespace Cfa\Admin\Application\Nova\Resources;

use Cfa\Admin\Application\Nova\Policies\RolePolicy;
use Cfa\Common\Domain\User\Career\Role\Role as RoleModel;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Laravel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;
use Override;

class Role extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = RoleModel::class;

    public static string $policy = RolePolicy::class;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            Select::make(trans('labels.name'), 'role_name')
                ->displayUsingLabels()
                ->options(RoleName::class),

            HasMany::make(
                trans_choice('nova.resources.permissionrolename', 2),
                'permissions',
                PermissionRoleName::class,
            )
                ->hideWhenUpdating()
                ->singularLabel(trans_choice('nova.resources.permissionrolename', 1)),
        ];
    }
}
