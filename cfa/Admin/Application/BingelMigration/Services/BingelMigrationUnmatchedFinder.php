<?php

namespace Cfa\Admin\Application\BingelMigration\Services;

use Cfa\Admin\Domain\BingelMigration\BingelModelInterface;
use Cfa\Admin\Domain\BingelMigration\ChapterUnmatchedRowDTO;
use Cfa\Admin\Domain\BingelMigration\CollectionUnmatchedRowDTO;

class BingelMigrationUnmatchedFinder
{
    private const TENANT = 'bingel-fl';

    public function findUnmatchedYearbooks(array $bingelLookups, array $usedCollections): array
    {
        $unmatchedYearbooks = [];
        foreach ($bingelLookups as $collectionName => $collectionDetails) {
            if (
                in_array(
                    $collectionName,
                    $usedCollections,
                ) ||
                !$collectionDetails->isActive() ||
                !$this->isTenantValid($collectionDetails)
            ) {
                continue;
            }

            $unmatchedYearbooks[] = new CollectionUnmatchedRowDTO(
                $collectionDetails->getUid(),
                $collectionName,
            );
        }

        return $unmatchedYearbooks;
    }

    public function isTenantValid(BingelModelInterface $record): bool
    {
        return $record->getTenant() === self::TENANT;
    }

    public function findUnmatchedChapters(array $bingelLookups, array $usedChapters): array
    {
        $unmatchedChapters = [];
        foreach ($bingelLookups as $collectionName => $collectionDetails) {
            foreach ($collectionDetails->getChapters() as $chapterName => $chapterDetails) {
                if (
                    (isset($usedChapters[$collectionName]) && in_array($chapterName, $usedChapters[$collectionName])) ||
                    !$chapterDetails->isActive() ||
                    !$collectionDetails->isActive()
                ) {
                    continue;
                }

                $unmatchedChapters[] = new ChapterUnmatchedRowDTO(
                    $chapterDetails->getUid(),
                    $chapterName,
                    $collectionName,
                );
            }
        }

        return $unmatchedChapters;
    }

    public function findUnmatchedDigiboardware(array $digiboardwareLookup, array $usedDigiboardware): array
    {
        $unmatchedDigiboardware = [];
        foreach ($digiboardwareLookup as $name => $uid) {
            if (!in_array($name, $usedDigiboardware)) {
                $unmatchedDigiboardware[] = new CollectionUnmatchedRowDTO(
                    $uid,
                    $name,
                );
            }
        }

        return $unmatchedDigiboardware;
    }
}
