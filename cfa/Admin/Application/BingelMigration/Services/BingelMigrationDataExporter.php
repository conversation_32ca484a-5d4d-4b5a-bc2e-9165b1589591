<?php

namespace Cfa\Admin\Application\BingelMigration\Services;

use App\Constants\FilesystemDisks;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class BingelMigrationDataExporter
{
    public const array OUTPUT_FILES = [
        'mapped_results' => 'mapping_results.csv',
        'unmatched_bingel_yearbooks' => 'unmatched_bingel_yearbooks.csv',
        'unmatched_bingel_chapters' => 'unmatched_bingel_chapters.csv',
        'unmatched_digiboardware' => 'unmatched_digiboardware.csv',
    ];

    public const array EXPECTED_MAPPING_RESULT_HEADER = [
        'planner_collection_id', 'collection_name', 'chapter_id', 'chapter_name',
        'original_bingel_name', 'bingel_uid', 'digiboardware_uid', 'bingel_dc_uid', 'match_status',
    ];

    public function exportMappingResults(array $results, string $outputPath): void
    {
        $this->saveCsv(
            $outputPath . self::OUTPUT_FILES['mapped_results'],
            $results['mapped'],
            self::EXPECTED_MAPPING_RESULT_HEADER,
        );

        $this->saveCsv(
            $outputPath . self::OUTPUT_FILES['unmatched_bingel_yearbooks'],
            $results['unmatched']['bingel_yearbooks'],
            ['id', 'name'],
        );

        $this->saveCsv(
            $outputPath . self::OUTPUT_FILES['unmatched_bingel_chapters'],
            $results['unmatched']['bingel_chapters'],
            ['id', 'name', 'collection'],
        );

        $this->saveCsv(
            $outputPath . self::OUTPUT_FILES['unmatched_digiboardware'],
            $results['unmatched']['digiboardware'],
            ['id', 'name'],
        );
    }

    private function saveCsv(string $filePath, array $data, array $headers): void
    {
        $fullPath = Storage::disk(FilesystemDisks::LOCAL_PROJECT_DIR)->path($filePath);
        $directory = dirname($fullPath);

        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0o777, true);
        }

        File::put($fullPath, '');
        $file = fopen($fullPath, 'w');

        fputcsv($file, $headers);
        foreach ($data as $row) {
            fputcsv($file, $row->toArray());
        }

        fclose($file);
    }
}
