<?php

namespace Cfa\Admin\Domain\CustomMigration;

class MigrationProgressRepository
{
    public function findOrCreateByKey(MigrationKey $key): MigrationProgress
    {
        return MigrationProgress::firstOrCreate(
            ['key' => $key->value],
            ['last_processed_id' => 0, 'status' => MigrationStatus::Running],
        );
    }

    public function markCompleted(MigrationProgress $progress, int $durationInSeconds): void
    {
        $progress->update([
            'status' => MigrationStatus::Completed,
            'duration_seconds' => $durationInSeconds,
            'updated_at' => now(),
        ]);
    }

    public function increment(MigrationProgress $progress, int $lastProcessedId, int $chunkCount): void
    {
        $progress->update([
            'last_processed_id' => $lastProcessedId,
            'processed_rows' => $progress->processed_rows + $chunkCount,
            'last_run_at' => now(),
        ]);
    }
}
