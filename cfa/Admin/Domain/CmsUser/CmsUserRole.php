<?php

namespace Cfa\Admin\Domain\CmsUser;

use App\Traits\Enum\EnumFromName;
use App\Traits\Enum\EnumGetValues;
use App\Traits\Enum\EnumTranslations;

enum CmsUserRole: int
{
    use EnumTranslations;
    use EnumFromName;
    use EnumGetValues;

    case Admin = 1;
    case Support = 2;
    case Author = 4;

    /**
     * Method to check if the role has a specific CmsUserPermission
     *
     * @param CmsUserPermission $permission Permission to check.
     */
    public function hasPermission(CmsUserPermission $permission): bool
    {
        if (self::Admin === $this) {
            return true;
        }

        return in_array($permission->value, config('cms.permissions.' . $this->value, []));
    }

    public function name(): string
    {
        return $this->getHumanReadableName();
    }
}
