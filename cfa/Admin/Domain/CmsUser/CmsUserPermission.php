<?php

namespace Cfa\Admin\Domain\CmsUser;

enum CmsUserPermission: int
{
    case CollectionsCrud = 1;
    case CollectionsImport = 2;
    case Queues = 4;
    case Impersonate = 5;
    case UserManagement = 7;
    case FlushCache = 9;
    case Badges = 10;
    case MergeTool = 11;
    case Print = 13;
    case EditRolePermissionLink = 14;
    case ManageRoles = 17;
    case DownloadQueryExports = 18;
    case ExportVanInCollections = 19;
    case ViewTmsDashboard = 20;
    case ViewFeatureToggles = 21;
    case ManageCustomerNotifications = 22;
    case ResetUserMfa = 23;
    case ActivatePlannerCollectionsForTeachers = 24;
}
