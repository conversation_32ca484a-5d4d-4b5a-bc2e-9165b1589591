<?php

namespace Cfa\Admin\Domain\User;

use Cfa\Admin\Domain\Career\CareerResource;
use Cfa\Common\Domain\User\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin User
 * @extends JsonResource<User>
 */
class UserResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
            'careers' => CareerResource::collection($this->activeCareers()),
        ];
    }
}
