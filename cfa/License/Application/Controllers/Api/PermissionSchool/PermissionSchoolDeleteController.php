<?php

namespace Cfa\License\Application\Controllers\Api\PermissionSchool;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Carbon\Carbon;
use Cfa\Common\Domain\Permission\LicenceInterface;
use Exception;
use Illuminate\Http\JsonResponse;

class PermissionSchoolDeleteController extends Controller
{
    /**
     * @throws Exception
     */
    public function __invoke(FormRequest $request, LicenceInterface $license): JsonResponse
    {
        $license->licenses_updated_at = Carbon::now();
        // Update the attributes before doing a deletion.
        $license->save();

        $license->delete();

        return $this->respondOk();
    }
}
