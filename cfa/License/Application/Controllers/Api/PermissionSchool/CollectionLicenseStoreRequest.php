<?php

namespace Cfa\License\Application\Controllers\Api\PermissionSchool;

use App\Http\Requests\FormRequest;
use Carbon\Carbon;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\LicenceInterface;
use Cfa\Common\Domain\Permission\LicensePortalLicenceType;
use Cfa\Common\Domain\Permission\LicensePortalProductType;
use Cfa\Common\Domain\School\School;
use Illuminate\Validation\Rule;
use Override;

class CollectionLicenseStoreRequest extends FormRequest implements LicenseStoreRequestInterface
{
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'licenseeId' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail): void {
                        $schoolExists = School::where('smd_external_uid', $value)
                            ->orWhere('uid', $value)
                            ->exists();

                        if (!$schoolExists) {
                            $fail(trans('validation.exists', ['attribute' => $attribute]));
                        }
                    },
                ],
                'productId' => [
                    'required',
                    'string',
                ],
                'licenseType' => [
                    'required',
                    Rule::in(LicensePortalLicenceType::getCollectionLicenseLicenseTypes()),
                ],
                'productType' => [
                    'required',
                    Rule::in(LicensePortalProductType::getCollectionLicenseProductTypes()),
                ],
                'startDate' => [
                    'date',
                    'required',
                ],
                'endDate' => [
                    'nullable',
                    'date',
                    'after_or_equal:startDate',
                ],
            ],
        );
    }

    #[Override]
    public function getLicense(): LicenceInterface
    {
        $data = $this->getData();

        return CollectionLicense::createOrRestoreCollectionLicense(
            $data['school_id'],
            $data['product_id'],
            $data['product_type'],
            $data['license_type'],
            $data['startdate'],
            $data['licenses_updated_at'],
            $data['enddate'],
        );
    }

    #[Override]
    public function getData(): array
    {
        $data = $this->validationData();
        $licenseeId = $data['licenseeId'];
        $schoolId = School::where('smd_external_uid', $licenseeId)
            ->orWhere('uid', $licenseeId)
            ->first(['id'])
            ->id;

        return
            [
                'school_id' => $schoolId,
                'product_id' => $data['productId'],
                'product_type' => LicensePortalProductType::from($data['productType']),
                'license_type' => LicensePortalLicenceType::from($data['licenseType']),
                'startdate' => Carbon::createFromFormat('Y-m-d', $data['startDate']),
                'enddate' => $data['endDate'] ? Carbon::createFromFormat('Y-m-d', $data['endDate']) : null,
                'licenses_updated_at' => Carbon::now(),
            ];
    }

    #[Override]
    public function validationData(): array
    {
        $validationData = parent::validationData();
        if (!isset($validationData['endDate']) || $validationData['endDate'] === 'null') {
            $validationData['endDate'] = null;
        }

        return $validationData;
    }
}
