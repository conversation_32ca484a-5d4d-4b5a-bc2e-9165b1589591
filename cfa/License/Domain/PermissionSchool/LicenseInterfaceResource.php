<?php

namespace Cfa\License\Domain\PermissionSchool;

use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\LicenceInterface;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin LicenceInterface
 * @extends JsonResource<LicenceInterface>
 */
class LicenseInterfaceResource extends JsonResource
{
    private JsonResource $jsonResource;

    public function __construct(LicenceInterface $resource)
    {
        parent::__construct($resource);
        $this->jsonResource = $resource instanceof CollectionLicense
            ? new CollectionLicenseResource($resource)
            : new PermissionSchoolResource($resource);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function toArray($request): array
    {
        return $this->jsonResource->toArray($request);
    }
}
