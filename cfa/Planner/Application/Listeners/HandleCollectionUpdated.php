<?php

namespace Cfa\Planner\Application\Listeners;

use App\Notifications\PlannercollectionUpdated;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\CollectionUpdatable;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Collection\PlannerCollectionUpdated as PlannercollectionUpdatedEvent;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionRepositoryInterface;
use Cfa\Planner\Domain\Record\CurriculumnodeRecordSynced;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Record\RecordExperienceSituationType\RecordExperienceSituationType;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class HandleCollectionUpdated
{
    /**
     * The injected user collection repository.
     *
     * @var UserCollectionRepositoryInterface
     */
    protected $usercollectionRepository;

    /**
     * LinkPublisherRecord constructor.
     *
     * @param UserCollectionRepositoryInterface $usercollectionRepository The injected user collection repository.
     */
    public function __construct(UserCollectionRepositoryInterface $usercollectionRepository)
    {
        $this->usercollectionRepository = $usercollectionRepository;
    }

    /**
     * Listen to updates of collections.
     *
     * @param PlannercollectionUpdatedEvent $event Event indicating the collection has been updated.
     */
    public function onCollectionUpdated(PlannercollectionUpdatedEvent $event): void
    {
        $this->handle($event->plannercollection);
    }

    /**
     * Listen to save events of chapters.
     *
     * @param Chapter $chapter The saved chapter.
     */
    public function onChapterSaved(Chapter $chapter): void
    {
        $this->handle($chapter->plannercollection);
    }

    /**
     * Listen to save events of records.
     *
     * @param Record $record The saved chapter.
     */
    public function onRecordSaved(Record $record): void
    {
        $this->handle($record->chapter->plannercollection ?? null);
    }

    /**
     * Listen to save events of materials.
     *
     * @param Material $material The saved material.
     */
    public function onMaterialSaved(Material $material): void
    {
        $this->handle($material->record->chapter->plannercollection ?? null);
    }

    /**
     * Listen to syncs of curriculum nodes of records.
     *
     * @param CurriculumnodeRecordSynced $event Event indicating the curriculum nodes of a record have been synced.
     */
    public function onCurriculumnodeRecordSynced(CurriculumnodeRecordSynced $event): void
    {
        $this->handle($event->model->chapter->plannercollection ?? null);
    }

    /**
     * Handle the event.
     *
     * @param PlannerCollection|null $collection The updated collection.
     */
    public function handle(?PlannerCollection $collection): void
    {
        // If no linked collection, send no updates.
        if (is_null($collection)) {
            return;
        }

        // When deleted event has been triggered, last time an update could happen to the collection.
        if ($collection->deleted_at !== null) {
            $this->setUpdatableFlag($collection, CollectionUpdatable::Blocked);

            return;
        }

        // Send no updates if a publisher collection is in draft.
        if (
            !is_null($collection->publisher_id) &&
            is_null($collection->owner_id) &&
            is_null($collection->published_at)
        ) {
            return;
        }

        $this->setUpdatableFlag($collection, CollectionUpdatable::Updatable);
        $this->notifyCollectionUpdated($collection);
    }

    /**
     * Set the updatable flag of the child collections of the given collection.
     *
     * @param PlannerCollection $collection The collection that has been updated.
     */
    private function setUpdatableFlag(PlannerCollection $collection, CollectionUpdatable $updatableValue): void
    {
        $usercollections = $collection->usercollections();

        // This should no longer be needed with collections v2, as updatable will no longer be used there.
        $attempts = 5;
        DB::transaction(function () use ($usercollections, $updatableValue): void {
            $usercollections->getQuery()
                ->where('updatable', '!=', CollectionUpdatable::Blocked)
                ->update([
                    'updatable' => $updatableValue,
                ]);
        }, $attempts);

        UserCollection::getRepository()->flushCacheForUsers($usercollections->pluck('owner_id')->unique()->toArray());
    }

    /**
     * Notify the users that have a child collection of the given collection that the collection has been updated.
     *
     * @param PlannerCollection $collection The collection that has been updated.
     */
    protected function notifyCollectionUpdated(PlannerCollection $collection): void
    {
        $ownerIds = $collection->usercollections()->pluck('owner_id');
        $users = User::whereIn('id', $ownerIds)->get();
        Notification::send(
            $users,
            new PlannercollectionUpdated($collection),
        );
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events The eventdispatcher, used to register listeners.
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            PlannercollectionUpdatedEvent::class,
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onCollectionUpdated',
        );
        $events->listen(
            $this->getSaveEventsForModel(Chapter::class),
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onChapterSaved',
        );
        $events->listen(
            $this->getSaveEventsForModel(Record::class),
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onRecordSaved',
        );
        $events->listen(
            RecordExperienceSituationType::class . '.saved',
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onRecordSaved',
        );

        $events->listen(
            $this->getSaveEventsForModel(Material::class),
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onMaterialSaved',
        );
        $events->listen(
            CurriculumnodeRecordSynced::class,
            'Cfa\Planner\Application\Listeners\HandleCollectionUpdated@onCurriculumnodeRecordSynced',
        );
    }

    /**
     * Get the created/updated/deleted event for the given model.
     *
     * @param string $model The model to get the events for.
     */
    private function getSaveEventsForModel(string $model): array
    {
        return [
            'eloquent.created: ' . $model,
            'eloquent.updated: ' . $model,
            'eloquent.deleted: ' . $model,
        ];
    }
}
