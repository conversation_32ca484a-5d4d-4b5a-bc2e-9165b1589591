<?php

namespace Cfa\Planner\Application\Controllers\Settings\Visibility;

use App\Http\Requests\FormRequest;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Planner\Domain\Settings\Activity\ActivityType;
use Illuminate\Validation\Rule;
use Override;

class VisibilityUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'activitySettings' => [
                'required',
                'array',
            ],
            'activitySettings.*' => [
                'required',
                'array',
            ],
            'activitySettings.*.*.is_active' => [
                'required',
                'in:true,false',
            ],
            'activitySettings.*.*.activity_type' => [
                'required',
                'array',
            ],
            'activitySettings.*.*.activity_type.value' => [
                'required',
                'int',
                Rule::in(ActivityType::getValues()),
            ],
            'activitySettings.*.*.is_relevant_for_pupil' => [
                'required',
                'in:true,false',
            ],
            SchoolSettingsType::HideOldOVSGCurriculum->value => [
                'required',
                'in:true,false',
            ],
        ]);
    }
}
