<?php

namespace Cfa\Planner\Application\Controllers\Collections;

use App\Controllers\Controller;
use Cfa\Planner\Domain\Collection\PlannerCollectionRepositoryInterface as LegacyPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionBaseResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Inertia\Inertia;
use Inertia\Response;

use function compact;

class CollectionsIndexController extends Controller
{
    public function __invoke(
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        LegacyPlannerCollectionRepositoryInterface $legacyPlannerCollectionRepository,
    ): View|Response {
        $school = school();

        $user = Auth::user();
        $withCounts = [
            'schoolsWithAccess',
            'usersWithAccess',
            'writeActivatedPlannerCollections',
        ];
        $condition = fn(Builder $builder): Builder => $builder->whereNull('archived_at');

        ActivatedPlannerCollection::enableDeactivated();
        $collections = PlannerCollectionBaseResource::collection(
            collect($legacyPlannerCollectionRepository->getUnmigratedCollectionsForUser($user, $condition))->merge(
                $plannerCollectionRepository
                    ->getAllByUser(user: $user, additionalWithCounts: $withCounts, additionalConditions: $condition),
            ),
        );
        ActivatedPlannerCollection::disableDeactivated();

        $collectionType = 'MY_COLLECTIONS';

        return Inertia::render(
            'Planner/Collections/CollectionsOverview',
            compact('school', 'collections', 'collectionType'),
        );
    }
}
