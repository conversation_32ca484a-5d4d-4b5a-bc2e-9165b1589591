<?php

namespace Cfa\Planner\Application\Controllers\Collections;

use App\Controllers\Controller;
use Auth;
use Cfa\Planner\Domain\Collection\PlannerCollectionRepositoryInterface as LegacyPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionBaseResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Inertia\Inertia;
use Inertia\Response;

use function school;

class ArchivedCollectionsController extends Controller
{
    public function __invoke(
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        LegacyPlannerCollectionRepositoryInterface $legacyPlannerCollectionRepository,
    ): Response {
        $school = school();
        $user = Auth::user();
        $withCounts = [
            'schoolsWithAccess',
            'usersWithAccess',
            'activatedPlannerCollections',
            'writeActivatedPlannerCollections',
        ];
        ActivatedPlannerCollection::enableDeactivated();
        $conditions = fn(Builder $builder): Builder => $builder->whereNotNull('archived_at');
        $collections = PlannerCollectionBaseResource::collection(
            $plannerCollectionRepository
                ->getAllByUser(user: $user, additionalWithCounts: $withCounts, additionalConditions: $conditions)
                ->concat($legacyPlannerCollectionRepository->getUnmigratedCollectionsForUser($user, $conditions)),
        );
        ActivatedPlannerCollection::disableDeactivated();

        $collectionType = 'ARCHIVED';

        return Inertia::render(
            'Planner/Collections/CollectionsOverview',
            compact('school', 'collections', 'collectionType', 'user'),
        );
    }
}
