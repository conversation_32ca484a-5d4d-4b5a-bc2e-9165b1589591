<?php

namespace Cfa\Planner\Application\Controllers\Api\CalendarItem;

use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Illuminate\Support\Collection;
use Override;

use function array_merge;

class LinkRecordsRequest extends FormRequest
{
    public const ITEMS = 'itemIds';
    public const RECORDS = 'recordIds';
    public const RECORD_ID = 'recordId';
    public const COLLECTION_ID = 'collectionId';

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::ITEMS => [
                    'array',
                    'required',
                ],
                self::ITEMS . '.*' => [
                    'string',
                ],
                self::RECORDS => [
                    'array',
                    'required',
                ],
                self::RECORDS . '.*.' . self::RECORD_ID => [
                    'required',
                    'string',
                ],
                self::RECORDS . '.*.' . self::COLLECTION_ID => [
                    'required',
                    'string',
                ],
            ],
        );
    }

    /**
     * Only returns the calendar items that belong to the current user.
     *
     * @return CalendarItem[] & Collection
     */
    public function getCalendaritems(): Collection
    {
        return CalendarItem::whereIn('uid', $this->get(self::ITEMS, []))
            ->where('owner_id', $this->user()->id)
            ->get();
    }

    public function getRecords(): Collection
    {
        return collect($this->get(self::RECORDS));
    }
}
