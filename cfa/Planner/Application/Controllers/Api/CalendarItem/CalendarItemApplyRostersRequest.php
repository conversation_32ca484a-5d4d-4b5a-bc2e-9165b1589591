<?php

namespace Cfa\Planner\Application\Controllers\Api\CalendarItem;

use App\Http\Requests\FormRequest;
use Override;

class CalendarItemApplyRostersRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'rostersToApply' => [
                'required',
                'array',
            ],
            'from' => [
                'required',
                'date',
                'before_or_equal:until',
            ],
            'until' => [
                'required',
                'date',
            ],
        ]);
    }
}
