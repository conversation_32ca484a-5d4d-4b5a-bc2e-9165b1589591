<?php

namespace Cfa\Planner\Application\Controllers\Api\CalendarItem;

use App\Controllers\Controller;
use App\Services\Carbon\CarbonService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Jobs\UpdateRepeatedItemsJob;
use Cfa\Planner\Application\Services\CalendarItem\CalendarItemDuplicateForUserService;
use Cfa\Planner\Application\Services\CalendarItem\CalendarItemService;
use Cfa\Planner\Application\Services\Timeslot\TimeslotConverterService;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\CalendarItemResource;
use Cfa\Planner\Domain\CalendarItem\CalendarItemType;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\Roster\Roster;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

/**
 * Class CalendarItemController.
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class CalendarItemController extends Controller
{
    public function __construct(
        private CalendarItemService $calendaritemService,
        private CalendarItemDuplicateForUserService $calendarItemDuplicateForUserService,
    ) {}

    /**
     * Get the calendaritems.
     *
     * @param CalendarItemAllRequest $request The Request instance used to validate the call.
     */
    public function all(CalendarItemAllRequest $request): JsonResponse
    {
        $fromStartOfDay = CarbonService::createOrNull($request->get('from'))->startOfDay();
        $toEndOfDay = CarbonService::createOrNull($request->get('until'))->endOfDay();
        $group = Group::whereUid($request->get('groupId'))->first();
        $users = User::whereIn('uid', $request->get('userIds', []))->get();

        $school = School::whereUid($request->get('schoolId'))->first();
        if ($group) {
            if ($school === null || $school->id !== $group->school_id) {
                $school = $group->school;
            }
            $request->user()->accessGroup($group->id);
        }

        $method = $request->getViewMode()->isList() ? 'allWithRecords' : 'all';
        $calendarItems = CalendarItem::getRepository()->$method(
            $school,
            $fromStartOfDay,
            $toEndOfDay,
            $request->user(),
            $users,
            $group,
        );

        return $this->respond(CalendarItemResource::collection($calendarItems));
    }

    /**
     * Get the calendar item.
     *
     * @param CalendarItem $calendaritem Calendaritem object to update that gets injected by implicit route binding.
     */
    public function show(CalendarItem $calendaritem): JsonResponse
    {
        $calendaritem->load('rows.record');

        return $this->respond(new CalendarItemResource($calendaritem));
    }

    /**
     * Update a Calendaritem.
     *
     * @param CalendarItem $calendaritem Calendaritem object to update that gets injected by implicit route binding.
     * @param CalendarItemUpdateRequest $request Incoming put request.
     */
    public function update(CalendarItem $calendaritem, CalendarItemUpdateRequest $request): JsonResponse
    {
        $originalRepeatEnd = $calendaritem->repetition_end;
        $originalMasterItemId = $calendaritem->masterItem->id ?? null;
        $originalRepeatedItems = $this->getOriginalRepeatedItems($calendaritem);
        $onlySelf = $request->get('onlySelf', false);

        $calendaritem->fill($request->all());
        $calendaritem->school_id = School::whereUid($request->get('school'))->firstOrFail(['id'])->id;
        $calendaritem->subject_id = optional(Subject::whereUid($request->get('subject'))->first(['id']))->id;

        // Save the changes for the calendar item.
        $this->calendaritemService->saveCalendaritem($calendaritem, $onlySelf, $request->get('groups', []));

        if (!$onlySelf && $originalRepeatedItems->isNotEmpty()) {
            // Perform a simple batch update so the main properties match and the update appears instant.
            $this->calendaritemService->batchUpdateRepeatedItems($calendaritem, $originalMasterItemId);

            // Remove calendar items out of repeat range.
            $originalRepeatedItems =
                $this->calendaritemService->deleteItemsOutOfRange($calendaritem, $originalRepeatedItems);
        }

        if ($originalRepeatEnd || $calendaritem->repetition_end) {
            // Create the repeating items instantly.
            $this->calendaritemService->deleteObsoleteRepeatingItems($calendaritem, $originalRepeatedItems);
            $this->calendaritemService->createRepeatingItems($calendaritem, $originalRepeatedItems);
            $calendaritem->refresh();
            $originalRepeatedItems = $this->getOriginalRepeatedItems($calendaritem);

            // Perform additional updates and sync via queued job.
            $this->dispatch(new UpdateRepeatedItemsJob(
                $calendaritem,
                $originalRepeatedItems,
                $onlySelf,
            ));
        }

        return $this->sendUpdateResponse($calendaritem);
    }

    /**
     * Create a new calendaritem.
     *
     * @param CalendarItemStoreRequest $request Incoming post request.
     */
    public function store(CalendarItemStoreRequest $request): JsonResponse
    {
        $calendaritem = new CalendarItem($request->all());

        // Start and end time for all day items is at noon.
        if (!$calendaritem->duration) {
            $calendaritem->start = $calendaritem->start->setTime(12, 0);
            $calendaritem->end = $calendaritem->end->setTime(12, 0);
        }

        $calendaritem->school_id = School::whereUid($request->get('school'))->firstOrFail(['id'])->id;
        $calendaritem->owner_id = optional(User::whereUid($request->get('owner'))->first(['id']))->id;

        $calendaritem->save();

        return $this->sendStoreResponse($calendaritem);
    }

    /**
     * Delete the calendaritem.
     *
     * @param CalendarItemDeleteRequest $request Incoming request.
     * @param CalendarItem $calendaritem The calendaritem to be removed.
     */
    public function delete(CalendarItemDeleteRequest $request, CalendarItem $calendaritem): JsonResponse
    {
        $onlySelf = $request->get('onlySelf');
        $originalMasterItemId = $calendaritem->masterItem->id ?? null;

        $this->calendaritemService->deleteCalendaritem($calendaritem, $onlySelf);

        if (!$onlySelf && $originalMasterItemId) {
            $this->calendaritemService->updateRepetitionEndForOriginalSeries($calendaritem, $originalMasterItemId);
        }

        return $this->respondNoContent();
    }

    /**
     * Delete multiple calendaritems.
     *
     * @param CalendarItemDeleteAllRequest $request Incoming post request.
     */
    public function deleteAll(CalendarItemDeleteAllRequest $request): JsonResponse
    {
        CalendarItem::whereIn('uid', $request->get('itemIds'))->delete();

        return $this->respondNoContent();
    }

    /**
     * Get the last calendaritem before a specific date.
     * There is a priority implemented:
     *      Matching subject and at least one group.
     *      Matching subject.
     *      Matching at least one group.
     *      Last calendaritem.
     *
     * @param CalendarItemLastRequest $request The Http request.
     */
    public function last(CalendarItemLastRequest $request): JsonResponse
    {
        $from = CarbonService::createOrNull($request->get('from'))->endOfDay();
        $subjectId = optional(Subject::whereUid($request->get('subject'))->first(['id']))->id;
        $groupIds = Group::whereIn('uid', $request->get('group', []))->pluck('id')->all();

        $calendaritems = CalendarItem::with('groups')
            ->whereOwnerId($request->user()->id)
            ->where('start', '<=', $from)
            ->whereType(CalendarItemType::Course->value)
            ->orderBy('start', 'desc')
            ->has('rows')
            ->get();

        /** @var CalendarItem $calendaritem */
        $calendaritem = null;
        if (!is_null($subjectId) && !empty($groupIds)) {
            $calendaritem = $calendaritems->filter->equalSubject($subjectId)
                ->filter->hasAtLeastOneGroup($groupIds)->first();
        }
        if (!is_null($subjectId) && is_null($calendaritem)) {
            $calendaritem = $calendaritems->filter->equalSubject($subjectId)->first();
        }
        if (!is_null($groupIds) && is_null($calendaritem)) {
            $calendaritem = $calendaritems->filter->hasAtLeastOneGroup($groupIds)->first();
        }
        if (is_null($calendaritem)) {
            $calendaritem = $calendaritems->filter->hasNoSubject($subjectId)
                ->filter->hasNoGroup($groupIds)->first();
        }

        if (is_null($calendaritem)) {
            return $this->respondNoContent();
        }
        PlannerCollection::setQueryingEnabled();
        ActivatedPlannerCollection::setQueryingEnabled();
        $calendaritem->load(
            'rows.record.chapter.plannerCollection:id,uid',
            'rows.plannerCollection:id,uid',
            'rows.activatedPlannerCollection:id,uid',
        );
        PlannerCollection::setQueryingDisabled();
        ActivatedPlannerCollection::setQueryingDisabled();

        return $this->respond(new CalendarItemResource($calendaritem));
    }

    /**
     * Apply the rosters (create new calendar items based on them) on the given date range.
     *
     * @param CalendarItemApplyRostersRequest $request Incoming request.
     */
    public function applyRosters(CalendarItemApplyRostersRequest $request): JsonResponse
    {
        DB::transaction(
            function () use ($request): void {
                $dates = CarbonService::datesInRange(
                    CarbonService::createOrNull($request->get('from')),
                    CarbonService::createOrNull($request->get('until')),
                );

                Roster::with('timeslots.groups', 'timeslots.roster')
                    ->whereIn('uid', $request->get('rostersToApply'))
                    ->get()
                    ->each(function (Roster $roster) use ($dates, $request): void {
                        // Remove any dates not within roster range.
                        $rosterDates = $dates
                            ->reject(function ($date) use ($roster) {
                                return
                                    ($roster->applicable_from && $date < $roster->applicable_from) ||
                                    ($roster->applicable_to && $date > $roster->applicable_to);
                            });

                        app(TimeslotConverterService::class)->toCalendaritems(
                            $roster->timeslots->filter->matchesAtleastOneDate($rosterDates),
                            $dates,
                            $request->user()->id,
                        );
                    });
            },
        );

        return $this->respondNoContent();
    }

    /**
     * Get the calendaritems from calendaritem uids and group uids.
     *
     * @param CalendarItemSearchRequest $request Http request.
     */
    public function search(CalendarItemSearchRequest $request): JsonResponse
    {
        $groupIds = Group::whereIn('uid', $request->get('groupIds', []))->pluck('id');
        $calendaritems = CalendarItem::with('groups')
            ->whereOwnerId($request->user()->id)
            ->whereIn('uid', $request->get('itemIds', []))
            ->get();

        if (!$groupIds->isEmpty()) {
            $calendaritems = $calendaritems->filter->hasAtLeastOneGroup($groupIds->toArray());
        }

        $calendaritems->load('groups', 'rows.record', 'subject', 'school', 'owner');

        return $this->respond(CalendarItemResource::collection($calendaritems->values()));
    }

    /**
     * Copy a calendaritem.
     *
     * @param CalendarItem $calendaritem The calendaritem.
     */
    public function copy(CalendarItem $calendaritem): JsonResponse
    {
        $duplicate = $this->calendarItemDuplicateForUserService->duplicate($calendaritem, auth()->user());

        return $this->respond(new CalendarItemResource($duplicate));
    }

    /**
     * Get items that are a repetition of the given item.
     *
     * @param CalendarItemRepeatedRequest $request The Http request.
     * @param CalendarItem $calendaritem The calendaritem.
     */
    public function repeated(CalendarItemRepeatedRequest $request, CalendarItem $calendaritem): JsonResponse
    {
        $calendaritem->load([
            'repeatedItems.owner',
            'repeatedItems.masterItem',
            'repeatedItems.rows',
            'repeatedItems.school',
            'repeatedItems.groups',
            'repeatedItems.subject',
        ]);
        $repeatedCalendarItems = $calendaritem
            ->repeatedItemsInTheFuture()
            ->filter(function (CalendarItem $repeatedItem) use ($request) {
                $until = CarbonService::createOrNull($request->get('until'))->endOfDay();

                return $until->greaterThanOrEqualTo($repeatedItem->start);
            });

        return $this->respond(CalendarItemResource::collection($repeatedCalendarItems));
    }

    private function getOriginalRepeatedItems(CalendarItem $calendarItem): Collection
    {
        return ($calendarItem->masterItem ?? $calendarItem)->repeatedItems;
    }
}
