<?php

namespace Cfa\Planner\Application\Controllers\Api\EducationalNetwork;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetworkResourceCollection;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class EducationalNetworkController extends Controller
{
    /**
     * CurriculumnodeRepository used to get the CurriculumnodeTypes.
     *
     * @var CurriculumNodeRepositoryInterface
     */
    protected $curriculumnodeRepository;

    /**
     * SchoolNetworksController constructor.
     *
     * @param CurriculumNodeRepositoryInterface $curriculumnodeRepository The CurriculumnodeRepositoryInterface.
     */
    public function __construct(CurriculumNodeRepositoryInterface $curriculumnodeRepository)
    {
        $this->curriculumnodeRepository = $curriculumnodeRepository;
    }

    /**
     * GET the networks of a school.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $schools = $user->activeSchools();
        $schoolsByEducationalNetwork = $schools->groupBy('educationalnetwork_id');
        $schoolsByEducationalNetworkIds = $schoolsByEducationalNetwork->keys()->all();
        $educationalNetworks = EducationalNetwork::whereIn('id', $schoolsByEducationalNetworkIds)->get();

        $resourceCollection = new EducationalNetworkResourceCollection($educationalNetworks);
        $resourceCollection->additional(['schoolsByEducationalNetwork' => $schoolsByEducationalNetwork]);

        return $this->respond($resourceCollection);
    }
}
