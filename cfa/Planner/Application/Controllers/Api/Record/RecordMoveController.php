<?php

namespace Cfa\Planner\Application\Controllers\Api\Record;

use App\Controllers\Controller;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Record\RecordMoveRepositoryInterface;
use Cfa\Planner\Domain\Record\RecordResource;
use Illuminate\Http\JsonResponse;

class RecordMoveController extends Controller
{
    public function __invoke(
        UserCollection $collection,
        Chapter $chapter,
        Record $record,
        RecordMoveRequest $request,
        RecordMoveRepositoryInterface $recordMoveRepository,
    ): JsonResponse {
        $record = $recordMoveRepository->move($record, $request->getTargetChapter());

        return $this->respond(new RecordResource($record));
    }
}
