<?php

namespace Cfa\Planner\Application\Controllers\Api\Record;

use App\Controllers\Controller;
use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Planner\Application\Jobs\Reporting\Legacy\ReplaceRecord;
use Cfa\Planner\Application\Jobs\Reporting\Legacy\UpdateRecord;
use Cfa\Planner\Application\Repositories\RecordRepository;
use Cfa\Planner\Application\Services\Zill\ZILLService;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Record\RecordFromGoalsResource;
use Cfa\Planner\Domain\Record\RecordPurpose;
use Cfa\Planner\Domain\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\Record\RecordResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;

use function app;
use function trans;

/** @SuppressWarnings(PHPMD.TooManyPublicMethods) */
class RecordController extends Controller
{
    /**
     * The repository for Records.
     *
     * @var RecordRepository
     */
    private $recordRepository;

    /**
     * RecordController constructor.
     *
     * @param RecordRepositoryInterface $recordRepository The record repository.
     */
    public function __construct(RecordRepositoryInterface $recordRepository)
    {
        $this->recordRepository = $recordRepository;
    }

    /**
     * Get the details of a record.
     *
     * @param Record $record The record to display.
     */
    public function show(Record $record): JsonResponse
    {
        $record->load('availableMaterials', 'experienceSituationTypes');

        return $this->respond(new RecordResource($record));
    }

    /** Fallthrough controller endpoint to ensure backwards compatibility between collections v2 and v1. */
    public function showWithCollection(string $collection, Record $record): JsonResponse
    {
        return $this->show($record);
    }

    public function showByCalendarItemRow(CalendarItemRow $calendarItemRow): JsonResponse
    {
        $record = $calendarItemRow->record ??
            forced_write_connection(fn(): ?Record => $calendarItemRow->fresh()->record);
        if ($record === null) {
            return $this->respondNotFound();
        }

        return $this->show($record);
    }

    /**
     * Get the template record of the collection.
     */
    public function showTemplate(UserCollection $collection): JsonResponse
    {
        $record = $collection->templateRecord()->first();
        if ($record === null) {
            Chapter::unguard();
            $templateChapter = Chapter::firstOrCreate([
                'name' => trans('labels.template.theme-chapter'),
                'is_template' => true,
                'plannercollection_id' => $collection->id,
            ]);
            Chapter::reguard();
            $record = $this->createRecord(
                [
                    'name' => trans('labels.template.focus-goals'),
                    'purpose' => RecordPurpose::Course,
                ],
                $templateChapter,
            );
            $record->is_template = true;
            $this->recordRepository->save($record);
        }
        $record->load('availableMaterials', 'experienceSituationTypes');

        return $this->respond(new RecordResource($record));
    }

    /**
     * Update a record.
     *
     * @param Record $record The record to display.
     * @param RecordUpdateRequest $request The record store request object.
     */
    public function update(Record $record, RecordUpdateRequest $request): JsonResponse
    {
        $reportingJobs = [];

        if ($this->shouldDuplicateRecord($record)) {
            $originalRecord = $record;
            $record = $originalRecord->duplicate(
                null,
                [
                    'owner_id' => $request->user()->id,
                    'parent_record_id' => $originalRecord->id,
                ],
            );

            // Link existing calenderitems linked to originalRecord to the new one.
            $originalRecord->moveCalenderItemsTo($record);
            $originalRecord->mask();

            $reportingJobs[] = new ReplaceRecord($originalRecord, $record);
        }

        $record->fill($request->all());

        // Sync the experienceSituationTypes to the record.
        $record->syncExperienceSituationTypes($request->get('experienceSituations', []));

        // Link materials and curriculum nodes.
        $record->syncMaterials($request->user(), $request->get('materialList', []));

        $curriculumNodeIds = CurriculumNode::whereIn('uid', $request->get('curriculumGoals', []))->get(['id']);
        $record->curriculumnodes()->sync($curriculumNodeIds);
        $zillService = app(ZILLService::class);
        if ($zillService->isZillActivatedForUser($request->user())) {
            $record->zill = $zillService->isZill($curriculumNodeIds->pluck('id')->all());
        }
        $this->recordRepository->save($record);

        $reportingJobs[] = new UpdateRecord($record);
        Bus::chain($reportingJobs)->dispatch();

        return $this->sendUpdateResponse($record, 'api.collections.records.show');
    }

    /** Fallthrough controller endpoint to ensure backwards compatibility between collections v2 and v1. */
    public function updateWithCollection(string $collection, Record $record, RecordUpdateRequest $request): JsonResponse
    {
        return $this->update($record, $request);
    }

    /**
     * Delete a record.
     *
     * @param UserCollection $collection The collection relating to the chapter.
     * @param Chapter $chapter The chapter relating to the record.
     * @param Record $record The record to be deleted.
     */
    public function delete(UserCollection $collection, Chapter $chapter, Record $record): JsonResponse
    {
        if ($chapter->plannercollection_id !== $collection->id || $record->chapter_id !== $chapter->id) {
            return $this->respondNotFound();
        }

        if ($record->isSavedByOwner()) {
            $record->unlinkOrDeleteRecord();

            return $this->respondNoContent();
        }

        $duplicatedRecord = $record->maskWithCalendaritemCheck();

        if (FeatureToggle::isActive(Feature::FillReportingQueue) && $duplicatedRecord) {
            dispatch(new ReplaceRecord($record, $duplicatedRecord));
        }

        return $this->respondNoContent();
    }

    /**
     * Create a new record belonging to a chapter.
     *
     * @param UserCollection $collection The usercollection the chapter belongs to.
     * @param Chapter $chapter The chapter the record belongs to.
     * @param RecordStoreRequest $request The incoming record store request.
     */
    public function store(UserCollection $collection, Chapter $chapter, RecordStoreRequest $request): JsonResponse
    {
        // Verify chapter belongs to collection.
        if ($chapter->plannercollection_id != $collection->id) {
            return $this->respondNotFound();
        }

        $template = $collection->templateRecord()->first();
        if ($template instanceof Record) {
            $record = $template->duplicate($request->all(), ['chapter_id' => $chapter->id]);
            $this->recordRepository->save($record);
        }

        if (!isset($record)) {
            $record = $this->createRecordAndSave($request->all(), $chapter);
        }

        return $this->sendStoreResponse($record);
    }

    /**
     * Create a new unlinked record.
     *
     * @param RecordStoreRequest $request The incoming record store request.
     */
    public function unlinkedStore(RecordStoreRequest $request): JsonResponse
    {
        $record = $this->createRecordAndSave($request->all());

        return $this->sendStoreResponse($record);
    }

    /**
     * Duplicate an existing Record.
     * If the flag remove_original is set then the original record will be deleted
     * and the linked calendaritemrows are moved to the new record.
     *
     * @param UserCollection $collection The linked usercollection.
     * @param Chapter $chapter The linked chapter.
     * @param Record $record The Record to duplicate.
     * @param RecordDuplicateRequest $request The PUT HTTP Request.
     */
    public function duplicate(
        UserCollection $collection,
        Chapter $chapter,
        Record $record,
        RecordDuplicateRequest $request,
    ): JsonResponse {
        $attributes = [];

        if ($request->hasTargetChapterAndCollection()) {
            $chapter = $request->getTargetChapter();
            $attributes = ['order' => ((int) Record::where('chapter_id', $chapter->id)->max('order')) + 1];
        }

        $newRecord = $record->duplicate($attributes, ['chapter_id' => $chapter->id]);
        $newRecord->parent_record_id = null;
        $newRecord->save();

        $newRecord->load('allMaterials');

        return $this->respond(new RecordResource($newRecord));
    }

    /**
     * Get the records via the linked goals.
     *
     * @param RecordFromGoalsRequest $request The POST HTTP Request.
     */
    public function fromGoals(RecordFromGoalsRequest $request): JsonResponse
    {
        $groupedRecords = app(RecordRepositoryInterface::class)
            ->fromUserAndLinkedGoals(
                Auth::user(),
                $request->get('goals'),
            );

        $recordOccurences = $groupedRecords
            ->flatten()
            ->keyBy('uid')
            ->map(function (Record $record) use ($groupedRecords) {
                return $groupedRecords->sum(function (Collection $records) use ($record) {
                    return $records->contains(
                        fn(Record $recordFromList): bool => $recordFromList->id === $record->id,
                    );
                });
            });

        return $this->respond(
            $groupedRecords
                ->map(function (Collection $records) use ($recordOccurences) {
                    return RecordFromGoalsResource::collection(
                        $records->sortByDesc(function (Record $record) use ($recordOccurences) {
                            return $recordOccurences->get($record->uid);
                        })
                            ->values(),
                    );
                }),
        );
    }

    /**
     * Create a new Record.
     */
    private function createRecord(array $attributes, ?Chapter $chapter = null): Record
    {
        $record = new Record($attributes);

        $record->owner_id = Auth::user()->id;
        $record->is_duration_applicable = false;
        if ($chapter !== null) {
            $record->chapter_id = $chapter->id;
        }

        return $record;
    }

    private function createRecordAndSave(array $attributes, ?Chapter $chapter = null): Record
    {
        $record = $this->createRecord($attributes, $chapter);
        $this->recordRepository->save($record);

        return $record;
    }

    private function shouldDuplicateRecord(Record $record): bool
    {
        // A template (focus) record may never be duplicated and should always be updated directly.
        // This is because only one template record is allowed per collection.
        if ($record->is_template === true) {
            return false;
        }

        return $record->isSavedByOwner() === false;
    }
}
