<?php

namespace Cfa\Planner\Application\Controllers\Api\Progress;

use App\Http\Requests\FormRequest;
use Override;

class ProgressZillCountRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'from' => [
                'date',
                'required',
                'before_or_equal:until',
            ],
            'until' => [
                'date',
                'required',
            ],
        ]);
    }
}
