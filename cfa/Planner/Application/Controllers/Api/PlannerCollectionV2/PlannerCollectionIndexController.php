<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionBaseResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionMinimalResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;

class PlannerCollectionIndexController extends Controller
{
    public function __invoke(
        FormRequest $request,
        PlannerCollectionRepositoryInterface $plannerCollectionRepository,
    ): JsonResponse {
        $isMinimal = ($request->get('minimal') === 'true');
        $columns = $isMinimal ? ['uid', 'name', 'archived_at', 'favourited_at'] : null;
        $resource = $isMinimal ? PlannerCollectionMinimalResource::class : PlannerCollectionBaseResource::class;

        return $this->respond(
            $resource::collection(
                $plannerCollectionRepository->getAllByUser(
                    user: $request->user(),
                    additionalWithCounts: ['usersWithWriteAccess', 'schoolsWithAccess', 'usersWithAccess'],
                    columns: $columns,
                    additionalConditions: fn(Builder $builder): Builder => $builder->whereNull('archived_at'),
                ),
            ),
        );
    }
}
