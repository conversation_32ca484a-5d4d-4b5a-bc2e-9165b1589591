<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2\Chapter\Record;

use App\Http\Requests\FormRequest;
use Override;

class RecordSearchRequest extends FormRequest
{
    public const QUERY = 'query';

    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            self::QUERY => [
                'required',
                'string',
            ],
        ]);
    }

    #[Override]
    public function getQueryString(): string
    {
        return $this->get(self::QUERY);
    }
}
