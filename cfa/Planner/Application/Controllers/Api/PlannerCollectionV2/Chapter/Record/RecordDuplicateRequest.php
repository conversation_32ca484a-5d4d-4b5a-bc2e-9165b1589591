<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2\Chapter\Record;

use App\Http\Requests\FormRequest;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use DomainException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Override;

use function app;

class RecordDuplicateRequest extends FormRequest
{
    public const TARGET_COLLECTION_ID = 'target_collection_id';
    public const TARGET_CHAPTER_ID = 'target_chapter_id';

    protected PlannerCollectionInterface $targetCollection;

    /**
     * @throws DomainException
     * @throws ModelNotFoundException
     */
    #[Override]
    public function authorize(): bool
    {
        if (parent::authorize() === false) {
            return false;
        }
        $this->getTargetChapter();

        // Will throw exception if collection is not found.
        // Will throw exception if the chapter is not in the collection or you get multiple matches.
        if (
            ($targetCollection = $this->getTargetCollection()) &&
            $targetCollection instanceof ActivatedPlannerCollection
        ) {
            return $targetCollection->isOwner();
        }

        if (is_null($targetCollection)) {
            return true;
        }

        if ($targetCollection->isOwner()) {
            return true;
        }

        $writableCollection = app(PlannerCollectionRepositoryInterface::class)
            ->getWritableCollection($targetCollection, $this->user());

        return (bool) $writableCollection;
    }

    public function getTargetCollection(): ?PlannerCollectionInterface
    {
        if ($this->isNotFilled(self::TARGET_COLLECTION_ID)) {
            return null;
        }

        if (isset($this->targetCollection)) {
            return $this->targetCollection;
        }

        $this->targetCollection = app(PlannerCollectionRepositoryInterface::class)
            ->getByUid($this->get(self::TARGET_COLLECTION_ID));

        return $this->targetCollection;
    }

    public function getTargetChapter(): ?Chapter
    {
        if ($this->isNotFilled(self::TARGET_CHAPTER_ID)) {
            return null;
        }

        if (isset($this->targetChapter)) {
            return $this->targetChapter;
        }

        $this->targetChapter = app(ChapterRepositoryInterface::class)
            ->getByUid(
                $this->get(self::TARGET_CHAPTER_ID),
                $this->getTargetCollection(),
            );

        return $this->targetChapter;
    }

    public function hasTargetChapterAndCollection(): bool
    {
        return $this->filled(self::TARGET_COLLECTION_ID) && $this->filled(self::TARGET_CHAPTER_ID);
    }
}
