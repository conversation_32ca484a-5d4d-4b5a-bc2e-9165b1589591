<?php

namespace Cfa\Planner\Application\Controllers\Api\PlannerCollectionV2;

use App\Controllers\Controller;
use Auth;
use Cfa\Common\Domain\User\UserRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ShareAccessStatusDTO;
use Cfa\Planner\Domain\CollectionV2\ShareAccessStatusRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ShareAccessStatusResource;
use Illuminate\Http\JsonResponse;

class ShareStatusPlannerCollectionController extends Controller
{
    public function __invoke(
        UserRepositoryInterface $userRepository,
        ShareAccessStatusRepositoryInterface $shareAccessStatusRepository,
        PlannerCollection $plannerCollection,
    ): JsonResponse {
        $colleagues = $userRepository->getColleaguesForUser(Auth::user())->keyBy('uid');
        $shareAccessStatusDTOs = $shareAccessStatusRepository
            ->get(
                $colleagues->pluck('uid'),
                $plannerCollection,
            );
        $usersWithShareAccessStatus = $shareAccessStatusDTOs
            ->mapWithKeys(
                fn(ShareAccessStatusDTO $shareAccessStatusDTO) => [
                    $shareAccessStatusDTO->userUid => new ShareAccessStatusResource(
                        $shareAccessStatusDTO,
                        $colleagues->get($shareAccessStatusDTO->userUid),
                    )->resolve(),
                ],
            );

        return $this->respond([
            'users' => $usersWithShareAccessStatus,
            'schoolHasAccess' => $shareAccessStatusRepository->schoolHasAccess($plannerCollection, school()->id),
        ]);
    }
}
