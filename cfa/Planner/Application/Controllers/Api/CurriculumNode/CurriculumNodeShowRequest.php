<?php

namespace Cfa\Planner\Application\Controllers\Api\CurriculumNode;

use App\Http\Requests\FormRequest;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Override;

class CurriculumNodeShowRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'type' => [
                'enum:' . CurriculumType::class,
                'nullable',
            ],
            'repetition' => [
                'string',
                'in:true,false',
            ],
            'targetAudienceType' => [
                'enum:' . TargetAudienceType::class,
                'nullable',
            ],
            'naturalStudyYear' => [
                'int',
                'nullable',
            ],
        ]);
    }
}
