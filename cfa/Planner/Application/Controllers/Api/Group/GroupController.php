<?php

namespace Cfa\Planner\Application\Controllers\Api\Group;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\Group\GroupResourceCollection;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class GroupController extends Controller
{
    /**
     * The GroupRepository
     *
     * @var GroupRepositoryInterface
     */
    protected $groupRepository;

    /**
     * GroupController constructor.
     *
     * @param GroupRepositoryInterface $groupRepository The injected Group Repository.
     */
    public function __construct(GroupRepositoryInterface $groupRepository)
    {
        $this->groupRepository = $groupRepository;
    }

    /**
     * Display detailed info of the groups linked to the school.
     *
     * @param School $school The school object that gets injected by implicit route binding.
     * @param GroupIndexRequest $request The request object.
     */
    public function index(School $school, GroupIndexRequest $request): JsonResponse
    {
        $schoolyear = $request->filled('schoolyearStartYear')
            ? Schoolyear::getRepository()->findSchoolyearByYear($request->get('schoolyearStartYear'))
            : Schoolyear::getRepository()->getCurrent();

        if (is_null($schoolyear)) {
            throw ValidationException::withMessages([
                'schoolyear' => trans(
                    'validation.in',
                    [
                        'attribute' => trans('validation.attributes.schoolyear'),
                    ],
                ),
            ]);
        }

        $groups = $this->groupRepository->getGroupsForUserAndSchool(
            $request->user(),
            $school,
            $schoolyear,
        );

        $groupResourceCollection = new GroupResourceCollection($groups);
        $groupResourceCollection->additional([
            'recentlyAccessedGroupsIds' => Group::getRepository()->recentlyAccessedGroupsIds($request->user(), $school),
        ]);

        return $this->respond($groupResourceCollection);
    }
}
