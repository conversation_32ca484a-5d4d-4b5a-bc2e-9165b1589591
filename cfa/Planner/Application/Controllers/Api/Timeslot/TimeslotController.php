<?php

namespace Cfa\Planner\Application\Controllers\Api\Timeslot;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Planner\Domain\Roster\Roster;
use Cfa\Planner\Domain\Roster\Timeslot\Timeslot;
use Illuminate\Http\JsonResponse;

use function optional;

class TimeslotController extends Controller
{
    /**
     * Create a new timeslot for this roster
     *
     * @param Roster $roster The roster injected by implicit binding.
     * @param TimeslotStoreRequest $request The timeslot store request object.
     */
    public function store(Roster $roster, TimeslotStoreRequest $request): JsonResponse
    {
        $timeslot = new Timeslot($request->all());
        $timeslot->roster_id = $roster->id;
        $timeslot->subject_id = optional(Subject::whereUid($request->get('subjectId'))->first(['id']))->id;
        $timeslot->save();
        $timeslot
            ->groups()
            ->attach(Group::whereIn('uid', $request->get('groupIds', []))->get());

        return $this->sendStoreResponse($timeslot);
    }

    /**
     * Update a certain timeslot for this roster.
     *
     * @param Roster $roster The roster to which the timeslot belongs.
     * @param Timeslot $timeslot The timeslot to be updated.
     * @param TimeslotUpdateRequest $request The request data object.
     */
    public function update(Roster $roster, Timeslot $timeslot, TimeslotUpdateRequest $request): JsonResponse
    {
        // Check if this roster and this timeslot are linked and valid schoolId.
        if (!$timeslot->isLinkedToRoster($roster)) {
            return $this->respondNotFound();
        }

        $timeslot->fill($request->all());
        $timeslot->subject_id = optional(Subject::whereUid($request->get('subjectId'))->first(['id']))->id;
        $timeslot->save();

        $this->retryWhenRaceConditionOccurs(
            fn(): array =>
            $timeslot
                ->groups()
                ->sync(Group::whereIn('uid', $request->get('groupIds', []))->get()),
        );

        return $this->sendUpdateResponse($timeslot);
    }

    /**
     * Delete a timeslot for this roster.
     *
     * @param Roster $roster The roster to which the timeslot belongs.
     * @param Timeslot $timeslot The timeslot to be deleted.
     * @param TimeslotDeleteRequest $request The request data object.
     */
    public function delete(Roster $roster, Timeslot $timeslot, TimeslotDeleteRequest $request): JsonResponse
    {
        // Check if this roster and this timeslot are linked and valid schoolId.
        if (!$timeslot->isLinkedToRoster($roster)) {
            return $this->respondNotFound();
        }

        $timeslot->delete();

        return $this->respondNoContent();
    }
}
