<?php

namespace Cfa\Planner\Application\Controllers\Api\Timeslot;

use Illuminate\Validation\Rule;
use Override;

class TimeslotUpdateRequest extends TimeslotStoreRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'subjectId' => [
                'string',
                'nullable',
                Rule::exists('subjects', 'uid')
                    ->whereNull('deleted_at'),
            ],
        ]);
    }
}
