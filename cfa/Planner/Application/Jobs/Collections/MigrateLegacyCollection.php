<?php

namespace Cfa\Planner\Application\Jobs\Collections;

use App\Constants\Queues;
use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\CollectionV2\Chapter\Record\TemplateRecordRepository;
use Cfa\Planner\Application\Services\Collection\MigrateLegacyChapters;
use Cfa\Planner\Application\Services\Collection\MigrateLegacyCollections;
use Cfa\Planner\Application\Services\Collection\MigrateLegacyRecords;
use Cfa\Planner\Domain\Collection\Attachment\PlannerCollectionAttachment;
use Cfa\Planner\Domain\Collection\Chapter\Chapter as LegacyChapter;
use Cfa\Planner\Domain\Collection\MigrationStatus;
use Cfa\Planner\Domain\Collection\PlannerCollection as LegacyCollection;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\ActivateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionOverwriteColumn;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\Record\Record as LegacyRecord;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

use function app;
use function collect;
use function config;
use function is_null;

class MigrateLegacyCollection implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public LegacyCollection $legacyCollection;
    private readonly RecordRepositoryInterface $recordRepository;

    public int $tries = 1;

    public function __construct(LegacyCollection $plannerCollection)
    {
        // Force write connection.
        if (config('database.default') === 'mysql') {
            config(['database.default' => 'mysql_write_only']);
        }
        $this->queue = Queues::REPORTING_FILLER;
        $this->legacyCollection = $plannerCollection;
        $this->recordRepository = app(RecordRepositoryInterface::class);
    }

    public function failed(Throwable $exception): void
    {
        $this->legacyCollection->migration_status = MigrationStatus::FAILED;
        $this->legacyCollection->save();
        Log::error('MigrateLegacyCollection Failed', [
            'legacyCollectionUid' => $this->legacyCollection?->uid,
            'exception' => $exception,
        ]);
    }

    public function handle(): void
    {
        if ($this->legacyCollection->migration_status !== MigrationStatus::QUEUED) {
            return;
        }

        $this->startMigration();

        $legacyParentCollection = $this->legacyCollection->parentCollectionWithTrashed;
        if (is_null($legacyParentCollection) || $legacyParentCollection->trashed()) {
            $this->migrateCollection();

            return;
        }

        $v2ParentCollection = app(PlannerCollectionRepositoryInterface::class)
            ->getPlannerCollectionByUid($legacyParentCollection->uid);

        if ($v2ParentCollection->user) {
            if (!$this->legacyCollection->owner->isColleague($v2ParentCollection->user)) {
                $this->migrateCollection();

                return;
            }
            $v2ParentCollection->usersWithAccess()->syncWithoutDetaching([$this->legacyCollection->owner->id]);
        }

        $this->legacyCollection->load([
            'chapters.parentChapter' => fn(BelongsTo $belongsTo) => $belongsTo->withMasked(),
            'chapters.records.parentRecord',
            'chapters.records.parentRecord.chapter',
        ]);

        DB::transaction(function () use ($v2ParentCollection): void {
            $v2ActivatedCollection = $this->activateCollection($v2ParentCollection, $this->legacyCollection->owner);
            $chapterIdsByLegacyIds = $this->handleChapters($v2ParentCollection, $v2ActivatedCollection);
            $this->handleRecords($v2ParentCollection, $v2ActivatedCollection, $chapterIdsByLegacyIds);
            $this->finishMigration();
        });
    }

    private function startMigration(): void
    {
        $this->legacyCollection->migration_status = MigrationStatus::PROCESSING;
        $this->legacyCollection->migrate_started_at = Carbon::now();
        $this->legacyCollection->save();
    }

    private function finishMigration(): void
    {
        $this->legacyCollection->migration_status = MigrationStatus::COMPLETED;
        $this->legacyCollection->migrated_at = Carbon::now();
        $this->legacyCollection->save();
    }

    private function migrateCollection(): void
    {
        DB::transaction(fn() => app(MigrateLegacyCollections::class)->migrateCollection($this->legacyCollection));
        $this->finishMigration();
    }

    private function activateCollection(PlannerCollection $plannerCollection, User $user): ActivatedPlannerCollection
    {
        $this->legacyCollection->load(['attachments', 'targetAudiences']);
        $v2ActivatedCollection = app(ActivateRepositoryInterface::class)->activateForUser(
            $user,
            $plannerCollection,
        );
        $v2ActivatedCollection->uid = $this->legacyCollection->uid;
        if ($this->legacyCollection->archived()) {
            $v2ActivatedCollection->archived_at = $this->legacyCollection->archived_at;
        }
        ActivatedPlannerCollection::setQueryingEnabled();
        $v2ActivatedCollection->save();
        ActivatedPlannerCollection::setQueryingDisabled();

        $legacyAttributes = Arr::only(
            $this->legacyCollection->getAttributes(),
            PlannerCollectionOverwriteColumn::getValues(),
        );
        if ($this->legacyCollection->attachments->isNotEmpty()) {
            $legacyAttributes = [
                'attachments' => $this->legacyCollection
                    ->attachments
                    ->map(fn(PlannerCollectionAttachment $attachment): string => $attachment->file)
                    ->toArray(),
                ...$legacyAttributes,
            ];
        }
        if ($this->legacyCollection->targetAudiences->isNotEmpty()) {
            $legacyAttributes = [
                'target_audiences' => $this->legacyCollection
                    ->targetAudiences
                    ->sortBy('type')
                    ->sortBy('natural_study_year')
                    ->pluck('uid')
                    ->toArray(),
                ...$legacyAttributes,
            ];
        }

        app(ActivatedPlannerCollectionRepositoryInterface::class)
            ->overwriteAttributes($v2ActivatedCollection, $legacyAttributes);

        return $v2ActivatedCollection;
    }

    private function handleChapters(
        PlannerCollection $v2ParentCollection,
        ActivatedPlannerCollection $v2ActivatedCollection,
    ): Collection {
        $chapterIdsByLegacyIds = [];
        [$chaptersWithoutParent, $chaptersWithParent] = $this->legacyCollection
            ->chaptersWithTemplates
            ->partition($this->shouldMigrateChapter(...));

        $chaptersWithParent
            ->each(function (LegacyChapter $legacyChapter) use (
                $v2ParentCollection,
                $v2ActivatedCollection,
                &$chapterIdsByLegacyIds,
            ): void {
                $legacyParentChapter = $legacyChapter->parentChapter;
                // Needed to deal with masked chapters, this causes chapters to have parents with parents.
                while ($legacyParentChapter->parentChapter !== null) {
                    $legacyParentChapter = $legacyParentChapter->parentChapter;
                }

                $v2Chapter = app(ChapterRepositoryInterface::class)
                    ->getByUid($legacyParentChapter->uid, $v2ParentCollection);
                $chapterIdsByLegacyIds[$legacyChapter->id] = $v2Chapter->id;

                $v2Chapter->setRelation('collection', $v2ActivatedCollection);

                $legacyAttributes = Arr::only($legacyChapter->getAttributes(), ChapterOverwriteColumn::getValues());

                app(ChapterRepositoryInterface::class)->overwriteAttributes($v2Chapter, $legacyAttributes);
            });

        [$templateChapters, $nonTemplateChaptersWithoutParent] = $chaptersWithoutParent
            ->partition(fn(LegacyChapter $legacyChapter): bool => $legacyChapter->is_template);

        $templateChapterIdsByLegacyIds = $templateChapters->mapWithKeys(
            function (LegacyChapter $chapter) use ($v2ActivatedCollection) {
                return [
                    $chapter->id => app(TemplateRecordRepository::class)
                        ->findOrCreateTemplateRecord($v2ActivatedCollection, $chapter->records->first()->name)
                        ->chapter_id,
                ];
            },
        );

        // Must use the + operator to ensure the numeric keys remain untouched.
        return collect(
            $chapterIdsByLegacyIds +
            $templateChapterIdsByLegacyIds->all() +
            app(MigrateLegacyChapters::class)->migrateChapters(
                $v2ActivatedCollection,
                $nonTemplateChaptersWithoutParent,
            )->all(),
        );
    }

    private function handleRecords(
        PlannerCollection $v2ParentCollection,
        ActivatedPlannerCollection $v2ActivatedCollection,
        Collection $chapterIdsByLegacyIds,
    ): void {
        [$recordsWithoutParent, $recordsWithParent] = $this->legacyCollection
            ->chaptersWithTemplates
            ->flatMap
            ->records
            ->partition($this->shouldMigrateRecord(...));

        $recordsWithParent
            ->each(function (LegacyRecord $legacyRecord) use (
                $v2ParentCollection,
                $v2ActivatedCollection,
                $chapterIdsByLegacyIds,
            ): void {
                $legacyParentRecord = $legacyRecord->parentRecord;
                // Needed to deal with masked records, this causes records to have parents with parents.
                while ($legacyParentRecord->parentRecord !== null) {
                    $legacyParentRecord = $legacyParentRecord->parentRecord;
                }
                $v2Record = app(RecordRepositoryInterface::class)
                    ->getByUid($legacyParentRecord->uid, $v2ParentCollection);
                $this->createOverwrites($v2ActivatedCollection, $legacyRecord, $v2Record, $chapterIdsByLegacyIds);
            });

        [$templateRecords, $nonTemplateRecordsWithoutParent] = $recordsWithoutParent
            ->partition(fn(LegacyRecord $legacyRecord): bool => $legacyRecord->is_template);

        $templateRecords->each(fn(LegacyRecord $legacyRecord) => $this->migrateLegacyTemplateRecordWithoutParent(
            $v2ActivatedCollection,
            $legacyRecord,
            $chapterIdsByLegacyIds,
        ));

        if ($recordsWithParent->isNotEmpty() || $templateRecords->isNotEmpty()) {
            $this->recordRepository->commitOverwritesWithoutDelete();
        }

        app(MigrateLegacyRecords::class)->migrateRecords(
            $v2ActivatedCollection,
            $nonTemplateRecordsWithoutParent,
            $chapterIdsByLegacyIds,
        );
    }

    private function migrateLegacyTemplateRecordWithoutParent(
        ActivatedPlannerCollection $v2ActivatedCollection,
        LegacyRecord $legacyRecord,
        Collection $chapterIdsByLegacyIds,
    ): void {
        $v2Record = app(TemplateRecordRepository::class)
            ->findOrCreateTemplateRecord($v2ActivatedCollection, $legacyRecord->name);
        $this->createOverwrites($v2ActivatedCollection, $legacyRecord, $v2Record, $chapterIdsByLegacyIds);
    }

    private function createOverwrites(
        ActivatedPlannerCollection $v2ActivatedCollection,
        LegacyRecord $legacyRecord,
        Record $v2Record,
        Collection $chapterIdsByLegacyIds,
    ): void {
        $v2Record->setRelation('collection', $v2ActivatedCollection);
        $v2Record->setRelation('overwrites', collect());

        $recordData = RecordData::fromLegacyRecord($legacyRecord);

        $legacyAttributes = Arr::only($recordData->toArray(), RecordOverwriteColumn::getValues());
        $legacyAttributes['chapter_id'] = $chapterIdsByLegacyIds[$legacyRecord->chapter_id];

        $this->recordRepository->overwriteAttributesWithoutCommit($v2Record, $legacyAttributes);
    }

    private function shouldMigrateChapter(LegacyChapter $legacyChapter): bool
    {
        if (is_null($legacyChapter->parentChapter)) {
            return true;
        }

        Chapter::setQueryingEnabled();
        $v2ChapterIsTrashed = Chapter::onlyTrashed()->whereUid($legacyChapter->parentChapter->uid)->exists();
        Chapter::setQueryingDisabled();
        if ($v2ChapterIsTrashed) {
            return true;
        }

        return $legacyChapter->parentChapter->isMasked() &&
            is_null($legacyChapter->parentChapter->parent_chapter_id);
    }

    private function shouldMigrateRecord(LegacyRecord $legacyRecord): bool
    {
        if (is_null($legacyRecord->parentRecord)) {
            return true;
        }

        // Migrate record when the parent is a template and the record is not.
        if (!$legacyRecord->is_template && $legacyRecord->parentRecord->is_template) {
            return true;
        }

        $legacyParentRecord = $legacyRecord->parentRecord;
        while ($legacyParentRecord->parentRecord !== null) {
            $legacyParentRecord = $legacyParentRecord->parentRecord;
        }
        // Ignore parent record ids to the same collection.
        if ($legacyParentRecord->chapter?->plannercollection_id === $this->legacyCollection->id) {
            return true;
        }

        Record::setQueryingEnabled();
        $v2RecordIsTrashed = Record::onlyTrashed()->whereUid($legacyParentRecord->uid)->exists();
        Record::setQueryingDisabled();
        if ($v2RecordIsTrashed) {
            return true;
        }

        // Migrate the record when the parent is not part of the parentCollection.
        return $legacyParentRecord->chapterWithMasked?->plannercollection_id
            !==
            $this->legacyCollection->parent_collection_id;
    }
}
