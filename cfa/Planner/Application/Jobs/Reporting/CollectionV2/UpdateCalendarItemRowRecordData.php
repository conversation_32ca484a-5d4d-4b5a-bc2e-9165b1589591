<?php

namespace Cfa\Planner\Application\Jobs\Reporting\CollectionV2;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Planner\Application\Exceptions\SchoolyearNotFoundException;
use Cfa\Planner\Application\Jobs\Reporting\ReportingJob;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CurriculumNode\Reporting\CurriculumNodeReporting;
use Illuminate\Support\Collection;

class UpdateCalendarItemRowRecordData extends ReportingJob
{
    public CalendarItemRow $calendarItemRow;

    private Collection $existingCurriculumNodeReporting;

    public function __construct(CalendarItemRow $calendarItemRow)
    {
        parent::__construct();

        $this->calendarItemRow = $calendarItemRow;
    }

    public function handle(): void
    {
        if (!$this->calendarItemRow->hasRecordData()) {
            return;
        }

        $calendaritem = $this->calendarItemRow->calendaritem?->fresh();

        if ($calendaritem === null) {
            return;
        }

        $schoolyear = Schoolyear::getRepository()->findSchoolyearByDate($calendaritem->start);

        if ($schoolyear === null) {
            throw new SchoolyearNotFoundException($calendaritem->start);
        }

        $this->existingCurriculumNodeReporting = $this->getExistingCurriculumNodeReporting($schoolyear);

        $recordData = $this->calendarItemRow->record_data;
        $curriculumNodeIds = $recordData->getCurriculumNodeIds();

        $curriculumNodeReportingToInsert = $calendaritem->groups
            ->map(fn(Group $group): array => $this->getCurriculumNodeReportingToInsert(
                $calendaritem,
                $group,
                $curriculumNodeIds,
            ))
            ->flatten(1);

        $curriculumNodeReportingToInsert->chunk(self::INSERT_LIMIT)
            ->each(function (Collection $chunkToInsert) use ($schoolyear): void {
                CurriculumNodeReporting::forSchoolyear($schoolyear)->insert($chunkToInsert->toArray());
            });

        CurriculumNodeReporting::forSchoolyear($schoolyear)
            ->where('calendar_item_row_id', $this->calendarItemRow->id)
            ->whereNotIn('curriculumnode_id', $curriculumNodeIds)
            ->delete();
    }

    private function getExistingCurriculumNodeReporting(Schoolyear $schoolyear): Collection
    {
        return CurriculumNodeReporting::forSchoolyear($schoolyear)
            ->where('calendar_item_row_id', $this->calendarItemRow->id)
            ->get(['id', 'calendar_item_id', 'calendar_item_group_uid', 'curriculumnode_id'])
            ->mapWithKeys(fn(CurriculumNodeReporting $item) => [
                $this->getKeyForSingleCurriculumNodeReporting(
                    $item->calendar_item_id,
                    $item->calendar_item_group_uid,
                    $item->curriculumnode_id,
                ) => $item->id,
            ]);
    }

    private function getCurriculumNodeReportingToInsert(
        CalendarItem $calendaritem,
        Group $group,
        array $curriculumNodeIds,
    ): array {
        $curriculumNodeReportingToInsert = [];

        foreach ($curriculumNodeIds as $curriculumNodeId) {
            $itemToInsert = $this->buildCurriculumNodeReportingArray(
                $calendaritem,
                $group,
                $curriculumNodeId,
            );

            if ($itemToInsert) {
                $curriculumNodeReportingToInsert[] = $itemToInsert;
            }
        }

        return $curriculumNodeReportingToInsert;
    }

    private function buildCurriculumNodeReportingArray(
        CalendarItem $calendaritem,
        Group $group,
        int $curriculumNodeId,
    ): ?array {
        $reportingKey = $this->getKeyForSingleCurriculumNodeReporting(
            $calendaritem->id,
            $group->uid,
            $curriculumNodeId,
        );

        if ($this->existingCurriculumNodeReporting->has($reportingKey)) {
            return null;
        }

        return [
            'calendar_item_id' => $calendaritem->id,
            'calendar_item_uid' => $calendaritem->uid,
            'calendar_item_row_id' => $this->calendarItemRow->id,
            'calendar_item_date' => $calendaritem->start,
            'calendar_item_creator_id' => $calendaritem->creator_id,
            'calendar_item_group_uid' => $group->uid,
            'calendar_item_row_type' => $this->calendarItemRow->purpose,
            'curriculumnode_id' => $curriculumNodeId,
        ];
    }

    private function getKeyForSingleCurriculumNodeReporting(
        int $calendarId,
        string $groupUid,
        int $curriculumNodeId,
    ): string {
        return sha1($calendarId . '$$' . $groupUid . '$$' . $curriculumNodeId);
    }
}
