<?php

namespace Cfa\Planner\Application\Jobs\Reporting\Legacy;

use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Planner\Application\Jobs\Reporting\ReportingJob;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\CurriculumNode\Reporting\CurriculumNodeReporting;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use stdClass;

class UpdateRecord extends ReportingJob
{
    public Record $record;

    public PlannerCollection $collection;

    private Collection $existingCurriculumNodeReporting;

    private Schoolyear $schoolyear;

    public bool $deleteWhenMissingModels = true;

    public function __construct(Record $record)
    {
        parent::__construct();

        $this->record = $record;
        $this->schoolyear = Schoolyear::getRepository()->getCurrent();
    }

    public function handle(): void
    {
        $calendarItems = $this->getCalendarItems();

        if ($calendarItems->isEmpty()) {
            return;
        }

        $curriculumNodeIds = $this->record->curriculumnodes->pluck('id');
        $this->existingCurriculumNodeReporting = $this->getExistingCurriculumNodeReporting();

        $curriculumNodeReportingToInsert = $calendarItems
            ->map(fn(stdClass $calendarItem): array => $this->getCurriculumNodeReportingToInsert(
                $calendarItem,
                $curriculumNodeIds,
            ))
            ->flatten(1);

        $curriculumNodeReportingToInsert->chunk(self::INSERT_LIMIT)
            ->each(function (Collection $chunkToInsert): void {
                CurriculumNodeReporting::forSchoolyear($this->schoolyear)->insert($chunkToInsert->toArray());
            });

        CurriculumNodeReporting::forSchoolyear($this->schoolyear)
            ->where('record_id', $this->record->id)
            ->whereNotIn('curriculumnode_id', $curriculumNodeIds)
            ->delete();
    }

    private function getExistingCurriculumNodeReporting(): Collection
    {
        return CurriculumNodeReporting::forSchoolyear($this->schoolyear)
            ->where('record_id', $this->record->id)
            ->get(['id', 'calendar_item_id', 'calendar_item_row_id', 'calendar_item_group_uid', 'curriculumnode_id'])
            ->mapWithKeys(fn(CurriculumNodeReporting $item) => [
                $this->getKeyForSingleCurriculumNodeReporting(
                    $item->calendar_item_id,
                    $item->calendar_item_row_id,
                    $item->calendar_item_group_uid,
                    $item->curriculumnode_id,
                ) => $item->id,
            ]);
    }

    private function getCalendarItems(): Collection
    {
        return DB::table(new CalendarItem()->getTable())
            ->select([
                'calendaritems.id',
                'calendaritems.uid',
                'calendaritems.start',
                'calendaritems.creator_id',
                'calendaritemrows.id as calendaritemrow_id',
                'calendaritemrows.purpose',
                'groups.uid as group_uid',
            ])
            ->join('calendaritemrows', 'calendaritemrows.calendaritem_id', '=', 'calendaritems.id')
            ->join('calendaritem_group', 'calendaritem_group.calendaritem_id', '=', 'calendaritems.id')
            ->join('groups', 'groups.id', '=', 'calendaritem_group.group_id')
            ->where('record_id', $this->record->id)
            ->where('calendaritems.start', '>=', $this->schoolyear->start)
            ->where('calendaritems.start', '<', $this->schoolyear->end)
            ->whereNull('calendaritems.deleted_at')
            ->whereNull('calendaritemrows.deleted_at')
            ->whereNull('groups.deleted_at')
            ->get();
    }

    private function getCurriculumNodeReportingToInsert(
        stdClass $calendaritem,
        Collection $curriculumNodeIds,
    ): array {
        return $curriculumNodeIds
            ->map(fn(int $curriculumNodeId): ?array => $this->buildCurriculumNodeReportingArray(
                $calendaritem,
                $curriculumNodeId,
            ))
            ->filter()
            ->toArray();
    }

    private function buildCurriculumNodeReportingArray(stdClass $calendaritem, int $curriculumNodeId): ?array
    {
        $reportingKey = $this->getKeyForSingleCurriculumNodeReporting(
            $calendaritem->id,
            $calendaritem->calendaritemrow_id,
            $calendaritem->group_uid,
            $curriculumNodeId,
        );

        if ($this->existingCurriculumNodeReporting->has($reportingKey)) {
            return null;
        }

        return [
            'calendar_item_id' => $calendaritem->id,
            'calendar_item_uid' => $calendaritem->uid,
            'calendar_item_row_id' => $calendaritem->calendaritemrow_id,
            'calendar_item_date' => $calendaritem->start,
            'calendar_item_creator_id' => $calendaritem->creator_id,
            'calendar_item_group_uid' => $calendaritem->group_uid,
            'calendar_item_row_type' => $calendaritem->purpose,
            'record_id' => $this->record->id,
            'curriculumnode_id' => $curriculumNodeId,
        ];
    }

    private function getKeyForSingleCurriculumNodeReporting(
        int $calendarId,
        int $calendarRowId,
        string $groupUid,
        int $curriculumNodeId,
    ): string {
        return sha1($calendarId . '$$' . $calendarRowId . '$$' . $groupUid . '$$' . $curriculumNodeId);
    }

    public function setSchoolyear(Schoolyear $schoolyear): self
    {
        $this->schoolyear = $schoolyear;

        return $this;
    }
}
