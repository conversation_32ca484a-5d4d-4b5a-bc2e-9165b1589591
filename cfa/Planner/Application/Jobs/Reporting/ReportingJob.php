<?php

namespace Cfa\Planner\Application\Jobs\Reporting;

use App\Constants\Queues;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/** @SuppressWarnings(PHPMD.NumberOfChildren) */
abstract class ReportingJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected const INSERT_LIMIT = 5000;

    public function __construct()
    {
        $this->queue = Queues::REPORTING;
    }

    public function onQueue(string $queue): self
    {
        $this->queue = $queue;

        return $this;
    }
}
