<?php

namespace Cfa\Planner\Application\Services\PlannerCollectionV2\Record;

use App\Services\String\TextHighlighter;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\CollectionV2\Chapter\Record\RecordSearchRepository;
use Illuminate\Support\Collection;

use function htmlentities;

class RecordSearchService
{
    public function __construct(
        private readonly RecordSearchRepository $recordSearchRepository,
        private readonly TextHighlighter $textHighlighter,
    ) {}

    public function search(User $user, string $query): Collection
    {
        return $this->recordSearchRepository
            ->search($user, $query)
            ->map(fn(array $result): array => [
                'id' => $result['uid'] . $result['collection_id'],
                'fields' => array_map(fn(string $field): array => [htmlentities($field)], $result),
                'highlights' => [
                    'record_name' => $this->textHighlighter->highlightMatchingText(
                        htmlentities($query),
                        htmlentities((string) $result['record_name']),
                    ),
                ],
            ]);
    }
}
