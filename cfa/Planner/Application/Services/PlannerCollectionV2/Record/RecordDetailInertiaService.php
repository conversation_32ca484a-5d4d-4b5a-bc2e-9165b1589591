<?php

namespace Cfa\Planner\Application\Services\PlannerCollectionV2\Record;

use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\CareInput\CareType\CareTypeRepositoryInterface;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\CurriculumNodeGoalsRepository;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordResource;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeResource;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\Zill\ZillVersion;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

use function app;
use function route;

class RecordDetailInertiaService
{
    public function __construct(
        private readonly CareTypeRepositoryInterface $careTypeRepository,
        private readonly CurriculumNodeGoalsRepository $curriculumNodeGoalsRepository,
    ) {}

    public function getInertiaResponse(
        User $user,
        School $school,
        RecordData $record,
        array $additionalParameters = [],
    ): Response {
        return Inertia::render(
            'Planner/Collections/RecordDetail/RecordDetail',
            [
                ...$additionalParameters,
                'schoolId' => $school->uid,
                'record' => app(RecordResource::class, ['resource' => $record]),
                'networks' => $this->getCurriculumTypes($user, $school),
                'careTypeOptions' => $this->getCareTypeOptions($user, $school),
                'goals' => CurriculumNodeResource::collection(
                    $this->curriculumNodeGoalsRepository->goals($record->getCurriculumNodeIds()),
                ),
                'zillVersion' => ZillVersion::whereIsCurrent(true)->value('uid'),
            ],
        );
    }

    private function getCareTypeOptions(User $user, School $school): Collection
    {
        return $user->hasPermission(PermissionName::HasAccessToCare, $school)
            ? $this->careTypeRepository
                ->getAllBySchoolWithoutArchived($school)
                ->map(fn(CareType $careType): array => [
                    'title' => $careType->name,
                    'href' => route('web.care.overview.notes.create', ['careType' => $careType->uid]),
                ])
            : collect();
    }

    private function getCurriculumTypes(User $user, School $school): array
    {
        $school->load('educationalnetwork:id,uid');

        $networks = [];
        /** @var CurriculumType $curriculumType */
        foreach ($school->educationalnetwork->getCurriculumTypes() as $curriculumType) {
            if ($this->shouldAddCurriculumTypeForUser($curriculumType, $school, $user)) {
                $networks[] = [
                    'type' => Str::upper($curriculumType->name),
                    'id' => $school->educationalnetwork->uid,
                    'name' => $curriculumType->getHumanReadableName(),
                ];
            }
        }

        return $networks;
    }

    private function shouldAddCurriculumTypeForUser(CurriculumType $curriculumType, School $school, User $user): bool
    {
        if ($curriculumType->isPreSchool() && !$user->hasPermission(PermissionName::HasAccessToPreschool, $school)) {
            return false;
        }

        if ($curriculumType !== CurriculumType::LeerLokaal && $this->shouldHideOVSGCurriculum($school)) {
            return false;
        }

        return true;
    }

    private function shouldHideOVSGCurriculum(School $school): bool
    {
        if ($school->educationalnetwork->uid !== EducationalNetwork::OVSG_UID) {
            return false;
        }

        return (bool) SchoolSettings::where('school_id', $school->id)
            ->where('type', SchoolSettingsType::HideOldOVSGCurriculum)
            ->first()?->value;
    }
}
