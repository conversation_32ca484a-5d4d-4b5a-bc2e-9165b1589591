<?php

namespace Cfa\Planner\Application\Services\PlannerCollectionV2\Record;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Repositories\CollectionV2\Chapter\Record\TemplateRecordRepository;
use Cfa\Planner\Application\Repositories\CurriculumNodeGoalsRepository;
use Cfa\Planner\Domain\CalendarItem\CalendarItemRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\LinkedCalendarItemResource;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwrittenFieldsResource;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordResource;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionResource;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeResource;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Inertia\Response;

use function app;

class RecordDetailResponseService
{
    public function __construct(
        private readonly RecordDetailInertiaService $recordDetailInertiaService,
        private readonly PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        private readonly CalendarItemRepositoryInterface $calendarItemRepository,
        private readonly ChapterRepositoryInterface $chapterRepository,
        private readonly RecordRepositoryInterface $recordRepository,
        private readonly TemplateRecordRepository $templateRecordRepository,
        private readonly CurriculumNodeGoalsRepository $curriculumNodeGoalsRepository,
    ) {}

    public function getTemplateResponse(
        User $user,
        School $school,
        PlannerCollectionInterface $plannerCollection,
        string $backUrl,
    ): View|Response {
        return $this->getResponseForRecord($user, $school, $plannerCollection, $backUrl, true);
    }

    public function getRecordResponse(
        User $user,
        School $school,
        PlannerCollectionInterface $plannerCollection,
        string $backUrl,
        string $chapterUid,
        string $recordUid,
    ): View|Response {
        return $this->getResponseForRecord(
            $user,
            $school,
            $plannerCollection,
            $backUrl,
            false,
            $chapterUid,
            $recordUid,
        );
    }

    private function getResponseForRecord(
        User $user,
        School $school,
        PlannerCollectionInterface $plannerCollection,
        string $backUrl,
        bool $isTemplate,
        string $chapterUid = '',
        string $recordUid = '',
    ): View|Response {
        $record = $this->getRecord($plannerCollection, $recordUid, $isTemplate);
        $chapters = $this->getChapters($chapterUid, $plannerCollection, $isTemplate);
        // Convert the ActivatedCollection to PlannerCollection if needed.
        $plannerCollection = $this->plannerCollectionRepository->loadAllData($plannerCollection);
        $plannerCollection->setRelation('chapters', $chapters);
        $recordData = RecordData::fromRecord($record);

        $originalRecordData = $record->relationLoaded('overwrites')
            ? RecordData::fromRecord($record->getOriginalModel())
            : null;

        $additionalParameters = [
            'collection' => app(PlannerCollectionResource::class, ['resource' => $plannerCollection]),
            'originalRecord' => $record->relationLoaded('overwrites')
                ? app(RecordResource::class, ['resource' => $originalRecordData])
                : null,
            'originalGoals' => $originalRecordData ? CurriculumNodeResource::collection(
                $this->curriculumNodeGoalsRepository->goals($originalRecordData->getCurriculumNodeIds()),
            ) : [],
            'overwrittenFields' => app(RecordOverwrittenFieldsResource::class, ['resource' => $record]),
            'backUrl' => $backUrl,
            'shareStatus' => $plannerCollection->share_status,
            'canEdit' => true,
        ];
        $additionalParameters['linkedCalendarItems'] =
            LinkedCalendarItemResource::collection(
                $this->calendarItemRepository->getLinkedToRecord($record, $user, $school),
            );

        return $this->recordDetailInertiaService->getInertiaResponse(
            $user,
            $school,
            $recordData,
            $additionalParameters,
        );
    }

    private function getRecord(
        PlannerCollectionInterface $plannerCollection,
        string $recordUid,
        bool $isTemplate,
    ): Record {
        if ($isTemplate) {
            return $this->templateRecordRepository->findOrCreateTemplateRecord($plannerCollection);
        }

        return $this->recordRepository->getByUid($recordUid, $plannerCollection);
    }

    private function getChapters(
        string $chapterUid,
        PlannerCollectionInterface $plannerCollection,
        bool $isTemplate,
    ): Collection {
        if ($isTemplate) {
            return collect();
        }
        $chapter = $this->chapterRepository->getByUid($chapterUid, $plannerCollection);
        $records =
            $this->recordRepository->getAllByChapter($chapter, $plannerCollection, ['id', 'uid', 'name', 'purpose']);

        $chapter->setRelation('records', $records);

        return collect([$chapter]);
    }
}
