<?php

namespace Cfa\Planner\Application\Services\Progress;

use Illuminate\Support\Facades\Log;
use Psr\Log\LogLevel;

class ProgressReporter
{
    /**
     * @var int
     */
    protected const REPORT_STEP_SIZE = 10;

    /**
     * Keep track of the current progress.
     *
     * @var int
     */
    protected $currentProgress = 0;

    /**
     * Name of the entity that's being processed.
     *
     * @var string
     */
    protected $reporterEntity;

    /**
     * Total amount of actions to be performed.
     *
     * @var int
     */
    protected $reporterTotal;

    /**
     * The loglevel to use for logging the progress.
     *
     * @var LogLevel
     */
    protected $logLevel;

    /**
     * Additional parameters to provide with the log statement.
     *
     * @var array
     */
    protected $additionalParameters;

    /**
     * ProgressReporter constructor.
     */
    public function __construct()
    {
        $this->logLevel = LogLevel::DEBUG;
    }

    /**
     * Set the reporter info for the following reportProgress calls.
     *
     * @param string $entity The entity we're processing (e.g. Chapters).
     * @param int $total The total amount of actions.
     */
    public function initProgressReporter(string $entity, ?int $total = null): void
    {
        $this->currentProgress = 0;
        $this->reporterEntity = $entity;
        $this->reporterTotal = $total;
    }

    /**
     * Set additional parameters to provide with the log statement.
     *
     * @param array $additionalParameters Additional parameters to provide with the log statement.
     */
    public function setAdditionalLoggingParameters(array $additionalParameters): void
    {
        $this->additionalParameters = $additionalParameters;
    }

    /**
     * Report the progress by logging statements.
     *
     * @param null|string $parentName Name of the parent entity you're logging for (e.g the collection of a chapter).
     * @param array $additionalParameters Additional parameters to provide with the log statement.
     */
    public function advanceProgress(?string $parentName = null, array $additionalParameters = []): void
    {
        $this->currentProgress++;

        if (
            $this->currentProgress > 1 &&
            ($this->currentProgress % static::REPORT_STEP_SIZE !== 0)
        ) {
            return;
        }

        Log::log(
            $this->logLevel,
            $this->getProgressMessage($parentName),
            array_merge($additionalParameters, $this->additionalParameters),
        );
    }

    /**
     * Get the message for logging the progress.
     *
     * @param null|string $parentName Name of the parent entity you're logging for (e.g the collection of a chapter).
     */
    protected function getProgressMessage(?string $parentName = null): string
    {
        $hasTotal = $this->reporterTotal > 0;
        $message = 'Processing %s' . ($parentName ? ' for "%s"' : '') . ': %d';
        $message .= $hasTotal ? '/%d (%d%%)' : '';
        $message .= ' processed';

        $messageArguments = array_merge(
            [$this->reporterEntity],
            $parentName ? [$parentName] : [],
            [$this->currentProgress],
            $hasTotal ?
            [
                $this->reporterTotal,
                (int) ($this->currentProgress * 100 / $this->reporterTotal),
            ] : [],
        );

        return vsprintf($message, $messageArguments);
    }
}
