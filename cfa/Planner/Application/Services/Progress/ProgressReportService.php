<?php

namespace Cfa\Planner\Application\Services\Progress;

use App\Http\Requests\FormRequest;
use App\Services\Carbon\CarbonService;
use Carbon\Carbon;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRowPurpose;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeRepositoryInterface;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Illuminate\Support\Collection;

class ProgressReportService
{
    /**
     * The curriculumnode repository.
     *
     * @var CurriculumNodeRepositoryInterface
     */
    private $curriculumnodeRepository;

    /**
     * ProgressReportService constructor.
     *
     * @param CurriculumNodeRepositoryInterface $curriculumnodeRepository The curriculumnoderepository.
     */
    public function __construct(CurriculumNodeRepositoryInterface $curriculumnodeRepository)
    {
        $this->curriculumnodeRepository = $curriculumnodeRepository;
    }

    /**
     * Get the collection of planned goals. This is needed to get the positive/negative list of goals.
     * The result is a plain list containers with each time:
     *  - the accomplished goal
     *  - the calendar item representing when the goal was accomplished
     *  - if it was accomplished during a course or an evaluation
     *
     * @param Collection $calendaritems Calendar items for which to get the planned goals.
     * @param int $educationalNetworkId Network for which to get the planned goals.
     * @param string|null $parentNodeUid Parent uid of the goals to retrieve.
     */
    public function getPlannedGoals(
        Collection $calendaritems,
        int $educationalNetworkId,
        ?string $parentNodeUid,
    ): Collection {
        return $calendaritems
            ->map(
                function (CalendarItem $calendaritem) use ($educationalNetworkId, $parentNodeUid) {
                    return $calendaritem->rows
                        ->filter(function (CalendarItemRow $row) {
                            return !empty($row->record) && !empty($row->record->curriculumnodes);
                        })
                        ->map(
                            function (CalendarItemRow $calendaritemrow) use (
                                $calendaritem,
                                $educationalNetworkId,
                                $parentNodeUid
                            ) {
                                return $calendaritemrow->record->curriculumnodes
                                    ->where('type', CurriculumNodeType::Goal)
                                    ->where('educationalnetwork_id', $educationalNetworkId)
                                    // Retrieve only children of the given parent uid.
                                    ->when(
                                        $parentNodeUid,
                                        function (Collection $collection) use ($parentNodeUid) {
                                            return $collection->filter(
                                                function (CurriculumNode $curriculumnode) use ($parentNodeUid) {
                                                    return $curriculumnode->parents
                                                        ->pluck('uid')
                                                        ->contains($parentNodeUid);
                                                },
                                            );
                                        },
                                    )
                                    ->map(
                                        function (CurriculumNode $curriculumnode) use (
                                            $calendaritemrow,
                                            $calendaritem
                                        ) {
                                            return [
                                                'goalId' => $curriculumnode->id,
                                                'goal' => $curriculumnode,
                                                'calendarItemId' => $calendaritem->id,
                                                'calendarItem' => $calendaritem,
                                                'isCourse' => CalendarItemRowPurpose::Course ===
                                                    $calendaritemrow->purpose,
                                                'color' => optional($curriculumnode->zilldevelopmentfield)->color,
                                            ];
                                        },
                                    );
                            },
                        );
                },
            )
            ->flatten(2);
    }

    /**
     * Get the positive goal tree based on the collection of planned goals.
     *
     * @param Collection $plannedGoals Planned goals collection, @see CurriculumnodeService::getPlannedGoals.
     */
    public function getPositiveGoalTree(Collection $plannedGoals): Collection
    {
        return $this->asGoalTree($plannedGoals->pluck('goal'));
    }

    /**
     * Get the negative goal tree based on the collection of planned goals.
     *
     * @param Collection $plannedGoals Planned goals collection, @see CurriculumnodeService::getPlannedGoals.
     * @param EducationalNetwork $educationalnetwork Educational network for which to get the negative goals.
     * @param null|TargetAudienceType $targetAudienceType The target audience type for which get the negative goals.
     * @param int $naturalStudyYear The natural study year for which get the negative goals.
     * @param null|string $parentNodeUid Parent uid of the goals to retrieve.
     */
    public function getNegativeGoalTree(
        Collection $plannedGoals,
        EducationalNetwork $educationalnetwork,
        ?TargetAudienceType $targetAudienceType,
        int $naturalStudyYear,
        ?string $parentNodeUid,
    ): ?Collection {
        return $this->asGoalTree(
            $this->curriculumnodeRepository->getNegativeGoals(
                $plannedGoals,
                $educationalnetwork,
                $targetAudienceType,
                $naturalStudyYear,
                $parentNodeUid,
            ),
        );
    }

    /**
     * Get a collection of all calendar items grouped by their goal id.
     * For every goal, you get two lists: one for course items and one for evaluation items.
     *
     * [
     *      '1' (goal with id1) => [
     *                  'true' (list calendar items that threated the goal during a course) => [
     *                      $calendarItem1,
     *                      $calendarItem2
     *                   ],
     *                  'false' (list calendar items that threated the goal during an evaluation) => [
     *                      $calendaritem3
     *                  ]
     *              ],
     *      '3' => ...
     * ]
     *
     * @param Collection $plannedGoals Planned goals collection, @see CurriculumnodeService::getPlannedGoals.
     */
    public function getCalendarItemsByGoalId(Collection $plannedGoals): Collection
    {
        return $plannedGoals
            ->groupBy('goalId')
            ->map(
                function (Collection $plannedGoalsForGoal) {
                    return $plannedGoalsForGoal
                        ->sortBy(
                            function ($plannedGoal) {
                                return $plannedGoal['calendarItem']->start;
                            },
                        )
                        ->groupBy('isCourse')
                        ->map(
                            function ($plannedGoals) {
                                return $plannedGoals->unique('calendarItemId');
                            },
                        )
                        ->map(
                            function ($plannedGoals) {
                                return $plannedGoals->map(
                                    function ($plannedGoal) {
                                        return $plannedGoal['calendarItem'];
                                    },
                                );
                            },
                        );
                },
            );
    }

    /**
     * Converts the collection of goals to a goal tree.
     *
     * @param Collection $goals Collection of goals.
     */
    private function asGoalTree(Collection $goals): Collection
    {
        $goalsWithParents = collect($goals);
        $goals->each(
            function (CurriculumNode $goal) use (&$goalsWithParents): void {
                $goalsWithParents = $goalsWithParents->merge($goal->parents);
            },
        );

        return $goalsWithParents
            ->unique('id')
            ->sortBy('sequence')
            ->fromMultiParentToTree();
    }

    /**
     * Get the number of goals to achieve grouped by group and learning area.
     */
    public function getNegativeTotals(School $school): Collection
    {
        return $this->curriculumnodeRepository
            ->getNegativeTotals(
                $school->educationalnetwork,
            )
            ->groupBy('target_audience_type')
            ->map
            ->groupBy('natural_study_year')
            ->map(function (Collection $learningAreasByTargetAudienceType) {
                return $learningAreasByTargetAudienceType->map(function (Collection $learningAreas) {
                    return $learningAreas
                        ->keyBy('learningAreaUid')
                        ->map(function ($learningArea) {
                            return [
                                'goalsTotal' => $learningArea->goalsTotal,
                                'learningAreaName' => $learningArea->learningAreaName,
                            ];
                        });
                });
            });
    }

    /**
     * Get the number of achieved goals grouped by group and learning area.
     *
     * @param array $inputData Contains the from and until parameter for date limitations.
     */
    public function getPositiveTotals(
        Collection $groups,
        array $inputData,
        School $school,
    ): Collection {
        $learningAreas = $this->curriculumnodeRepository
            ->getLearningAreas($school->educationalnetwork)
            ->pluck('uid', 'id');

        $positiveTotals = $this->curriculumnodeRepository->getPositiveTotals(
            $groups,
            $inputData,
            $school,
            $learningAreas->keys(),
        );

        return $positiveTotals->groupBy('groupUid')
            ->map(function ($positiveTotal) use ($learningAreas) {
                return $positiveTotal->mapWithKeys(function ($item) use ($learningAreas) {
                    return [
                        $learningAreas->get($item->learningAreaId) => $item->goalsCount,
                    ];
                });
            });
    }

    /**
     * Get the date range for the progress report.
     *
     * @param FormRequest $request The current request.
     */
    public function getDateRange(FormRequest $request): array
    {
        if ($request->filled('from')) {
            return [
                'fromStartOfDay' => CarbonService::createOrNull($request->get('from'))->startOfDay(),
                'untilEndOfDay' => CarbonService::createOrNull($request->get('until'))->endOfDay(),
            ];
        }

        $schoolyearRepository = Schoolyear::getRepository();

        $schoolyear = $request->filled('schoolyearStartYear') ?
            $schoolyearRepository->findSchoolyearByYear($request->get('schoolyearStartYear')) :
            $schoolyearRepository->getCurrent();
        $nextSchoolyear = $schoolyearRepository->findSchoolyearByYear($schoolyear->start->year + 1);

        $defaultUntil = Carbon::now();
        $defaultUntil = $defaultUntil < $nextSchoolyear->start ?
            $defaultUntil :
            $nextSchoolyear->start->subSecond();
        $defaultFrom = $schoolyear->start;

        return [
            'fromStartOfDay' => $defaultFrom->startOfDay(),
            'untilEndOfDay' => $defaultUntil->endOfDay(),
        ];
    }
}
