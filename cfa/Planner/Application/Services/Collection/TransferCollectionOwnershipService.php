<?php

namespace Cfa\Planner\Application\Services\Collection;

use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Application\Services\PlannerCollectionV2\CalendarItemMapService;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\ActivateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\DuplicateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TransferCollectionOwnershipService
{
    public function __construct(
        private readonly ActivateRepositoryInterface $activateRepository,
        private readonly PlannerCollectionRepositoryInterface $plannerCollectionRepository,
        private readonly DuplicateRepositoryInterface $duplicateRepository,
        private readonly CalendarItemMapService $calendarItemMapService,
    ) {}

    public function transferToUser(PlannerCollection $plannerCollection, User $newOwner): void
    {
        PlannerCollection::setQueryingEnabled();
        ActivatedPlannerCollection::setQueryingEnabled();
        $plannerCollection->load(['user' => fn($builder) => $builder->withTrashed()]);
        $plannerCollection->loadMissing(
            ['allActivatedPlannerCollections.user' => fn($builder) => $builder->withTrashed()],
        );
        ActivatedPlannerCollection::setQueryingDisabled();
        PlannerCollection::setQueryingDisabled();

        $originalOwner = $plannerCollection->user;

        // Get users who activated the collection and are not a colleague of the new user
        $nonColleaguesWithAccess = $plannerCollection
            ->usersWithAccess
            ->reject(fn(User $colleague): bool => $colleague->isColleague($newOwner));
        $nonColleaguesWhoActivatedCollection = $plannerCollection
            ->allActivatedPlannerCollections
            ->pluck('user')
            ->unique()
            ->reject(fn(User $colleague): bool => $colleague->isColleague($newOwner));

        DB::transaction(function () use (
            $plannerCollection,
            $originalOwner,
            $newOwner,
            $nonColleaguesWhoActivatedCollection,
            $nonColleaguesWithAccess,
        ): void {
            // When all users are colleagues of the new user
            if ($nonColleaguesWhoActivatedCollection->isEmpty()) {
                $this->handleTransfer($plannerCollection, $originalOwner, $newOwner);

                return;
            }

            // When there are users that are not a colleague of the new owner
            // Group the users by school as we will be creating a duplicate for each school
            $originalOwnerSchoolIds = $originalOwner->careers()->withTrashed()->pluck('school_id');
            $nonColleaguesWhoActivatedCollectionBySchoolId = $nonColleaguesWhoActivatedCollection
                ->reject(fn(User $user): bool => $user->activeCareers()->isEmpty())
                ->groupBy(
                    fn(User $user): int => (
                        $user->activeCareers()->first(fn(Career $career): bool =>
                            $originalOwnerSchoolIds->contains($career->school_id))
                        ?? $user->activeCareers()->first()
                    )->school_id,
                );
            $nonColleaguesWhoActivatedCollectionBySchoolId->each(
                function (Collection $users, int $schoolId) use ($plannerCollection): void {
                    $this->transferToSchool($schoolId, $plannerCollection, $users);
                },
            );

            $this->removeSchoolAccess($plannerCollection, $nonColleaguesWhoActivatedCollectionBySchoolId->keys());
            $this->removeUserAccess($plannerCollection, $nonColleaguesWithAccess);

            $this->handleTransfer($plannerCollection, $originalOwner, $newOwner);
        });
    }

    public function transferToSchool(int $schoolId, PlannerCollection $plannerCollection, Collection $users): void
    {
        $duplicatedPlannerCollection = $this->duplicateCollectionForSchool($plannerCollection, $schoolId);
        $users->each(function (User $user) use ($plannerCollection, $duplicatedPlannerCollection): void {
            $this->activateDuplicateForUser($user, $plannerCollection, $duplicatedPlannerCollection);
        });
    }

    public function handleTransfer(PlannerCollection $plannerCollection, ?User $originalOwner, User $newOwner): void
    {
        ActivatedPlannerCollection::setQueryingEnabled();
        $newOwnerOriginalActivatedCollection = $plannerCollection
            ->allActivatedPlannerCollections
            ->where('user_id', $newOwner->id)
            ->where('is_writable', true)
            ->first();
        ActivatedPlannerCollection::setQueryingDisabled();
        // Change the user_id of the plannerCollection to the new users
        $plannerCollection->user_id = $newOwner->id;
        $plannerCollection->school_id = null;
        $plannerCollection->favourited_at = $newOwnerOriginalActivatedCollection->favourited_at;
        $plannerCollection->archived_at = $newOwnerOriginalActivatedCollection->archived_at;
        $this->plannerCollectionRepository->save($plannerCollection);

        $plannerCollection->usersWithWriteAccess()->detach($newOwner);

        // Activate a write collection for the old user
        if ($originalOwner) {
            $this->activateCollectionForOriginalOwner($originalOwner, $plannerCollection);
        }

        $this->calendarItemMapService->remapCalendarItemRowsForUser(
            $newOwner,
            $newOwnerOriginalActivatedCollection,
            $plannerCollection,
        );

        ActivatedPlannerCollection::setQueryingEnabled();
        $newOwnerOriginalActivatedCollection->delete();
        ActivatedPlannerCollection::setQueryingDisabled();
    }

    private function activateCollectionForOriginalOwner(User $originalOwner, PlannerCollection $plannerCollection): void
    {
        $plannerCollection->usersWithWriteAccess()->attach($originalOwner);
        $originalOwnerActivatedCollection = $this->activateRepository->activateForUser(
            $originalOwner,
            $plannerCollection,
            true,
        );
        $originalOwnerActivatedCollection->favourited_at = $plannerCollection->favourited_at;
        $originalOwnerActivatedCollection->archived_at = $plannerCollection->archived_at;
        $this->plannerCollectionRepository->save($originalOwnerActivatedCollection);

        $this->calendarItemMapService->remapCalendarItemRowsForUser(
            $originalOwner,
            $plannerCollection,
            $originalOwnerActivatedCollection,
        );
    }

    private function duplicateCollectionForSchool(
        PlannerCollection $plannerCollection,
        int $schoolId,
    ): PlannerCollectionInterface {
        return $this->duplicateRepository->duplicate(
            $plannerCollection,
            ['user_id' => null, 'school_id' => $schoolId],
        );
    }

    private function activateDuplicateForUser(
        User $user,
        PlannerCollection $originalPlannerCollection,
        PlannerCollection $duplicatePlannerCollection,
    ): void {
        ActivatedPlannerCollection::setQueryingEnabled();
        $activatedPlannerCollections = $originalPlannerCollection
            ->allActivatedPlannerCollections
            ->where('user_id', $user->id);
        ActivatedPlannerCollection::setQueryingDisabled();

        $activatedPlannerCollections->each(
            function (ActivatedPlannerCollection $activatedPlannerCollection) use (
                $duplicatePlannerCollection
            ): void {
                $activatedPlannerCollection->planner_collection_id = $duplicatePlannerCollection->id;
                $this->plannerCollectionRepository->save($activatedPlannerCollection);
                $this->duplicateRepository->remapOverwrites(
                    $activatedPlannerCollection,
                    $duplicatePlannerCollection,
                );
            },
        );
    }

    private function removeSchoolAccess(PlannerCollection $plannerCollection, Collection $schoolIds): void
    {
        $plannerCollection->schoolsWithAccess()->detach($schoolIds);
    }

    private function removeUserAccess(PlannerCollection $plannerCollection, Collection $users): void
    {
        $plannerCollection->usersWithAccess()->detach($users->pluck('id'));
    }
}
