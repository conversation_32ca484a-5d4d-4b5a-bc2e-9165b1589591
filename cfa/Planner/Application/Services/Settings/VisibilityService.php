<?php

namespace Cfa\Planner\Application\Services\Settings;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Planner\Domain\Settings\Activity\ActivitySettings;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Collection;

class VisibilityService
{
    /**
     * Get the active settings settings for one school.
     *
     * @param School|null $school The given school.
     *
     * @throws AuthorizationException When no schools are linked to the user.
     */
    public function getActiveSettings(?School $school = null): Collection
    {
        return $this->getSettings(true, $school);
    }

    /**
     * Get all the settings settings for one school.
     *
     * @param School|null $school The given school.
     *
     * @throws AuthorizationException When no schools are linked to the user.
     */
    public function getAllSettings(?School $school = null): Collection
    {
        return $this->getSettings(false, $school);
    }

    /**
     * Get all the settings or only the active settings for one school.
     *
     * @param bool $onlyActive Get only the active settings.
     * @param School|null $school The current school.
     *
     * @throws AuthorizationException When no schools are linked to the user.
     */
    protected function getSettings(bool $onlyActive, ?School $school = null): Collection
    {
        if (!$school) {
            $school = school();
        }

        // Activity settings.
        $uniqueActivityKey = function (ActivitySettings $activitysettings) {
            return $activitysettings->personal_calendar . $activitysettings->activity_type->name;
        };

        $activitySettings = ActivitySettings::whereSchoolId($school->id)
            ->get()
            ->keyBy($uniqueActivityKey);

        $defaultActivitySettings = tenant()->getDefaultActivitySettings()
            ->keyBy($uniqueActivityKey);

        $activitySettings = $defaultActivitySettings->merge($activitySettings);
        $activitySettings = $onlyActive ? $activitySettings->filter->is_active : $activitySettings;

        // School settings
        $hideOldOVSGCurriculumSetting = SchoolSettings::where('school_id', $school->id)
            ->where('type', SchoolSettingsType::HideOldOVSGCurriculum)
            ->first()?->value;

        $showToggle =
            FeatureToggle::isActive(Feature::OVSGLeerlokaal) &&
            $school->educationalnetwork->uid === EducationalNetwork::OVSG_UID;

        return collect([
            'activity' => collect([
                'personal' => $activitySettings->filter->personal_calendar->values(),
                'school' => $activitySettings->reject->personal_calendar->values(),
                SchoolSettingsType::HideOldOVSGCurriculum->value => $hideOldOVSGCurriculumSetting ?? false,
                'showToggleHideOldOVSGCurriculum' => $showToggle,
            ]),
        ]);
    }
}
