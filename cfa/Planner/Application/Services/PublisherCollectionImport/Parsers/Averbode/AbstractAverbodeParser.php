<?php

namespace Cfa\Planner\Application\Services\PublisherCollectionImport\Parsers\Averbode;

use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Planner\Application\Services\PublisherCollectionImport\Parsers\AbstractParser;
use Override;
use SimpleXMLElement;

abstract class AbstractAverbodeParser extends AbstractParser
{
    /**
     * {@inheritdoc}
     *
     * @param SimpleXMLElement $xml LearningObjectiveCategories xml element.
     * @param int $index Counter for lesson.
     */
    #[Override]
    protected function parseMethodDetails(SimpleXMLElement $xml, int $index): array
    {
        $learningArea = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LEARNINGAREA');
        $lesson = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LESSON');
        $chapter = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'CHAPTER');

        return [
            'objective' => $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'OBJECTIVEPRIORITY'),
            'lesson_nr' => $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LESSONNUMBER'),
            'lesson' => $lesson,
            'learningarea' => $learningArea,
            'domain' => null,
            'chapter' => is_numeric($chapter) ? 'Hoofdstuk ' . $chapter : $chapter,
            'group' => $chapter . $lesson,
        ];
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function transformNetworkGoal(EducationalNetwork $educationalNetwork, string $originalGoal): string|array
    {
        $goal = parent::transformNetworkGoal($educationalNetwork, $originalGoal);

        switch ($educationalNetwork->uid) {
            case EducationalNetwork::GO_UID:
                $goal = $this->fixGOGoal($goal);
                break;
            case EducationalNetwork::OVSG_UID:
                $goal = $this->fixOVSGGoal($goal);
                break;
            case EducationalNetwork::VVKBAO_UID:
                $goal = $this->fixVVKBAOGoal($goal);
                $goal = preg_replace('/__+/', '_', $goal);
                break;
        }

        return $goal;
    }

    /**
     * Convert spaces to dots.
     *
     * @param string $goal The original goal.
     */
    protected function fixGOGoal(string $goal): string|array
    {
        return $this->trimZeros(str_replace(' ', '.', $goal));
    }

    /**
     * Format an incoming OVSG goal for database lookup.
     *
     * @param string $goal The original goal.
     */
    protected function fixOVSGGoal(string $goal): string|array
    {
        return $this->trimZeros($this->replaceSpecialChars($goal));
    }

    /**
     * Example: convert MK89a to MK_89_a
     *
     * @param string $goal The original goal.
     */
    abstract protected function fixVVKBAOGoal(string $goal): string|array;
}
