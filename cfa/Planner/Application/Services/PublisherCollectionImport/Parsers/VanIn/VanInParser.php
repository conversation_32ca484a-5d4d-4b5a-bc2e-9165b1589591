<?php

namespace Cfa\Planner\Application\Services\PublisherCollectionImport\Parsers\VanIn;

use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Planner\Application\Services\PublisherCollectionImport\Parsers\AbstractParser;
use Cfa\Planner\Application\Services\Zill\ZILLCorrelationService;
use Override;
use SimpleXMLElement;

class VanInParser extends AbstractParser
{
    /**
     * VanInParser constructor.
     */
    public function __construct(ZILLCorrelationService $zillCorrelationService)
    {
        parent::__construct($zillCorrelationService);

        $this->goalConfig[EducationalNetwork::GO_UID]['addLearningArea'] = false;
    }

    /**
     * {@inheritdoc}
     *
     * @param SimpleXMLElement $xml LearningObjectiveCategories xml element.
     * @param int $index Counter for lesson.
     */
    #[Override]
    protected function parseMethodDetails(SimpleXMLElement $xml, int $index): array
    {
        $learningArea = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LEARNINGAREA');
        $lesson = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LESSON');
        $chapter = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'CHAPTER');
        $chapter = is_string($chapter) ? $chapter :
            $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'BLOCK');
        $domain = $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LEARNINGDOMAIN');

        return [
            'lesson_nr' => $this->getFromAttribute($xml, 'LearningObjectiveCategory', 'LESSONNUMBER'),
            'lesson' => $lesson,
            'learningarea' => $learningArea,
            'domain' => $domain,
            'chapter' => $chapter,
            'group' => $chapter . '_' . $lesson,
        ];
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function transformNetworkGoal(EducationalNetwork $educationalNetwork, string $originalGoal): string|array
    {
        $goal = parent::transformNetworkGoal($educationalNetwork, $originalGoal);

        return $educationalNetwork->uid === EducationalNetwork::GO_UID ?
            $this->trimZeros($goal) :
            $goal;
    }
}
