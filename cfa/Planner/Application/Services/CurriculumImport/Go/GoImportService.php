<?php

namespace Cfa\Planner\Application\Services\CurriculumImport\Go;

use Illuminate\Support\Str;

class GoImportService
{
    /**
     * Map learningarea and domain to a prefix for reference codes.
     */
    public const GOAL_PREFIXES = [
        'wiskunde' => [
            'attitudes' => 'WIS-Att-',
            'algemene attitudes' => 'WIS-AA-',
            'bewerkingen' => 'WIS-Bew-',
            'getallen' => 'WIS-Get-',
            'meetkunde' => 'WIS-MK-',
            'meten' => 'WIS-Met-',
            'ruimte' => 'WIS-Rui-',
            'problemen oplossen' => 'WIS-PO-',
        ],
        'wo' => [
            'mens en maatschappij' => 'WO-MM-',
            'natuur' => 'WO-Nat-',
            'ruimte' => 'WO-Rui-',
            'techniek' => 'WO-Tec-',
            'tijd' => 'WO-Tij-',
            'bronnengebruik' => 'WO-Bro-',
        ],
        'nederlands' => [
            'overkoepelende attitudes mondelinge taalvaardigheid' => 'NED-OA-MT-',
            'overkoepelende attitudes schriftelijke taalvaardigheid' => 'NED-OA-ST-',
            'luisteren (mondelinge taalvaardigheid)' => 'NED-Lui-MT-',
            'luisteren (schriftelijke taalvaardigheid)' => 'NED-Lui-ST-',
            'luisteren' => 'NED-Lui-MT-',
            'spreken (mondelinge taalvaardigheid)' => 'NED-Spr-MT-',
            'spreken (schriftelijke taalvaardigheid)' => 'NED-Spr-ST-',
            'spreken' => 'NED-Spr-MT-',
            'lezen (schriftelijke taalvaardigheid)' => 'NED-Lez-ST-',
            'schrijven (schriftelijke taalvaardigheid)' => 'NED-Sch-ST-',
            'gesprekken voeren (mondelinge taalvaardigheid)' => 'NED-GV-MT-',
        ],
        'muzische vorming' => [
            'beeld' => 'MV-Bee-',
            'muziek' => 'MV-Muz-',
            'drama' => 'MV-Dra-',
            'beweging' => 'MV-Bew-',
            'media' => 'MV-Med-',
            'muzische grondhouding' => 'MV-MG-',
            'algemene attitudes' => 'MV-AA-',
        ],
        'media' => [
            'attitudinale doelen' => 'MED-AD-',
            'media begrijpen' => 'MED-MB-',
            'media gebruiken' => 'MED-MG-',
            'communiceren via media' => 'MED-CVM-',
            'nadenken over het eigen mediagebruik' => 'MED-NEM-',
        ],
        'lichamelijke opvoeding' => [
            'persoonsdoelen' => 'LO-PD-',
            'bewegingsdoelen (motorische competenties)' => 'LO-BD-MC-',
        ],
        'frans' => [
            'attitudes' => 'FRA-Att-',
            'luisteren' => 'FRA-Lui-',
            'lezen' => 'FRA-Lez-',
            'spreken' => 'FRA-Spr-',
            'schrijven' => 'FRA-Sch-',
            'functioneel inzetten' => 'FRA-FI-',
            'mondelinge interactie' => 'FRA-MI-',
            'lexicale en grammaticale elementen' => 'FRA-LGE-',
            '(geen deelleergebied)' => 'FRA-GDL-',
        ],
    ];

    /**
     * Try to find possible GO goal code prefixes using only domain and learning area.
     *
     * @param string $learningArea The learning area to lookup.
     * @param string $domain The domain to lookup.
     */
    public static function mapGoGoalPrefix(string $learningArea, string $domain): ?array
    {
        $isPreschool = false;

        if (Str::contains($domain, '(kleuter)')) {
            $isPreschool = true;
            $domain = Str::replaceLast('(kleuter)', '', $domain);
        }

        $domain = Str::lower(trim($domain));
        $learningArea = Str::lower(trim($learningArea));

        $prefix = self::GOAL_PREFIXES[$learningArea][$domain] ?? null;

        if ($prefix) {
            $types = [
                $prefix . 'KL',
                $prefix . 'inclKL',
            ];

            // Try all possible class types when preschool not specifically included in domain name.
            if (!$isPreschool) {
                $types[] = $prefix . 'L';
            }

            return $types;
        }

        // Not found.
        return null;
    }
}
