<?php

namespace Cfa\Planner\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelConfiguration;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfigurationRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Override;

class GradeLevelConfigurationRepository extends Repository implements GradeLevelConfigurationRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new GradeLevelConfiguration();
    }

    #[Override]
    public function clearAndUpdateNonGoalConfigurations(
        int $educationalNetworkId,
        CurriculumType $curriculumType,
        string $mandatoryLevelOVSG,
        string $observationLevelOVSG,
    ): void {
        $query = $this->baseQuery($educationalNetworkId, $curriculumType);

        // Step 1: Clear
        $query->update([
            'grade_level_configurations.level' => null,
            'parent_nodes.is_observation' => false,
        ]);

        // Step 2: Update to mandatory
        $query->clone()
            ->whereNotNull('child_grade_level_config.level')
            ->update(['grade_level_configurations.level' => $mandatoryLevelOVSG]);

        // Step 3: Update to observation
        $query->clone()
            ->where('child_grade_level_config.level', $observationLevelOVSG)
            ->update([
                'grade_level_configurations.level' => $observationLevelOVSG,
                'parent_nodes.is_observation' => true,
            ]);
    }

    private function baseQuery(int $educationalNetworkId, CurriculumType $curriculumType): Builder
    {
        return GradeLevelConfiguration::query()
            ->join(
                'curriculumnodes as parent_nodes',
                'grade_level_configurations.curriculumnode_id',
                '=',
                'parent_nodes.id',
            )
            ->join(
                'curriculumnode_curriculumnode',
                'parent_nodes.id',
                '=',
                'curriculumnode_curriculumnode.parent_id',
            )
            ->join(
                'grade_level_configurations as child_grade_level_config',
                function ($join): void {
                    $join->on(
                        'curriculumnode_curriculumnode.child_id',
                        '=',
                        'child_grade_level_config.curriculumnode_id',
                    )
                        ->on(
                            'grade_level_configurations.natural_study_year',
                            '=',
                            'child_grade_level_config.natural_study_year',
                        )
                        ->on(
                            'grade_level_configurations.target_audience_type',
                            '=',
                            'child_grade_level_config.target_audience_type',
                        );
                },
            )
            ->where('parent_nodes.type', '!=', CurriculumNodeType::Goal)
            ->where('parent_nodes.educationalnetwork_id', $educationalNetworkId)
            ->where('parent_nodes.curriculum_type', $curriculumType->value);
    }
}
