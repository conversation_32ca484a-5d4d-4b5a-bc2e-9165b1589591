<?php

namespace Cfa\Planner\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\CalendarItemRepositoryInterface;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordData;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\Record\Record as LegacyRecord;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Override;

use function optional;

class CalendarItemRepository extends Repository implements CalendarItemRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new CalendarItem();
    }

    #[Override]
    public function getLinkedToLegacyRecord(LegacyRecord $record): array
    {
        $schoolyear = Schoolyear::getRepository()->getCurrent();

        return $record->calendaritems()
            ->whereBetween('start', [$schoolyear->start, $schoolyear->end])
            ->whereNull('master_item_id')
            ->pluck('calendaritems.uid')
            ->all();
    }

    #[Override]
    public function getLinkedToRecord(Record $record, User $user, School $school): Collection
    {
        $schoolyear = Schoolyear::getRepository()->getCurrent();
        $originalCollection = $record->getCollectionRelation();
        $collection = app(PlannerCollectionRepositoryInterface::class)
            ->getWritableCollection($originalCollection, $user);
        $collection = $collection ?: $originalCollection;
        $collectionColumn = $collection instanceof ActivatedPlannerCollection ? 'activated_planner_collection_id'
            : 'planner_collection_id';
        $conditions = [
            'calendaritemrows.creator_id' => $user->id,
            $collectionColumn => $collection->id,
            'record_v2_id' => $record->id,
        ];

        return CalendarItem::join(
            'calendaritemrows',
            fn(JoinClause $joinClause): JoinClause => $joinClause
                ->on('calendaritems.id', 'calendaritemrows.calendaritem_id')
                ->where($conditions)
                ->whereNull('calendaritemrows.deleted_at'),
        )
            ->whereNull('master_item_id')
            ->where('owner_id', $user->id)
            ->where('school_id', $school->id)
            ->whereBetween('start', [$schoolyear->start, $schoolyear->end])
            ->distinct()
            ->get(
                [
                    'calendaritems.id',
                    'calendaritems.uid',
                    'start',
                    'repetition_period',
                ],
            );
    }

    #[Override]
    public function all(
        School $school,
        Carbon $fromDate,
        Carbon $toDate,
        User $owner,
        ?Collection $users = null,
        ?Group $group = null,
    ): EloquentCollection {
        return $this->getCalendarItemsBasedOnParams($school, $fromDate, $toDate, $owner, $users, $group);
    }

    public function allWithRecords(
        School $school,
        Carbon $fromDate,
        Carbon $toDate,
        User $owner,
        ?Collection $users = null,
        ?Group $group = null,
    ): EloquentCollection {
        $calendarItems = $this
            ->getCalendarItemsBasedOnParams($school, $fromDate, $toDate, $owner, $users, $group, true);
        $rows = $calendarItems->pluck('rows')->flatten(1);
        $curriculumNodesIds = $rows
            ->map(fn(CalendarItemRow $calendarItemRow): ?array => $calendarItemRow
                ->record_data?->getCurriculumNodeIds())
            ->flatten(1)
            ->unique()
            ->all();
        $legacyRecordIds = $rows->pluck('record_id');
        if ($legacyRecordIds->isNotEmpty()) {
            $legacyRecordCurriculumNodesIds = DB::table('curriculumnode_record')
                ->whereIn('record_id', $legacyRecordIds)
                ->pluck('curriculumnode_id');
            $curriculumNodesIds = [...$curriculumNodesIds, ...$legacyRecordCurriculumNodesIds];
        }
        $curriculumNodeGoals = app(CurriculumNodeGoalsRepository::class)
            ->goals($curriculumNodesIds);
        $rows->each(function (CalendarItemRow $calendarItemRow) use ($curriculumNodeGoals): void {
            if ($calendarItemRow->record_id !== null) {
                $calendarItemRow->record_data = RecordData::fromLegacyRecord($calendarItemRow->record);
            }
            $calendarItemRow->record_data?->setCurriculumNodeModels($curriculumNodeGoals);
        });

        return $calendarItems;
    }

    /** @SuppressWarnings(PHPMD.BooleanArgumentFlag) */
    private function getCalendarItemsBasedOnParams(
        School $school,
        Carbon $fromDate,
        Carbon $toDate,
        User $owner,
        ?Collection $users = null,
        ?Group $group = null,
        bool $withRecords = false,
    ): EloquentCollection {
        $accessToGroupsIds = Schoolyear::getRepository()
            ->getBetween($fromDate, $toDate)
            ->map(fn(Schoolyear $schoolyear): Collection => Group::getRepository()
                ->getGroupsForUserAndSchool($owner, $school, $schoolyear)
                ->pluck('id'))->flatten(1);

        $with = [
            'groups:id,uid',
            'subject:id,uid',
            'school:id,uid',
            'owner:id,uid',
            'masterItem:id,uid',
        ];
        $rowFields = ['id', 'calendaritem_id', 'uid', 'title', 'purpose'];
        if ($withRecords) {
            $with[] = 'rows.record';
            $rowFields[] = 'record_data';
            $rowFields[] = 'record_id';
        }
        $with['rows'] = fn(HasMany $query): HasMany => $query
            ->select([
                ...$rowFields,
            ]);

        $calendarItemsBasedOnParams = CalendarItem::query()
            ->with($with)
            ->select('calendaritems.*')
            ->withCount('repeatedItems')
            ->inDateInterval($fromDate, $toDate)
            ->distinct()
            ->when($group || $accessToGroupsIds, function (Builder $query): void {
                $query
                    ->leftJoin(
                        'calendaritem_group',
                        'calendaritem_group.calendaritem_id',
                        'calendaritems.id',
                    );
            })
            ->where('calendaritems.school_id', $school->id)
            ->where(function (Builder $query) use ($group, $users, $owner, $accessToGroupsIds): void {
                $query
                    // Group items.
                    ->when($group, function (Builder $query) use ($group): void {
                        $query->where('calendaritem_group.group_id', $group->id);
                    })
                    // User items.
                    ->when(!$group, function (Builder $query) use ($owner): void {
                        $query->where(function (Builder $query) use ($owner): void {
                            $query->where('calendaritems.owner_id', $owner->id);
                        });
                    })
                    // Colleague items.
                    ->when(
                        optional($users)->isNotEmpty(),
                        function ($query) use ($users): void {
                            $query->orWhere(function ($query) use ($users): void {
                                $query->whereIn('calendaritems.owner_id', $users->pluck('id')->toArray());
                            });
                        },
                    )
                    // School items.
                    ->orWhere(function (Builder $query) use ($group, $accessToGroupsIds): void {
                        $query
                            ->whereNull('calendaritems.owner_id')
                            ->when($group, function (Builder $query) use ($group): void {
                                $query->where('calendaritem_group.group_id', $group->id);
                            })
                            // If groups are linked to a school item we only show them to the user if he has access
                            // to those groups.
                            ->when($group === null, function (Builder $query) use ($accessToGroupsIds): void {
                                $query->where(function (Builder $query) use ($accessToGroupsIds): void {
                                    $query->whereNull('calendaritem_group.group_id')
                                        ->orWhereIn('calendaritem_group.group_id', $accessToGroupsIds);
                                });
                            });
                    });
            });

        $systemCalendarItems = CalendarItem::query()
            ->select(['calendaritems.*', DB::raw('0 AS repeated_items_count')])
            ->inDateInterval($fromDate, $toDate)
            ->whereNull('calendaritems.school_id');

        return $calendarItemsBasedOnParams->union($systemCalendarItems)->get();
    }
}
