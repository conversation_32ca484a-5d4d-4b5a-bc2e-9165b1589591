<?php

namespace Cfa\Planner\Application\Repositories\CollectionV2;

use App\Repositories\CacheRepository;
use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\Reporting\CurriculumNodeReportingRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Override;

class CurriculumNodeReportingCacheRepository implements CurriculumNodeReportingRepositoryInterface
{
    private const KEY_TOTALS_TO_ACHIEVE_FOR_TARGET_AUDIENCE = 'totalsToAchieveForTargetAudience:';
    private const KEY_TOTALS_TO_ACHIEVE_FOR_CURRICULUM_TYPE = 'totalsToAchieveForCurriculumType:';
    private const KEY_NATURAL_STUDY_YEAR = 'naturalStudyYear:';
    private const TAG_REPORTING_FOR_SCHOOL = 'curriculumNodeReportingForSchool:';

    public function __construct(
        private readonly CurriculumNodeReportingRepository $curriculumNodeReportingRepository,
    ) {}

    #[Override]
    public function getTotalsToAchieve(
        Group $group,
        CurriculumType $curriculumType = CurriculumType::Default,
    ): Collection {
        $key = $this->getCacheKey([
            self::KEY_TOTALS_TO_ACHIEVE_FOR_CURRICULUM_TYPE . $curriculumType->value,
            self::KEY_TOTALS_TO_ACHIEVE_FOR_TARGET_AUDIENCE . $group->target_audience_type->value,
            self::KEY_NATURAL_STUDY_YEAR . $group->natural_study_year,
        ]);

        return Cache::tags($this->getCacheTags($group->school))
            ->rememberForever(
                $key,
                fn(): Collection => $this->curriculumNodeReportingRepository->getTotalsToAchieve(
                    $group,
                    $curriculumType,
                ),
            );
    }

    #[Override]
    public function getAchievedTotals(
        Group $group,
        ?Schoolyear $schoolyear = null,
        ?Carbon $from = null,
        ?Carbon $until = null,
        CurriculumType $curriculumType = CurriculumType::Default,
    ): Collection {
        return $this->curriculumNodeReportingRepository->getAchievedTotals(
            $group,
            $schoolyear,
            $from,
            $until,
            $curriculumType,
        );
    }

    #[Override]
    public function getPlannedGoals(
        Group $group,
        CurriculumNode $parent,
        ?Schoolyear $schoolyear = null,
        ?Carbon $from = null,
        ?Carbon $until = null,
        ?User $user = null,
    ): Collection {
        return $this->curriculumNodeReportingRepository
            ->getPlannedGoals($group, $parent, $schoolyear, $from, $until, $user);
    }

    protected function getCacheTags(School $school): array
    {
        return [
            'repository:' . static::class,
            'tenant:' . tenant()->uid,
            self::TAG_REPORTING_FOR_SCHOOL . $school->uid,
        ];
    }

    protected function getCacheKey(array $keys): string
    {
        return implode(CacheRepository::KEY_GLUE, $keys);
    }
}
