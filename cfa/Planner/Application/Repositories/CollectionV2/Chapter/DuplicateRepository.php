<?php

namespace Cfa\Planner\Application\Repositories\CollectionV2\Chapter;

use App\Exceptions\System\RelationNotLoadedException;
use Cfa\Common\Application\Traits\CallWithQueryingEnabledOnModels;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterOverwrite;
use Cfa\Planner\Domain\CollectionV2\Chapter\ChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\DuplicateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\CopyRecordsToChapterRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\DuplicateRepositoryInterface as RecordDuplicateRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Override;

class DuplicateRepository implements DuplicateRepositoryInterface
{
    use CallWithQueryingEnabledOnModels;

    public function __construct(
        private readonly ChapterRepositoryInterface $chapterRepository,
        private readonly CopyRecordsToChapterRepositoryInterface $copyRecordsToChapter,
        private readonly RecordDuplicateRepositoryInterface $recordDuplicateRepository,
    ) {
        $this->addQueryingEnabledModel(ChapterOverwrite::class);
    }

    /** @throws RelationNotLoadedException */
    #[Override]
    public function duplicate(Chapter $chapter, PlannerCollectionInterface $collection): Chapter
    {
        /** @var Chapter $newChapter */
        $newChapter = $chapter->replicate([
            'previous_chapter_id',
            'planner_collection_id',
            'activated_planner_collection_id',
            'previous_chapter_id',
        ]);

        if ($collection instanceof PlannerCollection) {
            $newChapter->planner_collection_id = $collection->id;
            $newChapter->activated_planner_collection_id = null;
        }

        if ($collection instanceof ActivatedPlannerCollection) {
            if ($chapter->isInherited() && $chapter->relationLoaded('overwrites') === false) {
                throw new RelationNotLoadedException('overwrites');
            }
            $newChapter->activated_planner_collection_id = $collection->id;
            $newChapter->planner_collection_id = null;
        }

        $this->chapterRepository->save($newChapter);

        $records = $this->copyRecordsToChapter->copyRecordsToChapter($chapter, $collection, $newChapter);
        $newChapter->setRelation('records', $records);

        $newChapter->setAttribute('original_id', $chapter->id);

        return $newChapter;
    }

    #[Override]
    public function remapOverwrites(
        Chapter $duplicateChapter,
        ActivatedPlannerCollection $activatedPlannerCollection,
    ): Chapter {
        $this->withQueryingEnabledOnModels(
            fn(): bool =>
            ChapterOverwrite::where('chapter_id', $duplicateChapter->original_id)
                ->where('user_id', $activatedPlannerCollection->user_id)
                ->where('activated_planner_collection_id', $activatedPlannerCollection->id)
                ->update(['chapter_id' => $duplicateChapter->id]),
        );
        $this->recordDuplicateRepository->remapToChapter($activatedPlannerCollection, $duplicateChapter);

        $duplicateChapter->records->each(function (Record $record) use ($activatedPlannerCollection): void {
            $this->recordDuplicateRepository->remapOverwrites($record, $activatedPlannerCollection);
        });

        return $duplicateChapter;
    }

    #[Override]
    public function duplicateChapterOrOverwrites(
        Chapter $chapter,
        ActivatedPlannerCollection $originalActivatedCollection,
        ActivatedPlannerCollection $duplicateActivatedCollection,
    ): Chapter {
        $duplicateChapter = null;
        if ($chapter->planner_collection_id) {
            $this->duplicateOverwrites($chapter, $originalActivatedCollection, $duplicateActivatedCollection);
        }
        if ($chapter->activated_planner_collection_id) {
            $duplicateChapter = $chapter->replicate();
            $duplicateChapter->activated_planner_collection_id = $duplicateActivatedCollection->id;
            $this->chapterRepository->save($duplicateChapter);
        }

        $chapter
            ->records
            ->each(function (Record $record) use (
                $chapter,
                $duplicateChapter,
                $originalActivatedCollection,
                $duplicateActivatedCollection
            ): void {
                $record->planner_collection_id
                    ? $this->recordDuplicateRepository->duplicateOverwrites(
                        $record,
                        $duplicateChapter ?? $chapter,
                        $originalActivatedCollection,
                        $duplicateActivatedCollection,
                    )
                    : $this->recordDuplicateRepository->duplicateWithoutReordering(
                        $duplicateActivatedCollection,
                        $duplicateChapter ?? $chapter,
                        $record,
                    );
            });

        return $chapter;
    }

    private function duplicateOverwrites(
        Chapter $chapter,
        ActivatedPlannerCollection $originalActivatedCollection,
        ActivatedPlannerCollection $duplicateActivatedCollection,
    ): void {
        $overwrites = $this->withQueryingEnabledOnModels(
            fn(): Collection =>
            ChapterOverwrite::where('chapter_id', $chapter->id)
                ->where('user_id', $originalActivatedCollection->getOriginal('user_id'))
                ->where('activated_planner_collection_id', $originalActivatedCollection->id)
                ->get(),
        );

        $overwrites
            ->map(function (ChapterOverwrite $overwrite) use ($duplicateActivatedCollection): ChapterOverwrite {
                $overwrite->activated_planner_collection_id = $duplicateActivatedCollection->id;
                $overwrite->user_id = $duplicateActivatedCollection->user_id;
                $duplicateOverwrite = $overwrite->replicate();
                $duplicateOverwrite->created_at = Carbon::now();
                $this->withQueryingEnabledOnModels(fn(): bool => $duplicateOverwrite->save());

                return $duplicateOverwrite;
            });
    }
}
