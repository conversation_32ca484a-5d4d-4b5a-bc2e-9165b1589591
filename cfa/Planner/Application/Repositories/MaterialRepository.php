<?php

namespace Cfa\Planner\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Carbon\Carbon;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Material\MaterialRepositoryInterface;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Support\Collection;
use Override;

class MaterialRepository extends Repository implements MaterialRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new Material();
    }

    #[Override]
    public function massReplicateMaterials(
        Collection $sourceMaterials,
        Collection $destinationRecords,
        Carbon $createdAt,
    ): void {
        $this->model->insert(
            $sourceMaterials->map(
                function (Material $material) use ($destinationRecords, $createdAt) {
                    return array_merge(
                        $material->replicate()->getAttributes(),
                        [
                            'uid' => uuid(),
                            'created_at' => $createdAt,
                            'updated_at' => $createdAt,
                            'record_id' => $destinationRecords->get($material->record_id)->id,
                        ],
                    );
                },
            )->all(),
        );
    }

    /**
     * Activate the materials under the given publisher or shared collection.
     *
     * @param array $insertedRecordUids The uids of the inserted records.
     * @param Collection $materialsToActivate The materials to activate.
     * @param Carbon $activatedAt Time the materials are activated.
     */
    #[Override]
    public function activate(
        array $insertedRecordUids,
        Collection $materialsToActivate,
        Carbon $activatedAt,
    ): void {
        $records = Record::withMasked()
            ->whereIn('uid', $insertedRecordUids)
            ->get(['id', 'parent_record_id'])
            ->keyBy('parent_record_id');

        Material::insert(
            $materialsToActivate->map(
                function (Material $publisherMaterial) use (
                    $records,
                    $activatedAt
                ) {
                    return array_merge(
                        $publisherMaterial->replicate()->getAttributes(),
                        [
                            'uid' => uuid(),
                            'created_at' => $activatedAt,
                            'updated_at' => $activatedAt,
                            'record_id' => $records->get($publisherMaterial->record_id)->id,
                        ],
                    );
                },
            )
                ->all(),
        );
    }
}
