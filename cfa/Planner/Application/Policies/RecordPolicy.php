<?php

namespace Cfa\Planner\Application\Policies;

use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Auth\Access\HandlesAuthorization;

use function collect;

class RecordPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the Record.
     *
     * @param User $user Authenticated user.
     * @param Record $record Record item to update.
     */
    public function view(User $user, Record $record): bool
    {
        $activeSchools = optional($record->owner)->activeSchools() ?? collect();

        $inSameSchool = $activeSchools->contains(function (School $school) use ($user) {
            return $user->hasPermission(PermissionName::HasAccessToPlanner, $school);
        });

        if ($inSameSchool) {
            return true;
        }

        // This fallback is needed because of a bug detected in SOL20-3331, which has been around for more than a year.
        // It has too big an impact to fix the data, which is why we check the collection here as a fallback.
        $collection = optional($record->chapter)->plannercollection;

        return optional($collection)->owner_id === $user->id;
    }

    public function update(User $user, Record $record, ?Chapter $chapter = null): bool
    {
        if ($chapter !== null && $chapter->id !== $record->chapter_id) {
            return false;
        }

        return $record->owner_id === $user->id;
    }

    /**
     * Determine whether the user can view the Record in cms.
     *
     * @param CmsUser $user Authenticated user.
     * @param Record $record Record item to manage.
     */
    public function viewInCms(CmsUser $user, Record $record): bool
    {
        if (!$user->checkPermission(CmsUserPermission::CollectionsCrud)) {
            return false;
        }

        return empty($user->publisher_id) || $record->chapter->plannercollection->publisher_id === $user->publisher_id;
    }

    /**
     * Determine whether the user can make changes to the records.
     *
     * @param CmsUser $user Authenticated user.
     * @param Record $record Record item to manage.
     */
    public function manageInCms(CmsUser $user, Record $record): bool
    {
        return $this->viewInCms($user, $record) && !$record->chapter->plannercollection->isPublished();
    }
}
