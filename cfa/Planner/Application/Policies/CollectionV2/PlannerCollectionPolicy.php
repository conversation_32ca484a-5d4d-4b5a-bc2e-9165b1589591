<?php

namespace Cfa\Planner\Application\Policies\CollectionV2;

use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Admin\Domain\CmsUser\CmsUserPermission;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionRepositoryInterface;
use Illuminate\Auth\Access\HandlesAuthorization;

class PlannerCollectionPolicy
{
    use HandlesAuthorization;

    public function update(User $user, PlannerCollectionInterface $collection): bool
    {
        if ($collection->isOwner($user)) {
            return true;
        }
        if ($collection instanceof PlannerCollection) {
            if ($collection->school_id) {
                return $user->schoolIds()->contains($collection->school_id);
            }

            return $collection->usersWithWriteAccess()->where('entity_id', $user->id)->exists();
        }

        return false;
    }

    public function updateOwnerProperties(User $user, PlannerCollectionInterface $collection): bool
    {
        if ($collection instanceof PlannerCollection) {
            return $collection->isOwner($user);
        }
        if ($collection instanceof ActivatedPlannerCollection && $collection->isOwner($user)) {
            return !$collection->is_writable;
        }

        return false;
    }

    public function manage(User $user, PlannerCollectionInterface $collection): bool
    {
        return $collection->isOwner($user);
    }

    public function reactivate(User $user, PlannerCollectionInterface $collection): bool
    {
        if ($collection instanceof PlannerCollection) {
            ActivatedPlannerCollection::enableDeactivated();
            $writeCollection = app(PlannerCollectionRepositoryInterface::class)
                ->getWritableCollection($collection, $user);
            ActivatedPlannerCollection::disableDeactivated();

            return !is_null($writeCollection?->deactivated_at);
        }

        return !is_null($collection->deactivated_at);
    }

    public function viewInCms(CmsUser $user, PlannerCollectionInterface $collection): bool
    {
        if (!$user->checkPermission(CmsUserPermission::CollectionsCrud)) {
            return false;
        }

        return empty($user->publisher_id) || ($collection->publisher_id === $user->publisher_id);
    }

    public function publishInCms(CmsUser $user, PlannerCollectionInterface $collection): bool
    {
        return $collection->publisher_id !== Publisher::whereUid(Publisher::VAN_IN_DRAFT)->value('id');
    }

    public function manageInCms(CmsUser $user, PlannerCollectionInterface $collection): bool
    {
        return $this->viewInCms($user, $collection) && $collection->published_at === null;
    }

    public function activateLicensedCollections(CmsUser $user): bool
    {
        return $user->checkPermission(CmsUserPermission::ActivatePlannerCollectionsForTeachers);
    }
}
