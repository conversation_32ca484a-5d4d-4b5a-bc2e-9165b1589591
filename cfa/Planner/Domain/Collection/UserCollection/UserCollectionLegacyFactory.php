<?php

namespace Cfa\Planner\Domain\Collection\UserCollection;

use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\CollectionTemplate;
use Cfa\Planner\Domain\Collection\CollectionUpdatable;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Faker\Generator;

$factory->define(UserCollection::class, function (Generator $faker) {
    return [
        'uid' => uuid(),
        'owner_id' => User::randomId(),
        'publisher_id' => Publisher::randomId(),
        'parent_collection_id' => PublisherCollection::randomId(),
        'name' => $faker->name(),
        'version' => $faker->numberBetween(1, 5),
        'activated_at' => Carbon::instance($faker->dateTimeBetween('-2 years', 'now')),
        'updatable' => $faker->randomElement(CollectionUpdatable::cases()),
        'template' => $faker->randomElement(CollectionTemplate::cases()),
    ];
});

$factory->state(UserCollection::class, 'nonArchived', function (Generator $faker) {
    return [
        'archived_at' => null,
    ];
});

$factory->state(UserCollection::class, 'updatable', function (Generator $faker) {
    return [
        'updatable' => CollectionUpdatable::Updatable,
    ];
});
