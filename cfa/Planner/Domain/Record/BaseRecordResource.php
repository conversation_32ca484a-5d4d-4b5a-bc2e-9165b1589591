<?php

namespace Cfa\Planner\Domain\Record;

use Cfa\Planner\Application\Services\Zill\ZILLService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Override;

/**
 * @mixin Record
 * @extends JsonResource<Record>
 */
class BaseRecordResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'name' => $this->name,
            'purpose' => Str::upper($this->purpose->name),
            'zill' => $this->when(
                Auth::user() && app(ZILLService::class)->isZillActivatedForUser(Auth::user()),
                $this->zill,
                null,
            ),
            'is_saved_by_user' => $this->isSavedByOwner(),
        ];
    }
}
