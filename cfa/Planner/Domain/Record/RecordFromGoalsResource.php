<?php

namespace Cfa\Planner\Domain\Record;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Record
 * @extends JsonResource<Record>
 */
class RecordFromGoalsResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'name' => $this->name,
        ];
    }
}
