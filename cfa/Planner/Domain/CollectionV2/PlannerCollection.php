<?php

namespace Cfa\Planner\Domain\CollectionV2;

use App\Casts\EncodedString;
use App\Casts\Json;
use App\Casts\PlannerCollectionAttachments;
use App\Models\Model;
use Cfa\Common\Application\Traits\Archives;
use Cfa\Common\Application\Traits\DisableLazyLoading;
use Cfa\Common\Application\Traits\DisableQueryingOnModel;
use Cfa\Common\Application\Traits\Favourites;
use Cfa\Common\Application\Traits\InteractsWithMediaCovers;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Collection\CollectionUpdatable;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Auth;
use Override;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

use function array_key_exists;
use function array_merge;
use function collect;
use function json_decode;

/**
 * Cfa\Planner\Domain\CollectionV2\PlannerCollection
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int|null $user_id
 * @property int|null $school_id
 * @property int|null $previous_collection_id
 * @property int|null $publisher_id
 * @property int|null $published_parent_id
 * @property string $name
 * @property int|null $version
 * @property string|null $method_identifier
 * @property string|null $extra_info
 * @property Carbon|null $activated_at
 * @property Carbon|null $sent_at
 * @property Carbon|null $published_at
 * @property CollectionUpdatable $updatable
 * @property mixed|null $target_audiences
 * @property SupportCollection|null $attachments
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon|null $archived_at
 * @property Carbon|null $favourited_at
 * @property Carbon|null $deactivated_at
 * @property bool $is_writable
 * @property int|null $activated_planner_collections_count
 * @property-read Collection<int,ActivatedPlannerCollection> $activatedPlannerCollections
 * @property-read Collection<int,ActivatedPlannerCollection> $allActivatedPlannerCollections
 * @property-read Collection<int, Chapter> $chapters
 * @property-read int|null $chapters_count
 * @property-read ShareStatus $share_status
 * @property-read array $validation_rules
 * @property-read MediaCollection<int, Media> $media
 * @property-read int|null $media_count
 * @property-read PlannerCollection|null $previousCollection
 * @property-read Publisher|null $publisher
 * @property-read Collection<int, School> $schoolsWithAccess
 * @property-read int|null $schools_with_access_count
 * @property-read User|null $user
 * @property-read School|null $school
 * @property-read Collection<int, User> $usersWithAccess
 * @property-read int|null $users_with_access_count
 * @property-read Collection<int, User> $usersWithReadAccess
 * @property-read int|null $users_with_read_access_count
 * @property-read Collection<int, User> $usersWithWriteAccess
 * @property-read int|null $users_with_write_access_count
 * @property-read Collection<int,ActivatedPlannerCollection> $writeActivatedPlannerCollections
 * @property-read int|null $write_activated_planner_collections_count
 * @method static PlannerCollectionRepositoryInterface getRepository()
 * @method static Builder|PlannerCollection newModelQuery()
 * @method static Builder|PlannerCollection newQuery()
 * @method static Builder|PlannerCollection onlyTrashed()
 * @method static Builder|PlannerCollection query()
 * @method static Carbon|null randomActivatedAt()
 * @method static Carbon|null randomArchivedAt()
 * @method static SupportCollection|null randomAttachments()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomExtraInfo()
 * @method static Carbon|null randomFavouritedAt()
 * @method static int randomId()
 * @method static string randomName()
 * @method static int|null randomPreviousCollectionId()
 * @method static Carbon|null randomPublishedAt()
 * @method static int|null randomPublisherId()
 * @method static int|null randomSchoolId()
 * @method static Carbon|null randomSentAt()
 * @method static mixed|null randomTargetAudiences()
 * @method static string randomUid()
 * @method static CollectionUpdatable randomUpdatable()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUserId()
 * @method static int|null randomVersion()
 * @method static Builder|PlannerCollection whereActivatedAt($value)
 * @method static Builder|PlannerCollection whereArchivedAt($value)
 * @method static Builder|PlannerCollection whereAttachments($value)
 * @method static Builder|PlannerCollection whereCreatedAt($value)
 * @method static Builder|PlannerCollection whereDeletedAt($value)
 * @method static Builder|PlannerCollection whereExtraInfo($value)
 * @method static Builder|PlannerCollection whereFavouritedAt($value)
 * @method static Builder|PlannerCollection whereId($value)
 * @method static Builder|PlannerCollection whereName($value)
 * @method static Builder|PlannerCollection wherePreviousCollectionId($value)
 * @method static Builder|PlannerCollection wherePublishedAt($value)
 * @method static Builder|PlannerCollection wherePublisherId($value)
 * @method static Builder|PlannerCollection whereSchoolId($value)
 * @method static Builder|PlannerCollection whereSentAt($value)
 * @method static Builder|PlannerCollection whereTargetAudiences($value)
 * @method static Builder|PlannerCollection whereUid($value)
 * @method static Builder|PlannerCollection whereUpdatable($value)
 * @method static Builder|PlannerCollection whereUpdatedAt($value)
 * @method static Builder|PlannerCollection whereUserId($value)
 * @method static Builder|PlannerCollection whereVersion($value)
 * @method static Builder|PlannerCollection withTrashed()
 * @method static Builder|PlannerCollection withoutTrashed()
 * @method static PlannerCollectionFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class PlannerCollection extends Model implements PlannerCollectionInterface, HasMedia
{
    use Archives;
    use Favourites;
    use PruneSoftDeletes;
    use DisableQueryingOnModel;
    use DisableLazyLoading;
    use SoftDeletes;
    use Uid;
    use InteractsWithMediaCovers;

    /** @var array */
    protected $table = 'planner_collections_v2';

    /**
     * Max number of attachments per collection.
     */
    public const MAX_ATTACHMENTS = 5;

    /** @var array */
    protected $fillable = [
        'name',
        'extra_info',
        'target_audiences',
        'method_identifier',
    ];

    /** @var array */
    protected $visible = [
        'attachments',
    ];

    private bool $isWritableOverwrite;

    protected string $morphClassName = 'planner_collection_v2';

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'version' => [
                    'required',
                    'integer',
                ],
                'extra_info' => [
                    'nullable',
                    'string',
                ],
                'method_identifier' => [
                    'nullable',
                    'string',
                ],
                'attachments' => [
                    'nullable',
                    'array',
                ],
                'target_audiences' => [
                    'nullable',
                    'array',
                ],
                'target_audiences.*' => [
                    'uuid',
                ],
            ],
        );
    }

    /** @var array */
    protected $casts = [
        'activated_at' => 'datetime',
        'attachments' => PlannerCollectionAttachments::class,
        'extra_info' => EncodedString::class,
        'name' => EncodedString::class,
        'parent_collection_id' => 'integer',
        'previous_collection_id' => 'integer',
        'published_at' => 'datetime',
        'publisher_id' => 'integer',
        'sent_at' => 'datetime',
        'target_audiences' => Json::class,
        'user_id' => 'integer',
        'school_id' => 'integer',
        'version' => 'integer',
    ];

    protected array $enums = [
        'updatable' => CollectionUpdatable::class,
    ];

    #[Override]
    protected static function boot(): void
    {
        parent::boot();

        static::addDisabledRelation('chapters');
    }

    #[Override]
    protected function validationData(): array
    {
        $data = parent::validationData();
        if (array_key_exists('target_audiences', $data) && is_string($data['target_audiences'])) {
            // We want the json decoded data, not the raw data.
            $data['target_audiences'] = json_decode($data['target_audiences'], true);
        }
        if (array_key_exists('attachments', $data) && is_string($data['attachments'])) {
            // We want the json decoded data, not the raw data.
            $data['attachments'] = json_decode($data['attachments'], true);
        }

        return $data;
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class, 'planner_collection_id')->orderBy('order');
    }

    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    public function previousCollection(): BelongsTo
    {
        return $this->belongsTo(self::class, 'previous_collection_id');
    }

    public function usersWithAccess(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'entity', 'planner_collections_v2_access', 'plannercollection_id');
    }

    public function usersWithReadAccess(): MorphToMany
    {
        return $this->usersWithAccess()
            ->withPivotValue(['is_writable' => 0]);
    }

    public function usersWithWriteAccess(): MorphToMany
    {
        return $this->usersWithAccess()
            ->withPivotValue(['is_writable' => 1]);
    }

    public function schoolsWithAccess(): MorphToMany
    {
        return $this->morphedByMany(School::class, 'entity', 'planner_collections_v2_access', 'plannercollection_id');
    }

    public function allActivatedPlannerCollections(): HasMany
    {
        return $this->hasMany(ActivatedPlannerCollection::class);
    }

    public function activatedPlannerCollections(): HasMany
    {
        return $this->allActivatedPlannerCollections()
            ->whereHas('user', function (Builder $relation): void {
                $relation->whereHasActiveCareers();
            });
    }

    public function writeActivatedPlannerCollections(): HasMany
    {
        return $this->activatedPlannerCollections()->where('is_writable', true);
    }

    public function targetAudiences(): SupportCollection
    {
        $relation = 'targetAudiences';
        if ($this->relationLoaded($relation)) {
            return $this->getRelation($relation);
        }

        $targetAudienceUids = $this->target_audiences;
        $targetAudiencesByUid = empty($targetAudienceUids)
            ? collect()
            : TargetAudience::whereIn('uid', $targetAudienceUids)->get()->keyBy('uid');
        $targetAudiences = collect($targetAudienceUids)
            ->map(fn(string $uid) => $targetAudiencesByUid->get($uid))
            ->values();

        $this->setRelation($relation, $targetAudiences);

        return $targetAudiences;
    }

    public function isPublished(): bool
    {
        return !empty($this->published_at);
    }

    #[Override]
    public function isOwner(?User $user = null): bool
    {
        $user ??= Auth::user();

        return !isset($this->isWritableOverwrite) && $this->user_id === $user->id;
    }

    public function activablePublisherCollection(): bool
    {
        return empty($this->user_id);
    }

    public function activableSharedCollection(): bool
    {
        return $this->usersWithAccess->pluck('id')->contains(Auth::user()->id) ||
            $this->schoolsWithAccess->pluck('id')->intersect(Auth::user()->schoolIds())->isNotEmpty();
    }

    public function getIsWritableAttribute(): bool
    {
        return $this->isWritableOverwrite
            ?? $this->getRawOriginal('is_writable')
            ?? ($this->user_id !== null || $this->school_id !== null);
    }

    public function setIsWritableAttribute(bool $isWritable): void
    {
        $this->isWritableOverwrite = $isWritable;
    }

    public function getShareStatusAttribute(): ShareStatus
    {
        if ($this->isOwner()) {
            return ShareStatus::OWNER;
        }

        return $this->is_writable ? ShareStatus::CO_AUTHOR : ShareStatus::READER;
    }
}
