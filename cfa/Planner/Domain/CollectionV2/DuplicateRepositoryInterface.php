<?php

namespace Cfa\Planner\Domain\CollectionV2;

use App\Exceptions\System\RelationNotLoadedException;

interface DuplicateRepositoryInterface
{
    /** @throws RelationNotLoadedException */
    public function duplicate(
        PlannerCollectionInterface $collection,
        array $optionalAttributes = [],
    ): PlannerCollectionInterface;

    public function duplicateAsActivated(
        ActivatedPlannerCollection $activatedPlannerCollection,
        array $optionalAttributes = [],
    ): ActivatedPlannerCollection;

    public function remapOverwrites(
        ActivatedPlannerCollection $activatedPlannerCollection,
        PlannerCollection $duplicatePlannerCollection,
    ): ActivatedPlannerCollection;
}
