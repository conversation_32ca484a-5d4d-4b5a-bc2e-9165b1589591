<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter;

use Cfa\Planner\Domain\CollectionV2\ActivatedPlannerCollection;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;

interface DuplicateRepositoryInterface
{
    public function duplicate(Chapter $chapter, PlannerCollectionInterface $collection): Chapter;

    public function remapOverwrites(
        Chapter $duplicateChapter,
        ActivatedPlannerCollection $activatedPlannerCollection,
    ): Chapter;

    public function duplicateChapterOrOverwrites(
        Chapter $chapter,
        ActivatedPlannerCollection $originalActivatedCollection,
        ActivatedPlannerCollection $duplicateActivatedCollection,
    ): Chapter;
}
