<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Record
 * @extends JsonResource<Record>
 */
class RecordOverwrittenFieldsResource extends JsonResource
{
    /** {@inheritdoc} */
    #[Override]
    public function toArray($request): array
    {
        if (!$this->relationLoaded('overwrites')) {
            return [];
        }

        return $this->overwrites
            ->pluck('column')
            ->map(
                fn(RecordOverwriteColumn $column): string => match ($column) {
                    RecordOverwriteColumn::ExperienceSituationTypes => RecordResource::EXPERIENCE_SITUATIONS,
                    RecordOverwriteColumn::Curriculumnodes => RecordResource::CURRICULUM_GOALS,
                    RecordOverwriteColumn::MaterialFiles,
                    RecordOverwriteColumn::MaterialLinks,
                    RecordOverwriteColumn::MaterialPhysicals => RecordResource::MATERIAL_LIST,
                    default => $column->value,
                },
            )
            ->unique()
            ->values()
            ->toArray();
    }
}
