<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Override;

/**
 * @mixin CalendarItem
 * @extends JsonResource<CalendarItem>
 */
class LinkedCalendarItemResource extends JsonResource
{
    /** {@inheritdoc} */
    #[Override]
    public function toArray($request): array
    {
        return [
            'uid' => $this->uid,
            'begin' => $this->start,
            'repetition' => Str::upper($this->repetition_period?->name),
        ];
    }
}
