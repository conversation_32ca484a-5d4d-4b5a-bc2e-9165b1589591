<?php

namespace Cfa\Planner\Domain\CollectionV2\Chapter\Record;

use Cfa\Planner\Domain\CollectionV2\AbstractPlannerCollectionRepositoryInterface;
use Cfa\Planner\Domain\CollectionV2\Chapter\Chapter;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionInterface;
use Illuminate\Support\Collection;

interface RecordRepositoryInterface extends AbstractPlannerCollectionRepositoryInterface
{
    public function getByUid(string $uid, PlannerCollectionInterface $plannerCollection): Record;

    public function getById(int $recordId, PlannerCollectionInterface $plannerCollection): ?Record;

    public function getAllByUids(array $uids, PlannerCollectionInterface $plannerCollection): Collection;

    public function getAllByChapter(
        Chapter $chapter,
        PlannerCollectionInterface $plannerCollection,
        array $columns = ['*'],
    ): Collection;

    public function setOrderAndChapter(Record $record, int $order, Chapter $chapter): void;

    public function getAllByPlannerCollection(
        PlannerCollectionInterface $plannerCollection,
        array $columns = ['*'],
    ): Collection;

    public function getByPlannerCollectionInterface(
        PlannerCollectionInterface $plannerCollection,
        array $columns = ['*'],
        ?callable $additionalConditions = null,
    ): Collection;

    public function overwriteAttributes(Record $record, array $attributes): void;

    public function overwriteAttributesWithoutCommit(Record $record, array $attributes): void;

    public function commitOverwrites(): void;

    public function commitOverwritesWithoutDelete(): void;
}
