<?php

namespace Cfa\Planner\Domain\CollectionV2;

use Carbon\Carbon;
use Cfa\Common\Domain\User\User;
use Override;

class PlannerCollectionOverwriteFactory extends AbstractPlannerCollectionFactory
{
    protected array $defaultQueryingEnabledOnModels = [
        ActivatedPlannerCollection::class,
        PlannerCollection::class,
        PlannerCollectionOverwrite::class,
    ];

    /** @var string */
    protected $model = PlannerCollectionOverwrite::class;

    public function forUser(User $user): self
    {
        return $this->set('user_id', $user->id);
    }

    public function forPlannerCollection(PlannerCollection $plannerCollection): self
    {
        return $this->set('planner_collection_id', $plannerCollection->id);
    }

    public function forActivatedPlannerCollection(ActivatedPlannerCollection $activatedPlannerCollection): self
    {
        return $this->set('activated_planner_collection_id', $activatedPlannerCollection->id);
    }

    public function forColumn(PlannerCollectionOverwriteColumn $column): self
    {
        return $this->set('column', $column);
    }

    public function withValue(mixed $value): self
    {
        return $this->set('value', $value);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'created_at' => Carbon::now(),
        ];
    }
}
