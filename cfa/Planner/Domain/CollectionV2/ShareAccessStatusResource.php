<?php

namespace Cfa\Planner\Domain\CollectionV2;

use Cfa\Common\Domain\User\User;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin ShareAccessStatusDTO
 * @extends JsonResource<ShareAccessStatusDTO>
 */
class ShareAccessStatusResource extends JsonResource
{
    public function __construct(ShareAccessStatusDTO $shareAccessStatusDTO, private readonly User $user)
    {
        parent::__construct($shareAccessStatusDTO);
    }

    /** {@inheritdoc} */
    #[Override]
    public function toArray($request): array
    {
        return [
            'uid' => $this->user->uid,
            'fullname' => $this->user->fullname,
            'activated' => $this->hasActivatedTheCollection,
            'access' => $this->accessLevel,
        ];
    }
}
