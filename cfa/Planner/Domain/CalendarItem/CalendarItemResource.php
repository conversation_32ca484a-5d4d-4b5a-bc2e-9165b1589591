<?php

namespace Cfa\Planner\Domain\CalendarItem;

use App\Services\Carbon\CarbonService;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRowResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Override;

/**
 * @mixin CalendarItem
 * @extends JsonResource<CalendarItem>
 */
class CalendarItemResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'allDayItem' => is_null($this->duration),
            'duration' => $this->duration,
            'end' => CarbonService::formatOrNull($this->end),
            'id' => $this->uid,
            'location' => $this->location,
            'notes' => $this->notes,
            'owner' => $this->owner->uid ?? null,
            'repetition_period' => $this->repetition_period ? Str::upper($this->repetition_period->name) : null,
            'repetition_end' => CarbonService::formatOrNull($this->repetition_end),
            'masterItemId' => $this->masterItem?->uid,
            'repeatedItemCount' => $this->repetition_period && !$this->masterItem ? $this->repeated_items_count : 0,
            'rows' => CalendarItemRowResource::collection($this->rows),
            'school' => $this->school->uid ?? null,
            'schoolCalendarItem' => is_null($this->owner) && !is_null($this->school),
            'start' => CarbonService::formatOrNull($this->start),
            'groups' => $this->groups->pluck('uid')->toArray(),
            'subject' => $this->subject?->uid,
            'systemItem' => is_null($this->school),
            'tenant' => tenant()->uid,
            'title' => $this->title,
            'type' => Str::upper($this->type->name),
            'activity_type' => $this->activity_type ? Str::upper($this->activity_type->getLegacyName()) : null,
        ];
    }
}
