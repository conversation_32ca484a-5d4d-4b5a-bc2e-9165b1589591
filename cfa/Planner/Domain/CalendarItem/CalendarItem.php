<?php

namespace Cfa\Planner\Domain\CalendarItem;

use App\Casts\EncodedString;
use App\Models\Model;
use App\Services\Carbon\CarbonService;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Settings\Activity\ActivityType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection as SupportCollection;

/**
 * Cfa\Planner\Domain\CalendarItem\CalendarItem
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int|null $subject_id The subject the calendar item belongs to
 * @property int|null $school_id The school the calendar item belongs to
 * @property int|null $owner_id The user the calendar item belongs to
 * @property string|null $title The title of the calendaritem
 * @property CalendarItemType $type The type of the calendar item, @see CalendarItemType
 * @property Carbon $start The moment this calendar item starts
 * @property Carbon|null $end The moment this calendar item ends, used when it is a day item
 * @property int|null $duration The duration is the number of minutes, used when it is not a day item
 * @property string|null $location The location this event takes place
 * @property string|null $notes The notes of the calendar item
 * @property ActivityType $activity_type The activity type of the calendar item, @see ActivityType
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $master_item_id The calendar item this item is a repetition of.
 * @property Carbon|null $repetition_end The date on which the repetition ends.
 * @property Period $repetition_period The interval used to repeat the item.
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read Collection<int, Group> $groups
 * @property-read int|null $groups_count
 * @property-read CalendarItem|null $masterItem
 * @property-read User|null $owner
 * @property-read Collection<int, Record> $records
 * @property-read int|null $records_count
 * @property-read Collection<int, CalendarItem> $repeatedItems
 * @property-read int|null $repeated_items_count
 * @property-read Collection<int, CalendarItemRow> $rows
 * @property-read int|null $rows_count
 * @property-read School|null $school
 * @property-read Subject|null $subject
 * @property-read User|null $updater
 * @method static CalendarItemFactory factory($count = null, $state = [])
 * @method static CalendarItemRepositoryInterface getRepository()
 * @method static Builder|CalendarItem inDateInterval(Carbon $fromStartOfDay, Carbon $toEndOfDay)
 * @method static Builder|CalendarItem newModelQuery()
 * @method static Builder|CalendarItem newQuery()
 * @method static Builder|CalendarItem onlyTrashed()
 * @method static Builder|CalendarItem query()
 * @method static ActivityType randomActivityType()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon|null randomDeletedAt()
 * @method static int|null randomDuration()
 * @method static Carbon|null randomEnd()
 * @method static int randomId()
 * @method static string|null randomLocation()
 * @method static int|null randomMasterItemId()
 * @method static string|null randomNotes()
 * @method static int|null randomOwnerId()
 * @method static Carbon|null randomRepetitionEnd()
 * @method static Period randomRepetitionPeriod()
 * @method static int|null randomSchoolId()
 * @method static Carbon randomStart()
 * @method static int|null randomSubjectId()
 * @method static string|null randomTitle()
 * @method static CalendarItemType randomType()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|CalendarItem whereActivityType($value)
 * @method static Builder|CalendarItem whereCreatedAt($value)
 * @method static Builder|CalendarItem whereCreatorId($value)
 * @method static Builder|CalendarItem whereDeletedAt($value)
 * @method static Builder|CalendarItem whereDuration($value)
 * @method static Builder|CalendarItem whereEnd($value)
 * @method static Builder|CalendarItem whereId($value)
 * @method static Builder|CalendarItem whereLocation($value)
 * @method static Builder|CalendarItem whereMasterItemId($value)
 * @method static Builder|CalendarItem whereNotes($value)
 * @method static Builder|CalendarItem whereOwnerId($value)
 * @method static Builder|CalendarItem whereRepetitionEnd($value)
 * @method static Builder|CalendarItem whereRepetitionPeriod($value)
 * @method static Builder|CalendarItem whereSchoolId($value)
 * @method static Builder|CalendarItem whereStart($value)
 * @method static Builder|CalendarItem whereSubjectId($value)
 * @method static Builder|CalendarItem whereTitle($value)
 * @method static Builder|CalendarItem whereType($value)
 * @method static Builder|CalendarItem whereUid($value)
 * @method static Builder|CalendarItem whereUpdatedAt($value)
 * @method static Builder|CalendarItem whereUpdaterId($value)
 * @method static Builder|CalendarItem withTrashed()
 * @method static Builder|CalendarItem withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class CalendarItem extends Model
{
    use PruneSoftDeletes;
    use SoftDeletes;
    use Uid;
    use TouchedByUser;
    use SerializesModels;

    /**
     * {@inheritdoc}
     *
     * @var string
     */
    protected $table = 'calendaritems';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'type',
        'start',
        'end',
        'duration',
        'location',
        'notes',
        'activity_type',
        'repetition_period',
        'repetition_end',
    ];

    protected array $enums = [
        'type' => CalendarItemType::class,
        'activity_type' => ActivityType::class,
        'repetition_period' => Period::class,
    ];

    /** @var array */
    protected $casts = [
        'duration' => 'integer',
        'end' => 'datetime',
        'location' => EncodedString::class,
        'master_item_id' => 'integer',
        'notes' => EncodedString::class,
        'owner_id' => 'integer',
        'repetition_end' => 'datetime',
        'school_id' => 'integer',
        'start' => 'datetime',
        'subject_id' => 'integer',
        'title' => EncodedString::class,
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $visible = [
        'owner',
    ];

    /**
     * Get the subject of the Calendaritem.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the school this Calendaritem belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the owner of the Calendaritem.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the groups linked to the Calendaritem.
     */
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(Group::class, 'calendaritem_group', 'calendaritem_id', 'group_id');
    }

    /**
     * Get the calendar item rows linked to this calendar item.
     */
    public function rows(): HasMany
    {
        return $this->hasMany(CalendarItemRow::class, 'calendaritem_id');
    }

    /**
     * Get the collection chapter records linked to this calendar item.
     */
    public function records(): BelongsToMany
    {
        return $this->belongsToMany(Record::class, 'calendaritemrows', 'calendaritem_id', 'record_id');
    }

    /**
     * Get the item this calendar item is a repetition of.
     */
    public function masterItem(): BelongsTo
    {
        return $this->belongsTo(self::class, 'master_item_id')->withTrashed();
    }

    /**
     * Get the items that are a repetition of this calendar item.
     */
    public function repeatedItems(): HasMany
    {
        return $this->hasMany(self::class, 'master_item_id');
    }

    /**
     * Get the items, after this item in time, that are part of the same repetition series as this item.
     */
    public function repeatedItemsInTheFuture(): SupportCollection
    {
        return ($this->masterItem ?? $this)
            ->repeatedItems
            // Limited to items after this item.
            ->filter(function (self $repeatedItem) {
                return $repeatedItem->start->greaterThan($this->start);
            });
    }

    /**
     * Subquery that checks if:
     *  - For all day items: start is before the last asked date and end is after the first asked date
     *  - For non all day items: start is between the first and last asked date
     *
     * @param Builder $query Querybuilder instance.
     * @param Carbon $fromStartOfDay The from date parameter to check (should be start of the date).
     * @param Carbon $toEndOfDay The to date parameter to check (should be end of the given date).
     */
    public function scopeInDateInterval(Builder $query, Carbon $fromStartOfDay, Carbon $toEndOfDay): Builder
    {
        return $query->where(
            [
                ['duration', '=', null],
                ['start', '<', $toEndOfDay],
                ['end', '>', $fromStartOfDay],
            ],
        )->orWhere(
            fn(Builder $subQuery): Builder => $subQuery->where(
                [
                    ['duration', '<>', null],
                    ['start', '>=', $fromStartOfDay],
                    ['start', '<=', $toEndOfDay],
                ],
            ),
        );
    }

    /**
     * Check if the calendaritem is a system item.
     */
    public function isSystemItem(): bool
    {
        return is_null($this->school);
    }

    /**
     * Check if the calendaritem is a school item.
     */
    public function isSchoolCalendarItem(): bool
    {
        return !$this->isSystemItem() && is_null($this->owner);
    }

    /**
     * Check if the linked subject is the same via subjectId.
     *
     * @param int $subjectId Subject id.
     */
    public function equalSubject(int $subjectId): bool
    {
        return $subjectId === $this->subject_id;
    }

    /**
     * Check if at least one group overlaps with the linked groups.
     *
     * @param array $groups List of groups to check.
     */
    public function hasAtLeastOneGroup(array $groups): bool
    {
        return !$this->groups->pluck('id')->intersect($groups)->isEmpty();
    }

    /**
     * Check is this calendaritem has no linked subject.
     */
    public function hasNoSubject(): bool
    {
        return is_null($this->subject_id);
    }

    /**
     * Check if the calendaritem has no linked groups.
     */
    public function hasNoGroup(): bool
    {
        return $this->groups->isEmpty();
    }

    /**
     * Check if the calendaritem has a different owner than the given owner (if any).
     *
     * @param User|null $owner The owner to check against.
     */
    protected function hasDifferentOwner(?User $owner): bool
    {
        if (!$owner) {
            return false;
        }

        return $this->owner && $this->owner_id !== $owner->id;
    }

    /**
     * Duplicate records in the calendar item's rows that belong to a different user
     * and sync groups with the calendaritem's user.
     */
    protected function duplicateRecordsAndSyncGroupsForOwner(): self
    {
        $this->rows->filter(function (CalendarItemRow $row) {
            // Only rows with record belonging to different user.
            return $row->record && $row->record->owner_id !== $this->owner_id;
        })->each(function (CalendarItemRow $row): void {
            $newRecord = $row->record->duplicate();
            $newRecord->owner_id = $this->owner_id;
            $newRecord->chapter_id = null;
            $newRecord->save();

            $row->record_id = $newRecord->id;
            $row->save();
        });

        // Only link with groups the new user has access to.
        $accessToGroups = Group::getRepository()->getGroupsForUserAndSchool($this->owner, $this->school);
        $groups = $this->groups->intersect($accessToGroups);

        $this->groups()->sync($groups);

        // Refresh the calendaritem to make sure everything is up to date in model.
        return $this->refresh();
    }

    /**
     * Gets the days on which the calendar item should be repeated.
     */
    public function getDaysOnWhichToRepeat(): SupportCollection
    {
        return app(CarbonService::class)
            ->getDaysOnWhichToRepeat($this->start, $this->repetition_period, $this->repetition_end);
    }
}
