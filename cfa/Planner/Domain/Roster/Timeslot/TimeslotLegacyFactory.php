<?php

namespace Cfa\Planner\Domain\Roster\Timeslot;

use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Roster\Roster;
use Faker\Generator;

$factory->define(Timeslot::class, function (Generator $faker) {
    return [
        'subject_id' => Subject::randomId(),
        'roster_id' => Roster::randomId(),
        'day' => $faker->randomElement(Day::cases()),
        'start' => $faker->time(),
        // Minimum 10, maximum 120 minutes with a 5 minute gap.
        'duration' => $faker->numberBetween(2, 24) * 5,
        'creator_id' => User::randomId(),
        'updater_id' => User::randomId(),
    ];
});
