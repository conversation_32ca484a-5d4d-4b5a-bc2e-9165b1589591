<?php

namespace Cfa\Planner\Domain\Roster\Timeslot;

use App\Traits\Enum\EnumFromName;
use App\Traits\Enum\EnumTranslations;
use Illuminate\Contracts\Support\Arrayable;

enum Day: int implements Arrayable
{
    use EnumTranslations;
    use EnumFromName;

    case Sunday = 0;
    case Monday = 1;
    case Tuesday = 2;
    case Wednesday = 3;
    case Thursday = 4;
    case Friday = 5;
    case Saturday = 6;
}
