<?php

namespace Cfa\Planner\Domain\CurriculumNode\Reporting;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Group
 * @extends JsonResource<Group>
 */
class CurriculumNodeReportingProgressResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        $totalsToAchieve = $this->additional['totalsToAchieve'];
        $achievedTotals = $this->additional['achievedTotals'];
        $curriculumType = $this->additional['curriculumType'];

        $learningAreas = CurriculumNode::getRepository()
            ->getLearningAreas($this->school->educationalnetwork, $curriculumType);

        $learningAreas = $learningAreas
            ->map(fn(CurriculumNode $curriculumNode): array => [
                'id' => $curriculumNode->uid,
                'name' => $curriculumNode->name,
                'goalsTotal' => $totalsToAchieve->get($curriculumNode->uid, 0),
                'goalsAchieved' => $achievedTotals->get($curriculumNode->uid, 0),
            ])
            ->values()
            ->toArray();

        return [
            'id' => $this->uid,
            'name' => $this->name,
            'naturalStudyYear' => $this->natural_study_year,
            'learningAreas' => $learningAreas,
        ];
    }
}
