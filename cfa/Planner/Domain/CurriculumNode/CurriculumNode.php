<?php

namespace Cfa\Planner\Domain\CurriculumNode;

use App\Casts\RichTextField;
use App\Infrastructure\Database\Eloquent\Relations\BelongsToMany as SolBelongsToMany;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelConfiguration;
use Cfa\Planner\Domain\CurriculumNode\Zill\ZillDevelopmentField;
use Cfa\Planner\Domain\CurriculumNode\Zill\ZillVersion;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection as SupportCollection;
use Override;

/**
 * Cfa\Planner\Domain\CurriculumNode\CurriculumNode
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $educationalnetwork_id
 * @property string|null $name Name of the Curriculumnode.
 * @property string|null $description
 * @property string|null $code
 * @property int|null $sequence Number used for sorting.
 * @property string|null $reference_code Unique reference from external partners.
 * @property CurriculumNodeType $type
 * @property bool $is_active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property string $search_code Code stripped of non alphanumeric characters for easier searching.
 * @property int|null $version The version of the curriculumnode.
 * @property string|null $change_hash Hashed string to check for changes.
 * @property CurriculumType $curriculum_type The curriculum this node belongs to.
 * @property string|null $smartschool_uid
 * @property string|null $smartschool_code
 * @property int|null $zill_version_id The Zill version of the curriculumnode.
 * @property bool|null $is_leerlijn_cluster
 * @property bool $is_observation
 * @property-read Collection<int, CurriculumNode> $children
 * @property-read int|null $children_count
 * @property-read EducationalNetwork $educationalnetwork
 * @property-read array $validation_rules
 * @property-read Collection<int, GradeLevelConfiguration> $gradelevelconfigurations
 * @property-read int|null $gradelevelconfigurations_count
 * @property-read Collection<int, CurriculumNode> $mappedCurriculumnodesToGoalbooknode
 * @property-read int|null $mapped_curriculumnodes_to_goalbooknode_count
 * @property-read Collection<int, CurriculumNode> $mappedCurriculumnodesToPreschoolnode
 * @property-read int|null $mapped_curriculumnodes_to_preschoolnode_count
 * @property-read Collection<int, CurriculumNode> $mappedGoalbooknodes
 * @property-read int|null $mapped_goalbooknodes_count
 * @property-read Collection<int, CurriculumNode> $parents
 * @property-read int|null $parents_count
 * @property-read ZillVersion|null $zillVersion
 * @property-read ZillDevelopmentField|null $zilldevelopmentfield
 * @method static CurriculumNodeFactory factory($count = null, $state = [])
 * @method static Builder|CurriculumNode filterByGrade(?TargetAudienceType $targetAudienceType, ?int $naturalStudyYear, Collection $includedLevels, bool $alsoChildren)
 * @method static CurriculumNodeRepositoryInterface getRepository()
 * @method static Builder|CurriculumNode newModelQuery()
 * @method static Builder|CurriculumNode newQuery()
 * @method static Builder|CurriculumNode onlyTrashed()
 * @method static Builder|CurriculumNode query()
 * @method static string|null randomChangeHash()
 * @method static string|null randomCode()
 * @method static Carbon|null randomCreatedAt()
 * @method static CurriculumType randomCurriculumType()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomDescription()
 * @method static int randomEducationalnetworkId()
 * @method static int randomId()
 * @method static bool randomIsActive()
 * @method static bool|null randomIsLeerlijnCluster()
 * @method static string|null randomName()
 * @method static string|null randomReferenceCode()
 * @method static string randomSearchCode()
 * @method static int|null randomSequence()
 * @method static string|null randomSmartschoolCode()
 * @method static string|null randomSmartschoolUid()
 * @method static CurriculumNodeType randomType()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomVersion()
 * @method static int|null randomZillVersionId()
 * @method static Builder|CurriculumNode whereChangeHash($value)
 * @method static Builder|CurriculumNode whereCode($value)
 * @method static Builder|CurriculumNode whereCreatedAt($value)
 * @method static Builder|CurriculumNode whereCurriculumType($value)
 * @method static Builder|CurriculumNode whereDeletedAt($value)
 * @method static Builder|CurriculumNode whereDescription($value)
 * @method static Builder|CurriculumNode whereEducationalnetworkId($value)
 * @method static Builder|CurriculumNode whereId($value)
 * @method static Builder|CurriculumNode whereIsActive($value)
 * @method static Builder|CurriculumNode whereIsLeerlijnCluster($value)
 * @method static Builder|CurriculumNode whereIsObservation($value)
 * @method static Builder|CurriculumNode whereName($value)
 * @method static Builder|CurriculumNode whereReferenceCode($value)
 * @method static Builder|CurriculumNode whereSearchCode($value)
 * @method static Builder|CurriculumNode whereSequence($value)
 * @method static Builder|CurriculumNode whereSmartschoolCode($value)
 * @method static Builder|CurriculumNode whereSmartschoolUid($value)
 * @method static Builder|CurriculumNode whereType($value)
 * @method static Builder|CurriculumNode whereUid($value)
 * @method static Builder|CurriculumNode whereUpdatedAt($value)
 * @method static Builder|CurriculumNode whereVersion($value)
 * @method static Builder|CurriculumNode whereZillVersionId($value)
 * @method static Builder|CurriculumNode withTrashed()
 * @method static Builder|CurriculumNode withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class CurriculumNode extends Model
{
    use SoftDeletes;
    use Uid;

    /**
     * The database table name.
     *
     * @var string
     */
    protected $table = 'curriculumnodes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'code',
        'reference_code',
        'is_leerlijn_cluster',
        'educationalnetwork_id',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'educationalnetwork_id' => 'integer',
        'is_active' => 'boolean',
        'sequence' => 'integer',
        'reference_code' => 'string',
        'version' => 'integer',
        'is_leerlijn_cluster' => 'boolean',
        'is_observation' => 'boolean',
        'zill_version_id' => 'integer',
        'description' => RichTextField::class,
        'name' => RichTextField::class,
        'type' => CurriculumNodeType::class,
        'curriculum_type' => CurriculumType::class,
    ];

    /**
     * Descendants can be set by using the fromMultiParentToTree function.
     *
     * @var SupportCollection
     */
    public $descendants;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public static function boot(): void
    {
        parent::boot();

        /*
         * Set the search_code when saving, example:
         * FR-SCH-06.04 -> FRSCH064
         */
        static::saving(function ($curriculumnode): void {
            $curriculumnode->search_code = static::makeSearchCode($curriculumnode->code);
        });
    }

    /**
     * Helper function to create the stripped search code field based on the code.
     *
     * @param null|string $code The original code.
     */
    public static function makeSearchCode(?string $code): string
    {
        // Replace special chars to underscore.
        $onlyUnderscore = preg_replace("/[\(\.\- ]/", '_', (string) $code);
        // Remove the leading zeros.
        $trimLeadingZeros = preg_replace("/_0+(\d)/", '_$1', (string) $onlyUnderscore);

        // Remove all non-alphanumeric characters.
        return preg_replace('/\W/', '', (string) $trimLeadingZeros);
    }

    /**
     * Get the child Curriculumnodes of this Curriculumnode.
     */
    public function children(): BelongsToMany
    {
        return $this->belongsToMany(self::class, 'curriculumnode_curriculumnode', 'parent_id', 'child_id');
    }

    /**
     * Get the educational network the Curriculumnode belongs to.
     */
    public function educationalnetwork(): BelongsTo
    {
        return $this->belongsTo(EducationalNetwork::class, 'educationalnetwork_id');
    }

    /**
     * Get the parent Curriculumnodes of this Curriculumnode.
     */
    public function parents(): BelongsToMany
    {
        return $this->belongsToMany(self::class, 'curriculumnode_curriculumnode', 'child_id', 'parent_id')
            ->orderBy('sequence');
    }

    /**
     * Get the curriculum nodes mapped to this goalbook node.
     */
    public function mappedCurriculumnodesToGoalbooknode(): SolBelongsToMany
    {
        return $this->belongsToMany(self::class, 'goalbooknode_curriculumnode', 'goalbooknode_id', 'curriculumnode_id');
    }

    /**
     * Get the goal book nodes mapped to this node.
     */
    public function mappedGoalbooknodes(): SolBelongsToMany
    {
        return $this->belongsToMany(self::class, 'goalbooknode_curriculumnode', 'curriculumnode_id', 'goalbooknode_id');
    }

    /**
     * Get the curriculum nodes mapped to this preschool node.
     */
    public function mappedCurriculumnodesToPreschoolnode(): SolBelongsToMany
    {
        return $this->belongsToMany(
            self::class,
            'preschoolnode_curriculumnode',
            'preschoolnode_id',
            'curriculumnode_id',
        );
    }

    /**
     * Get the grade level configurations for the Curriculumnode.
     */
    public function gradelevelconfigurations(): HasMany
    {
        return $this->hasMany(GradeLevelConfiguration::class, 'curriculumnode_id');
    }

    /**
     * Get the linked ZILL development field.
     */
    public function zilldevelopmentfield(): HasOne
    {
        return $this->hasOne(ZillDevelopmentField::class, 'curriculumnode_id');
    }

    /**
     * Get the Zill version the Curriculumnode belongs to.
     */
    public function zillVersion(): BelongsTo
    {
        return $this->belongsTo(ZillVersion::class);
    }

    /**
     * Filter curriculum nodes by their grade with or without repetition.
     *
     * @param Builder $builder The current query builder.
     * @param TargetAudienceType|null $targetAudienceType Type of target audience to filter on.
     * @param int|null $naturalStudyYear Natural study year to filter on.
     * @param SupportCollection $includedLevels Grade levels to filter by.
     * @param bool $alsoChildren Filter also on child gradelevelconfigurations.
     */
    public function scopeFilterByGrade(
        Builder $builder,
        ?TargetAudienceType $targetAudienceType,
        ?int $naturalStudyYear,
        SupportCollection $includedLevels,
        bool $alsoChildren,
    ): Builder {
        if (!$targetAudienceType) {
            return $builder;
        }

        $builder
            ->select('curriculumnodes.*')
            ->distinct()
            ->join(
                'grade_level_configurations',
                'grade_level_configurations.curriculumnode_id',
                '=',
                'curriculumnodes.id',
            )
            ->where(function (Builder $builder) use ($naturalStudyYear, $targetAudienceType, $includedLevels): void {
                $builder
                    ->when(!is_null($naturalStudyYear), function (Builder $builder) use ($naturalStudyYear): void {
                        $builder->where('grade_level_configurations.natural_study_year', $naturalStudyYear);
                    })
                    ->where('grade_level_configurations.target_audience_type', $targetAudienceType)
                    ->whereIn('grade_level_configurations.level', $includedLevels);
            });

        if (!$alsoChildren) {
            return $builder;
        }

        $builder
            ->leftJoin(
                'curriculumnode_curriculumnode as child_curriculumnode_curriculumnode',
                'child_curriculumnode_curriculumnode.parent_id',
                '=',
                'curriculumnodes.id',
            )
            ->leftJoin(
                'curriculumnodes as children',
                'child_curriculumnode_curriculumnode.child_id',
                '=',
                'children.id',
            )
            ->leftJoin(
                'grade_level_configurations as children_grade_level_configurations',
                'children_grade_level_configurations.curriculumnode_id',
                '=',
                'children.id',
            )
            ->orWhere(function (Builder $builder) use ($naturalStudyYear, $targetAudienceType, $includedLevels): void {
                $builder
                    ->when(!is_null($naturalStudyYear), function (Builder $builder) use ($naturalStudyYear): void {
                        $builder->where('children_grade_level_configurations.natural_study_year', $naturalStudyYear);
                    })
                    ->where('children_grade_level_configurations.target_audience_type', $targetAudienceType)
                    ->whereIn('children_grade_level_configurations.level', $includedLevels);
            });

        return $builder;
    }

    /**
     * Create a hash that can be used to check if anything has changed when updating.
     *
     * @param array|null $gradelevelconfigurations Provide grade level configuration instead of using the relation.
     */
    public function getChangeHash(?array $gradelevelconfigurations = null): string
    {
        // The fields to check for changes.
        $fields = collect(['description', 'name', 'code']);

        // Concatenate to one string.
        $concat = $fields->reduce(function ($prev, $field) {
            return $prev . $this->{$field};
        }, '');

        $gradelevelconfigCollection = !empty($gradelevelconfigurations) ?
            $this->gradelevelconfigurations :
            collect($gradelevelconfigurations)->map(function ($item) {
                return collect($item);
            });

        // Include the gradelevel configuration.
        $gradelevelconfig = json_encode(
            $gradelevelconfigCollection
                ->map
                ->only([
                    'target_audience_type',
                    'natural_study_year',
                    'level',
                ])
                ->transform(function (array $gradeLevelConfiguration) {
                    if (!empty($gradeLevelConfiguration['target_audience_type'])) {
                        $typeName = $gradeLevelConfiguration['target_audience_type']->getName();
                        $gradeLevelConfiguration['target_audience_type'] = $typeName;
                    }

                    return $gradeLevelConfiguration;
                }),
        );

        return md5($concat . $gradelevelconfig);
    }

    /**
     * Set the change hash for all nodes.
     */
    public static function setAllChangeHashes(): void
    {
        $queryInstance = new static();
        $queryInstance::all()->each(function ($curriculumNode): void {
            $curriculumNode->change_hash = $curriculumNode->getChangeHash();
            $curriculumNode->save();
        });
    }
}
