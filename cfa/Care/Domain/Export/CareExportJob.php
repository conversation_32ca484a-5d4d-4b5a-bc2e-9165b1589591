<?php

namespace Cfa\Care\Domain\Export;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

use function array_merge;

/**
 * Cfa\Care\Domain\Export\CareExportJob
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property ExportJobStatus $status
 * @property string|null $file
 * @property int|null $care_info_id
 * @property int $school_id
 * @property int|null $creator_id
 * @property int|null $lambda_result_id
 * @property string|null $job_batch_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read CareInfo|null $careInfo
 * @property-read array $validation_rules
 * @property-read School $school
 * @method static Builder|CareExportJob newModelQuery()
 * @method static Builder|CareExportJob newQuery()
 * @method static Builder|CareExportJob query()
 * @method static int|null randomCareInfoId()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static string|null randomFile()
 * @method static int randomId()
 * @method static string|null randomJobBatchId()
 * @method static int|null randomLambdaResultId()
 * @method static int randomSchoolId()
 * @method static ExportJobStatus randomStatus()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|CareExportJob whereCareInfoId($value)
 * @method static Builder|CareExportJob whereCreatedAt($value)
 * @method static Builder|CareExportJob whereCreatorId($value)
 * @method static Builder|CareExportJob whereFile($value)
 * @method static Builder|CareExportJob whereId($value)
 * @method static Builder|CareExportJob whereJobBatchId($value)
 * @method static Builder|CareExportJob whereLambdaResultId($value)
 * @method static Builder|CareExportJob whereSchoolId($value)
 * @method static Builder|CareExportJob whereStatus($value)
 * @method static Builder|CareExportJob whereUid($value)
 * @method static Builder|CareExportJob whereUpdatedAt($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class CareExportJob extends Model
{
    use Uid;

    /** @var string[] */
    protected $dispatchesEvents = [
        'updated' => CareExportJobUpdated::class,
    ];

    protected array $enums = [
        'status' => ExportJobStatus::class,
    ];

    /** @var array */
    protected $casts = [
        'creator_id' => 'integer',
        'care_info_id' => 'integer',
        'lambda_result_id' => 'integer',
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'status' => [
                    'required',
                    'enumValue:' . ExportJobStatus::class,
                ],
                'care_info_id' => [
                    'nullable',
                    'integer',
                ],
                'school_id' => [
                    'required',
                    'integer',
                ],
                'creator_id' => [
                    'nullable',
                    'integer',
                ],
            ],
        );
    }

    public function careInfo(): BelongsTo
    {
        return $this->belongsTo(CareInfo::class);
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }
}
