<?php

namespace Cfa\Care\Application\Services\Export;

use App\Jobs\SafeExcelExport;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Override;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

use function optional;
use function trans;

class PupilOverviewForCareExport extends SafeExcelExport implements
    FromQuery,
    WithHeadings,
    WithMapping,
    WithColumnFormatting,
    ShouldAutoSize
{
    private School $school;

    public function __construct(School $school)
    {
        $this->school = $school;
    }

    /** {@inheritdoc} */
    #[Override]
    public function query()
    {
        return User::query()
            ->with('careers')
            ->select([
                DB::raw('care_infos.uid AS care_info_uid'),
                'users.*',
            ])
            ->join('care_infos', 'care_infos.pupil_id', 'users.id')
            ->where('care_infos.school_id', $this->school->id)
            ->whereNull('care_infos.deleted_at');
    }

    /** {@inheritdoc} */
    #[Override]
    public function map($user): array
    {
        if (!$user instanceof User) {
            return [];
        }

        $schoolyear = $user->getLastActiveSchoolyear($this->school->id);
        $group = $user->lastCareerGroups($this->school->id)->where('is_classgroup')->first();

        return [
            Str::substr($user->care_info_uid, 0, 8),
            $user->firstname,
            $user->lastname,
            optional($group)->name,
            optional($schoolyear)->description,
        ];
    }

    /** {@inheritdoc} */
    #[Override]
    public function headings(): array
    {
        return [
            'ID',
            trans('labels.firstname'),
            trans('labels.lastname'),
            trans('labels.group-selector.group'),
            trans('labels.schoolyear'),
        ];
    }

    /** {@inheritdoc} */
    #[Override]
    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER,
        ];
    }
}
