<?php

namespace Cfa\Care\Application\Services\PupilImportJob;

use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\PupilImportJob\Mapping\PupilImportJobDestination;
use Cfa\Care\Domain\PupilImportJob\PupilImportJob;
use Cfa\Common\Application\Exceptions\NationalRegisterNumberStoreService\InvalidMatchingPupilException;
use Cfa\Common\Domain\User\Gender;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;

use function array_filter;

class PupilImportJobRowProcessor
{
    private PupilImportJob $pupilImportJob;

    public function __construct(PupilImportJob $pupilImportJob)
    {
        $this->pupilImportJob = $pupilImportJob;
    }

    /**
     * Will try and process one row from the PupilImportJobImport.
     *
     * @param array $mappedRow The row mapped to the PupilImportJobDestinations.
     *
     * @throws InvalidMatchingPupilException
     */
    public function processRow(array $mappedRow): void
    {
        if (count(array_filter($mappedRow)) === 0) {
            return;
        }

        $uid = $mappedRow[PupilImportJobDestination::Uid->value];
        /** @var Pupil $pupil */
        $pupil = Pupil::with('careInfos')
            ->where('users.uid', $uid)
            ->firstOrFail();
        $careInfo = $pupil->careInfos->firstWhere('school_id', $this->pupilImportJob->school_id);
        if (!$careInfo instanceof CareInfo) {
            throw new PupilImportException(PupilImportException::CARE_INFO_NOT_FOUND_IN_SCHOOL . ': ' . $uid);
        }
        $dateOfBirth = Carbon::parse($mappedRow[PupilImportJobDestination::DateOfBirth->value]);
        $gender = Gender::fromHumanReadableString($mappedRow[PupilImportJobDestination::Gender->value]);
        $nationalRegisterNumber = (string) $mappedRow[PupilImportJobDestination::Nrn->value];

        $careInfo->storeNationalRegisterNumber(
            $nationalRegisterNumber,
            $dateOfBirth,
            $gender,
            User::withoutGlobalScopes()->findOrFail($this->pupilImportJob->creator_id),
        );
    }
}
