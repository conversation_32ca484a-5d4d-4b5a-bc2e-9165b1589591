<?php

namespace Cfa\Care\Application\Services\Statistics\Graphs\Group;

use Cfa\Care\Application\Repositories\Statistics\CareThemeStatisticsRepositoryInterface;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;
use Override;

use function app;
use function trans;

class CareThemeCharts extends PieCharts
{
    public function __construct()
    {
        parent::__construct(trans('titles.graphs.care-theme'), 'careTheme');
    }

    #[Override]
    protected function getStatisticsForGroups(array $groupIds, School $school, User $user): Collection
    {
        return app(CareThemeStatisticsRepositoryInterface::class)->getStatisticsByGroups($groupIds, $school, $user);
    }
}
