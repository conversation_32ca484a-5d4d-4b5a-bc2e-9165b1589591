<?php

namespace Cfa\Care\Application\Services\Statistics\Graphs\Group;

use Cfa\Care\Application\Repositories\Statistics\CareLevelStatisticsRepositoryInterface;
use Cfa\Care\Domain\CareInfo\CareLevel\CareLevelType;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;
use Override;
use stdClass;

use function app;
use function array_merge;
use function trans;

class CareLevelCharts extends ColumnCharts
{
    public function __construct()
    {
        parent::__construct(trans('titles.graphs.care-level'));
    }

    #[Override]
    protected function getStatisticsForGroups(array $groupIds, School $school, User $user): Collection
    {
        return app(CareLevelStatisticsRepositoryInterface::class)->getStatisticsByGroups($groupIds, $school, $user);
    }

    #[Override]
    protected function transformStatisticsResult(stdClass $statisticsResult, Group $group): array
    {
        return array_merge(
            parent::transformStatisticsResult($statisticsResult, $group),
            [
                'name' => CareLevelType::from($statisticsResult->name)->getHumanReadableName(),
            ],
        );
    }
}
