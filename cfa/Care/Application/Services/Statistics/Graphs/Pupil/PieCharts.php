<?php

namespace Cfa\Care\Application\Services\Statistics\Graphs\Pupil;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;
use Override;
use stdClass;

use function route;

abstract class PieCharts implements PupilCharts
{
    private string $title;

    private string $routeParameter;

    public function __construct(string $title, string $routeParameter)
    {
        $this->title = $title;
        $this->routeParameter = $routeParameter;
    }

    abstract protected function getStatisticsByPupil(Pupil $pupil, School $school, User $user): Collection;

    #[Override]
    public function getGraphForPupil(Pupil $pupil, School $school, ?User $user = null): array
    {
        $statistics = $this->getStatisticsByPupil($pupil, $school, $user);
        if ($statistics->isEmpty()) {
            return [];
        }

        return [
            $this->getChartsData($this->transformStatisticsResults($statistics, $pupil)),
        ];
    }

    private function transformStatisticsResults(Collection $statisticsResults, Pupil $pupil): array
    {
        return $statisticsResults
            ->map(function (stdClass $statisticsResult) use ($pupil) {
                $route = route(
                    'web.care.overview.pupils.notes',
                    [
                        'pupil' => $pupil->uid,
                        $this->routeParameter => $statisticsResult->uid,
                    ],
                );

                return [
                    'name' => $statisticsResult->name,
                    'y' => $statisticsResult->value,
                    'link' => $route,
                    'tooltip' => $this->getTooltip($statisticsResult),
                ];
            })
            ->toArray();
    }

    private function getChartsData(array $dataArray): array
    {
        return [
            'title' => [
                'text' => $this->title,
            ],
            'tooltip' => [
                'useHTML' => true,
                'pointFormat' => '{point.tooltip}',
            ],
            'series' => empty($dataArray) ? [] : [
                [
                    'data' => $dataArray,
                ],
            ],
            'chart' => [
                'type' => 'pie',
            ],
            'legend' => [
                'enabled' => true,
                'labelFormat' => '{name}',
            ],
            'plotOptions' => [
                'pie' => [
                    'cursor' => 'pointer',
                    'dataLabels' => [
                        'enabled' => false,
                        'format' => '<b>{point.name}</b> => {point.y}',
                    ],
                    'innerSize' => '70%',
                    'showInLegend' => true,
                ],
            ],
        ];
    }

    private function getTooltip(stdClass $statisticsResult): string
    {
        if (!$statisticsResult->pupil_names) {
            return $statisticsResult->value;
        }

        return $statisticsResult->value . '<br />' . $statisticsResult->pupil_names;
    }
}
