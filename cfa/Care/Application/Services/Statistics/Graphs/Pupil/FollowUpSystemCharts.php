<?php

namespace Cfa\Care\Application\Services\Statistics\Graphs\Pupil;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Application\Repositories\Statistics\PredefinedFollowUpSystemStatisticsRepository;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use DomainException;
use Illuminate\Support\Collection;
use Override;

use function app;

abstract class FollowUpSystemCharts implements PupilCharts
{
    protected FollowUpSystemType $followUpSystemType;

    public function __construct(FollowUpSystemType $followUpSystemType)
    {
        $this->followUpSystemType = $followUpSystemType;
    }

    #[Override]
    public function getGraphForPupil(Pupil $pupil, School $school, ?User $user = null): array
    {
        $graphData = [];
        $followUpSystems = PredefinedFollowUpSystem::whereType($this->followUpSystemType->value)
            ->whereSchoolId($school->id)
            ->get();

        $followUpSystems->each(
            function (FollowUpSystem $followUpSystem) use (&$graphData, $pupil): bool {
                if (!$followUpSystem instanceof PredefinedFollowUpSystem) {
                    return true;
                }

                $followUpSystemStatisticsRepository = app(PredefinedFollowUpSystemStatisticsRepository::class);
                $scores = $followUpSystemStatisticsRepository->getGraphDataForFollowUpSystem(
                    $pupil,
                    $followUpSystem,
                );

                if (
                    $scores->isNotEmpty() &&
                    (!($scores->first() instanceof Collection) || $scores->first()->isNotEmpty())
                ) {
                    $graphData[] = $this->getChartsData($scores, $followUpSystem);
                }

                return true;
            },
        );

        return $graphData;
    }

    /**
     * Process raw data into graph compatible format.
     * Needs to be implemented by Child class
     *
     * @throws DomainException
     */
    protected function getChartsData(Collection $scores, FollowUpSystem $followUpSystem): array
    {
        return [];
    }

    /**
     * Add scrollable behaviour to chart.
     */
    protected function setScrollableChart(Collection $scores, array $chartData): array
    {
        $chartData['chart']->scrollablePlotArea = [
            'minWidth' => count($scores) * 50,
            'scrollPositionX' => 0,
        ];

        return $chartData;
    }
}
