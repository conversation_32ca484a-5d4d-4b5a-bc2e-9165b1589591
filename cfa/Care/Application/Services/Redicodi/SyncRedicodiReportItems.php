<?php

namespace Cfa\Care\Application\Services\Redicodi;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class SyncRedicodiReportItems
{
    public function __invoke(array $reportItems, CareInfo $careInfo, Redicodi $redicodi): void
    {
        $pivotValues = ['care_info_id' => $careInfo->id];
        $redicodi->subjects()
            ->wherePivot('care_info_id', $careInfo->id)
            ->syncWithPivotValues($this->getSubjectIds($careInfo->school_id, $reportItems), $pivotValues);
        $redicodi->followUpSystems()
            ->wherePivot('care_info_id', $careInfo->id)
            ->syncWithPivotValues($this->getFollowUpSystemIds($careInfo->school_id, $reportItems), $pivotValues);
    }

    private function getSubjectIds(int $schoolId, array $uids): Collection
    {
        if (empty($uids)) {
            return collect();
        }

        return Subject::whereIn('uid', $uids)
            ->where(
                fn(Builder $builder): Builder =>
                    $builder->whereNull('school_id')->orWhere('school_id', $schoolId),
            )
            ->pluck('id');
    }

    private function getFollowUpSystemIds(int $schoolId, array $uids): Collection
    {
        if (empty($uids)) {
            return collect();
        }

        return FollowUpSystem::where('school_id', $schoolId)
            ->whereIn('uid', $uids)
            ->pluck('id');
    }
}
