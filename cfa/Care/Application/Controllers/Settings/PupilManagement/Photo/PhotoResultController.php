<?php

declare(strict_types=1);

namespace Cfa\Care\Application\Controllers\Settings\PupilManagement\Photo;

use App\Controllers\Controller;
use App\Services\Export\ZipService;
use Cfa\Care\Domain\PhotoImportJob\PhotoImportJob;
use Illuminate\View\View;

final class PhotoResultController extends Controller
{
    /** @var ZipService */
    protected $zipService;

    public function __invoke(PhotoImportJob $photoImportJob): View
    {
        return view('settings.care.pupil-management.upload-photos.result', [
            'photoImportJob' => $photoImportJob,
        ]);
    }
}
