<?php

declare(strict_types=1);

namespace Cfa\Care\Application\Controllers\Settings\PupilManagement\Photo;

use App\Http\Requests\FormRequest;
use App\Models\File\FileUsage;
use App\Rules\FileUploadAllowedRule;
use App\Services\File\FileValidationService;
use Override;

final class PhotoImportJobRequest extends FormRequest
{
    public const ATTACHMENTS = 'attachments';

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::ATTACHMENTS . '.0.file' => [
                    'required',
                    'string',
                    new FileUploadAllowedRule(app(FileValidationService::class), FileUsage::PhotoImportAttachments),
                ],
            ],
        );
    }
}
