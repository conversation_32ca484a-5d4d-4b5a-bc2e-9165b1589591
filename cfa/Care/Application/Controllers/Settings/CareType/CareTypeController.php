<?php

namespace Cfa\Care\Application\Controllers\Settings\CareType;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\CareInput\CareType\CareTypeRepositoryInterface;
use Cfa\Care\Domain\CareInput\VisibilityType;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\View\View;

class CareTypeController extends Controller
{
    /**
     * The injected CareType repository.
     *
     * @var CareTypeRepositoryInterface
     */
    private $careTypeRepository;

    /**
     * CareTypeController constructor.
     *
     * @param CareTypeRepositoryInterface $careTypeRepository The injected repository.
     */
    public function __construct(CareTypeRepositoryInterface $careTypeRepository)
    {
        $this->careTypeRepository = $careTypeRepository;
    }

    /**
     * List careTypes for given school.
     *
     * @throws AuthorizationException Thrown if the school is not found in the request.
     */
    public function index(): View
    {
        $models = CareType::query()
            ->withCount('careInputs')
            ->whereSchoolId(school()->id)
            ->orderByRaw('CASE WHEN archived_at THEN 1 ELSE 0 END')
            ->orderBy('name')
            ->get()
            ->each
            ->append('has_relations');

        $baseUrl = route('web.settings.care.care-types.index');
        $title = trans('titles.care-types.overview');
        $disableTooltip = trans('tooltips.delete-disable-reason-care-types');

        return view('settings.pages.index', compact('models', 'baseUrl', 'title', 'disableTooltip'));
    }

    /**
     * Renders the input view for creating new CareType.
     */
    public function create(): View
    {
        $model = new CareType();
        $model->show_visibility = true;
        $model->show_attachments = true;
        $model->show_care_givers = true;
        $model->show_care_themes = true;
        $model->default_visibility = VisibilityType::Everyone;

        $title = trans('titles.care-types.create');
        $action = route('web.settings.care.care-types.store');
        $cancelUrl = route('web.settings.care.care-types.index');
        $method = Request::METHOD_POST;

        $visibilityTypes = collect(VisibilityType::cases())->map->toArray();

        return view('settings.care.care-types.form', compact(
            'model',
            'title',
            'action',
            'cancelUrl',
            'method',
            'visibilityTypes',
        ));
    }

    /**
     * Handles the post of the create form.
     *
     * @param FormRequest $request The post request containing the data.
     *
     * @throws AuthorizationException Thrown if the school is not found in the request.
     */
    public function store(FormRequest $request): JsonResponse
    {
        $this->saveCareType($request, new CareType());

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.care-types.index'),
        );
    }

    /**
     * Renders the input view for editing a CareType.
     *
     * @param CareType $careType The careType we want to edit.
     */
    public function edit(CareType $careType): View
    {
        $title = trans('titles.care-types.edit', ['name' => $careType->name]);
        $action = route('web.settings.care.care-types.update', ['careType' => $careType->uid]);
        $cancelUrl = route('web.settings.care.care-types.index');
        $method = Request::METHOD_PUT;

        $confirmToSaveMessage = null;
        if ($careType->loadCount('careInputs')->has_relations) {
            $confirmToSaveMessage = trans('labels.confirm-save-linked-items');
        }

        return view(
            'settings.care.care-types.form',
            [
                'model' => $careType,
                'title' => $title,
                'action' => $action,
                'cancelUrl' => $cancelUrl,
                'method' => $method,
                'confirmToSaveMessage' => $confirmToSaveMessage,
                'visibilityTypes' => collect(VisibilityType::cases())->map->toArray(),
            ],
        );
    }

    /**
     * Handles the request of the edit form.
     *
     * @param CareType $careType The careType we want to patch.
     * @param FormRequest $request The post request containing the data.
     *
     * @throws AuthorizationException Thrown if the school is not found in the request.
     */
    public function update(CareType $careType, FormRequest $request): JsonResponse
    {
        $this->saveCareType($request, $careType);

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.care-types.index'),
        );
    }

    /**
     * Delete the careType.
     *
     * @param CareType $careType The careType we want to delete.
     * @param CareTypeDeleteRequest $request The delete request.
     */
    public function delete(CareType $careType, CareTypeDeleteRequest $request): JsonResponse
    {
        $this->careTypeRepository->delete($careType);

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.care-types.index'),
        );
    }

    /**
     * Archive the careType.
     *
     * @param CareType $careType The careType we want to delete.
     */
    public function archive(CareType $careType): JsonResponse
    {
        $this->careTypeRepository->archive($careType);

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.care-types.index'),
        );
    }

    /**
     * Dearchive the careType.
     *
     * @param CareType $careType The careType we want to delete.
     */
    public function dearchive(CareType $careType): JsonResponse
    {
        $this->careTypeRepository->dearchive($careType);

        return $this->respondJsonRedirectWriteConnection(
            route('web.settings.care.care-types.index'),
        );
    }

    /**
     * Saves the given care type with the data from the incoming request.
     *
     * @param FormRequest $request The incoming request.
     * @param CareType $careType The given care type.
     *
     * @throws AuthorizationException Thrown if the school is not found in the request.
     */
    private function saveCareType(FormRequest $request, CareType $careType): void
    {
        $careType->fill($request->all());
        if ($defaultDescription = $request->get('default_description')) {
            $defaultDescription = Str::cleanZeroWidthSpaces($defaultDescription);
            // The spans get added by the editor if you have a selection, but cause issues in editor later.
            $defaultDescription = preg_replace('/<span class=.+?ql-cursor.+?>\s*<\/span>/', '', $defaultDescription);
            $careType->default_description = $defaultDescription;
        }

        $careType->school_id = school()->id;
        $careType->show_visibility = $request->get('show_visibility') === 'true';
        $careType->show_attachments = $request->get('show_attachments') === 'true';
        $careType->show_care_givers = $request->get('show_care_givers') === 'true';
        $careType->show_care_themes = $request->get('show_care_themes') === 'true';

        $this->careTypeRepository->save($careType);
    }
}
