<?php

namespace Cfa\Care\Application\Listeners;

use Cfa\Care\Domain\Export\CareExportJob;
use Cfa\Care\Domain\Export\CareExportJobUpdated;
use Cfa\Common\Application\Exports\ExportJobStatus;

class EvaluateSchoolExportCareJobFailure
{
    public function handle(CareExportJobUpdated $event): void
    {
        $careExportJob = $event->careExportJob;
        // We only want to check failures on the pupil exports. Not the whole school export.
        if ($careExportJob->care_info_id === null) {
            return;
        }

        if ($careExportJob->status !== ExportJobStatus::Failed) {
            return;
        }

        // If one of the pupil exports fails, we want to fail the whole school export.
        CareExportJob::where('school_id', $careExportJob->school_id)
            ->whereNull('care_info_id')
            ->where('status', ExportJobStatus::InProgress)
            ->update(['status' => ExportJobStatus::Failed]);
    }
}
