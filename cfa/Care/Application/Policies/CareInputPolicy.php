<?php

namespace Cfa\Care\Application\Policies;

use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\VisibilityType;
use Cfa\Common\Application\Services\Permission\PermissionService;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CareInputPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user created the note or has the 'edit notes' permission.
     *
     * @param User|null $user User who wants to update the care input.
     * @param CareInput $careInput CareInput that needs to be updated.
     */
    public function update(?User $user, CareInput $careInput): bool
    {
        if (is_null($user)) {
            return false;
        }

        if ($careInput->creator_id === $user->id) {
            return true;
        }

        if (!$user->hasPermission(PermissionName::CanEditNotes, $careInput->school)) {
            return false;
        }

        return $this->userHasAccessToAtLeastOnePupil($user, $careInput, $careInput->school);
    }

    public function view(User $user, CareInput $careInput): bool
    {
        $school = $careInput->school;

        if ($careInput->creator_id === $user->id) {
            return true;
        }

        if (!$this->userHasAccessToAtLeastOnePupil($user, $careInput, $school)) {
            return false;
        }

        return match ($careInput->visibility) {
            VisibilityType::Everyone => true,
            VisibilityType::Confidential => $this->userHasAccessToConfidentialNotes($user, $school),
            default => false,
        };
    }

    private function userHasAccessToConfidentialNotes(User $user, School $school): bool
    {
        return $user->hasPermission(PermissionName::HasAccessToConfidentialCareInputs, $school);
    }

    private function userHasAccessToAtLeastOnePupil(User $user, CareInput $careInput, School $school): bool
    {
        if ($user->hasPermission(PermissionName::CanAccessHistory, $school)) {
            return true;
        }

        $userCareDataAccessGroupIds = app(PermissionService::class)
            ->getGroupsWithCareDataAccess($user, $school)
            ->pluck('id');
        $pupilGroupIds = Career::active()
            ->distinct()
            ->whereIn('user_id', $careInput->careInfosWithTrashed->pluck('pupil_id'))
            ->where('school_id', $school->id)
            ->pluck('group_id');

        return $userCareDataAccessGroupIds->intersect($pupilGroupIds)->isNotEmpty();
    }
}
