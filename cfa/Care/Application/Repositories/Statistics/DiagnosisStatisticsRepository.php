<?php

namespace Cfa\Care\Application\Repositories\Statistics;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Override;

class DiagnosisStatisticsRepository extends StatisticsRepository implements DiagnosisStatisticsRepositoryInterface
{
    /**
     * {@inheritdoc}
     *
     * A count of the care infos by the different diagnoses are returned.
     * Since the possibility to filter on school or pupil needs to exists, we need to include the relation
     * towards the care infos.
     */
    #[Override]
    protected function getQueryBuilder(School $school, ?User $user = null): Builder
    {
        return DB::table('diagnoses')
            ->select(
                'diagnoses.uid',
                'diagnoses.name',
                DB::raw($this->getQueryForConcatenatedPupilNames()),
                DB::raw('COUNT(DISTINCT care_infos.uid) AS value'),
            )
            ->join('care_info_diagnosis', 'care_info_diagnosis.diagnosis_id', '=', 'diagnoses.id')
            ->join('care_infos', 'care_infos.id', '=', 'care_info_diagnosis.care_info_id')
            ->join('users', 'users.id', '=', 'care_infos.pupil_id')
            ->where('care_infos.school_id', $school->id)
            ->whereNull('care_infos.deleted_at')
            ->whereNull('diagnoses.deleted_at')
            ->groupBy('diagnoses.uid', 'diagnoses.name')
            ->orderBy('value', 'desc')
            ->orderBy('diagnoses.name');
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getStatisticsByPupils(array $pupilIds, School $school, ?User $user = null): Collection
    {
        return $this->transformPupilNames($this->getStatisticsByPupilsQuery($pupilIds, $school, $user)->get());
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getStatisticsByGroups(array $groupIds, School $school, ?User $user = null): Collection
    {
        return $this->transformPupilNames($this->getStatisticsByGroupsQuery($groupIds, $school, $user)->get());
    }
}
