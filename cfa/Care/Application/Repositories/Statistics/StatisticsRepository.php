<?php

namespace Cfa\Care\Application\Repositories\Statistics;

use Carbon\Carbon;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Override;
use stdClass;

use function explode;
use function mb_substr_count;
use function str_ends_with;
use function strlen;
use function substr;

abstract class StatisticsRepository implements StatisticsRepositoryInterface
{
    private const PUPIL_TOOLTIP_LIMIT = 15;

    public const SEPARATOR = '߇ࠑ';

    /**
     * Group concat can only take 1024 characters. To prevent a chopped of name, we set the limit a little lower.
     */
    protected const MAX_GROUP_CONCAT_LENGTH = '980';

    /**
     * {@inheritdoc}
     *
     * This returns the result get from getStatisticsByPupilsQuery.
     */
    #[Override]
    public function getStatisticsByPupils(array $pupilIds, School $school, ?User $user = null): Collection
    {
        return $this->getStatisticsByPupilsQuery($pupilIds, $school, $user)->get();
    }

    /**
     * {@inheritdoc}
     *
     * This returns the result get from getStatisticsByGroupsQuery.
     */
    #[Override]
    public function getStatisticsByGroups(array $groupIds, School $school, ?User $user = null): Collection
    {
        return $this->getStatisticsByGroupsQuery($groupIds, $school, $user)->get();
    }

    /**
     * In every repository, a main query builder is set, but that query only contains a filter on school.
     * If the result out of the query needs to be filtered on certain pupils,
     * this function will add the condition to the query.
     *
     * @param array $pupilIds The pupil id's.
     * @param School $school The given school.
     */
    protected function getStatisticsByPupilsQuery(array $pupilIds, School $school, ?User $user): Builder
    {
        return $this->getQueryBuilder($school, $user)
            ->whereIn('care_infos.pupil_id', $pupilIds);
    }

    /**
     * In every repository, a main query builder is set, but that query only contains a filter on school.
     * If the result out of the query needs to be filtered on certain groups,
     * this function will add the needed relations (careers and groups) and the condition to the query.
     *
     * @param array $groupIds The group id's.
     * @param School $school The given school.
     */
    protected function getStatisticsByGroupsQuery(array $groupIds, School $school, ?User $user): Builder
    {
        $now = Carbon::now();

        return $this->getQueryBuilder($school, $user)
            ->join('careers', 'careers.user_id', '=', 'care_infos.pupil_id')
            ->where('careers.startdate', '<=', $now)
            ->where(function (Builder $query) use ($now): void {
                $query->where('careers.enddate', '>', $now)
                    ->orWhereNull('careers.enddate');
            })
            ->whereIn('careers.group_id', $groupIds)
            ->whereNull('careers.deleted_at');
    }

    /**
     * In certain cases, the result out of the given builder will only contain the data on what to
     * calculate the statistics on. We hereby specify the builder as a subquery.
     * In this function, a count of the care inputs by the different names of that subquery are returned.
     *
     * @param Builder $builder The original query to take the statistics from.
     */
    protected function calculateStatisticsOnSubquery(Builder $builder): Collection
    {
        $statistics = DB::table(DB::raw('(' . $builder->toSql() . ') AS statistics'))
            ->setBindings($builder->getBindings())
            ->select(
                'uid',
                'name',
                DB::raw(
                    $this->getQueryForConcatenatedPupilNames(),
                ),
                DB::raw('COUNT(care_input_uid) AS value'),
            )
            ->groupBy('uid', 'name')
            ->orderBy('value', 'desc')
            ->orderBy('name')
            ->get();

        return $this->transformPupilNames($statistics);
    }

    /**
     * Transforms the pupilnames for rendering in tooltips.
     */
    public static function transformPupilNames(Collection $results): Collection
    {
        return $results->each(function (stdClass $item): void {
            if (!isset($item->pupil_names)) {
                return;
            }
            $pupilNames = $item->pupil_names;
            $resultIsTooLong = false;

            if (
                strlen($pupilNames) > self::MAX_GROUP_CONCAT_LENGTH ||
                mb_substr_count($pupilNames, self::SEPARATOR) !== mb_substr_count($pupilNames, ',') + 1
            ) {
                $pupilNames = Str::beforeLast($pupilNames, ',');
                $resultIsTooLong = true;
            }

            $pupilNamesCollection = collect(explode(',', $pupilNames))
                ->sort()
                ->values()
                ->slice(0, self::PUPIL_TOOLTIP_LIMIT + 1)
                ->map(fn(string $fullName): string => self::formatName($fullName));

            if ($pupilNamesCollection->count() >= self::PUPIL_TOOLTIP_LIMIT) {
                $resultIsTooLong = true;
            }
            $item->pupil_names = $pupilNamesCollection->implode('<br />');

            if ($resultIsTooLong) {
                $item->pupil_names .= '<br />...';
            }
        });
    }

    /**
     * Returns the group_concat for pupil names.
     * We replace a regular comma in the pupilname by ‚ as this caused unwanted side effects/warnings when pupils had
     * commas in their name(s).
     * @see http://www.fileformat.info/info/unicode/char/201a/index.htm
     */
    public static function getQueryForConcatenatedPupilNames(): string
    {
        return 'GROUP_CONCAT(DISTINCT(
            REPLACE(lastname || "' . self::SEPARATOR . '" || firstname, ",", "‚"))) as pupil_names';
    }

    abstract protected function getQueryBuilder(School $school, ?User $user = null): Builder;

    protected function addCareInputVisibilityConditions(Builder $builder, ?School $school, ?User $user = null): Builder
    {
        return new CareInput()->scopeVisible($builder, $school, $user);
    }

    private static function formatName(string $fullName): string
    {
        $stars = '';
        $maxStars = 3;
        $starCount = 0;
        while (str_ends_with($fullName, '*') && $starCount < $maxStars) {
            $fullName = substr($fullName, 0, -1);
            $stars .= '*';
            $starCount++;
        }
        $names = explode(self::SEPARATOR, $fullName);

        return $names[1] . ' ' . $names[0] . $stars;
    }
}
