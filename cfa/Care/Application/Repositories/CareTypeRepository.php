<?php

namespace Cfa\Care\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\CareInput\CareType\CareTypeRepositoryInterface;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Override;

class CareTypeRepository extends Repository implements CareTypeRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new CareType();
    }

    #[Override]
    public function getAllBySchoolWithoutArchived(School $school): Collection
    {
        return $this->model->withoutArchived()
            ->where('school_id', $school->id)
            ->orderBy('name')
            ->get();
    }

    #[Override]
    public function getAllBySchool(School $school): Collection
    {
        return $this->model->where('school_id', $school->id)
            ->orderBy('name')
            ->get();
    }

    #[Override]
    public function getAllInUseBySchool(School $school): Collection
    {
        return CareType::where('school_id', $school->id)
            ->whereExists(
                fn(QueryBuilder $query): QueryBuilder => $query->select(DB::raw(1))
                    ->from('care_inputs')
                    ->where('school_id', $school->id)
                    ->whereColumn('care_type_id', 'care_types.id')
                    ->whereNull('care_inputs.deleted_at'),
            )
            ->orderBy('name')
            ->get();
    }
}
