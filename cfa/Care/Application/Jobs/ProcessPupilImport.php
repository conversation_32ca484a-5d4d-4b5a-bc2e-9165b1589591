<?php

namespace Cfa\Care\Application\Jobs;

use App\Constants\Queues;
use Cfa\Care\Application\Services\PupilImportJob\PupilImportJobImport;
use Cfa\Care\Domain\PupilImportJob\PupilImportJob;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class ProcessPupilImport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private int $pupilImportJobId;

    /** @var int */
    public $tries = 1;

    public function __construct(int $pupilImportJobId)
    {
        $this->queue = Queues::LOW_PRIORITY;
        $this->pupilImportJobId = $pupilImportJobId;
    }

    public function handle(): void
    {
        $pupilImportJob = PupilImportJob::findOrFail($this->pupilImportJobId);
        new PupilImportJobImport($pupilImportJob)
            ->import($pupilImportJob->file, Storage::getDefaultCloudDriver());
    }

    public function getPupilImportJobId(): int
    {
        return $this->pupilImportJobId;
    }
}
