<?php

namespace Cfa\Evaluation\Domain\EvaluationTest;

use App\Casts\EncodedString;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Override;

/**
 * Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $school_id
 * @property int $group_id
 * @property int $subject_id
 * @property int $report_period_id
 * @property string $name
 * @property Carbon $date
 * @property float|null $min
 * @property float|null $max
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property bool $has_score
 * @property int|null $quotation_system_id
 * @property bool $has_comment
 * @property bool $on_report
 * @property Carbon|null $imported_at
 * @property string|null $bingel_test_uid
 * @property string|null $bingel_domain_uid
 * @property string|null $description
 * @property float|null $average
 * @property float|null $median
 * @property-read Collection<int, EvaluationTestScore> $evaluationTestScores
 * @property-read int|null $evaluation_test_scores_count
 * @property-read array $validation_rules
 * @property-read Group $group
 * @property-read QuotationSystem|null $quotationSystem
 * @property-read ReportPeriod $reportPeriod
 * @property-read School $school
 * @property-read Subject $subject
 * @method static EvaluationTestFactory factory($count = null, $state = [])
 * @method static EvaluationTestRepositoryInterface getRepository()
 * @method static Builder|EvaluationTest newModelQuery()
 * @method static Builder|EvaluationTest newQuery()
 * @method static Builder|EvaluationTest onlyTrashed()
 * @method static Builder|EvaluationTest query()
 * @method static float|null randomAverage()
 * @method static string|null randomBingelDomainUid()
 * @method static string|null randomBingelTestUid()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon randomDate()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomDescription()
 * @method static int randomGroupId()
 * @method static bool randomHasComment()
 * @method static bool randomHasScore()
 * @method static int randomId()
 * @method static Carbon|null randomImportedAt()
 * @method static float|null randomMax()
 * @method static float|null randomMedian()
 * @method static float|null randomMin()
 * @method static string randomName()
 * @method static bool randomOnReport()
 * @method static int|null randomQuotationSystemId()
 * @method static int randomReportPeriodId()
 * @method static int randomSchoolId()
 * @method static int randomSubjectId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|EvaluationTest whereAverage($value)
 * @method static Builder|EvaluationTest whereBingelDomainUid($value)
 * @method static Builder|EvaluationTest whereBingelTestUid($value)
 * @method static Builder|EvaluationTest whereCreatedAt($value)
 * @method static Builder|EvaluationTest whereDate($value)
 * @method static Builder|EvaluationTest whereDeletedAt($value)
 * @method static Builder|EvaluationTest whereDescription($value)
 * @method static Builder|EvaluationTest whereGroupId($value)
 * @method static Builder|EvaluationTest whereHasComment($value)
 * @method static Builder|EvaluationTest whereHasScore($value)
 * @method static Builder|EvaluationTest whereId($value)
 * @method static Builder|EvaluationTest whereImportedAt($value)
 * @method static Builder|EvaluationTest whereMax($value)
 * @method static Builder|EvaluationTest whereMedian($value)
 * @method static Builder|EvaluationTest whereMin($value)
 * @method static Builder|EvaluationTest whereName($value)
 * @method static Builder|EvaluationTest whereOnReport($value)
 * @method static Builder|EvaluationTest whereQuotationSystemId($value)
 * @method static Builder|EvaluationTest whereReportPeriodId($value)
 * @method static Builder|EvaluationTest whereSchoolId($value)
 * @method static Builder|EvaluationTest whereSubjectId($value)
 * @method static Builder|EvaluationTest whereUid($value)
 * @method static Builder|EvaluationTest whereUpdatedAt($value)
 * @method static Builder|EvaluationTest withTrashed()
 * @method static Builder|EvaluationTest withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class EvaluationTest extends Model
{
    use SoftDeletes;
    use Uid;

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'date',
        'min',
        'max',
        'description',
    ];

    /** {@inheritdoc} */
    protected $visible = [
        'average',
        'median',
        'evaluationTestScores',
        'quotationSystem',
        'on_report',
    ];

    /** @var array */
    protected $dispatchesEvents = [
        'creating' => EvaluationTestCreating::class,
    ];

    /** @var array */
    protected $casts = [
        'average' => 'float',
        'median' => 'float',
        'date' => 'datetime',
        'group_id' => 'integer',
        'has_comment' => 'boolean',
        'has_score' => 'boolean',
        'max' => 'float',
        'min' => 'float',
        'name' => EncodedString::class,
        'on_report' => 'boolean',
        'quotation_system_id' => 'integer',
        'report_period_id' => 'integer',
        'school_id' => 'integer',
        'subject_id' => 'integer',
        'imported_at' => 'datetime',
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'school_id' => [
                'required',
                'integer',
            ],
            'group_id' => [
                'required',
                'integer',
            ],
            'subject_id' => [
                'required',
                'integer',
            ],
            'report_period_id' => [
                'required',
                'integer',
            ],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'date' => [
                'required',
                'date',
            ],
            'min' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999',
            ],
            'max' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999',
            ],
            'has_score' => [
                'nullable',
                'boolean',
            ],
            'quotation_system_id' => [
                'nullable',
                'integer',
            ],
            'has_comment' => [
                'nullable',
                'boolean',
            ],
            'description' => [
                'nullable',
                'string',
                'max: 65535',
            ],
        ]);
    }

    /**
     * Get the school this test belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the group this test belongs to.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get the subject this test belongs to.
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the report period this test belongs to.
     */
    public function reportPeriod(): BelongsTo
    {
        return $this->belongsTo(ReportPeriod::class);
    }

    public function quotationSystem(): BelongsTo
    {
        return $this->belongsTo(QuotationSystem::class);
    }

    public function evaluationTestScores(): HasMany
    {
        return $this->hasMany(EvaluationTestScore::class);
    }
}
