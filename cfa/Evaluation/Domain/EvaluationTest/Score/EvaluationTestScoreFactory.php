<?php

namespace Cfa\Evaluation\Domain\EvaluationTest\Score;

use App\Factories\Factory;
use Carbon\Carbon;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * EvaluationTestScoreFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|EvaluationTestScore[]|EvaluationTestScore create($attributes = [], Model|null $parent = null)
 * @method Collection|EvaluationTestScore[]|EvaluationTestScore createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class EvaluationTestScoreFactory extends Factory
{
    /** @var string */
    protected $model = EvaluationTestScore::class;

    public function setScore(?float $score): self
    {
        return $this->state(fn(): array => ['score' => $score]);
    }

    public function setComment(?string $comment): self
    {
        return $this->state(fn(): array => ['comment' => $comment]);
    }

    public function forPupil(User $pupil): self
    {
        return $this->state(fn(): array => ['pupil_id' => $pupil->id]);
    }

    public function forEvaluationTest(EvaluationTest $evaluationTest): self
    {
        return $this->state(fn(): array => ['evaluation_test_id' => $evaluationTest->id]);
    }

    public function forQuotation(?Quotation $quotation): self
    {
        return $this->state(fn(): array => ['quotation_id' => $quotation?->id]);
    }

    public function setDeletedAt(Carbon $deleteddAt): self
    {
        return $this->state(fn(): array => ['deleted_at' => $deleteddAt]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'score' => $this->faker->randomFloat(1, 0, 7),
            'comment' => $this->faker->sentence(),
            'pupil_id' => Pupil::randomId(),
            'evaluation_test_id' => EvaluationTest::randomId(),
        ];
    }
}
