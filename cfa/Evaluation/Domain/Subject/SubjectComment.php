<?php

namespace Cfa\Evaluation\Domain\Subject;

use App\Casts\EncodedString;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

/**
 * Cfa\Evaluation\Domain\Subject\SubjectComment
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $pupil_id
 * @property int $report_period_id
 * @property int $subject_id
 * @property string|null $comment
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read Pupil $pupil
 * @property-read ReportPeriod $reportPeriod
 * @property-read Subject $subject
 * @property-read User|null $updater
 * @method static Builder|SubjectComment newModelQuery()
 * @method static Builder|SubjectComment newQuery()
 * @method static Builder|SubjectComment query()
 * @method static string|null randomComment()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static int randomId()
 * @method static int randomPupilId()
 * @method static int randomReportPeriodId()
 * @method static int randomSubjectId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|SubjectComment whereComment($value)
 * @method static Builder|SubjectComment whereCreatedAt($value)
 * @method static Builder|SubjectComment whereCreatorId($value)
 * @method static Builder|SubjectComment whereId($value)
 * @method static Builder|SubjectComment wherePupilId($value)
 * @method static Builder|SubjectComment whereReportPeriodId($value)
 * @method static Builder|SubjectComment whereSubjectId($value)
 * @method static Builder|SubjectComment whereUid($value)
 * @method static Builder|SubjectComment whereUpdatedAt($value)
 * @method static Builder|SubjectComment whereUpdaterId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class SubjectComment extends Model
{
    use Uid;
    use TouchedByUser;

    /** @var array */
    protected $fillable = ['comment'];

    /** @var array */
    protected $casts = [
        'comment' => EncodedString::class,
        'pupil_id' => 'integer',
        'report_period_id' => 'integer',
        'subject_id' => 'integer',
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'comment' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
            ],
        );
    }

    public function pupil(): BelongsTo
    {
        return $this->belongsTo(Pupil::class);
    }

    public function reportPeriod(): BelongsTo
    {
        return $this->belongsTo(ReportPeriod::class);
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }
}
