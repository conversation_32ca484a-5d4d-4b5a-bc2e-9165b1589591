<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment;

use App\Casts\EncodedString;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

/**
 * Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string $comment The comment on a pupil for a given goal.
 * @property int $pupil_id
 * @property int $follow_up_system_goal_id
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $report_period_id
 * @property int|null $follow_up_system_input_moment_id
 * @property bool $comment_on_report
 * @property-read User|null $creator
 * @property-read FollowUpSystemGoal $followUpSystemGoal
 * @property-read FollowUpSystemInputMoment|null $followUpSystemInputMoment
 * @property-read array $validation_rules
 * @property-read Pupil $pupil
 * @property-read ReportPeriod|null $reportPeriod
 * @property-read User|null $updater
 * @method static FollowUpSystemGoalCommentFactory factory($count = null, $state = [])
 * @method static FollowUpSystemGoalCommentRepositoryInterface getRepository()
 * @method static Builder|FollowUpSystemGoalComment newModelQuery()
 * @method static Builder|FollowUpSystemGoalComment newQuery()
 * @method static Builder|FollowUpSystemGoalComment query()
 * @method static string randomComment()
 * @method static bool randomCommentOnReport()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static int randomFollowUpSystemGoalId()
 * @method static int|null randomFollowUpSystemInputMomentId()
 * @method static int randomId()
 * @method static int randomPupilId()
 * @method static int|null randomReportPeriodId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|FollowUpSystemGoalComment whereComment($value)
 * @method static Builder|FollowUpSystemGoalComment whereCommentOnReport($value)
 * @method static Builder|FollowUpSystemGoalComment whereCreatedAt($value)
 * @method static Builder|FollowUpSystemGoalComment whereCreatorId($value)
 * @method static Builder|FollowUpSystemGoalComment whereFollowUpSystemGoalId($value)
 * @method static Builder|FollowUpSystemGoalComment whereFollowUpSystemInputMomentId($value)
 * @method static Builder|FollowUpSystemGoalComment whereId($value)
 * @method static Builder|FollowUpSystemGoalComment wherePupilId($value)
 * @method static Builder|FollowUpSystemGoalComment whereReportPeriodId($value)
 * @method static Builder|FollowUpSystemGoalComment whereUid($value)
 * @method static Builder|FollowUpSystemGoalComment whereUpdatedAt($value)
 * @method static Builder|FollowUpSystemGoalComment whereUpdaterId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class FollowUpSystemGoalComment extends Model
{
    use Uid;
    use TouchedByUser;

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $casts = [
        'comment' => EncodedString::class,
        'comment_on_report' => 'boolean',
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $visible = [
        'creator',
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'comment' => [
                    'string',
                    'required',
                    'max:65535',
                ],
            ],
        );
    }

    /** @var array */
    protected $fillable = ['comment', 'comment_on_report'];

    public function followUpSystemGoal(): BelongsTo
    {
        return $this->belongsTo(FollowUpSystemGoal::class);
    }

    public function reportPeriod(): BelongsTo
    {
        return $this->belongsTo(ReportPeriod::class);
    }

    public function pupil(): BelongsTo
    {
        return $this->belongsTo(Pupil::class);
    }

    public function followUpSystemInputMoment(): BelongsTo
    {
        return $this->belongsTo(FollowUpSystemInputMoment::class);
    }
}
