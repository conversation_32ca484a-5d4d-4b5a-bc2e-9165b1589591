<?php

namespace Cfa\Evaluation\Domain\FollowUpSystemGoalQuotation\Comment;

use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Faker\Generator as Faker;

$factory->define(
    FollowUpSystemGoalComment::class,
    function (Faker $faker) {
        return [
            'comment' => $faker->text(191),
            'pupil_id' => Pupil::randomId(),
            'follow_up_system_goal_id' => FollowUpSystemGoal::randomId(),
            'follow_up_system_input_moment_id' => FollowUpSystemInputMoment::randomId(),
            'comment_on_report' => true,
        ];
    },
);
