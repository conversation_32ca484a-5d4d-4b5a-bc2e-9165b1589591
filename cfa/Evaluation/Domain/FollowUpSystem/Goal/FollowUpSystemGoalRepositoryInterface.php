<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\Goal;

use App\Repositories\RepositoryInterface;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;

interface FollowUpSystemGoalRepositoryInterface extends RepositoryInterface
{
    public function getGoalsForFollowUpSystemForInputMoment(
        FollowUpSystem $followUpSystem,
        User $pupil,
        FollowUpSystemInputMoment $followUpSystemInputMoment,
    ): Collection;

    public function getGoalTreeForFollowUpSystem(FollowUpSystem $followUpSystem): SupportCollection;

    public function getGoalTreeForFollowUpSystemWithArchived(FollowUpSystem $followUpSystem): SupportCollection;

    public function getGoalTreeForFollowUpSystemByTargetAudienceAndIncludedLevels(
        FollowUpSystem $followUpSystem,
        TargetAudience $targetAudience,
        SupportCollection $includedLevels,
    ): SupportCollection;

    public function getGoalTreeStructureForFollowUpSystem(
        FollowUpSystem $followUpSystem,
        ?int $maxDepth = null,
    ): SupportCollection;

    public function flushCacheForFollowUpSystem(FollowUpSystem $followUpSystem): void;

    public function getChildDepthForGoal(FollowUpSystemGoal $followUpSystemGoal): int;
}
