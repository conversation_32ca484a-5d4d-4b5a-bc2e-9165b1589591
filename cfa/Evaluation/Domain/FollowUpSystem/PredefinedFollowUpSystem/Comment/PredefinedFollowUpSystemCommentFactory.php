<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment;

use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\PredefinedFollowUpSystemElementFactory;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * PredefinedFollowUpSystemCommentFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|PredefinedFollowUpSystemComment[]|PredefinedFollowUpSystemComment create($attributes = [], Model|null $parent = null)
 * @method Collection|PredefinedFollowUpSystemComment[]|PredefinedFollowUpSystemComment createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class PredefinedFollowUpSystemCommentFactory extends PredefinedFollowUpSystemElementFactory
{
    /** @var string */
    protected $model = PredefinedFollowUpSystemComment::class;

    public function setComment(string $comment): self
    {
        return $this->state(fn(): array => [
            'comment' => $comment,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        return array_merge(parent::definition(), [
            'comment' => $this->faker->text(),
        ]);
    }
}
