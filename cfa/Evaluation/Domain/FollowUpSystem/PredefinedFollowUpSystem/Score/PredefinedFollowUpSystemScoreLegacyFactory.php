<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Faker\Generator as Faker;

$factory->define(PredefinedFollowUpSystemScore::class, function (Faker $faker) {
    return [
        'pupil_id' => Pupil::randomId(),
        'school_id' => School::randomId(),
        'schoolyear_id' => Schoolyear::getRepository()->getCurrent()->id,
        'predefined_follow_up_system_id' => PredefinedFollowUpSystem::randomId(),
        'subtype' => FollowUpSystemSubType::Default,
        'date' => $faker->date(),
        'score' => $faker->numberBetween(0, 20),
        'percentile' => $faker->numberBetween(0, 100),
        'zone' => $faker->randomElement(['A', 'B', 'C', 'D', 'E']),
    ];
});
