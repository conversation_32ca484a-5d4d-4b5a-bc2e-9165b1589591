<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score;

use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\PredefinedFollowUpSystemElement;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Illuminate\Database\Eloquent\Builder;
use Override;

/**
 * Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $pupil_id
 * @property int $school_id
 * @property int $schoolyear_id
 * @property int $predefined_follow_up_system_id
 * @property TestMoment $test_moment
 * @property TestAudience $test_audience
 * @property Carbon $date
 * @property int|null $score
 * @property int|null $percentile
 * @property string|null $zone
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $deleted_at
 * @property FollowUpSystemSubType $subtype
 * @property string|null $source
 * @property int|null $score_nn Score for pupil without Dutch as first language (Niet Nederlandstalig)
 * @property int|null $percentile_nn Percentile for pupil without Dutch as first language (Niet Nederlandstalig)
 * @property string|null $zone_nn Zone for pupil without Dutch as first language (Niet Nederlandstalig)
 * @property int $repeating_year
 * @property-read User|null $creator
 * @property-read array $validation_rules
 * @property-read PredefinedFollowUpSystem $predefinedFollowUpSystem
 * @property-read Pupil $pupil
 * @property-read School $school
 * @property-read Schoolyear $schoolyear
 * @property-read User|null $updater
 * @method static PredefinedFollowUpSystemScoreFactory factory($count = null, $state = [])
 * @method static PredefinedFollowUpSystemScoreRepositoryInterface getRepository()
 * @method static Builder|PredefinedFollowUpSystemScore newModelQuery()
 * @method static Builder|PredefinedFollowUpSystemScore newQuery()
 * @method static Builder|PredefinedFollowUpSystemScore onlyTrashed()
 * @method static Builder|PredefinedFollowUpSystemScore query()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static Carbon randomDate()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomId()
 * @method static int|null randomPercentile()
 * @method static int|null randomPercentileNn()
 * @method static int randomPredefinedFollowUpSystemId()
 * @method static int randomPupilId()
 * @method static int randomRepeatingYear()
 * @method static int randomSchoolId()
 * @method static int randomSchoolyearId()
 * @method static int|null randomScore()
 * @method static int|null randomScoreNn()
 * @method static FollowUpSystemSubType randomSubtype()
 * @method static TestAudience randomTestAudience()
 * @method static TestMoment randomTestMoment()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static string|null randomZone()
 * @method static string|null randomZoneNn()
 * @method static Builder|PredefinedFollowUpSystemScore whereCreatedAt($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereCreatorId($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereDate($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereDeletedAt($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereId($value)
 * @method static Builder|PredefinedFollowUpSystemScore wherePercentile($value)
 * @method static Builder|PredefinedFollowUpSystemScore wherePercentileNn($value)
 * @method static Builder|PredefinedFollowUpSystemScore wherePredefinedFollowUpSystemId($value)
 * @method static Builder|PredefinedFollowUpSystemScore wherePupilId($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereRepeatingYear($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereSchoolId($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereSchoolyearId($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereScore($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereScoreNn($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereSubtype($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereTestAudience($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereTestMoment($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereUid($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereUpdatedAt($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereUpdaterId($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereZone($value)
 * @method static Builder|PredefinedFollowUpSystemScore whereZoneNn($value)
 * @method static Builder|PredefinedFollowUpSystemScore withTrashed()
 * @method static Builder|PredefinedFollowUpSystemScore withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class PredefinedFollowUpSystemScore extends PredefinedFollowUpSystemElement
{
    /** @var array */
    protected $fillable = [
        'score',
        'date',
    ];

    /** @var array */
    protected $casts = [
        'pupil_id' => 'integer',
        'school_id' => 'integer',
        'schoolyear_id' => 'integer',
        'predefined_follow_up_system_id' => 'integer',
        'score' => 'integer',
        'percentile' => 'integer',
        'score_nn' => 'integer',
        'percentile_nn' => 'integer',
        'repeating_year' => 'integer',
        'date' => 'datetime',
    ];

    /** @var string[] */
    protected $dispatchesEvents = [
        'creating' => PredefinedFollowUpSystemScoreCreatingEvent::class,
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'score' => [
                    'nullable',
                    'integer',
                ],
                'percentile' => [
                    'nullable',
                    'integer',
                ],
                'zone' => [
                    'nullable',
                    'string',
                    'max:191',
                ],
                'score_nn' => [
                    'nullable',
                    'integer',
                ],
                'percentile_nn' => [
                    'nullable',
                    'integer',
                ],
                'zone_nn' => [
                    'nullable',
                    'string',
                    'max:191',
                ],
            ],
        );
    }
}
