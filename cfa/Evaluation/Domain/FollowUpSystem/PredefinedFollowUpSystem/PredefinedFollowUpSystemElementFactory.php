<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;

use App\Factories\Factory;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use DateTime;
use Override;

class PredefinedFollowUpSystemElementFactory extends Factory
{
    public function forPupil(Pupil $pupil): self
    {
        return $this->state(fn(): array => [
            'pupil_id' => $pupil->id,
        ]);
    }

    public function inSchool(School $school): self
    {
        return $this->state(fn(): array => [
            'school_id' => $school->id,
        ]);
    }

    public function inSchoolyear(Schoolyear $schoolyear): self
    {
        return $this->state(fn(): array => [
            'schoolyear_id' => $schoolyear->id,
        ]);
    }

    public function forPredefinedFollowUpSystem(PredefinedFollowUpSystem $followUpSystem): self
    {
        return $this->state(fn(): array => [
            'predefined_follow_up_system_id' => $followUpSystem->id,
        ]);
    }

    public function setSubType(FollowUpSystemSubType $subType): self
    {
        return $this->state(fn(): array => [
            'subtype' => $subType,
        ]);
    }

    public function setTestMoment(TestMoment $testMoment): self
    {
        return $this->state(fn(): array => [
            'test_moment' => $testMoment,
        ]);
    }

    public function setTestAudience(TestAudience $testAudience): self
    {
        return $this->state(fn(): array => [
            'test_audience' => $testAudience,
        ]);
    }

    public function setDate(Carbon|DateTime|string $date): self
    {
        return $this->state(fn(): array => [
            'date' => $date,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'uid' => uuid(),
            'pupil_id' => Pupil::randomId(),
            'school_id' => School::randomId(),
            'schoolyear_id' => Schoolyear::getRepository()->getCurrent()->id,
            'predefined_follow_up_system_id' => PredefinedFollowUpSystem::randomId(),
            'subtype' => FollowUpSystemSubType::Default,
            'test_moment' => $this->faker->randomElement(TestMoment::cases()),
            'test_audience' => $this->faker->randomElement(TestAudience::cases()),
            'date' => $this->faker->date(),
        ];
    }
}
