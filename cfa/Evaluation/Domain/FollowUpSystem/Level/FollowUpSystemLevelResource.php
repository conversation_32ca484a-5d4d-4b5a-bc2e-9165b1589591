<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\Level;

use Cfa\Evaluation\Application\Services\FollowUpSystem\Level\FollowUpSystemLevelService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * Class FollowUpSystemLevelResource
 *
 * @mixin FollowUpSystemLevel
 * @extends JsonResource<FollowUpSystemLevel>
 */
class FollowUpSystemLevelResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'is_commentable' => $this->is_commentable,
            'is_evaluable' => $this->is_evaluable,
            'id' => $this->uid,
            'label' => app(FollowUpSystemLevelService::class)->getTranslationForLevel(
                $this->followUpSystem->type,
                $this->order,
            ),
        ];
    }
}
