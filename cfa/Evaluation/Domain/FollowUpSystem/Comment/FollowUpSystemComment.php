<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\Comment;

use App\Casts\EncodedString;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\TouchedByUser;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

/**
 * Cfa\Evaluation\Domain\FollowUpSystem\Comment\FollowUpSystemComment
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $pupil_id
 * @property int $report_period_id
 * @property int $follow_up_system_id
 * @property string|null $comment
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read User|null $creator
 * @property-read FollowUpSystem $followUpSystem
 * @property-read array $validation_rules
 * @property-read Pupil $pupil
 * @property-read ReportPeriod $reportPeriod
 * @property-read User|null $updater
 * @method static FollowUpSystemCommentFactory factory($count = null, $state = [])
 * @method static FollowUpSystemCommentRepositoryInterface getRepository()
 * @method static Builder|FollowUpSystemComment newModelQuery()
 * @method static Builder|FollowUpSystemComment newQuery()
 * @method static Builder|FollowUpSystemComment query()
 * @method static string|null randomComment()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static int randomFollowUpSystemId()
 * @method static int randomId()
 * @method static int randomPupilId()
 * @method static int randomReportPeriodId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|FollowUpSystemComment whereComment($value)
 * @method static Builder|FollowUpSystemComment whereCreatedAt($value)
 * @method static Builder|FollowUpSystemComment whereCreatorId($value)
 * @method static Builder|FollowUpSystemComment whereFollowUpSystemId($value)
 * @method static Builder|FollowUpSystemComment whereId($value)
 * @method static Builder|FollowUpSystemComment wherePupilId($value)
 * @method static Builder|FollowUpSystemComment whereReportPeriodId($value)
 * @method static Builder|FollowUpSystemComment whereUid($value)
 * @method static Builder|FollowUpSystemComment whereUpdatedAt($value)
 * @method static Builder|FollowUpSystemComment whereUpdaterId($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class FollowUpSystemComment extends Model
{
    use Uid;
    use TouchedByUser;

    /** @var array */
    protected $fillable = ['comment'];

    /** @var array */
    protected $casts = [
        'comment' => EncodedString::class,
        'follow_up_system_id' => 'integer',
        'pupil_id' => 'integer',
        'report_period_id' => 'integer',
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'comment' => [
                    'nullable',
                    'string',
                    'max:65535',
                ],
            ],
        );
    }

    public function pupil(): BelongsTo
    {
        return $this->belongsTo(Pupil::class);
    }

    public function reportPeriod(): BelongsTo
    {
        return $this->belongsTo(ReportPeriod::class);
    }

    public function followUpSystem(): BelongsTo
    {
        return $this->belongsTo(FollowUpSystem::class);
    }
}
