<?php

namespace Cfa\Evaluation\Domain\FollowUpSystem\EvaluationPeriod;

use App\Casts\EncodedString;
use App\Infrastructure\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Override;

/**
 * Cfa\Evaluation\Domain\FollowUpSystem\EvaluationPeriod\EvaluationPeriod
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string $name
 * @property string $start_date Suggested start date for the period.
 * @property string $end_date Suggested end date for the period.
 * @property int $school_id
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read Collection<int, FollowUpSystemGoalComment> $followUpSystemGoalComments
 * @property-read int|null $follow_up_system_goal_comments_count
 * @property-read Collection<int, FollowUpSystemGoalQuotation> $followUpSystemGoalQuotations
 * @property-read int|null $follow_up_system_goal_quotations_count
 * @property-read Collection<int, FollowUpSystem> $followUpSystems
 * @property-read int|null $follow_up_systems_count
 * @property-read Carbon $full_end_date
 * @property-read Carbon $full_start_date
 * @property-read bool $has_relations
 * @property-read array $validation_rules
 * @property-read School $school
 * @method static Builder|EvaluationPeriod newModelQuery()
 * @method static Builder|EvaluationPeriod newQuery()
 * @method static Builder|EvaluationPeriod onlyTrashed()
 * @method static Builder|EvaluationPeriod query()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static string randomEndDate()
 * @method static int randomId()
 * @method static string randomName()
 * @method static int randomSchoolId()
 * @method static string randomStartDate()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|EvaluationPeriod whereCreatedAt($value)
 * @method static Builder|EvaluationPeriod whereDeletedAt($value)
 * @method static Builder|EvaluationPeriod whereEndDate($value)
 * @method static Builder|EvaluationPeriod whereId($value)
 * @method static Builder|EvaluationPeriod whereName($value)
 * @method static Builder|EvaluationPeriod whereSchoolId($value)
 * @method static Builder|EvaluationPeriod whereStartDate($value)
 * @method static Builder|EvaluationPeriod whereUid($value)
 * @method static Builder|EvaluationPeriod whereUpdatedAt($value)
 * @method static Builder|EvaluationPeriod withTrashed()
 * @method static Builder|EvaluationPeriod withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class EvaluationPeriod extends Model
{
    use SoftDeletes;
    use Uid;

    /**
     * The minimum number of evaluation periods.
     */
    public const MINIMUM = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'structure_type',
        'name',
        'description',
        'start_date',
        'end_date',
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $casts = [
        'name' => EncodedString::class,
    ];

    /**
     * The format used to store start and end date.
     *
     * @var string
     */
    protected $periodDateFormat = 'd-m';

    /**
     * Value used for invalid dates so model validation won't pass.
     *
     * @var string
     */
    protected $invalidValue = 'invalid';

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'start_date' => [
                    'required',
                    'regex:/(0[1-9]|[12]\d|3[01])\-(0[1-9]|1[0-2])/',
                ],
                'end_date' => [
                    'required',
                    'regex:/(0[1-9]|[12]\d|3[01])\-(0[1-9]|1[0-2])/',
                ],
            ],
        );
    }

    /**
     * Checks if the model has any relations.
     */
    public function getHasRelationsAttribute(): bool
    {
        return $this->followUpSystemGoalQuotations->isNotEmpty()
            || $this->followUpSystems->isNotEmpty()
            || $this->followUpSystemGoalComments->isNotEmpty();
    }

    /**
     * Get the start date attribute as a date in the current schoolyear.
     */
    public function getStartDateAttribute(): string
    {
        return $this->getPeriodDateAttributeValue('start_date');
    }

    /**
     * Get the end date attribute as a date in the current schoolyear.
     */
    public function getEndDateAttribute(): string
    {
        return $this->getPeriodDateAttributeValue('end_date');
    }

    /**
     * Set the start date attribute by using a Carbon instance or a string.
     */
    public function setStartDateAttribute(Carbon|string|null $value): void
    {
        $this->setPeriodDateAttributeValue('start_date', $value);
    }

    /**
     * Set the end date attribute by using a Carbon instance or a string.
     */
    public function setEndDateAttribute(Carbon|string|null $value): void
    {
        $this->setPeriodDateAttributeValue('end_date', $value);
    }

    /**
     * The start date converted to Carbon date in the current schoolyear.
     *
     * @param Schoolyear|null $schoolyear The schoolyear to apply the conversion to.
     */
    public function getFullStartDateAttribute(?Schoolyear $schoolyear = null): Carbon
    {
        return $this->convertDateStringToDateUsingSchoolYear($this->attributes['start_date'], $schoolyear);
    }

    /**
     * The end date converted to Carbon date in the current schoolyear.
     *
     * @param Schoolyear|null $schoolyear The schoolyear to apply the conversion to.
     */
    public function getFullEndDateAttribute(?Schoolyear $schoolyear = null): Carbon
    {
        return $this->convertDateStringToDateUsingSchoolYear($this->attributes['end_date'], $schoolyear);
    }

    /**
     * Set the period date attribute by using a Carbon instance or a string.
     */
    public function setPeriodDateAttributeValue(string $key, Carbon|string|null $value): void
    {
        $carbonValue = $value instanceof Carbon || empty($value) ?
            $value : Carbon::createFromTimeString($this->fromDateTime($value));
        $this->attributes[$key] = optional($carbonValue)->format($this->periodDateFormat);
    }

    /**
     * Get the period date attribute as a date in the current schoolyear.
     *
     * @param string $key Period date attribute key.
     */
    public function getPeriodDateAttributeValue(string $key): string
    {
        return $this->convertDateStringToDateUsingSchoolYear($this->attributes[$key])->toDateString();
    }

    /**
     * Will convert a date string to a Carbon date and add the correct year to it using the schoolyear it start date.
     *
     * @param string $dateString The date string to convert.
     * @param Schoolyear|null $schoolyear The schoolyear we want the date converted to.
     */
    public function convertDateStringToDateUsingSchoolYear(string $dateString, ?Schoolyear $schoolyear = null): Carbon
    {
        if (is_null($schoolyear)) {
            $schoolyear = Schoolyear::getRepository()->getCurrent();
        }
        $date = Carbon::createFromFormat($this->periodDateFormat, $dateString)
            ->year($schoolyear->start->year + 1)
            ->startOfDay();
        if (
            $date->month > $schoolyear->start->month ||
            ($date->month === $schoolyear->start->month && $date->day >= $schoolyear->start->day)
        ) {
            $date->year($schoolyear->start->year);
        }

        // If 29/02 is chosen, it won't always work correctly because createFromFormat uses the current year.
        // e.g 29/02 will be converted to 01/03/2019 and then the year will be set to 2020
        // (correctly, because that is the schoolyear).
        if ($dateString === '29-02') {
            $date->day(29)->month(2);
        }

        return $date;
    }

    /**
     * Get the school this evaluation period belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the follow up systems belonging to this evaluation period.
     */
    public function followUpSystems(): BelongsToMany
    {
        return $this->belongsToMany(FollowUpSystem::class);
    }

    public function followUpSystemGoalComments(): HasMany
    {
        return $this->hasMany(FollowUpSystemGoalComment::class);
    }

    public function followUpSystemGoalQuotations(): HasMany
    {
        return $this->hasMany(FollowUpSystemGoalQuotation::class);
    }
}
