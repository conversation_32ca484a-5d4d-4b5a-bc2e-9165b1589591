<?php

namespace Cfa\Evaluation\Domain\QuotationSystem\Quotation;

use App\Casts\EncodedString;
use App\Casts\VuetifyColor;
use App\Exceptions\System\RelationCountNotLoadedException;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\Icon\Icon;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Override;

/**
 * Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string $label Text label (e.g. good, bad).
 * @property int $order Determines the order in which the quotations are shown.
 * @property Carbon|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string $color Color label (e.g. green for good).
 * @property int $quotation_system_id
 * @property int|null $icon_id
 * @property int|null $icon_count
 * @property string|null $icon_color
 * @property int|null $empty_icon_id
 * @property int|null $empty_icon_count
 * @property string|null $empty_icon_color
 * @property string|null $description
 * @property-read Icon|null $emptyIcon
 * @property-read Collection<int, EvaluationTestScore> $evaluationTestScores
 * @property-read int|null $evaluation_test_scores_count
 * @property-read Collection<int, FollowUpSystemGoalQuotation> $followUpSystemGoalQuotations
 * @property-read int|null $follow_up_system_goal_quotations_count
 * @property-read bool $has_relations
 * @property-read array $validation_rules
 * @property-read Icon|null $icon
 * @property-read QuotationSystem $quotationSystem
 * @method static QuotationFactory factory($count = null, $state = [])
 * @method static Builder|Quotation newModelQuery()
 * @method static Builder|Quotation newQuery()
 * @method static Builder|Quotation onlyTrashed()
 * @method static Builder|Quotation query()
 * @method static string randomColor()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomDescription()
 * @method static string|null randomEmptyIconColor()
 * @method static int|null randomEmptyIconCount()
 * @method static int|null randomEmptyIconId()
 * @method static string|null randomIconColor()
 * @method static int|null randomIconCount()
 * @method static int|null randomIconId()
 * @method static int randomId()
 * @method static string randomLabel()
 * @method static int randomOrder()
 * @method static int randomQuotationSystemId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|Quotation whereColor($value)
 * @method static Builder|Quotation whereCreatedAt($value)
 * @method static Builder|Quotation whereDeletedAt($value)
 * @method static Builder|Quotation whereDescription($value)
 * @method static Builder|Quotation whereEmptyIconColor($value)
 * @method static Builder|Quotation whereEmptyIconCount($value)
 * @method static Builder|Quotation whereEmptyIconId($value)
 * @method static Builder|Quotation whereIconColor($value)
 * @method static Builder|Quotation whereIconCount($value)
 * @method static Builder|Quotation whereIconId($value)
 * @method static Builder|Quotation whereId($value)
 * @method static Builder|Quotation whereLabel($value)
 * @method static Builder|Quotation whereOrder($value)
 * @method static Builder|Quotation whereQuotationSystemId($value)
 * @method static Builder|Quotation whereUid($value)
 * @method static Builder|Quotation whereUpdatedAt($value)
 * @method static Builder|Quotation withTrashed()
 * @method static Builder|Quotation withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class Quotation extends Model
{
    use SoftDeletes;
    use Uid;

    /**
     * The minimum number of quotations.
     */
    public const MINIMUM = 2;

    /**
     * The maximum number of quotations.
     */
    public const MAXIMUM = 5;

    /** @var array */
    protected $fillable = [
        'label',
        'description',
        'color',
        'icon_count',
        'icon_color',
        'empty_icon_count',
        'empty_icon_color',
    ];

    /** @var array */
    protected $visible = [
        'icon',
        'emptyIcon',
    ];

    /** @var array */
    protected $with = [
        'icon',
        'emptyIcon',
    ];

    /** @var array */
    protected $casts = [
        'color' => VuetifyColor::class,
        'description' => EncodedString::class,
        'empty_icon_color' => VuetifyColor::class,
        'icon_color' => VuetifyColor::class,
        'label' => EncodedString::class,
        'quotation_system_id' => 'integer',
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'label' => [
                    'required',
                    'string',
                    'max:15',
                ],
                'description' => [
                    'nullable',
                    'string',
                    'max:255',
                ],
                'color' => [
                    'required',
                ],
                'order' => [
                    'required',
                    'numeric',
                ],
                'icon_id' => [
                    'nullable',
                    'integer',
                ],
                'icon_count' => [
                    'nullable',
                    'integer',
                    'between:0,5',
                ],
                'icon_color' => [
                    'nullable',
                    'string',
                ],
                'empty_icon_id' => [
                    'nullable',
                    'integer',
                ],
                'empty_icon_count' => [
                    'nullable',
                    'integer',
                    'between:0,5',
                ],
                'empty_icon_color' => [
                    'nullable',
                    'string',
                ],
                'quotation_system_id' => [
                    'nullable',
                    'integer',
                ],
            ],
        );
    }

    public function quotationSystem(): BelongsTo
    {
        return $this->belongsTo(QuotationSystem::class);
    }

    public function icon(): HasOne
    {
        return $this->hasOne(Icon::class, 'id', 'icon_id');
    }

    public function emptyIcon(): HasOne
    {
        return $this->hasOne(Icon::class, 'id', 'empty_icon_id');
    }

    public function followUpSystemGoalQuotations(): HasMany
    {
        return $this->hasMany(FollowUpSystemGoalQuotation::class);
    }

    public function evaluationTestScores(): HasMany
    {
        return $this->hasMany(EvaluationTestScore::class);
    }

    /**
     * Returns if there are linked scores or followUpSystemGoalQuotations for this Quotation.
     * It provides an indication of how safe it is to change this quotation.
     *
     * @throws RelationCountNotLoadedException
     */
    public function getHasRelationsAttribute(): bool
    {
        if ($this->relationLoaded('evaluationTestScores') && $this->evaluationTestScores->isNotEmpty()) {
            return true;
        }

        if (
            $this->relationLoaded('followUpSystemGoalQuotations') &&
            $this->followUpSystemGoalQuotations->isNotEmpty()
        ) {
            return true;
        }

        if ($this->evaluation_test_scores_count === null) {
            throw new RelationCountNotLoadedException('evaluationTestScores');
        }

        if ($this->follow_up_system_goal_quotations_count === null) {
            throw new RelationCountNotLoadedException('followUpSystemGoalQuotations');
        }

        return $this->evaluation_test_scores_count > 0 || $this->follow_up_system_goal_quotations_count > 0;
    }
}
