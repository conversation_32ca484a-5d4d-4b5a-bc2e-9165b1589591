<?php

namespace Cfa\Evaluation\Domain\Report\EvaluationPrintJob;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

/**
 * Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJob
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property EvaluationPrintJobType $type
 * @property ExportJobStatus $status
 * @property string|null $file
 * @property int|null $pupil_id
 * @property int $school_id
 * @property int $group_id
 * @property int|null $subject_id
 * @property int $report_period_id
 * @property int $creator_id
 * @property Carbon $created_at
 * @property Carbon|null $started_at
 * @property Carbon|null $completed_at
 * @property-read User $creator
 * @property-read array $validation_rules
 * @property-read Group $group
 * @property-read Pupil|null $pupil
 * @property-read ReportPeriod $reportPeriod
 * @property-read School $school
 * @property-read Subject|null $subject
 * @method static Builder|EvaluationPrintJob newModelQuery()
 * @method static Builder|EvaluationPrintJob newQuery()
 * @method static Builder|EvaluationPrintJob query()
 * @method static Carbon|null randomCompletedAt()
 * @method static Carbon randomCreatedAt()
 * @method static int randomCreatorId()
 * @method static string|null randomFile()
 * @method static int randomGroupId()
 * @method static int randomId()
 * @method static int|null randomPupilId()
 * @method static int randomReportPeriodId()
 * @method static int randomSchoolId()
 * @method static Carbon|null randomStartedAt()
 * @method static ExportJobStatus randomStatus()
 * @method static int|null randomSubjectId()
 * @method static EvaluationPrintJobType randomType()
 * @method static string randomUid()
 * @method static Builder|EvaluationPrintJob whereCompletedAt($value)
 * @method static Builder|EvaluationPrintJob whereCreatedAt($value)
 * @method static Builder|EvaluationPrintJob whereCreatorId($value)
 * @method static Builder|EvaluationPrintJob whereFile($value)
 * @method static Builder|EvaluationPrintJob whereGroupId($value)
 * @method static Builder|EvaluationPrintJob whereId($value)
 * @method static Builder|EvaluationPrintJob wherePupilId($value)
 * @method static Builder|EvaluationPrintJob whereReportPeriodId($value)
 * @method static Builder|EvaluationPrintJob whereSchoolId($value)
 * @method static Builder|EvaluationPrintJob whereStartedAt($value)
 * @method static Builder|EvaluationPrintJob whereStatus($value)
 * @method static Builder|EvaluationPrintJob whereSubjectId($value)
 * @method static Builder|EvaluationPrintJob whereType($value)
 * @method static Builder|EvaluationPrintJob whereUid($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class EvaluationPrintJob extends Model
{
    use Uid;

    /** @var bool */
    public $timestamps = false;

    /** @var array */
    protected $fillable = [
        'file',
    ];

    protected array $enums = [
        'status' => ExportJobStatus::class,
    ];

    /** @var array */
    protected $casts = [
        'creator_id' => 'integer',
        'school_id' => 'integer',
        'group_id' => 'integer',
        'pupil_id' => 'integer',
        'report_period_id' => 'integer',
        'subject_id' => 'integer',
        'created_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'type' => EvaluationPrintJobType::class,
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'file' => [
                    'string',
                    'max:255',
                    'nullable',
                ],
                'status' => [
                    'string',
                    'required',
                    'max:255',
                ],
                'pupil_id' => [
                    'integer',
                    'nullable',
                ],
                'school_id' => [
                    'integer',
                    'required',
                ],
                'group_id' => [
                    'integer',
                    'required',
                ],
                'report_period_id' => [
                    'integer',
                    'required',
                ],
                'creator_id' => [
                    'integer',
                    'nullable',
                ],
                'created_at' => [
                    'date',
                    'required',
                ],
                'started_at' => [
                    'date',
                    'nullable',
                ],
                'completed_at' => [
                    'date',
                    'nullable',
                ],
            ],
        );
    }

    public function pupil(): BelongsTo
    {
        return $this->belongsTo(Pupil::class, 'pupil_id');
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function reportPeriod(): BelongsTo
    {
        return $this->belongsTo(ReportPeriod::class);
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function updateStatus(ExportJobStatus $status): self
    {
        $this->status = $status;
        $this->save();

        return $this;
    }
}
