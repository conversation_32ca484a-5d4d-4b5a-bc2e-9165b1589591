<?php

namespace Cfa\Evaluation\Domain\Report\FollowUpSystem;

use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Application\Repositories\Report\FollowUpSystem\FollowUpSystemReportRepository;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Override;

class ReportFollowUpSystemInputMoment implements Arrayable
{
    private Pupil $pupil;
    private FollowUpSystem $followUpSystem;
    private FollowUpSystemInputMoment $followUpSystemInputMoment;
    private FollowUpSystemReportRepository $repository;

    public function __construct(
        Pupil $pupil,
        FollowUpSystem $followUpSystem,
        FollowUpSystemInputMoment $inputMoment,
        FollowUpSystemReportRepository $repository,
    ) {
        $this->pupil = $pupil;
        $this->followUpSystem = $followUpSystem;
        $this->followUpSystemInputMoment = $inputMoment;
        $this->repository = $repository;
    }

    #[Override]
    public function toArray(): array
    {
        $goals = $this->repository->getGoals($this->followUpSystem, $this->pupil, $this->followUpSystemInputMoment);
        $quotations = $this->repository->getQuotations($this->pupil, $this->followUpSystemInputMoment, $goals);
        $comments = $this->repository->getComments($this->pupil, $this->followUpSystemInputMoment, $goals);

        return [
            'quotations' => $quotations,
            'comments' => $comments,
            'followUpSystemInputMoment' => $this->followUpSystemInputMoment,
            'has_scores' => $this->hasScores($quotations, $comments),
        ];
    }

    private function hasScores(Collection $quotations, Collection $comments): bool
    {
        $hasNonEmptyQuotation = $quotations->contains(
            fn(FollowUpSystemGoalQuotation $quotation): bool => $quotation->quotation !== null,
        );
        if ($hasNonEmptyQuotation) {
            return true;
        }

        return $comments->contains(fn(FollowUpSystemGoalComment $comment): bool => !empty($comment->comment));
    }
}
