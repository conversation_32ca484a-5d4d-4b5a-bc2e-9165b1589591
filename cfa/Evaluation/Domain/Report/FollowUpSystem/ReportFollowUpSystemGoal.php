<?php

namespace Cfa\Evaluation\Domain\Report\FollowUpSystem;

use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Override;

class ReportFollowUpSystemGoal implements Arrayable
{
    private FollowUpSystemGoal $goal;

    private Collection $usedGoalUids;

    private Collection $children;

    private bool $shouldFilterFollowUpSystem;

    private bool $showGoal;

    public function __construct(
        FollowUpSystemGoal $followUpSystemGoal,
        bool $shouldFilterFollowUpSystem,
        ?Collection $usedGoalUids,
    ) {
        $this->goal = $followUpSystemGoal;
        $this->shouldFilterFollowUpSystem = $shouldFilterFollowUpSystem;
        $this->usedGoalUids = $usedGoalUids ?? collect();

        $this->showGoal = false;

        $this->initializeGoalAndChildren();
    }

    private function initializeGoalAndChildren(): void
    {
        $descendants = $this->goal->{'descendants'} ?? collect();

        $this->showGoal = $this->checkGoalVisibility();

        $this->children = $descendants
            ->map(function (FollowUpSystemGoal $goal) {
                return new self($goal, $this->shouldFilterFollowUpSystem, $this->usedGoalUids);
            })
            ->filter(fn(self $goal): bool => $goal->shouldShowGoal())
            ->values();

        $this->showGoal = $this->showGoal || $this->children->isNotEmpty();
    }

    public function shouldShowGoal(): bool
    {
        return $this->showGoal;
    }

    private function checkGoalVisibility(): bool
    {
        return (!$this->shouldFilterFollowUpSystem && !$this->goal->archived()) ||
            $this->usedGoalUids->has($this->goal->uid);
    }

    #[Override]
    public function toArray(): array
    {
        return [
            'uid' => $this->goal->uid,
            'description' => $this->goal->description,
            'children' => $this->children->toArray(),
        ];
    }
}
