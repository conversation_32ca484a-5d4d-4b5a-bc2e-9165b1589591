<?php

namespace Cfa\Evaluation\Domain\Report;

use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\Subject\SubjectLearningArea;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Support\Collection;

interface SubjectReportRepositoryInterface
{
    /** @return Collection<int, Subject|SubjectLearningArea> */
    public function getAll(ReportSettings $reportSettings): Collection;
}
