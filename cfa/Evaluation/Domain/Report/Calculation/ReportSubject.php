<?php

namespace Cfa\Evaluation\Domain\Report\Calculation;

use Cfa\Care\Domain\CareInfo\Redicodi\RedicodiReportResource;
use Cfa\Common\Domain\Icon\Icon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\Subject\SubjectDomain;
use Cfa\Common\Domain\Subject\SubjectLearningArea;
use Cfa\Evaluation\Application\Repositories\Report\Calculation\ReportCalculationRepository;
use Cfa\Evaluation\Application\Services\Report\ReportLeastCommonMultipleService;
use Cfa\Evaluation\Application\Services\Report\ShowRedicodisOnReport;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Cfa\Evaluation\Domain\Subject\SubjectComment;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Override;

use function optional;

/**
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.ExcessiveClassComplexity)
 */
class ReportSubject implements Arrayable
{
    private ?float $recalculatedWeightNumerator = null;

    private ?float $recalculatedWeightDenominator = null;

    private ?Collection $comments;

    private bool $canComment = false;

    private ?float $total = null;

    private float $totalMax;

    /** @var Collection|self[] */
    private Collection $domains;

    /** @var Collection|ReportEvaluationTest[] */
    private Collection $tests;

    private bool $hasScores = false;

    private bool $hasComments = false;

    private bool $hasQuotations = false;

    private bool $hasDescriptions = false;

    private bool $shouldIncludeComments = true;

    private bool $showIndividualTests;

    private ?Icon $icon = null;

    public function __construct(
        private readonly Subject|SubjectLearningArea|SubjectDomain $subject,
        private readonly Group $group,
        private readonly ReportSettings $reportSettings,
        private readonly ReportCalculationRepository $repository,
    ) {
        $this->totalMax = (float) $reportSettings->recalculate_subjects_to;

        if ($subject instanceof SubjectDomain) {
            $this->totalMax = (float) $reportSettings->recalculate_domains_to;
        }

        $this->domains = collect();
        $this->tests = collect();
        $this->comments = collect();

        $this->showIndividualTests = $reportSettings->show_individual_tests;
    }

    public function getRecalculatedWeightNumerator(): ?float
    {
        return $this->recalculatedWeightNumerator;
    }

    public function getRecalculatedWeightDenominator(): ?float
    {
        return $this->recalculatedWeightDenominator;
    }

    public function getRecalculatedWeight(): ?float
    {
        if ($this->recalculatedWeightDenominator === null || $this->recalculatedWeightDenominator === 0) {
            return null;
        }

        if ($this->recalculatedWeightNumerator === null) {
            return null;
        }

        if ($this->total === null) {
            return null;
        }

        return $this->recalculatedWeightNumerator / $this->recalculatedWeightDenominator * 100;
    }

    public function getTotal(): ?float
    {
        return $this->total;
    }

    public function getTotalMax(): float
    {
        return $this->totalMax;
    }

    public function hasScores(): bool
    {
        return $this->hasScores;
    }

    public function hasComments(): bool
    {
        return $this->hasComments;
    }

    public function hasQuotations(): bool
    {
        return $this->hasQuotations;
    }

    public function hasDescriptions(): bool
    {
        return $this->hasDescriptions;
    }

    public function setRecalculatedWeightDenominator(?float $recalculatedWeightDenominator): self
    {
        $this->recalculatedWeightDenominator = $recalculatedWeightDenominator;

        return $this;
    }

    public function shouldNotIncludeComments(): self
    {
        $this->shouldIncludeComments = false;

        return $this;
    }

    /** @SuppressWarnings(PHPMD.CyclomaticComplexity) */
    public function getReportSubject(): self
    {
        $this->recalculatedWeightNumerator = $this->calculateWeightNumerator();

        $subjectQuotationSettings = $this->repository->getQuotationSettingsForSubject($this->subject);
        $hasNoQuotationSettings = $this->repository->hasQuotationSettingsForGroup() === false;
        $mayEvaluateOnSubject = $subjectQuotationSettings->mayEvaluate();

        if ($hasNoQuotationSettings || (!empty($subjectQuotationSettings->id) && $mayEvaluateOnSubject)) {
            $this->addTestsForReportSubject();
        }

        if ($hasNoQuotationSettings || empty($subjectQuotationSettings->id) || $mayEvaluateOnSubject === false) {
            $this->addTestsForDomainsOfReportSubject();
        }

        $this->total = $this->calculateTotal();
        $this->comments = $this->fetchComments();
        $this->canComment = $this->determinePermission();
        $this->hasScores = $this->determineHasScores();
        $this->hasQuotations = $this->determineHasQuotations();
        $this->hasComments = $this->determineHasComments();
        $this->hasDescriptions = $this->determineHasDescriptions();
        $this->icon = $this->subject->selectedIcon;

        return $this;
    }

    private function addTestsForReportSubject(): void
    {
        $evaluationTests = $this->repository->getEvaluationTestsBySubject($this->subject);

        if ($evaluationTests === null) {
            return;
        }

        foreach ($evaluationTests as $evaluationTest) {
            $reportTest = new ReportEvaluationTest($evaluationTest, $this->reportSettings, $this->repository);

            if ($this->shouldIncludeComments === false) {
                $reportTest->shouldNotIncludeComments();
            }

            $this->tests->push($reportTest->getReportEvaluationTest());
        }
    }

    private function addTestsForDomainsOfReportSubject(): void
    {
        if ($this->determineHasScores()) {
            return;
        }

        if (!$this->subject instanceof SubjectLearningArea || $this->subject->domains->isEmpty()) {
            return;
        }

        $this->domains = $this->getReportSubjectDomains($this->subject->domains);
    }

    /** @param Collection|SubjectDomain[] $subjects */
    public function getReportSubjectDomains(Collection $subjects): Collection
    {
        $domains = collect();

        // Get all the quotation settings of the given domains.
        $quotationSettings = $subjects
            ->map(function (Subject $subject): ?SubjectQuotationSettings {
                return $this->repository->getQuotationSettingsForSubject($subject);
            })
            ->filter()
            ->values();

        // Find the least common multiple of the weight denominators of all the given subjectQuotationSettings.
        $denominator = app(ReportLeastCommonMultipleService::class)->find($quotationSettings);

        foreach ($subjects as $subject) {
            $reportSubject = new self($subject, $this->group, $this->reportSettings, $this->repository)
                // Set the least common multiple as the recalculated weight denominator.
                ->setRecalculatedWeightDenominator($denominator);

            if ($this->shouldIncludeComments === false) {
                $reportSubject->shouldNotIncludeComments();
            }

            $reportSubject->getReportSubject();

            if ($subject->archived() === true && $reportSubject->hasTests() === false) {
                continue;
            }

            $domains->push($reportSubject);
        }

        // Get the sum of all the weight numerators of the ReportSubjects.
        $totalWeight = $domains->sum(fn(ReportSubject $subject) => $subject->getRecalculatedWeightNumerator());

        // If the sum of the numerators is a different number than the previously calculated denominator,
        // it means that the sum of the weights of the subjectQuotationSettings is not equal to 100%.
        // Changing the denominator in the sum of the numerators, fixes this problem.
        if ($totalWeight > 0 && $totalWeight !== $denominator) {
            $domains->each->setRecalculatedWeightDenominator($totalWeight);
        }

        return $domains;
    }

    public function getSubject(): SubjectLearningArea|SubjectDomain
    {
        return $this->subject;
    }

    public function getDomains(): Collection
    {
        return $this->domains;
    }

    public function getTests(): Collection
    {
        return $this->tests;
    }

    public function hasTests(): bool
    {
        return $this->tests->isNotEmpty();
    }

    #[Override]
    public function toArray(): array
    {
        $showRedicodis = $this->subject->relationLoaded('redicodis')
            && app(ShowRedicodisOnReport::class)($this->reportSettings);

        return [
            'uid' => $this->subject->uid,
            'name' => $this->subject->name,
            'recalculated_weight' => $this->getRecalculatedWeight(),
            'comments' => optional($this->comments)->map(fn(SubjectComment $comment): array => [
                'comment' => $comment->only(['uid', 'comment']),
                'can_save' => $comment->creator_id === optional($this->repository->getTeacher())->id,
                'creator' => optional($comment->creator)->firstname_with_salutation,
            ]),
            'can_comment' => $this->canComment,
            'total' => $this->total,
            'total_max' => $this->totalMax,
            'domains' => $this->domains->map->toArray(),
            'tests' => $this->tests->map->toArray(),
            'has_scores' => $this->hasScores(),
            'has_comments' => $this->hasComments(),
            'has_quotations' => $this->hasQuotations(),
            'has_descriptions' => $this->hasDescriptions(),
            'icon' => $this->icon,
            'redicodis' => $showRedicodis
                ? RedicodiReportResource::collection($this->subject->redicodis)->resolve()
                : [],
        ];
    }

    private function calculateWeightNumerator(): ?float
    {
        if ($this->recalculatedWeightDenominator === null) {
            return null;
        }

        $quotationSettings = $this->repository->getQuotationSettingsForSubject($this->subject);

        if ($quotationSettings === null || $quotationSettings->hasWeight() === false) {
            return null;
        }

        $weight = null;
        $currentDenominator = null;

        if ($percentage = $quotationSettings->weight_percentage) {
            $weight = $percentage;
            $currentDenominator = 100;
        }

        if ($weight === null) {
            $weight = $quotationSettings->weight_fraction_numerator;
            $currentDenominator = $quotationSettings->weight_fraction_denominator;
        }

        return $weight / $currentDenominator * $this->recalculatedWeightDenominator;
    }

    private function calculateTotal(): ?float
    {
        if ($this->domains->isNotEmpty()) {
            return $this->calculateTotalOfDomains();
        }

        return $this->calculateTotalOfEvaluationTests();
    }

    private function calculateTotalOfDomains(): ?float
    {
        $domains = $this->domains->filter(function (self $domain): bool {
            return $domain->getRecalculatedWeight() !== null && $domain->getTotal() !== null;
        });

        if ($domains->isEmpty()) {
            return null;
        }

        $totalScore = $domains->sum(function (self $domain): float {
            return $domain->getTotal() / $domain->getTotalMax() * $domain->getRecalculatedWeightNumerator();
        });

        $totalMax = $domains->sum(fn(self $domain): ?float => $domain->getRecalculatedWeightNumerator());
        $domains->each->setRecalculatedWeightDenominator($totalMax);

        return $totalScore / $totalMax * $this->totalMax;
    }

    private function calculateTotalOfEvaluationTests(): ?float
    {
        $testsWithScores = $this->tests->filter(fn(ReportEvaluationTest $test): bool => $test->hasScore());

        if ($testsWithScores->isEmpty()) {
            return null;
        }

        $totalScore = $testsWithScores->sum(fn(ReportEvaluationTest $test): ?float => $test->getRecalculatedScore());
        $totalMax = $testsWithScores->sum(fn(ReportEvaluationTest $test): float => $test->getRecalculatedMax());

        return $totalScore / $totalMax * $this->totalMax;
    }

    private function fetchComments(): ?Collection
    {
        if ($this->shouldIncludeComments === false) {
            return null;
        }

        return $this->repository->getReportSubjectComments($this->subject);
    }

    private function determinePermission(): bool
    {
        if ($this->shouldIncludeComments === false) {
            return false;
        }

        return $this->repository->hasPermissionOnSubject($this->subject);
    }

    private function determineHasScores(): bool
    {
        $hasOwnTestsWithScores = $this->tests->contains(
            fn(ReportEvaluationTest $evaluationTest): bool => $evaluationTest->hasEvaluationTestScore(),
        );

        return $hasOwnTestsWithScores ||
            optional($this->domains)->contains(fn(ReportSubject $reportSubject): bool => $reportSubject->hasScores());
    }

    private function determineHasComments(): bool
    {
        $hasOwnTestsWithComments = $this->tests->contains(
            fn(ReportEvaluationTest $evaluationTest): bool => $evaluationTest->hasComment(),
        );

        if ($this->showIndividualTests === false && $this->hasQuotations() === false) {
            return false;
        }

        return $hasOwnTestsWithComments ||
            $this->domains->contains(fn(ReportSubject $reportSubject): bool => $reportSubject->hasComments());
    }

    private function determineHasQuotations(): bool
    {
        $hasOwnTestsWithQuotations = $this->tests->contains(
            fn(ReportEvaluationTest $evaluationTest): bool => $evaluationTest->hasQuotation(),
        );

        return $hasOwnTestsWithQuotations ||
            $this->domains->contains(fn(ReportSubject $reportSubject): bool => $reportSubject->hasQuotations());
    }

    private function determineHasDescriptions(): bool
    {
        $hasOwnTestsWithDescriptions = $this->tests->contains(
            fn(ReportEvaluationTest $evaluationTest): bool => $evaluationTest->hasDescription(),
        );

        return $hasOwnTestsWithDescriptions ||
            $this->domains->contains(fn(ReportSubject $reportSubject): bool => $reportSubject->hasDescriptions());
    }
}
