<?php

namespace Cfa\Evaluation\Domain\Settings\Report\Period;

use App\Factories\Factory;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * ReportPeriodFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|ReportPeriod[]|ReportPeriod create($attributes = [], Model|null $parent = null)
 * @method Collection|ReportPeriod[]|ReportPeriod createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class ReportPeriodFactory extends Factory
{
    /** @var string */
    protected $model = ReportPeriod::class;

    public function inSchool(School $school): self
    {
        return $this->state(fn(): array => [
            'school_id' => $school->id,
        ]);
    }

    public function forSchoolyear(Schoolyear $schoolyear): self
    {
        return $this->state(fn(): array => [
            'schoolyear_id' => $schoolyear->id,
            'start' => $schoolyear->start->setTime(0, 0),
            'end' => $schoolyear->end->setTime(0, 0),
        ]);
    }

    public function forReportSettings(ReportSettings $reportSettings): self
    {
        return $this->state(fn(): array => [
            'school_id' => $reportSettings->school_id,
            'report_settings_id' => $reportSettings->id,
        ]);
    }

    public function withYearReportAndReportSettings(ReportSettings $reportSettings): self
    {
        $yearReport = ReportPeriod::factory()
            ->isYearReport()
            ->forReportSettings($reportSettings);

        return $this->state(fn(): array => [
            'parent_id' => $yearReport->create()->id,
        ])->forReportSettings($reportSettings);
    }

    public function isYearReport(): self
    {
        return $this->state(fn(): array => [
            'is_period' => false,
            'parent_id' => null,
        ]);
    }

    public function setYearReport(ReportPeriod $reportPeriod): self
    {
        return $this->state(fn(): array => [
            'parent_id' => $reportPeriod->id,
        ]);
    }

    public function setStartDate(Carbon|string $startDate): self
    {
        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }

        return $this->state(fn(): array => [
            'start' => $startDate->startOfDay(),
        ]);
    }

    public function setEndDate(Carbon|string|null $endDate): self
    {
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        return $this->state(fn(): array => [
            'end' => optional($endDate)->startOfDay(),
        ]);
    }

    #[Override]
    public function definition(): array
    {
        $currentSchoolyear = app(SchoolyearRepositoryInterface::class)->getCurrent();

        return [
            'uid' => uuid(),
            'school_id' => School::randomId(),
            'parent_id' => ReportPeriod::randomId(),
            'schoolyear_id' => $currentSchoolyear->id,
            'name' => $this->faker->name(),
            'short_name' => $this->faker->text(ReportPeriod::MAX_SHORT_NAME_LENGTH),
            'is_period' => true,
            'start' => $currentSchoolyear->start->setTime(0, 0),
            'end' => $currentSchoolyear->end->setTime(0, 0),
        ];
    }
}
