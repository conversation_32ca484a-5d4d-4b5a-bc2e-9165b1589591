<?php

namespace Cfa\Evaluation\Domain\Settings\Report\Period;

use App\Casts\EncodedString;
use App\Exceptions\System\RelationCountNotLoadedException;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Override;

/**
 * Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $school_id
 * @property int|null $parent_id
 * @property Carbon $start
 * @property Carbon $end
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property string $name
 * @property bool $is_period
 * @property int $schoolyear_id
 * @property string|null $short_name
 * @property int $report_settings_id
 * @property-read Collection<int, EvaluationTest> $evaluationTests
 * @property-read int|null $evaluation_tests_count
 * @property-read Collection<int, FollowUpSystemInputMoment> $followUpSystemInputMoments
 * @property-read int|null $follow_up_system_input_moments_count
 * @property-read bool $has_relations
 * @property-read array $validation_rules
 * @property-read ReportPeriod|null $parent
 * @property-read ReportSettings $reportSettings
 * @property-read School $school
 * @property-read Schoolyear $schoolyear
 * @method static ReportPeriodFactory factory($count = null, $state = [])
 * @method static ReportPeriodRepositoryInterface getRepository()
 * @method static Builder|ReportPeriod newModelQuery()
 * @method static Builder|ReportPeriod newQuery()
 * @method static Builder|ReportPeriod onlyTrashed()
 * @method static Builder|ReportPeriod query()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static Carbon randomEnd()
 * @method static int randomId()
 * @method static bool randomIsPeriod()
 * @method static string randomName()
 * @method static int|null randomParentId()
 * @method static int randomReportSettingsId()
 * @method static int randomSchoolId()
 * @method static int randomSchoolyearId()
 * @method static string|null randomShortName()
 * @method static Carbon randomStart()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|ReportPeriod whereCreatedAt($value)
 * @method static Builder|ReportPeriod whereDeletedAt($value)
 * @method static Builder|ReportPeriod whereEnd($value)
 * @method static Builder|ReportPeriod whereId($value)
 * @method static Builder|ReportPeriod whereIsPeriod($value)
 * @method static Builder|ReportPeriod whereName($value)
 * @method static Builder|ReportPeriod whereParentId($value)
 * @method static Builder|ReportPeriod whereReportSettingsId($value)
 * @method static Builder|ReportPeriod whereSchoolId($value)
 * @method static Builder|ReportPeriod whereSchoolyearId($value)
 * @method static Builder|ReportPeriod whereShortName($value)
 * @method static Builder|ReportPeriod whereStart($value)
 * @method static Builder|ReportPeriod whereUid($value)
 * @method static Builder|ReportPeriod whereUpdatedAt($value)
 * @method static Builder|ReportPeriod withTrashed()
 * @method static Builder|ReportPeriod withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class ReportPeriod extends Model
{
    use PruneSoftDeletes;
    use Uid;
    use SoftDeletes;

    public const MAX_SHORT_NAME_LENGTH = 10;

    public int $depth;

    public Collection $descendants;

    /** @var array */
    protected $fillable = [
        'name',
        'start',
        'end',
    ];

    /** @var array */
    protected $casts = [
        'end' => 'datetime',
        'is_period' => 'boolean',
        'name' => EncodedString::class,
        'short_name' => EncodedString::class,
        'parent_id' => 'integer',
        'school_id' => 'integer',
        'start' => 'datetime',
        'schoolyear_id' => 'integer',
    ];

    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'school_id' => [
                'required',
                'integer',
                Rule::exists('schools', 'id')->whereNull('deleted_at'),
            ],
            'report_settings_id' => [
                'required',
                'integer',
                Rule::exists('report_settings', 'id')->whereNull('deleted_at'),
            ],
            'schoolyear_id' => [
                'required',
                'integer',
                Rule::exists('schoolyears', 'id')->whereNull('deleted_at'),
            ],
            'start' => [
                'required',
                'date',
                'before:end',
            ],
            'end' => [
                'required',
                'date',
                'after:start',
            ],
            'short_name' => [
                'nullable',
                'required_if:is_period,true',
                'string',
                'max:' . self::MAX_SHORT_NAME_LENGTH,
            ],
        ]);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    public function schoolyear(): BelongsTo
    {
        return $this->belongsTo(Schoolyear::class);
    }

    public function reportSettings(): BelongsTo
    {
        return $this->belongsTo(ReportSettings::class);
    }

    public function evaluationTests(): HasMany
    {
        return $this->hasMany(EvaluationTest::class);
    }

    public function followUpSystemInputMoments(): HasMany
    {
        return $this->hasMany(FollowUpSystemInputMoment::class);
    }

    public function isExpired(): bool
    {
        return Carbon::now()->startOfDay()->isAfter($this->end->startOfDay());
    }

    public function getHasRelationsAttribute(): bool
    {
        if ($this->evaluation_tests_count === null) {
            throw new RelationCountNotLoadedException('evaluationTests');
        }

        if ($this->follow_up_system_input_moments_count === null) {
            throw new RelationCountNotLoadedException('followUpSystemInputMoments');
        }

        if ($this->evaluation_tests_count > 0) {
            return true;
        }

        if ($this->follow_up_system_input_moments_count > 0) {
            return true;
        }

        if (!isset($this->descendants) || $this->descendants === null) {
            return false;
        }

        return $this->descendants->contains(fn($child) => $child->has_relations);
    }
}
