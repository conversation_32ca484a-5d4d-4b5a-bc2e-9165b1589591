<?php

namespace Cfa\Evaluation\Domain\Settings\Report\Period;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * Class ReportPeriodResource
 *
 * @mixin ReportPeriod
 * @extends JsonResource<ReportPeriod>
 */
class ReportPeriodResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'parentId' => optional($this->parent)->uid,
            'name' => $this->name,
            'short_name' => $this->short_name,
            'end' => $this->end->toDateString(),
            'depth' => $this->depth ?? null,
            'isPeriod' => $this->is_period,
            'open' => true,
            'has_relations' => $this->has_relations,
        ];
    }
}
