<?php

namespace Cfa\Evaluation\Application\View\Components\Report;

use Illuminate\View\Component;
use Override;

use function cloudfront_url;

class SectionImageIcon extends Component
{
    public function __construct(
        public readonly string $imagePath,
    ) {}

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        $signedPath = cloudfront_url($this->imagePath);
        [$width, $height] = getimagesize($signedPath);
        $wideImage = $width > $height;

        $longSide = 33;
        $shortSide = 30;

        $recalculatedWidth = $wideImage ? $longSide : $width / $height * $shortSide;
        $recalculatedHeight = $wideImage ? $height / $width * $longSide : $shortSide;
        $bottomPadding = $wideImage ? $longSide - $recalculatedHeight : 3;
        $leftPadding = $wideImage ? 0 : $longSide - $recalculatedWidth;

        return <<<HTML
                <img src="$signedPath"
                    alt="pupil profile picture"
                    width="{$recalculatedWidth}px"
                    height="{$recalculatedHeight}px"
                    style="padding-left: {$leftPadding}px; padding-bottom: {$bottomPadding}px; padding-top:8px;"
                />
            HTML;
    }
}
