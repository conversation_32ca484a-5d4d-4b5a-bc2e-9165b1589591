<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report\Tests;

use Illuminate\View\Component;
use Override;

use function implode;
use function view;

/**
 * @SuppressWarnings(PHPMD.CyclomaticComplexity)
 * @SuppressWarnings(PHPMD.TooManyFields)
 */
class ResultsTable extends Component
{
    public readonly ?array $comments;
    public readonly ?array $domainComments;
    public readonly array $tests;
    public readonly ?array $parentSubject;
    public readonly array $subject;

    public readonly bool $showTableHeader;
    public readonly int $numberOfColumns;

    public readonly string $primaryBorderColor;
    public readonly string $subjectStyle;
    public readonly string $domainStyle;

    public readonly bool $hasScores;
    public readonly bool $hasQuotations;
    public readonly bool $hasComments;
    public readonly bool $hasDescriptions;
    public readonly bool $hasRedicodis;
    public readonly bool $subjectHasRedicodis;
    public readonly int $headerColspan;

    public function __construct(
        public readonly array $reportSettings,
        public readonly array $reportGroupSettings,
        public readonly array $report,
        array $scoreTableSection,
        bool $showTableHeader,
    ) {
        $subject = $scoreTableSection;
        $this->parentSubject = $subject['parent'] ?? null;
        $isDomain = isset($this->parentSubject);

        $this->comments = isset($this->parentSubject) && $this->parentSubject['comments']->isNotEmpty() ?
            $this->parentSubject['comments']->all() : null;

        $this->domainComments = $subject['comments']->isEmpty() ? null : $subject['comments']->all();
        $this->showTableHeader = $showTableHeader && $isDomain;
        $this->primaryBorderColor = $reportSettings['primary_border_color'];

        $this->domainStyle = $this->getDomainStyle();
        $this->subjectStyle = $this->getSubjectStyle();
        $this->hasQuotations = $this->hasQuotations($subject);
        $this->hasScores = $this->hasScores($subject);
        $this->hasComments = $this->hasComments($subject);
        $this->hasDescriptions = $this->hasDescriptions($subject);
        $this->tests = $this->hasComments || $this->hasQuotations || $reportSettings['show_individual_tests'] ?
            $subject['tests']->filter(fn(array $test) => $test['has_score'])->all() : [];

        if ($isDomain && $reportSettings['show_domain_totals'] === false) {
            $subject['total'] = $subject['total_max'] = null;
        }

        if (!$isDomain && $this->shouldHideSubjectTotals($reportSettings, $subject)) {
            $subject['total'] = $subject['total_max'] = null;
        }

        $this->numberOfColumns = $this->getNumberOfColumns();
        $this->headerColspan = $this->getHeaderColspan();

        $this->subject = $subject;

        $this->hasRedicodis = $this->hasRedicodis($subject);
        $this->subjectHasRedicodis = isset($this->parentSubject) ? $this->hasRedicodis($this->parentSubject) : false;
    }

    private function shouldHideSubjectTotals(array $reportSettings, array $subject): bool
    {
        if ($reportSettings['show_subject_totals'] === true) {
            return false;
        }

        // Hide if scoring system is a 'combination-type' (score + comment or score + quotation)
        if ($this->hasScores && ($this->hasQuotations || $this->hasComments)) {
            return true;
        }

        // Hide if tests are shown
        if ($reportSettings['show_individual_tests'] === true) {
            return true;
        }

        // Hide if the subjects has domains
        return $subject['domains']->isNotEmpty();
    }

    public function getCommentsForTest(array $test): array
    {
        if ($test['comment_on_report'] === false) {
            return [[]];
        }

        return [
            [
                ...$test,
                'comment' => ['comment' => $test['comment']],
            ],
        ];
    }

    private function getNumberOfColumns(): int
    {
        $columnCount = 1;
        $columnCount += $this->hasQuotations ? 1 : 0;
        $columnCount += $this->hasDescriptions ? 1 : 0;

        return $columnCount + ($this->hasScores ? 2 : 0);
    }

    private function getHeaderColspan(): int
    {
        return 1 + $this->hasQuotations + $this->hasDescriptions;
    }

    private function hasRedicodis(array $subject): bool
    {
        return $this->reportSettings['show_redicodis'] === true &&
            $this->reportGroupSettings['redicodis_on_report'] === true &&
            count($subject['redicodis']) > 0;
    }

    private function hasComments(array $subject): bool
    {
        if ($this->parentSubject !== null && $this->reportSettings['show_individual_tests'] === true) {
            return $this->parentSubject['has_comments'];
        }

        return $subject['tests']->contains(fn(array $test) => $test['has_comment']);
    }

    private function hasDescriptions(array $subject): bool
    {
        if ($this->parentSubject !== null && $this->reportSettings['show_individual_tests'] === true) {
            return $this->parentSubject['has_descriptions'];
        }

        return $subject['tests']->contains(fn(array $test) => $test['has_description']);
    }

    private function hasQuotations(array $subject): bool
    {
        if ($this->parentSubject !== null && $this->reportSettings['show_individual_tests'] === true) {
            return $this->parentSubject['has_quotations'];
        }

        return $subject['tests']->contains(fn(array $test) => $test['has_quotation']);
    }

    private function hasScores(array $subject): bool
    {
        if ($this->parentSubject !== null) {
            return $this->parentSubject['has_total'];
        }

        return $subject['tests']->contains(fn(array $test): bool => $test['recalculated_score'] !== null);
    }

    private function getSubjectStyle(): string
    {
        $styleElements = [
            $this->reportSettings['secondary_border_color'],
            'border-whiten-4',
        ];

        if ($this->reportSettings['show_individual_tests']) {
            array_push($styleElements, $this->reportSettings['primary_background_color']);
        }

        return isset($this->parentSubject)
            ? implode(
                ' ',
                $styleElements,
            )
            : $this->domainStyle;
    }

    private function getDomainStyle(): string
    {
        return implode(
            ' ',
            [$this->reportSettings['secondary_background_color'], $this->primaryBorderColor, 'border-whiten-4'],
        );
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return view('components.report.report.tests.results-table');
    }
}
