<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report;

use Illuminate\View\Component;
use Override;

use function view;

class ResultsTableComments extends Component
{
    public readonly string $primaryBorderColor;

    public function __construct(
        public readonly array $reportSettings,
        public readonly array $comments,
        public readonly int $numberOfColumns,
    ) {
        $this->primaryBorderColor = $this->reportSettings['primary_border_color'];
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return view('components.report.report.results-table-comments');
    }
}
