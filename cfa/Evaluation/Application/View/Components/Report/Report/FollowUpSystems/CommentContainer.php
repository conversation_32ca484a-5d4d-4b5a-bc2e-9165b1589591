<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report\FollowUpSystems;

use Cfa\Common\Domain\User\User;

use function htmlentities;

class CommentContainer
{
    public function __construct(
        private array $comments = [],
        private int $commentIndex = 1,
    ) {}

    public function addComment(string $comment, ?User $author): int
    {
        $this->comments[$this->commentIndex] = [
            'comment' => nl2br(htmlentities($comment)),
            'author' => optional($author)->firstname_with_salutation,
        ];

        return $this->commentIndex++;
    }

    public function getIndex(): int
    {
        return $this->commentIndex;
    }

    public function getComments(): array
    {
        return $this->comments;
    }

    public function hasComments(): bool
    {
        return empty($this->comments) === false;
    }
}
