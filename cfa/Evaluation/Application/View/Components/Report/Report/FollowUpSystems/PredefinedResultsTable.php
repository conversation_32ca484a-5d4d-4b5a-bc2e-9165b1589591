<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report\FollowUpSystems;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Override;

use function collect;
use function view;

class PredefinedResultsTable extends ResultsTable
{
    public readonly int $numberOfColumns;
    public readonly bool $hasScores;
    public readonly bool $hasPercentiles;
    public readonly array $testMoments;

    public function __construct(
        public readonly FollowUpSystem $scoreTableSection,
        public readonly bool $showTableHeader,
        public readonly bool $showComments,
        array $reportSettings,
        array $reportGroupSettings,
        array $report,
    ) {
        parent::__construct($reportSettings, $this->scoreTableSection, $report, $reportGroupSettings);

        $this->testMoments = $this->quotations['scores']['test_moments'] ?? [];
        $this->hasScores = collect($this->testMoments)->some(fn(array $value): bool => $value['score'] !== null);
        $this->hasPercentiles = $this->quotations['percentileAware'] ?? false;
        $this->numberOfColumns = $this->getNumberOfColumns();
    }

    private function getNumberOfColumns(): int
    {
        if ($this->hasScores && $this->hasPercentiles) {
            return 4;
        }

        return $this->hasScores || $this->hasPercentiles ? 3 : 2;
    }

    public function getCommentsForTestMoment(array $testMoment): array
    {
        if (empty($testMoment['comment'])) {
            return [];
        }

        return [
            [
                'comment' => ['comment' => $testMoment['comment']['comment']],
                'creator' => $testMoment['comment']['creator'],
                'updater' => $testMoment['comment']['updater'],
            ],
        ];
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return view('components.report.report.follow-up-systems.predefined-results-table');
    }
}
