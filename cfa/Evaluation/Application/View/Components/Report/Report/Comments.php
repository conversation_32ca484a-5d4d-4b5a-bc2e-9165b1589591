<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report;

use Illuminate\View\Component;
use Override;

use function array_filter;
use function htmlentities;

class Comments extends Component
{
    public readonly ?array $comments;

    public function __construct(
        public readonly array $reportSettings,
        array $comments,
    ) {
        $this->comments = $this->getComments($comments);
    }

    private function getComments(array $comments): array
    {
        $comments = array_filter(
            $comments,
            fn(array $comment): bool => empty($comment['comment']['comment']) === false,
        );
        $showAuthor = $this->reportSettings['show_feedback_author'];

        return array_map(
            fn(array $comment): array => [
                'author' => $showAuthor ? $comment['creator'] ?? $comment['updater'] ?? null : null,
                'comment' => nl2br(htmlentities((string) $comment['comment']['comment'])),
            ],
            $comments,
        );
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return '{{ $slot }}';
    }
}
