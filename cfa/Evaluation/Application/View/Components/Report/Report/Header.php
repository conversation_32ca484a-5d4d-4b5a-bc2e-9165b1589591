<?php

namespace Cfa\Evaluation\Application\View\Components\Report\Report;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\View\Component;
use Override;

use function view;

class Header extends Component
{
    public readonly string $primaryBorderColor;
    public readonly string $primaryColor;
    public readonly string $secondaryColor;
    public readonly ?string $schoolName;
    public readonly ?string $logo;
    public readonly string $reportPeriodName;
    public readonly string $schoolyearDescription;
    public readonly ?string $profilePicturePath;
    public readonly string $pupilName;
    public readonly string $groupName;

    public function __construct(
        public readonly array $reportSettings,
        public readonly string $date,
        ReportPeriod $reportPeriod,
        Schoolyear $schoolyear,
        CareInfo $careInfo,
        Pupil $pupil,
        Group $group,
    ) {
        $this->primaryBorderColor = $this->reportSettings['primary_border_color'];
        $this->primaryColor = $this->reportSettings['primaryColor'];
        $this->secondaryColor = $this->reportSettings['secondaryColor'];
        $this->schoolName = $reportSettings['name'] ?? null;
        $this->logo = $reportSettings['logo'] ?? null;
        $this->reportPeriodName = $reportPeriod->name;
        $this->schoolyearDescription = $schoolyear->description;
        $this->profilePicturePath = $reportSettings['profile_picture_enabled'] ? $careInfo->profile_picture_path : null;
        $this->pupilName = $pupil->fullname;
        $this->groupName = $group->name;
    }

    /** {@inheritdoc} */
    #[Override]
    public function render()
    {
        return view('components.report.report.header');
    }
}
