<?php

namespace Cfa\Evaluation\Application\Policies;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Database\Eloquent\Builder;

class SubjectQuotationSettingPolicy
{
    public function edit(User $user, Subject $subject, Group $group, ReportSettings $reportSettings): bool
    {
        return Subject::query()
            ->join('subject_quotation_settings as children', 'children.subject_id', 'subjects.id')
            ->where('subjects.parent_id', $subject->id)
            ->where('children.group_id', $group->id)
            ->where('children.report_settings_id', $reportSettings->id)
            ->whereNull('children.deleted_at')
            ->where(function (Builder $query): void {
                $query->where('children.has_score', true)
                    ->orWhereNotNull('children.quotation_system_id')
                    ->orWhere('children.has_comment', true);
            })
            ->doesntExist();
    }
}
