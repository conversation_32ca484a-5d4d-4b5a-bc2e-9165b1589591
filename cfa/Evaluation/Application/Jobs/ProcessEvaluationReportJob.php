<?php

namespace Cfa\Evaluation\Application\Jobs;

use App\Constants\Queues;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Evaluation\Application\Services\Report\CalculateReportTotals;
use Cfa\Evaluation\Domain\Report\Calculation\EvaluationReportJob;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettingsRepositoryInterface;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Throwable;

use function app;
use function collect;

class ProcessEvaluationReportJob implements ShouldQueue
{
    use Batchable;
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public int $tries = 5;

    public bool $deleteWhenMissingModels = true;

    private EvaluationReportJob $evaluationReportJob;

    private Schoolyear $schoolyear;

    public function __construct(
        SchoolyearRepositoryInterface $schoolyearRepository,
        private readonly ReportPeriodRepositoryInterface $reportPeriodRepository,
        private readonly ReportSettingsRepositoryInterface $reportSettingsRepository,
        private readonly GroupRepositoryInterface $groupRepository,
        EvaluationReportJob $evaluationReportJob,
    ) {
        $this->onQueue(Queues::MEDIUM_PRIORITY);
        $this->evaluationReportJob = $evaluationReportJob;
        $this->schoolyear = $schoolyearRepository->getCurrent();
    }

    public function handle(): void
    {
        try {
            if ($this->shouldBeDelayed()) {
                $this->saveStatus(ExportJobStatus::NotExisting);

                return;
            }

            $this->processJob();
        } catch (Throwable $throwable) {
            $this->saveStatus(ExportJobStatus::Failed);
            throw $throwable;
        }
    }

    private function processJob(): void
    {
        $this->saveStatus(ExportJobStatus::InProgress);
        $service = app(CalculateReportTotals::class);

        $this->getGroups()->each(function (Group $group) use ($service): void {
            $reportSettings = $this->reportSettingsRepository->getForGroup($group, $this->schoolyear);
            $reportPeriods = $this->evaluationReportJob->reportPeriod
                ? collect([$this->evaluationReportJob->reportPeriod])
                : $this->reportPeriodRepository->getReportPeriodsAndYearReportForGroup($group, $this->schoolyear);
            $pupils = $this->groupRepository->getPupilsOfGroup($group, $this->schoolyear);
            $service->calculate($reportSettings, $group, $pupils, $reportPeriods);
        });
        $this->saveStatus(ExportJobStatus::Completed);
    }

    private function getGroups(): Collection
    {
        if ($this->evaluationReportJob->group !== null) {
            return collect([$this->evaluationReportJob->group]);
        }

        $groupIds = $this->groupRepository->getGroupIdsOfGroupsWithPupilsForSchool(
            $this->evaluationReportJob->school,
            $this->schoolyear,
        );

        return Group::query()->whereIn('id', $groupIds)->get();
    }

    private function shouldBeDelayed(): bool
    {
        $reportPeriod = $this->evaluationReportJob->reportPeriod;
        if ($reportPeriod === null || $reportPeriod->parent_id !== null) {
            return false;
        }

        return EvaluationReportJob::query()
            ->leftJoin('report_periods', 'report_periods.id', 'evaluation_report_jobs.report_period_id')
            ->where(
                fn(Builder $builder): Builder =>
                    $builder->where('report_period_id', '!=', $reportPeriod->id)->orWhereNull('report_period_id'),
            )
            ->where(
                fn(Builder $builder): Builder =>
                    $builder->where('group_id', $this->evaluationReportJob->group_id)->orWhereNull('group_id'),
            )
            ->where(
                fn(Builder $builder): Builder => $builder->whereNotNull('parent_id')->orWhereNull('report_period_id'),
            )
            ->where('evaluation_report_jobs.school_id', $this->evaluationReportJob->school_id)
            ->whereNotIn('status', [ExportJobStatus::Completed, ExportJobStatus::Failed])
            ->exists();
    }

    private function saveStatus(ExportJobStatus $status): void
    {
        $this->evaluationReportJob->status = $status;
        $this->evaluationReportJob->save();
    }
}
