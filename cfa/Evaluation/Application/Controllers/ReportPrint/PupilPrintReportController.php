<?php

declare(strict_types=1);

namespace Cfa\Evaluation\Application\Controllers\ReportPrint;

use App\Controllers\Controller;
use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Carbon\Carbon;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Application\Handlers\GroupSwitcherDataHandler;
use Cfa\Common\Application\SharedDataProviders\GroupSwitcherProvider;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJob;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

use function app;
use function cloudfront_url;
use function group;
use function route;

final class PupilPrintReportController extends Controller
{
    private SchoolyearRepositoryInterface $schoolyearRepository;

    private ReportPeriodRepositoryInterface $reportPeriodRepository;

    private GroupRepositoryInterface $groupRepository;

    public function __construct(
        SchoolyearRepositoryInterface $schoolyearRepository,
        ReportPeriodRepositoryInterface $reportPeriodRepository,
        GroupRepositoryInterface $groupRepository,
    ) {
        $this->schoolyearRepository = $schoolyearRepository;
        $this->reportPeriodRepository = $reportPeriodRepository;
        $this->groupRepository = $groupRepository;
    }

    public function __invoke(Pupil $pupil): Response
    {
        $group = group();

        $schoolyear = $this->schoolyearRepository->getCurrent();
        $this->handleGroupSwitcher(Auth::user(), school(), $schoolyear);

        $pupils = $this->groupRepository->getPupilsOfGroup($group)
            ->map(fn(Pupil $pupil): array => [
                'uid' => $pupil->uid,
                'name' => $pupil->fullname,
                'redirectLink' => route('web.evaluation.print.pupil-print-reports', [
                    'pupil' => $pupil->uid,
                ]),
                'disabled' => $pupil->careInfo->pupil_status->isDisabled(),
            ]);

        $currentReportPeriod = $this->reportPeriodRepository->getCurrentOrLastReportPeriodForGroup($group);

        $existingPrintJobs = EvaluationPrintJob::wherePupilId($pupil->id)
            ->get(['id', 'status', 'file', 'report_period_id', 'group_id'])
            ->keyBy(fn(EvaluationPrintJob $job): string => $job->report_period_id . '#' . $job->group_id);

        $reportPeriods = $this->reportPeriodRepository->getReportPeriodsForGroup($group, $schoolyear);

        if (FeatureToggle::isActive(Feature::SynthesisReports) && $reportPeriods->isNotEmpty()) {
            $parentPeriod = $reportPeriods->first()->parent;
            $reportPeriods->push($parentPeriod);
        }

        $reportPeriodsForPupil = $this->reportPeriodRepository->getReportPeriodsForPupil($pupil->careInfo);

        $groups = Group::withoutGlobalScopes()
            ->whereIn('id', $reportPeriodsForPupil->pluck('group_id'))
            ->get(['id', 'uid', 'name', 'target_audience_type', 'natural_study_year'])
            ->push($group)
            ->keyBy('id');
        $schoolyears = Schoolyear::query()
            ->whereIn('id', $reportPeriodsForPupil->pluck('schoolyear_id')->unique())
            ->pluck('description', 'id');
        $schoolyears[$schoolyear->id] = $schoolyear->description;

        $result = $reportPeriods
            ->map(fn(ReportPeriod $reportPeriod): ReportPeriod => $reportPeriod->setAttribute('group_id', $group->id))
            ->concat($reportPeriodsForPupil)
            ->keyBy(fn(ReportPeriod $period): string => $period->id . '#' . $period->getAttribute('group_id'))
            ->map(fn(ReportPeriod $period, string $key): Collection => $this->getFieldsForFrontend(
                $period,
                $groups[$period->getAttribute('group_id')],
                $existingPrintJobs->get($key),
                $schoolyears[$period->schoolyear_id],
            ))
            ->sortBy(fn(Collection $period): int => $period['groupOrder'])
            ->sortByDesc(
                fn(Collection $period): Carbon => $period['is_year_report'] ? $period['end'] : $period['start'],
            )
            ->map
            ->only(['period', 'file', 'schoolYear', 'uid', 'status', 'groupName', 'groupUid'])
            ->values();

        return Inertia::render('Evaluation/Print/PupilOverview', [
            'pupils' => $pupils,
            'pupil' => $pupil,
            'printJob' => $result->values()->toArray(),
            'pupilSearchAutocompleteUrl' => route('web.common.pupils.search'),
            'statusUrl' => route('web.evaluation.print.pupil-print-status', ['pupil' => $pupil->uid]),
            'reportPeriodPrintUrl' => route(
                'web.evaluation.report.pupil-print',
                ['reportPeriod' => 'reportPeriodUid', 'pupil' => $pupil->uid, 'group' => 'groupUid'],
            ),
            'overviewPageRoute' => route('web.evaluation.print.group-print-overview', [
                'reportPeriod' => $currentReportPeriod,
            ]),
        ]);
    }

    private function getFile(?string $filePath): ?string
    {
        return $filePath ? cloudfront_url($filePath) : null;
    }

    private function getFieldsForFrontend(
        ReportPeriod $period,
        Group $group,
        ?EvaluationPrintJob $printJob,
        string $schoolyearDescription,
    ): Collection {
        return collect([
            'is_year_report' => $period->parent_id === null,
            'start' => $period->start,
            'end' => $period->end,
            'period' => $period->parent_id === null ?
                trans('navigation.tabs.evaluation.report.synthesis') :
                $period->name,
            'file' => $this->getFile($printJob?->file),
            'schoolYear' => $schoolyearDescription,
            'uid' => $period->uid,
            'status' => $printJob?->status->value ?? ExportJobStatus::NotExisting,
            'groupName' => $period->group_name ?? $group->name,
            'groupUid' => $group->uid,
            'groupOrder' => ($group->target_audience_type === TargetAudienceType::Ko ? 0 : 10) +
                $group->natural_study_year,
        ]);
    }

    protected function handleGroupSwitcher(User $user, School $school, Schoolyear $schoolyear): void
    {
        app(GroupSwitcherProvider::class)->setGroups(
            $school,
            $this->groupRepository->getClassGroupsForUserAndSchoolHavingReportPeriods($user, $school, $schoolyear),
        );
        app(GroupSwitcherDataHandler::class)->setRouteName('web.evaluation.print.redirect');
    }
}
