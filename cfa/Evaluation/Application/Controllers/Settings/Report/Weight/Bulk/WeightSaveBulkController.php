<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\Weight\Bulk;

use App\Controllers\Controller;
use App\Http\Response\NoContentResponse;
use Carbon\Carbon;
use Cfa\Common\Application\Repositories\SettingsGroupRepository;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Application\Controllers\Settings\Report\Weight\WeightSaveRequest;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobsService;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class WeightSaveBulkController extends Controller
{
    public function __invoke(WeightSaveRequest $request, ReportSettings $reportSettings, Subject $subject): Response
    {
        $now = Carbon::now();
        $user = Auth::user();

        $school = school();
        $groupIds = app(SettingsGroupRepository::class)->getGroupsByTargetAudiences(
            $reportSettings->targetAudiences,
            $user,
            $school,
            null,
        )->pluck('id');

        $attributes = [
            'weight_percentage' => $request->get(WeightSaveRequest::WEIGHT_PERCENTAGE),
            'weight_fraction_numerator' => $request->get(WeightSaveRequest::WEIGHT_FRACTION_NUMERATOR),
            'weight_fraction_denominator' => $request->get(WeightSaveRequest::WEIGHT_FRACTION_DENOMINATOR),
        ];

        $existingSubjectQuotationSettings = SubjectQuotationSettings::query()
            ->where('report_settings_id', $reportSettings->id)
            ->whereIn('group_id', $groupIds)
            ->where('subject_id', $subject->id)
            ->pluck('group_id', 'id');

        $updateAttributes = array_merge($attributes, [
            'updated_at' => $now,
            'updater_id' => $user->id,
        ]);

        SubjectQuotationSettings::whereIn('id', $existingSubjectQuotationSettings->keys())->update($updateAttributes);

        $quotationSettingsToInsert = $groupIds->diff($existingSubjectQuotationSettings->values())
            ->map(function (int $groupId) use ($attributes, $school, $subject, $reportSettings, $user, $now) {
                return array_merge($attributes, [
                    'uid' => uuid(),
                    'school_id' => $school->id,
                    'group_id' => $groupId,
                    'subject_id' => $subject->id,
                    'report_settings_id' => $reportSettings->id,
                    'created_at' => $now,
                    'updated_at' => $now,
                    'creator_id' => $user->id,
                    'updater_id' => $user->id,
                ]);
            });

        SubjectQuotationSettings::insert($quotationSettingsToInsert->toArray());

        app(EvaluationReportJobsService::class)->createForSchool($school);

        return new NoContentResponse();
    }
}
