<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\ReportSettings;

use App\Traits\ApiResponse;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettingsResource;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class ReportSettingsIndexController
{
    use ApiResponse;

    public function __invoke(
        ReportSettings $reportSettings,
    ): JsonResponse|View {
        return view('settings.evaluation.report.step-report-settings', [
            'reportSettings' => new ReportSettingsResource($reportSettings)->toJson(),
            'currentStep' => 6,
            'synthesisTotalInfoUrl' => config('tenants.' . tenant()->uid . '.externalUrls.calculate-synthesis-totals'),
            'pageTitle' => $this->getPageTitle($reportSettings),
        ]);
    }

    private function getPageTitle(ReportSettings $reportSettings): string
    {
        return trans('titles.report.report') . ': ' .
            $reportSettings->report_name . ' - ' .
            trans('labels.report.breadcrumbs.step-report-settings');
    }
}
