<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\Order;

use App\Controllers\Controller;
use Cfa\Evaluation\Application\Repositories\Report\FollowUpSystem\FollowUpSystemReportSettingsSaveOrderRepository;
use Cfa\Evaluation\Application\Repositories\Report\Subject\SubjectReportSettingsSaveOrderRepository;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Http\JsonResponse;

use function app;

class ReportSettingsOrderSaveController extends Controller
{
    public function __invoke(ReportSettingsOrderSaveRequest $request, ReportSettings $reportSettings): JsonResponse
    {
        if ($request->isSubject()) {
            $subjectReportSettingsSaveOrderRepository =
                app(SubjectReportSettingsSaveOrderRepository::class, ['reportSettings' => $reportSettings]);
            $subjectReportSettingsSaveOrderRepository->saveOrder($request->getItem(), $request->getOrder());

            return $this->respondNoContent();
        }
        $followUpSystemReportSettingsSaveOrderRepository =
            app(FollowUpSystemReportSettingsSaveOrderRepository::class, ['reportSettings' => $reportSettings]);

        $followUpSystemReportSettingsSaveOrderRepository->saveOrder($request->getItem(), $request->getOrder());

        return $this->respondNoContent();
    }
}
