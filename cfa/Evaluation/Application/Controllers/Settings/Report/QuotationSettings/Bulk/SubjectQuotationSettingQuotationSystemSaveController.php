<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\Report\QuotationSettings\Bulk;

use App\Http\Response\NoContentResponse;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\QuotationSystem\QuotationSystem;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Http\Response;

class SubjectQuotationSettingQuotationSystemSaveController extends SubjectQuotationSettingsBulkController
{
    public function __invoke(
        SubjectQuotationSettingQuotationSystemSaveRequest $request,
        ReportSettings $reportSettings,
        Subject $subject,
    ): Response {
        $quotationSystemUid = $request->get('quotationSystemUid');
        $quotationSystem = $quotationSystemUid ?
            QuotationSystem::where('uid', $quotationSystemUid)->first() :
            null;

        $attributes = [
            'quotation_system_id' => optional($quotationSystem)->id,
        ];

        $this->updateOrCreateSubjectQuotationSettings($reportSettings, $subject, $attributes);

        return new NoContentResponse();
    }
}
