<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\BingelMethod;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Common\Domain\Subject\CalendarType;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\Subject\SubjectMinimalResource;
use Cfa\Evaluation\Domain\BingelMethod\BingelMethod;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;

class BingelMethodIndexController
{
    public function __invoke(): View
    {
        $leaningAreasAndDomains = Subject::getRepository()
            ->getAllLearningAreasWithDomainsBySchoolFlatWithoutArchived(
                school(),
                CalendarType::Primary,
            );

        $bingelMethods = BingelMethod::get(['uid', 'name', 'subject', 'domain'])->keyBy('uid');
        /** @var School $school */
        $school = school();
        /** @var SchoolSettings $schoolSetting */
        $schoolSetting = SchoolSettings::where('school_id', $school->id)
            ->where('type', SchoolSettingsType::BingelMethodsMapping)
            ->first();
        if ($schoolSetting !== null) {
            collect($schoolSetting->value)->each(
                function (array $value) use (
                    $bingelMethods,
                    $leaningAreasAndDomains
                ): void {
                    /** @var BingelMethod $bingelMethod */
                    $bingelMethod = $bingelMethods->get($value['bingel_method']);
                    $subject = $leaningAreasAndDomains->firstWhere('uid', $value['subject']);
                    if ($subject !== null && $bingelMethod !== null) {
                        $bingelMethod->setAttribute('selected', $subject->uid);
                        $bingelMethod->makeVisible('selected');
                    }
                },
            );
        }
        $bingelMethods = $bingelMethods
            ->sortBy(
                fn(BingelMethod $bingelMethod): string => Str::lower($bingelMethod->name . '#' . $bingelMethod->domain),
            )
            ->values();
        $subjects = SubjectMinimalResource::collection($leaningAreasAndDomains);

        $action = Route('web.settings.evaluation.bingel-methods.save');

        return view(
            'settings.evaluation.bingel-methods.index',
            compact('bingelMethods', 'subjects', 'action'),
        );
    }
}
