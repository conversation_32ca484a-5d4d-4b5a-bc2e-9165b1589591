<?php

namespace Cfa\Evaluation\Application\Controllers\Settings\BingelMethod;

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;

use function school;

class BingelMethodSaveController
{
    public function __invoke(BingelMethodSaveRequest $bingelMethodSaveRequest): void
    {
        /** @var School $school */
        $school = school();
        $schoolSetting = SchoolSettings::where('school_id', $school->id)
            ->where('type', SchoolSettingsType::BingelMethodsMapping)
            ->first();
        if ($schoolSetting === null) {
            $schoolSetting = new SchoolSettings();
            $schoolSetting->school_id = $school->id;
            $schoolSetting->type = SchoolSettingsType::BingelMethodsMapping;
        }
        $schoolSetting->value = $bingelMethodSaveRequest->getMapping();
        $schoolSetting->save();
    }
}
