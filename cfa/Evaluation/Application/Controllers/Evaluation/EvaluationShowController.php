<?php

namespace Cfa\Evaluation\Application\Controllers\Evaluation;

use App\Controllers\Controller;
use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Application\Handlers\GroupSwitcherDataHandler;
use Cfa\Common\Application\SharedDataProviders\GroupSwitcherProvider;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\Subject\SubjectRepositoryInterface;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTestResource;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettingsRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettingsResource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

use function route;

class EvaluationShowController extends Controller
{
    /**
     * Show evaluation overview screen for a given subject.
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     *
     * @throws NoActiveSchoolsException
     */
    public function __invoke(
        Group $group,
        ReportPeriod $reportPeriod,
        Subject $subject,
        SubjectRepositoryInterface $subjectRepository,
        ReportPeriodRepositoryInterface $reportPeriodRepository,
        SchoolyearRepositoryInterface $schoolyearRepository,
        ReportSettingsRepositoryInterface $reportSettingsRepository,
    ): Response {
        app(GroupSwitcherDataHandler::class)->setRouteName('web.evaluation.evaluate')->setParams([
            'reportPeriod' => $reportPeriod,
        ]);

        $user = Auth::user();
        $schoolyear = $schoolyearRepository->getCurrent();

        $subjects = $subjectRepository->getEvaluableSubjectsByUserForGroup($user, $group, $schoolyear);
        $subjectsIds = $subjects->pluck('id');

        $subjectsToAdd = collect();
        $subjects->each(function (Subject $subject) use (&$subjectsToAdd, $subjectsIds): void {
            if (!is_null($subject->parent_id) && !$subjectsIds->contains($subject->parent_id)) {
                $subjectsToAdd->push($subject->parent->setRelation('parent', null));
                $subjectsIds->push($subject->parent_id);
            }
        });

        $subjects = $subjects->concat($subjectsToAdd)->sortBy(function (Subject $subject) {
            return $subject->parent_id
                ? $subject->parent->name . '$$' . $subject->parent->uid . '$$' . $subject->name
                : $subject->name . '$$' . $subject->uid;
        });

        $reportSettings = $reportSettingsRepository->getForGroup($group, $schoolyear);
        $quotationSettingsBySubjectId = $this->getQuotationsSettingsBySubjectId($subjects, $reportSettings, $group);
        $subjects = $this->getMappedSubjects($subjects, $quotationSettingsBySubjectId, $reportPeriod, $group);

        $periodTabs = $this->getMappedReportPeriods(
            $group,
            $reportPeriodRepository->getReportPeriodsForGroup($group, $schoolyear),
            $subject,
        );

        $currentTab = $reportPeriod->name;

        $groupRepository = Group::getRepository();
        $pupils = $groupRepository->getPupilsOfGroup($group);
        $pupilIds = $pupils->pluck('id')->toArray();

        $school = school();
        $groups = $groupRepository->getGroupsForUserBySubjectPermissions(Auth::user(), $school, $schoolyear);
        app(GroupSwitcherProvider::class)->setGroups($school, $groups);
        $groupsForSchoolYear = $reportPeriod->isExpired() ?
         $groupRepository
             ->getHistoricalGroupsForPupilsInSchoolYear($pupilIds, $schoolyear)
             ->pluck('id')
             ->push($group->id)
             ->unique()
             ->toArray() :
            collect([$group])->pluck('id');
        $groupsForSchoolYear = $groups->pluck('id')->intersect($groupsForSchoolYear)->toArray();
        $tests = EvaluationTest::getRepository()
            ->getAllForGroupAndSubjectInReportPeriod($groupsForSchoolYear, $pupilIds, $subject, $reportPeriod)
            ->reject(fn(EvaluationTest $test): bool =>
                $test->group_id !== $group->id && $test->evaluationTestScores->isEmpty());
        $tests = EvaluationTestResource::collection($tests)->resolve();
        $quotationSettings = new SubjectQuotationSettingsResource(
            $quotationSettingsBySubjectId[$subject->id],
        )->jsonSerialize();

        $commentValidationRules = new FollowUpSystemGoalComment()
            ->withoutRule('comment', 'required')
            ->getValidationRulesAttribute();

        $baseParameters = ['subject' => $subject, 'reportPeriod' => $reportPeriod];
        $parameters = ['evaluationTest' => '#evaluationTestUid#', ...$baseParameters];

        $pupils = $pupils->map(function (User $pupil) {
            $data = $pupil->toArray();
            if ($pupil->careInfo) {
                $data['disabled'] = $pupil->careInfo->pupil_status->isDisabled();
            }

            return $data;
        });

        return Inertia::render(
            'Evaluation/Evaluate/Index',
            [
                'subjects' => $subjects,
                'subject' => $subject,
                'pupils' => $pupils,
                'periodTabs' => $periodTabs,
                'currentTab' => $currentTab,
                'reportPeriod' => $reportPeriod,
                'periodIsExpired' => $reportPeriod->isExpired(),
                'tests' => $tests,
                'schoolyear' => $schoolyear,
                'quotationSettings' => $quotationSettings,
                'baseUrl' => route('web.evaluation.evaluation-test.save', ['group' => $group, ...$baseParameters]),
                'deleteTestUrl' => route('web.evaluation.evaluation-test.delete', $parameters),
                'saveScoresUrl' => route('web.evaluation.evaluation-test-scores.save', $parameters),
                'subjectOverviewPrintIsActive' => FeatureToggle::isActive(Feature::ShowSubjectOverviewPrint),
                'subjectOverviewPrintUrl' => route('web.evaluation.print.subject-overview-print', [
                    'reportPeriod' => $reportPeriod,
                    'subject' => $subject,
                ]),
                'subjectPrintStatusUrl' => route('web.evaluation.print.subject-print-status', [
                    'reportPeriod' => $reportPeriod,
                    'subject' => $subject,
                ]),
                'commentValidationRules' => $commentValidationRules,
                'showAverageAndMedian' => $reportSettings['show_synthesis_average'],
            ],
        );
    }

    private function getQuotationsSettingsBySubjectId(
        Collection $subjects,
        ReportSettings $reportSettings,
        Group $group,
    ): Collection {
        return SubjectQuotationSettings::query()
            ->with('quotationSystem:id,uid,name')
            ->where('report_settings_id', $reportSettings->id)
            ->where('school_id', $group->school->id)
            ->where('group_id', $group->id)
            ->whereIn('subject_id', $subjects->pluck('id'))
            ->get()
            ->keyBy('subject_id');
    }

    private function getMappedSubjects(
        Collection $subjects,
        Collection $quotationSettingsBySubjectId,
        ReportPeriod $reportPeriod,
        Group $group,
    ): Collection {
        return $subjects->map(
            fn(Subject $subject): array =>
            [
                'name' => $subject->name,
                'uid' => $subject->uid,
                'parent_id' => $subject->parent_id,
                'disabled' => !($quotationSettingsBySubjectId[$subject->id] ?? null)?->mayEvaluate(),
                'redirectLink' => route(
                    'web.evaluation.evaluate.show',
                    ['group' => $group, 'subject' => $subject->uid, 'reportPeriod' => $reportPeriod->uid],
                ),
            ],
        )->values();
    }

    private function getMappedReportPeriods(Group $group, Collection $reportPeriods, Subject $subject): Collection
    {
        return $reportPeriods->map(
            fn(ReportPeriod $reportPeriod): array =>
            [
                'href' => route(
                    'web.evaluation.evaluate.show',
                    ['group' => $group, 'subject' => $subject->uid, 'reportPeriod' => $reportPeriod->uid],
                ),
                'value' => $reportPeriod->name,
            ],
        );
    }
}
