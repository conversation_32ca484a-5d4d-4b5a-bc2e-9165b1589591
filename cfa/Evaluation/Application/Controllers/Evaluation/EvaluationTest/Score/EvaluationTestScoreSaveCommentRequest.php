<?php

namespace Cfa\Evaluation\Application\Controllers\Evaluation\EvaluationTest\Score;

use App\Http\Requests\FormRequest;
use Cfa\Common\Domain\User\Pupil;
use Override;

use function trim;

class EvaluationTestScoreSaveCommentRequest extends FormRequest
{
    private const COMMENT = 'comment';

    private const PUPIL = 'pupil';

    private Pupil $pupil;

    private ?string $comment;

    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                self::COMMENT => [
                    'nullable',
                    'string',
                ],
                self::PUPIL => [
                    'required',
                    'string',
                ],
            ],
        );
    }

    public function getPupil(): Pupil
    {
        if (isset($this->pupil)) {
            return $this->pupil;
        }

        return $this->pupil = Pupil::where('users.uid', $this->get(self::PUPIL))->firstOrFail();
    }

    public function getComment(): ?string
    {
        if (isset($this->comment)) {
            return $this->comment;
        }

        $comment = trim((string) $this->get('comment', ''));

        return $this->comment = empty($comment) ? null : $comment;
    }
}
