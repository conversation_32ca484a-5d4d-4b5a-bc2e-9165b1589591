<?php

namespace Cfa\Evaluation\Application\Controllers\Evaluation\EvaluationTest;

use App\Controllers\Controller;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobsService;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTestResource;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Http\JsonResponse;

use function app;

class EvaluationTestSaveController extends Controller
{
    /**
     * Update or create a test.
     *
     * @throws NoActiveSchoolsException
     */
    public function __invoke(
        Group $group,
        EvaluationTestSaveRequest $request,
        ReportPeriod $reportPeriod,
        Subject $subject,
        ?EvaluationTest $evaluationTest = null,
    ): JsonResponse {
        if ($evaluationTest === null) {
            $evaluationTest = new EvaluationTest();
            $evaluationTest->school_id = $group->school_id;
            $evaluationTest->group_id = $group->id;
            $evaluationTest->subject_id = $subject->id;
        }

        if ($evaluationTest->imported_at === null) {
            $evaluationTest->report_period_id = $reportPeriod->id;
            $evaluationTest->fill($request->all());
        }

        $evaluationTest->on_report = $request->get('on_report');
        $evaluationTest->name = $request->get('name');
        $evaluationTest->description = $request->get('description');
        $evaluationTest->save();

        if ($evaluationTest->wasChanged('max')) {
            app(EvaluationReportJobsService::class)->createForReportPeriod(
                $reportPeriod->school,
                $evaluationTest->group,
                $reportPeriod,
            );
        }

        return $this->respond(new EvaluationTestResource($evaluationTest->load('quotationSystem.quotations')));
    }
}
