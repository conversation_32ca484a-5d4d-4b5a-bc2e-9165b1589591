<?php

namespace Cfa\Evaluation\Application\Controllers\Report;

use App\Controllers\Controller;
use App\Http\Requests\FormRequest;
use Cfa\Common\Application\Services\Filters\FilterGroupsWithPupils;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriodRepositoryInterface;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

use function app;
use function group;
use function redirect;
use function route;

class ReportRedirectController extends Controller
{
    private GroupRepositoryInterface $groupRepository;

    private ReportPeriodRepositoryInterface $reportPeriodRepository;

    private SchoolyearRepositoryInterface $schoolyearRepository;

    public function __construct(
        GroupRepositoryInterface $groupRepository,
        ReportPeriodRepositoryInterface $reportPeriodRepository,
        SchoolyearRepositoryInterface $schoolyearRepository,
    ) {
        $this->groupRepository = $groupRepository;
        $this->reportPeriodRepository = $reportPeriodRepository;
        $this->schoolyearRepository = $schoolyearRepository;
    }

    public function __invoke(FormRequest $request, ?ReportPeriod $reportPeriod = null): RedirectResponse
    {
        $tabRoute = 'web.evaluation.report.' . $request->get('tab', 'edit');
        $pupilUid = $request->get('pupil');

        $school = school();
        $originalGroup = group();
        $pupils = null;
        $schoolyear = $this->schoolyearRepository->getCurrent();

        $availableGroups = app(FilterGroupsWithPupils::class)->forSchoolyear($schoolyear)(
            $school,
            $this->groupRepository->getClassGroupsForUserAndSchoolHavingReportPeriods(
                Auth::user(),
                $school,
                $schoolyear,
            )
        );

        $group = $availableGroups->first(
            fn(Group $availableGroup): bool => $originalGroup?->uid === $availableGroup->uid,
            $availableGroups->first(),
        );

        if ($group === null) {
            return redirect(route('web.evaluation.report.fallback.permissions'));
        }

        if ($group->uid !== $originalGroup?->uid) {
            group($group);
        }

        $group->loadMissing(['school']);

        $availablePeriods = $reportPeriod !== null
            ? $this->reportPeriodRepository->getReportPeriodsForGroup($group, $schoolyear)
            : collect();

        if ($reportPeriod === null || !$availablePeriods->contains('uid', $reportPeriod->uid)) {
            $reportPeriod = $this->reportPeriodRepository->getCurrentOrLastReportPeriodForGroup($group);
        }

        if ($reportPeriod === null) {
            return redirect()->route('web.evaluation.evaluate.fallback.report-periods');
        }

        if ($pupils === null) {
            $pupils = $pupilUid !== null
                ? collect([User::whereUid($pupilUid)->first()])
                : $this->groupRepository->getPupilsOfGroup($group, $schoolyear);
        }

        return redirect()->route($tabRoute, [
            'reportPeriod' => $reportPeriod->uid,
            'pupil' => $pupils->first()->uid,
        ]);
    }
}
