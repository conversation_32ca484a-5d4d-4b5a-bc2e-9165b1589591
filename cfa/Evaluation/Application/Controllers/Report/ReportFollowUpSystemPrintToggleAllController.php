<?php

namespace Cfa\Evaluation\Application\Controllers\Report;

use App\Controllers\Controller;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\Group\ReportGroupSettings;
use Illuminate\Http\JsonResponse;

class ReportFollowUpSystemPrintToggleAllController extends Controller
{
    public function __invoke(ReportPeriod $reportPeriod): JsonResponse
    {
        $group = group();
        $reportGroupSettings = ReportGroupSettings::where('group_id', $group->id)->first();

        if ($reportGroupSettings === null) {
            $reportGroupSettings = new ReportGroupSettings();
            $reportGroupSettings->group_id = $group->id;
            $reportGroupSettings->follow_up_systems_on_report = false;
            $reportGroupSettings->save();

            return $this->respondNoContent();
        }

        $reportGroupSettings->follow_up_systems_on_report = !$reportGroupSettings->follow_up_systems_on_report;
        $reportGroupSettings->save();

        return $this->respondNoContent();
    }
}
