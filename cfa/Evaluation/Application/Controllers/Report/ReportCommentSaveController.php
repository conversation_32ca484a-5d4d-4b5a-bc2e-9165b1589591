<?php

namespace Cfa\Evaluation\Application\Controllers\Report;

use App\Controllers\Controller;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\Report\ReportComment;
use Cfa\Evaluation\Domain\Report\ReportCommentType;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ReportCommentSaveController extends Controller
{
    public function __invoke(
        ReportCommentSaveRequest $request,
        ReportPeriod $reportPeriod,
        Pupil $pupil,
        ?ReportComment $reportComment = null,
    ): JsonResponse {
        $comment = $request->get(ReportCommentSaveRequest::COMMENT);
        $reportCommentType = ReportCommentType::from(
            $request->get(ReportCommentSaveRequest::TYPE, ReportCommentType::Footer->value),
        );

        if ($reportComment instanceof ReportComment && $comment === null) {
            $reportComment->forceDelete();

            return $this->respondOk();
        }

        if ($reportComment === null) {
            $reportComment = new ReportComment();
            $reportComment->pupil_id = $pupil->id;
            $reportComment->report_period_id = $reportPeriod->id;
            $reportComment->type = $reportCommentType;
        }

        $reportComment->comment = $comment;

        if ($this->hasRaceConditionOccurred(fn(): bool => $reportComment->save())) {
            // We encountered a race condition on creator duplicate.
            ReportComment::where('report_period_id', $reportPeriod->id)
                ->where('pupil_id', $pupil->id)
                ->where('creator_id', Auth::user()->id)
                ->where('type', $reportCommentType)
                ->update(['comment' => $comment]);
        }

        return $this->respond([
            'uid' => $reportComment->uid,
        ]);
    }
}
