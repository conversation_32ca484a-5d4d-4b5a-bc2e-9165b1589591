<?php

namespace Cfa\Evaluation\Application\Listeners;

use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTestCreating;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;

class AddQuotationSettingsToEvaluationTestOnCreating
{
    public function handle(EvaluationTestCreating $event): void
    {
        $evaluationTest = $event->getEvaluationTest();
        $subject = $evaluationTest->subject;
        $group = $evaluationTest->group;

        $quotationSettings = SubjectQuotationSettings::query()
            ->where(
                'report_settings_id',
                ReportSettings::getRepository()->getForGroup($group, $evaluationTest->reportPeriod->schoolyear)->id,
            )
            ->where('group_id', $group->id)
            ->where('subject_id', $subject->id)
            ->firstOrFail();

        $evaluationTest->has_score = $quotationSettings->mayEvaluateUsingScores();
        $evaluationTest->quotation_system_id = $quotationSettings->quotation_system_id;
        $evaluationTest->has_comment = $quotationSettings->has_comment;
    }
}
