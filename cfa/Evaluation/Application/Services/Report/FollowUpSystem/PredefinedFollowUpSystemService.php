<?php

namespace Cfa\Evaluation\Application\Services\Report\FollowUpSystem;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;

class PredefinedFollowUpSystemService extends AbstractFollowUpSystemService
{
    protected ?array $percentileTranslations = null;

    public function getDataForPupil(Pupil $pupil, Group $group, PredefinedFollowUpSystem $followUpSystem): array
    {
        $pupilsData = PredefinedFollowUpSystemScore::getRepository()->getPupilsWithScores(
            collect([$pupil]),
            $group,
            $followUpSystem,
            FollowUpSystemSubType::Default,
        );

        $scores = $this->translateScores($pupilsData->first(), $followUpSystem->type);

        return [
            'percentileAware' => $followUpSystem->type->isPercentileAware(),
            'scores' => $scores,
            'has_scores' => $this->determineHasScores($scores),
            'has_comments' => $this->determineHasComments($scores),
        ];
    }
}
