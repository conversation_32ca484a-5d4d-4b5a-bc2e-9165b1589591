<?php

namespace Cfa\Evaluation\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalCommentRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Override;

class FollowUpSystemGoalCommentRepository extends Repository implements FollowUpSystemGoalCommentRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new FollowUpSystemGoalComment();
    }

    #[Override]
    public function getCommentsByGoalsForSinglePupil(
        Pupil $pupil,
        FollowUpSystemInputMoment $followUpSystemInputMoment,
        Collection $followUpSystemGoals,
    ): Collection {
        return $pupil->followUpSystemGoalComments()
            ->with(['followUpSystemGoal', 'creator'])
            ->whereIn('follow_up_system_goal_id', $followUpSystemGoals->pluck('id'))
            ->where('follow_up_system_input_moment_id', $followUpSystemInputMoment->id)
            ->get()
            ->keyBy('followUpSystemGoal.uid');
    }

    #[Override]
    public function getCommentsByPupilForSingleGoal(
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemInputMoment $inputMoment,
        Collection $pupils,
    ): Collection {
        return FollowUpSystemGoalComment::query()
            ->with('pupil:users.id,users.uid')
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->where('follow_up_system_goal_id', $followUpSystemGoal->id)
            ->where('follow_up_system_input_moment_id', $inputMoment->id)
            ->get()
            ->keyBy('pupil.uid');
    }

    #[Override]
    public function getHistoryByPupilsForSingleGoal(
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemInputMoment $inputMoment,
        Collection $pupils,
    ): Collection {
        $followUpSystemGoalComments = FollowUpSystemGoalComment::query()
            ->select('follow_up_system_goal_comments.*')
            ->with('pupil', 'followUpSystemInputMoment')
            ->join('follow_up_system_input_moments', function (JoinClause $join): void {
                $join->on(
                    'follow_up_system_input_moments.id',
                    '=',
                    'follow_up_system_goal_comments.follow_up_system_input_moment_id',
                )->whereNull('follow_up_system_input_moments.deleted_at');
            })
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->where('follow_up_system_goal_id', $followUpSystemGoal->id)
            ->where('follow_up_system_input_moments.date', '<', $inputMoment->date)
            ->orderBy('follow_up_system_input_moments.date')
            ->get();

        return $followUpSystemGoalComments->groupBy('pupil.uid');
    }
}
