<?php

namespace Cfa\Evaluation\Application\Repositories;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Override;

class FollowUpSystemLastInputMomentRepository extends AbstractFollowUpSystemDashboardRepository
{
    #[Override]
    protected function getResultsInternal(FollowUpSystem $followUpSystem, Group $group, ?int $parentId): Collection
    {
        return DB::query()
            ->from(
                fn(Builder $builder): Builder => $this
                    ->getSubQuery(
                        $builder->from('follow_up_system_goal_quotations'),
                        $followUpSystem,
                        $group,
                        $parentId,
                    )
                    ->join('quotations', 'follow_up_system_goal_quotations.quotation_id', 'quotations.id')
                    ->addSelect('quotations.uid AS quotation_uid')
                    ->whereNull('quotations.deleted_at')
                    ->whereNull('follow_up_system_goal_quotations.deleted_at')
                    ->union(
                        fn(Builder $union) => $this
                            ->getSubQuery(
                                $union->from('follow_up_system_goal_comments'),
                                $followUpSystem,
                                $group,
                                $parentId,
                            )
                            ->addSelect(DB::raw('NULL AS quotation_uid')),
                    ),
            )
            ->join('users', 'pupil_id', 'users.id')
            ->whereNull('users.deleted_at')
            ->orderBy('users.id')
            ->orderBy('goal_uid')
            ->orderByDesc('quotation_uid')
            ->get([
                'users.uid AS user_uid',
                'goal_uid',
                'quotation_uid',
            ]);
    }

    private function getSubQuery(
        Builder $builder,
        FollowUpSystem $followUpSystem,
        Group $group,
        ?int $parentId,
    ): Builder {
        return $builder
            ->select(['pupil_id', 'follow_up_system_goals.uid AS goal_uid'])
            ->join('follow_up_system_goals', 'follow_up_system_goal_id', 'follow_up_system_goals.id')
            ->where('follow_up_system_goals.follow_up_system_id', $followUpSystem->id)
            ->where(
                'follow_up_system_input_moment_id',
                fn(Builder $subQuery): Builder => $subQuery->select('id')
                    ->from('follow_up_system_input_moments')
                    ->where('follow_up_system_id', $followUpSystem->id)
                    ->where('follow_up_system_input_moments.group_id', $group->id)
                    ->whereNull('deleted_at')
                    ->orderByDesc('date')
                    ->orderByDesc('created_at')
                    ->limit(1),
            )
            ->where('parent_id', $parentId)
            ->whereNull('follow_up_system_goals.deleted_at');
    }
}
