<?php

namespace Cfa\Evaluation\Application\Repositories\Report\FollowUpSystem;

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\FollowUpSystem\FollowUpSystemReportSettings;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;

class FollowUpSystemReportSettingsSaveOrderRepository
{
    public function __construct(public readonly ReportSettings $reportSettings) {}

    public function saveOrder(FollowUpSystem $followUpSystem, ?int $order): void
    {
        FollowUpSystemReportSettings::forceUpdateOrCreate([
            'report_setting_id' => $this->reportSettings->id,
            'follow_up_system_id' => $followUpSystem->id,
        ], ['order' => $order]);
    }
}
