<?php

namespace Cfa\Evaluation\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMomentRepositoryInterface;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Illuminate\Support\Collection;
use Override;

class FollowUpSystemInputMomentRepository extends Repository implements FollowUpSystemInputMomentRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new FollowUpSystemInputMoment();
    }

    #[Override]
    public function getAllForGroupAndFollowUpSystemInReportPeriod(
        Group $group,
        FollowUpSystem $followUpSystem,
        ReportPeriod $period,
    ): Collection {
        return FollowUpSystemInputMoment::withCount(['quotations', 'comments'])
            ->where('group_id', $group->id)
            ->where('follow_up_system_id', $followUpSystem->id)
            ->where('report_period_id', $period->id)
            ->orderBy('date')
            ->get();
    }
}
