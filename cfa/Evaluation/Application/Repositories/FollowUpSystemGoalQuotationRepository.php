<?php

namespace Cfa\Evaluation\Application\Repositories;

use App\Models\Model;
use App\Repositories\Repository;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotationRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Override;

// phpcs:ignore
class FollowUpSystemGoalQuotationRepository extends Repository implements FollowUpSystemGoalQuotationRepositoryInterface
{
    #[Override]
    public function getModel(): Model
    {
        return new FollowUpSystemGoalQuotation();
    }

    #[Override]
    public function getQuotationsByGoalsForSinglePupil(
        Pupil $pupil,
        FollowUpSystemInputMoment $followUpSystemInputMoment,
        Collection $followUpSystemGoals,
    ): Collection {
        $followUpSystemGoalQuotations = $pupil->followUpSystemGoalQuotations()
            ->with('quotation', 'followUpSystemGoal', 'followUpSystemInputMoment')
            ->where('follow_up_system_input_moment_id', $followUpSystemInputMoment->id)
            ->whereIn('follow_up_system_goal_id', $followUpSystemGoals->pluck('id'))
            ->get();

        return $followUpSystemGoalQuotations->keyBy('followUpSystemGoal.uid');
    }

    #[Override]
    public function getQuotationsByPupilsForSingleGoal(
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemInputMoment $inputMoment,
        Collection $pupils,
    ): Collection {
        $followUpSystemGoalQuotations = FollowUpSystemGoalQuotation::query()
            ->select('follow_up_system_goal_quotations.*')
            ->with('quotation', 'pupil', 'followUpSystemInputMoment')
            ->join('follow_up_system_input_moments', function (JoinClause $join): void {
                $join->on(
                    'follow_up_system_input_moments.id',
                    '=',
                    'follow_up_system_goal_quotations.follow_up_system_input_moment_id',
                )
                    ->whereNull('follow_up_system_input_moments.deleted_at');
            })
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->where('follow_up_system_goal_id', $followUpSystemGoal->id)
            ->where('follow_up_system_input_moment_id', $inputMoment->id)
            ->orderBy('follow_up_system_input_moments.date')
            ->get();

        return $followUpSystemGoalQuotations->keyBy('pupil.uid');
    }

    #[Override]
    public function getHistoryByPupilsForSingleGoal(
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemInputMoment $inputMoment,
        Collection $pupils,
    ): Collection {
        $followUpSystemGoalQuotations = FollowUpSystemGoalQuotation::query()
            ->select('follow_up_system_goal_quotations.*')
            ->with('quotation', 'pupil', 'followUpSystemInputMoment')
            ->join('follow_up_system_input_moments', function (JoinClause $join): void {
                $join->on(
                    'follow_up_system_input_moments.id',
                    '=',
                    'follow_up_system_goal_quotations.follow_up_system_input_moment_id',
                )
                    ->whereNull('follow_up_system_input_moments.deleted_at');
            })
            ->whereIn('pupil_id', $pupils->pluck('id'))
            ->where('follow_up_system_goal_id', $followUpSystemGoal->id)
            ->where('follow_up_system_input_moments.date', '<', $inputMoment->date)
            ->orderBy('follow_up_system_input_moments.date')
            ->get();

        return $followUpSystemGoalQuotations->groupBy('pupil.uid');
    }
}
