<?php

namespace Cfa\Wisa\Application\Repositories;

use Cfa\Common\Domain\School\School;
use Cfa\Wisa\Domain\CareData\WisaSchoolRepositoryInterface;
use Illuminate\Support\Collection;
use Override;

class WisaSchoolRepository implements WisaSchoolRepositoryInterface
{
    #[Override]
    public function getSchoolIdsByInstituteNumber(string $instituteNumber): Collection
    {
        return School::join('institute_school', 'schools.id', '=', 'school_id')
            ->join('institutes', 'institutes.id', '=', 'institute_id')
            ->where('institute_number', $instituteNumber)
            ->whereNull('institutes.deleted_at')
            ->pluck('schools.id');
    }
}
