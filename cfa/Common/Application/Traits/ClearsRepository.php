<?php

namespace Cfa\Common\Application\Traits;

use App\Models\Model;
use App\Repositories\CacheRepository;

/**
 * @mixin Model
 */
trait ClearsRepository
{
    protected static function bootClearsRepository(): void
    {
        static::saved(function (Model $model): void {
            self::flushCacheForModel($model);
        });

        static::deleted(function ($model): void {
            self::flushCacheForModel($model);
        });
    }

    private static function flushCacheForModel(Model $model): void
    {
        $repository = self::getRepository();
        if ($repository instanceof CacheRepository) {
            $repository->flush($model);
        }
    }
}
