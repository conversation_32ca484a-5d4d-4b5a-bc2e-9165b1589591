<?php

namespace Cfa\Common\Application\Traits;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\MassPrunable;

use function method_exists;

trait PruneSoftDeletes
{
    use MassPrunable;

    public function prunable(): Builder
    {
        // SMD Models shouldn't be cleaned, check for method from UpdatedBySMD trait.
        $class = self::class;

        return static::query()
            ->when(
                method_exists($class, 'initializeUpdatedBySMD'),
                fn(Builder $query): Builder => $query->whereNull($class::getSMDUpdatedAtColumn()),
            )
            ->where(
                $this->getDeletedAtColumn(),
                '<=',
                Carbon::now()->subDays(self::DAYS_TO_KEEP_SOFT_DELETES),
            );
    }
}
