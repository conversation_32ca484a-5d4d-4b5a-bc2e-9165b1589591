<?php

namespace Cfa\Common\Application\Traits;

trait CallWithQueryingEnabledOnModels
{
    protected array $defaultQueryingEnabledOnModels = [];

    protected function withQueryingEnabledOnModels(callable $callback, ?array $models = null): mixed
    {
        $models = ($models === null) ? collect($this->defaultQueryingEnabledOnModels) : collect($models);
        $models->each(fn(string $model) => $model::setQueryingEnabled());

        try {
            return $callback();
        } finally {
            $models->each(fn(string $model) => $model::setQueryingDisabled());
        }
    }

    protected function addQueryingEnabledModel(string $className): void
    {
        $this->defaultQueryingEnabledOnModels[] = $className;
    }

    protected function setQueryingEnabledModels(array $classNames): void
    {
        $this->defaultQueryingEnabledOnModels = $classNames;
    }
}
