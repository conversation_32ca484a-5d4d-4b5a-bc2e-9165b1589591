<?php

namespace Cfa\Common\Application\Repositories;

use App\Models\Model;
use App\Repositories\CacheRepository;
use Carbon\Carbon;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Override;

use function array_merge;
use function collect;

class GroupCacheRepository extends CacheRepository implements GroupRepositoryInterface
{
    private const KEY_GROUPS_BY_SCHOOL = 'groupsBySchoolUid:';
    private const KEY_GROUP_IDS_BY_SCHOOL = 'groupIdsBySchoolUid:';
    private const KEY_GROUPS_WITH_PUPILS_BY_SCHOOL = 'groupsWithPupilsBySchoolUid:';
    private const KEY_CLASS_GROUPS_BY_SCHOOL = 'classGroupsBySchoolUid:';
    private const KEY_CLASS_GROUPS_HAVING_REPORT_PERIODS_BY_SCHOOL = 'classGroupsHavingReportPeriodsBySchoolUid:';
    private const KEY_USER = 'userUid:';
    private const KEY_SCHOOLYEAR = 'schoolyear:';
    private const KEY_USERS_FOR_GROUP = 'usersOfGroup:';
    private const KEY_PUPILS_FOR_GROUP = 'pupilsOfGroup:';
    private const TAG_GROUPS_FOR_SCHOOL = 'groupsForSchool:';

    /** @var GroupRepository */
    protected $repository;

    public function __construct(GroupRepository $groupRepository)
    {
        $groupRepository->setCacheRepository($this);

        parent::__construct($groupRepository);
    }

    #[Override]
    public function getGroupsForUserAndSchool(User $user, School $school, ?Schoolyear $schoolyear = null): Collection
    {
        $key = $this->getCacheKey([
            self::KEY_GROUPS_BY_SCHOOL . $school->uid,
            self::KEY_USER . $user->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        return Cache::tags($this->getCacheTags($school))
            ->remember($key, Carbon::tomorrow()->startOfDay(), function () use ($school, $user, $schoolyear) {
                return $this->repository->getGroupsForUserAndSchool($user, $school, $schoolyear);
            });
    }

    #[Override]
    public function getGroupIdsForUserAndSchool(User $user, School $school, Schoolyear $schoolyear): Collection
    {
        $key = $this->getCacheKey([
            self::KEY_GROUP_IDS_BY_SCHOOL . $school->uid,
            self::KEY_USER . $user->uid,
            self::KEY_SCHOOLYEAR . $schoolyear->start->year,
        ]);

        return Cache::tags($this->getCacheTags($school))
            ->remember($key, Carbon::tomorrow()->startOfDay(), function () use ($school, $user, $schoolyear) {
                return $this->repository->getGroupIdsForUserAndSchool($user, $school, $schoolyear);
            });
    }

    #[Override]
    public function getGroupIdsOfGroupsWithPupilsForSchool(School $school, ?Schoolyear $schoolyear = null): Collection
    {
        $key = $this->getCacheKey([
            self::KEY_GROUPS_WITH_PUPILS_BY_SCHOOL . $school->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        return Cache::tags($this->getCacheTags($school))
            ->remember(
                $key,
                Carbon::tomorrow()->startOfDay(),
                fn(): Collection => $this->repository->getGroupIdsOfGroupsWithPupilsForSchool($school, $schoolyear),
            );
    }

    #[Override]
    public function getClassGroupsForUserAndSchool(
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        $key = $this->getCacheKey([
            self::KEY_CLASS_GROUPS_BY_SCHOOL . $school->uid,
            self::KEY_USER . $user->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        return Cache::tags($this->getCacheTags($school))
            ->remember($key, Carbon::tomorrow()->startOfDay(), function () use ($school, $user, $schoolyear) {
                return $this->repository->getClassGroupsForUserAndSchool($user, $school, $schoolyear);
            });
    }

    #[Override]
    public function getClassGroupsForUserAndSchoolHavingReportPeriods(
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        $key = $this->getCacheKey([
            self::KEY_CLASS_GROUPS_HAVING_REPORT_PERIODS_BY_SCHOOL . $school->uid,
            self::KEY_USER . $user->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        return Cache::tags($this->getCacheTags($school))
            ->remember($key, Carbon::tomorrow()->startOfDay(), function () use ($school, $user, $schoolyear) {
                return $this->repository->getClassGroupsForUserAndSchoolHavingReportPeriods(
                    $user,
                    $school,
                    $schoolyear,
                );
            });
    }

    #[Override]
    public function getAllGroupsOfSchool(School $school): Collection
    {
        return Cache::tags($this->getCacheTags($school))
            ->rememberForever(self::KEY_GROUPS_BY_SCHOOL . $school->uid, function () use ($school) {
                return $this->repository->getAllGroupsOfSchool($school);
            });
    }

    #[Override]
    public function getGroupsWithPupilsForUserByTargetAudiences(
        Collection $targetAudiences,
        User $user,
        School $school,
        ?Schoolyear $schoolyear = null,
    ): Collection {
        return $this->repository->getGroupsWithPupilsForUserByTargetAudiences(
            $targetAudiences,
            $user,
            $school,
            $schoolyear,
        );
    }

    #[Override]
    public function getGroupsByTargetAudiences(
        Collection $targetAudiences,
        User $user,
        School $school,
        ?Schoolyear $schoolyear,
    ): Collection {
        return $this->repository->getGroupsByTargetAudiences(
            $targetAudiences,
            $user,
            $school,
            $schoolyear,
        );
    }

    #[Override]
    public function getGroupsForUserBySubjectPermissions(User $user, School $school, Schoolyear $schoolyear): Collection
    {
        return $this->repository->getGroupsForUserBySubjectPermissions($user, $school, $schoolyear);
    }

    #[Override]
    public function getLinkedCalendaritemsWithGoals(
        Group $group,
        array $dateRange,
        string $parentNodeUid,
        ?User $owner = null,
    ): Collection {
        return $this->repository->getLinkedCalendaritemsWithGoals($group, $dateRange, $parentNodeUid, $owner);
    }

    #[Override]
    public function getUsersWithAccessToGroups(School $school, Collection $groups): Collection
    {
        if ($groups->isEmpty()) {
            return collect();
        }

        $keyParts = $groups->sortBy('uid')
            ->map(fn(Group $group): string => self::KEY_USERS_FOR_GROUP . $group->uid)
            ->all();
        $key = $this->getCacheKey($keyParts);

        return Cache::tags($this->getCacheTags($school))
            ->remember(
                $key,
                Carbon::tomorrow()->startOfDay(),
                fn(): Collection => $this->repository->getUsersWithAccessToGroups($school, $groups),
            );
    }

    #[Override]
    public function getPupilsOfGroup(Group $group, ?Schoolyear $schoolyear = null): Collection
    {
        $key = $this->getCacheKey([
            self::KEY_PUPILS_FOR_GROUP . $group->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        return Cache::tags($this->getCacheTags($group->school))
            ->remember($key, Carbon::tomorrow()->startOfDay(), function () use ($group, $schoolyear) {
                return $this->repository->getPupilsOfGroup($group, $schoolyear);
            });
    }

    #[Override]
    public function getHistoricalGroupsForPupilsInSchoolYear(array $pupilsIds, Schoolyear $schoolyear): Collection
    {
        return $this->repository->getHistoricalGroupsForPupilsInSchoolYear($pupilsIds, $schoolyear);
    }

    #[Override]
    public function recentlyAccessedGroupsIds(User $user, School $school, int $count = 5): Collection
    {
        return $this->repository->recentlyAccessedGroupsIds($user, $school, $count);
    }

    #[Override]
    public function flushGroupCacheForSchool(School $school): void
    {
        Cache::tags(self::TAG_GROUPS_FOR_SCHOOL . $school->uid)->flush();
    }

    #[Override]
    public function flushPupilsOfGroup(Group $group, ?Schoolyear $schoolyear = null): void
    {
        $key = $this->getCacheKey([
            self::KEY_PUPILS_FOR_GROUP . $group->uid,
            self::KEY_SCHOOLYEAR . ($schoolyear === null || $schoolyear->isCurrent() ? 'current' :
                $schoolyear->start->year),
        ]);

        Cache::tags($this->getCacheTags($group->school))->forget($key);
    }

    #[Override]
    protected function getCacheTags(?Model $model = null): array
    {
        /* @var School $model */
        return array_merge(
            parent::getCacheTags(),
            [self::TAG_GROUPS_FOR_SCHOOL . $model->uid],
        );
    }

    #[Override]
    public function flush(?Model $model = null): void
    {
        /* @var Group $model */
        Cache::tags(self::TAG_GROUPS_FOR_SCHOOL . $model->school->uid)->flush();
    }
}
