<?php

namespace Cfa\Common\Application\Repositories;

use App\Models\Model;
use App\Repositories\CacheRepository;
use Carbon\Carbon;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Cfa\Common\Domain\User\User;
use Cfa\Common\Domain\User\UserRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Cache;
use Override;

class UserCacheRepository extends CacheRepository implements UserRepositoryInterface
{
    private const KEY_COLLEAGUES_FOR_USER = 'colleaguesForUser:';
    private const KEY_ICT_COORDINATORS_FOR_USER = 'ictCoordinatorsForUser:';
    private const KEY_ACTIVE_ICT_COORDINATORS_FOR_USER = 'activeIctCoordinatorsForUser:';
    private const KEY_FULL_USER = 'getFullUser:';
    private const KEY_INACTIVE_PUPILS = 'inactivePupilsForSchool:';
    private const TAG_SCHOOL = 'userCacheSchool:';

    /**
     * Local cache of the fullUsers on their id.
     *
     * @var array
     */
    protected $fullUsers = [];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getStaff(int $schoolId, ?int $groupId = null): Collection
    {
        return $this->repository->getStaff($schoolId, $groupId);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getTeachersWithAccessToAllGroups(School $school): SupportCollection
    {
        return $this->repository->getTeachersWithAccessToAllGroups($school);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function getColleaguesForUser(User $user): SupportCollection
    {
        $schoolIds = $user->schoolIds()->sort();
        $tags = $this->getCacheTags();
        foreach ($schoolIds as $schoolId) {
            $tags[] = self::TAG_SCHOOL . $schoolId;
        }

        return Cache::tags($tags)
            ->remember(
                self::KEY_COLLEAGUES_FOR_USER . $user->id,
                Carbon::tomorrow()->startOfDay(),
                function () use ($user) {
                    return $this->repository->getColleaguesForUser($user);
                },
            );
    }

    /** @return SupportCollection|User[] */
    #[Override]
    public function getIctCoordinatorsForUser(User $user): SupportCollection
    {
        return Cache::tags($this->getCacheTags())
            ->remember(
                self::KEY_ICT_COORDINATORS_FOR_USER . $user->id,
                Carbon::tomorrow()->startOfDay(),
                function () use ($user) {
                    return $this->repository->getIctCoordinatorsForUser($user);
                },
            );
    }

    /** @return SupportCollection|User[] */
    #[Override]
    public function getActiveIctCoordinatorsForUser(User $user): SupportCollection
    {
        return Cache::tags($this->getCacheTags())
            ->remember(
                self::KEY_ACTIVE_ICT_COORDINATORS_FOR_USER . $user->id,
                Carbon::tomorrow()->startOfDay(),
                function () use ($user) {
                    return $this->repository->getActiveIctCoordinatorsForUser($user);
                },
            );
    }

    /**
     * {@inheritdoc}
     *
     * @param string $userUid The uid of the user.
     */
    #[Override]
    public function getFullUser(string $userUid): User
    {
        if (!array_key_exists($userUid, $this->fullUsers)) {
            $this->fullUsers[$userUid] = Cache::tags($this->getCacheTags())
                ->remember(
                    self::KEY_FULL_USER . $userUid,
                    Carbon::tomorrow()->startOfDay(),
                    fn(): User => $this->repository->getFullUser($userUid),
                );
        }

        return $this->fullUsers[$userUid];
    }

    /**
     * {@inheritdoc}
     *
     * @return Collection|User[]
     */
    #[Override]
    public function getInactivePupilsForSchool(School $school): Collection
    {
        return Cache::tags($this->getCacheTags())
            ->remember(
                self::KEY_INACTIVE_PUPILS . $school->id,
                Carbon::tomorrow()->startOfDay(),
                function () use ($school) {
                    return $this->repository->getInactivePupilsForSchool($school);
                },
            );
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function flushUserCaches(int $userId): void
    {
        $schoolyearRepository = Schoolyear::getRepository();
        Cache::tags($this->getCacheTags())->forget(self::KEY_ICT_COORDINATORS_FOR_USER . $userId);
        Cache::tags($this->getCacheTags())->forget(self::KEY_ACTIVE_ICT_COORDINATORS_FOR_USER . $userId);
        $schoolIds = SchoolUserAccess::whereUserId($userId)->pluck('school_id');
        Cache::tags($schoolIds->map(fn(int $schoolId): string => self::TAG_SCHOOL . $schoolId)->values()->all())
            ->flush();
        $schoolIds->each(
            function (int $schoolId) use ($userId, $schoolyearRepository): void {
                $schoolyearRepository->flushForUserAndSchool($userId, $schoolId);
                Cache::tags($this->getCacheTags())->forget(self::KEY_INACTIVE_PUPILS . $schoolId);
            },
        );

        $this->flushFullUserCache($userId);
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function flushFullUserCache(int $userId): void
    {
        $userUid = User::withTrashed()->whereId($userId)->value('uid');
        unset($this->fullUsers[$userUid]);

        Cache::tags($this->getCacheTags())->forget(self::KEY_FULL_USER . $userUid);
    }

    /**
     * {@inheritdoc}
     *
     * Don't cache the user on tenant. This creates an infinite loop, as the tenant is fetched using the user.
     */
    #[Override]
    protected function getCacheTags(?Model $model = null): array
    {
        return [
            'repository:' . static::class,
        ];
    }

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function flush(?Model $model = null): void
    {
        /* @var User $model */
        $this->flushUserCaches($model->id);
    }
}
