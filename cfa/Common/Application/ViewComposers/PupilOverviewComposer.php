<?php

namespace Cfa\Common\Application\ViewComposers;

use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\Route;
use Illuminate\View\View;

class PupilOverviewComposer
{
    /**
     * Provide a list of the pupils of the given group of the current user.
     *
     * @param View $view The view to provide the group list for.
     */
    public function compose(View $view): void
    {
        $pupils = collect();

        if ($selectedGroup = group()) {
            $schoolyear = Route::current()->parameter('schoolyear', null);

            $pupils = app(GroupRepositoryInterface::class)
                ->getPupilsOfGroup($selectedGroup, $schoolyear)
                ->values()
                ->map(function (User $pupil) {
                    return [
                        'uid' => $pupil->uid,
                        'name' => $pupil->fullname,
                        'disabled' => $pupil->careInfos->first()->pupil_status->isDisabled(),
                    ];
                });
        }

        $view->with('pupils', $pupils);
    }
}
