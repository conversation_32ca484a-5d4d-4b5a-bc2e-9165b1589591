<?php

namespace Cfa\Common\Application\Jobs\Wisa;

use App\Constants\Queues;
use Cfa\Common\Application\Services\Wisa\WisaApiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use function app;

class RequestWisaCareDataForUser implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private string $wisaUserUid;

    public function __construct(string $wisaUserUid)
    {
        $this->queue = Queues::MEDIUM_PRIORITY;
        $this->wisaUserUid = $wisaUserUid;
    }

    public function handle(): void
    {
        app(WisaApiService::class)->requestWisaCareDataForUser($this->wisaUserUid);
    }
}
