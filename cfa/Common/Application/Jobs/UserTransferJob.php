<?php

namespace Cfa\Common\Application\Jobs;

use Cfa\Admin\Application\Cms\Services\SyncModels\TransferAccountsService;
use Cfa\Admin\Domain\MergeLog\MergeLogType;
use Cfa\Common\Domain\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserTransferJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(private readonly User $sourceUser, private readonly User $destinationUser) {}

    public function handle(TransferAccountsService $transferAccountsService): void
    {
        $transferAccountsService->transfer(
            $this->sourceUser,
            $this->destinationUser,
            MergeLogType::UserTransfer,
        );
    }
}
