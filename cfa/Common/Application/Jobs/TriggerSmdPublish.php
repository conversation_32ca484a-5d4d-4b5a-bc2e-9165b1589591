<?php

namespace Cfa\Common\Application\Jobs;

use App\Constants\Queues;
use Cfa\Common\Application\Services\Smd\SmdApiService;
use Cfa\Common\Domain\School\School;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TriggerSmdPublish implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * @var int
     */
    private $schoolId;

    public function __construct(School $school)
    {
        $this->queue = Queues::MEDIUM_PRIORITY;
        $this->schoolId = $school->id;
    }

    /**
     * Execute the job.
     */
    public function handle(SmdApiService $smdApiService): void
    {
        $school = School::findOrFail($this->schoolId);
        $smdApiService
            ->publishFullBingelSchool($school)
            ->wait();
    }
}
