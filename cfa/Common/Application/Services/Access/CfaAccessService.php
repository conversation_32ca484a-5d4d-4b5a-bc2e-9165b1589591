<?php

namespace Cfa\Common\Application\Services\Access;

use App\Constants\Subdomain;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Str;

use function get_subdomain;

class CfaAccessService
{
    /**
     * Determine if a user has access to a certain customer facing application.
     *
     * @param User $user The authed user.
     * @param School $school The given school.
     */
    public function accessCfa(User $user, School $school): bool
    {
        $application = get_subdomain();
        if (empty($application)) {
            Context::push(SchoolHelperService::class, 'empty($application)');

            return false;
        }

        if ($application === Subdomain::TMS) {
            $application = Subdomain::PLANNER;
        }

        if (!$this->hasActiveCareerForSchool($user, $school)) {
            Context::push(SchoolHelperService::class, '!$this->hasActiveCareerForSchool($user, $school)');
            Context::push(SchoolHelperService::class, ['careers' => $user->careers->pluck('uid')]);

            return false;
        }

        $permissionCase = 'HasAccessTo' . Str::ucfirst(Str::lower($application));
        $permission = PermissionName::tryFromName($permissionCase);
        if ($permission !== null) {
            Context::push(SchoolHelperService::class, 'Permission name is defined: ' . $permissionCase);

            return $this->accessWithPermission($permission, $user, $school);
        }
        Context::push(SchoolHelperService::class, 'Permission name undefined: ' . $permissionCase);

        return false;
    }

    /**
     * Determine whether the user has access to a specific part of the cfa as user in specific school.
     *
     * @param PermissionName $permissionName The permission a user has to have to access the part of the cfa.
     * @param User $user Authenticated user.
     * @param School|null $school School item to check.
     *
     * @throws AuthorizationException Thrown if the user has no schools.
     */
    public function accessWithPermission(PermissionName $permissionName, User $user, ?School $school = null): bool
    {
        if ($school === null) {
            $school = school();
        }

        return $user->hasPermission($permissionName, $school);
    }

    /**
     * Determine if the user has an active career for a given school.
     *
     * @throws NoActiveSchoolsException
     */
    public function hasActiveCareerForSchool(User $user, School $school): bool
    {
        return $user->hasActiveCareersForSchool($school->id);
    }
}
