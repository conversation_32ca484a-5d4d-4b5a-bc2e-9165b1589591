<?php

namespace Cfa\Common\Application\Services\Wisa;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Application\Exceptions\NationalRegisterNumberStoreService\MultipleMatchingPupilsException;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Wisa\Domain\CareData\WisaCareData;

class WisaCareDataMatcher
{
    public function findModelByWisaCareData(
        User $user,
        School $school,
        string $firstName,
        string $lastName,
        string $activeGroupName,
        int $classNumber,
    ): WisaCareDataMatch {
        // If there is exactly one pupil in the school, with exactly the same name as the incoming pupil,
        // we can be certain (enough) that the pupil is the same person in reality.
        // If and when we also get group data, we might also check the target audience to limit possible matches.
        $careDataMatches = WisaCareData::where('first_name', $firstName)
            ->where('last_name', $lastName)
            ->where('school_id', $school->id)
            ->where('group_name', $activeGroupName)
            ->where('class_number', $classNumber)
            ->get(['wisa_uid', 'national_register_number_hash', 'care_data']);
        if ($careDataMatches->count() !== 1) {
            return new WisaCareDataMatch($user, null);
        }

        // If there is an existing care info with the same national register number hash, we can use the pupil linked
        // to that care info. If not, we just make sure the care info gets created using the national register number.
        /** @var WisaCareData $careDataMatch */
        $careDataMatch = $careDataMatches->first();
        $careInfoMatch = $this->findCareInfoMatch(
            $careDataMatch->wisa_uid,
            $careDataMatch->national_register_number_hash,
            $school,
        );

        // If we don't have a national register number, try to find a unique and exact name match.
        if ($careInfoMatch === null && $careDataMatch->national_register_number_hash === null) {
            $user = $this->findUserMatchByName($firstName, $lastName, $school) ?? $user;
        }

        if ($careInfoMatch !== null) {
            $user = User::withoutGlobalScopes()->find($careInfoMatch->pupil_id) ?? $user;
        }

        return new WisaCareDataMatch($user, $careDataMatch);
    }

    private function findUserMatchByName(string $firstName, string $lastName, School $school): ?User
    {
        $userMatch = User::withoutGlobalScopes()
            ->join('care_infos', 'users.id', 'care_infos.pupil_id')
            ->where('firstname', $firstName)
            ->where('lastname', $lastName)
            ->where('school_id', $school->id)
            ->whereNull('national_register_number')
            ->whereNull('wisa_national_register_number')
            ->whereNull('care_infos.deleted_at')
            ->get(['users.*']);

        if ($userMatch->count() !== 1) {
            $userMatch = null;
        }

        return $userMatch?->first();
    }

    private function findCareInfoMatch(string $wisaUid, ?string $nationalRegisterNumber, School $school): ?CareInfo
    {
        $fields = ['pupil_id', 'wisa_uid'];
        $careInfoMatches = CareInfo::where('school_id', $school->id)->where('wisa_uid', $wisaUid)->get($fields);

        if ($careInfoMatches->isEmpty() && $nationalRegisterNumber !== null) {
            $careInfoMatches = CareInfo::where('school_id', $school->id)
                ->where('wisa_national_register_number', $nationalRegisterNumber)
                ->get($fields);
        }

        if ($careInfoMatches->count() > 1) {
            throw new MultipleMatchingPupilsException($careInfoMatches->map->pupilWithTrashed->all());
        }

        return $careInfoMatches->first();
    }
}
