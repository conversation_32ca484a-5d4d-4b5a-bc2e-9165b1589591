<?php

namespace Cfa\Common\Application\Services\Helpers;

use App\Constants\Cookies;
use App\Constants\SessionKeys;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Application\Services\Access\CfaAccessService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Context;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Response;
use Route;
use Session;

use function app;
use function in_array;
use function is_null;
use function json_decode;

class SchoolHelperService
{
    /**
     * The school specified in the cookies of the request.
     *
     * @var School
     */
    private $school;

    /**
     * The school cookie, this is the current value of the school in the cookie.
     *
     * @var School
     */
    private $schoolCookie;

    /**
     * Get the request through the request helper.
     * This is needed because you can't mock the Request. This way you only need to mock this function.
     *
     * @codeCoverageIgnore
     */
    protected function getRequest(): Request|string|array|null
    {
        return request();
    }

    /**
     * Retrieve the selected school related to the request and sets the cookie if necessary.
     * If a school is given, that school will be returned and cookie wil be set.
     *
     * @param School|null $school School that has to be saved for this user.
     *
     * @throws NoActiveSchoolsException Thrown if the cookie is empty.
     */
    public function school(?School $school = null): School
    {
        Context::push(self::class, [
            'function_param_school' => $school?->getRawOriginal(),
            'local_cache' => $this->school?->getRawOriginal(),
        ]);
        // If local cache is available, and school isn't given, use it!
        if ($this->school && (is_null($school) || $school->uid === $this->school->uid)) {
            return $this->school;
        }

        $school ??= $this->getCurrentSchoolOrFallback();

        // If a school is found, save it to the cookie.
        if ($school) {
            $this->setSchoolCookie($school);

            return $school;
        }

        throw new NoActiveSchoolsException();
    }

    /**
     * When viewing a pupil return his active school.
     */
    protected function getSchoolFromPupil(): ?School
    {
        if (Route::current() !== null && $selectedPupil = Route::current()->parameter('pupil')) {
            if (!$selectedPupil instanceof User) {
                return null;
            }

            return $selectedPupil->activeSchools()->first()
                ?: School::find($selectedPupil->lastCareers()->pluck('school_id')->first());
        }

        return null;
    }

    protected function getSchoolFromGroup(): ?School
    {
        if (Route::current() !== null && $selectedGroup = Route::current()->parameter('group')) {
            if (!$selectedGroup instanceof Group) {
                return null;
            }

            return $selectedGroup->school;
        }

        return null;
    }

    /**
     * Retrieve the selected school from the cookie.
     */
    protected function getSchoolFromCookie(): ?School
    {
        $request = $this->getRequest();

        if ($school = $request->cookie(Cookies::selectedSchool())) {
            $this->schoolCookie = json_decode($school);

            return (!isset($this->school) || $this->schoolCookie?->id !== $this->school->id) ?
                School::find($this->schoolCookie->id) : null;
        }

        return null;
    }

    /**
     * Retrieve school from the current route.
     */
    protected function getSchoolFromRoute(): ?School
    {
        if (Route::current() !== null && Route::current()->parameter('school')) {
            return Route::current()->parameter('school');
        }

        return null;
    }

    /**
     * Attach School cookie to the outgoing response.
     *
     * @param School $school The school to be set in the cookie.
     */
    protected function setSchoolCookie(School $school): void
    {
        $this->school = $school;

        // make certain schoolCookie is set if a cookie is available.
        $this->getSchoolFromCookie();

        if (!$this->schoolCookie || $this->schoolCookie->id !== $school->id) {
            $this->schoolCookie = (object) $school->only(['id', 'uid']);
            Cookie::queue(
                Cookie::forever(
                    Cookies::selectedSchool(),
                    json_encode($this->schoolCookie),
                    '/',
                    config('session.base_domain'),
                ),
            );
            app(GroupHelperService::class)->setGroupCookie(null);
        }
    }

    protected function getCurrentSchoolOrFallback(): ?School
    {
        Context::push(self::class, 'call getCurrentSchoolOrFallback()');
        $user = $this->getRequest()->user();

        if ($user instanceof User === false) {
            Context::push(self::class, '$user instanceof User === false');

            return null;
        }
        Context::push(self::class, ['$user->uid' => $user?->uid]);
        $school = $this->getCurrentSchoolFromRouteOrCookie();
        Context::push(self::class, '$school = $this->getCurrentSchoolFromRouteOrCookie();');
        Context::push(self::class, ['school' => $school?->getRawOriginal()]);

        if ($school && !$user->career($school->id)) {
            Context::push(self::class, '$school && !$user->career($school->id)');
            $school = null;
        }

        $cfaService = app(CfaAccessService::class);

        if ($school && !$cfaService->accessCfa($user, $school)) {
            Context::push(self::class, '($school && !$cfaService->accessCfa($user, $school))');
            $school = null;
        }

        if (is_null($school)) {
            Context::push(self::class, '$school is_null');
            $firstSchool = $user->activeSchools()
                ->first(fn(School $activeSchool) => $cfaService->accessCfa($user, $activeSchool));
            Context::push(self::class, ['$firstSchool' => $firstSchool?->getRawOriginal()]);
            if (
                ($school = $firstSchool) &&
                in_array('can:accessCfa,' . School::class, Route::current()?->gatherMiddleware() ?? [], true)
            ) {
                Context::push(self::class, 'abort redirectToRoute: ' . $school->uid);
                abort(Response::redirectToRoute('web.common.schools.switch', ['school' => $school->uid]));
            }
        }

        return $school;
    }

    protected function getCurrentSchoolFromRouteOrCookie(): ?School
    {
        $school = null;
        // Get school from route if available.
        if (is_null($school) && $school = $this->getSchoolFromRoute()) {
            return $school;
        }

        // Get school from pupil if set in the current route.
        if (is_null($school)) {
            $school = $this->getSchoolFromPupil();
        }

        // Get school from group if set in the current route.
        if (is_null($school)) {
            $school = $this->getSchoolFromGroup();
        }

        // Get school from Bingel if set in session
        if (is_null($school)) {
            $school = Session::pull(SessionKeys::PREFERRED_BINGEL_SCHOOL);
        }

        // Get school from cookie.
        if (is_null($school)) {
            $school = $this->getSchoolFromCookie();
        }

        return $school;
    }
}
