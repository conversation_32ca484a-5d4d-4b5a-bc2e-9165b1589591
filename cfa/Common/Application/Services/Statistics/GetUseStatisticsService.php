<?php

namespace Cfa\Common\Application\Services\Statistics;

use Carbon\Carbon;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\Permission\PermissionSchool;
use Cfa\Common\Domain\School\SchoolMfaStatus;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\School\Settings\SchoolSettingsType;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Mfa\Status\MfaStatus;
use Cfa\Common\Domain\User\Mfa\UserMfa;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class GetUseStatisticsService
{
    public const DAYS_BETWEEN_MAX_AND_MIN_STARTDATE = 14;
    public const MINIMUM_CALENDAR_ITEMS = 100;
    public const MINIMUM_CARE_INPUT_ITEMS = 10;

    /**
     * Startdate, first day of this schoolyear
     *
     * @var string
     */
    protected $schoolYearStart;

    /**
     * The reference date
     *
     * @var Carbon
     */
    protected $referenceDate;

    /**
     * GetUseStatisticsService constructor.
     *
     * @param Carbon $date Reference date.
     */
    public function __construct(Carbon $date)
    {
        $this->referenceDate = $date;

        $this->schoolYearStart =
            Schoolyear::getRepository()->findSchoolyearByDate($this->referenceDate)->start->subMonth(3)->toDateString();
    }

    /**
     * Gets the enddate used in where conditions for respective queries
     *
     * @param int $daysBack Calculates a date string for a given days back in time.
     */
    protected function getEndDate(int $daysBack): string
    {
        return $this->referenceDate->copy()->subDays($daysBack)->toDateString();
    }

    /**
     * Get the base query for fetching the number of active planner users.
     *
     * Users are active when they have over MINIMUM_CALENDAR_ITEMS items
     * in a DAYS_BETWEEN_MAX_AND_MIN_STARTDATE-day timespan
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    private function getPlannerUserCountQuery(int $daysBack = 0): Builder
    {
        return DB::table('calendaritems')
            ->select('school_id', DB::raw('owner_id AS user_id'))
            ->whereNotNull('calendaritems.owner_id')
            ->where('calendaritems.start', '>', $this->schoolYearStart)
            ->where('calendaritems.created_at', '<', $this->getEndDate($daysBack))
            ->whereNull('calendaritems.deleted_at')
            ->groupBy(['school_id', 'owner_id'])
            ->having(
                DB::raw('datediff(max(calendaritems.start), min(calendaritems.start))'),
                '>',
                self::DAYS_BETWEEN_MAX_AND_MIN_STARTDATE,
            )
            ->having(DB::raw('count(calendaritems.id)'), '>', self::MINIMUM_CALENDAR_ITEMS);
    }

    /**
     * Get the number of active planner users.
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    public function getPlannerUserCount(int $daysBack = 0): int
    {
        return DB::table($this->getPlannerUserCountQuery($daysBack), 'planner_user_count')
            ->count(DB::raw('DISTINCT user_id'));
    }

    /**
     * Get an array with the distinct school count.
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    public function getPlannerSchoolCount(int $daysBack = 0): int
    {
        return DB::table($this->getPlannerUserCountQuery($daysBack), 'planner_user_count')
            ->count(DB::raw('DISTINCT school_id'));
    }

    /**
     * Get the number of active care users.
     *
     * Users are active when they have created MINIMUM_CARE_INPUT_ITEMS this schoolyear.
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    private function getCareUserCountQuery(int $daysBack = 0): Builder
    {
        return DB::table('care_inputs')
            ->select('school_id', DB::raw('creator_id AS user_id'))
            ->where('care_inputs.created_at', '>', $this->schoolYearStart)
            ->where('care_inputs.created_at', '<', $this->getEndDate($daysBack))
            ->groupBy(['school_id', 'creator_id'])
            ->having(DB::raw('count(care_inputs.id)'), '>', self::MINIMUM_CARE_INPUT_ITEMS);
    }

    public function getTotalCareUserCount(): int
    {
        $enddate = $this->getEndDate(0);

        return DB::table('careers')
            ->join('permission_schools', 'permission_schools.school_id', 'careers.school_id')
            ->join('roles', 'roles.id', 'careers.role_id')
            ->where('permission_schools.name', PermissionName::HasAccessToCare)
            ->where('careers.startdate', '<=', $enddate)
            ->where(
                fn($query) =>
                $query->where('careers.enddate', '>', $enddate)
                    ->orWhereNull('careers.enddate'),
            )
            ->whereIn('roles.role_name_enum', RoleName::getStaffRoles())
            ->where('permission_schools.startdate', '<=', $enddate)
            ->where(
                fn($query) =>
                $query->where('permission_schools.enddate', '>', $enddate)
                    ->orWhereNull('permission_schools.enddate'),
            )
            ->whereNull('careers.deleted_at')
            ->whereNull('permission_schools.deleted_at')
            ->count(DB::raw('DISTINCT careers.user_id'));
    }

    /**
     * Get the number of active care users.
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    public function getCareUserCount(int $daysBack = 0): int
    {
        return DB::table($this->getCareUserCountQuery($daysBack), 'care_user_count')
            ->count(DB::raw('DISTINCT user_id'));
    }

    /**
     * Gets a count from the unique schools where care-notes are created.
     *
     * @param int $daysBack Fetch the data from x days ago.
     */
    public function getCareSchoolCount(int $daysBack = 0): int
    {
        return DB::table($this->getCareUserCountQuery($daysBack), 'care_user_count')
            ->count(DB::raw('DISTINCT school_id'));
    }

    public function getTotalCareSchoolCount(int $daysBack = 0): int
    {
        $enddate = $this->getEndDate($daysBack);

        return PermissionSchool::where('name', PermissionName::HasAccessToCare)
            ->where('startdate', '<=', $enddate)
            ->where(
                fn($query) =>
                $query->where('enddate', '>', $enddate)
                    ->orWhereNull('enddate'),
            )
            ->distinct()
            ->count();
    }

    public function getMfaSchoolCount(): int
    {
        return SchoolSettings::where('type', SchoolSettingsType::MfaStatus)
            ->where('value', SchoolMfaStatus::Active->value)
            ->count();
    }

    public function getMfaActiveUserCount(): int
    {
        return UserMfa::where('status', MfaStatus::Active)->count();
    }

    public function getMfaResetRequestedUserCount(): int
    {
        return UserMfa::where('status', MfaStatus::ResetRequested)->count();
    }

    public function getMfaRequestedUserCount(): int
    {
        return UserMfa::where('status', MfaStatus::MfaRequested)->count();
    }
}
