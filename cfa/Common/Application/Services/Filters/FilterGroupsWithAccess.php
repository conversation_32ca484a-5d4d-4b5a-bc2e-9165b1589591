<?php

namespace Cfa\Common\Application\Services\Filters;

use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\Group\GroupRepositoryInterface;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Collection;

class FilterGroupsWithAccess
{
    private ?Schoolyear $schoolyear = null;

    public function __construct(private readonly GroupRepositoryInterface $groupRepository) {}

    public function forSchoolyear(?Schoolyear $schoolyear): self
    {
        $this->schoolyear = $schoolyear;

        return $this;
    }

    public function __invoke(User $user, School $school, Collection $groups): Collection
    {
        if ($user->hasPermission(PermissionName::HasAccessToAllGroups, $school)) {
            return $groups;
        }

        $groupIds = $this->schoolyear === null
            ? $user->activeCareerGroups($school->id)->pluck('id')
            : $this->groupRepository->getGroupIdsForUserAndSchool($user, $school, $this->schoolyear);

        return $groups->keyBy('id')->intersectByKeys($groupIds->flip())->values();
    }
}
