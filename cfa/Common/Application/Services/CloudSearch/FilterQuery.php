<?php

namespace Cfa\Common\Application\Services\CloudSearch;

use Illuminate\Support\Collection;

use function is_string;
use function sprintf;

abstract class FilterQuery
{
    protected const SEARCH_TYPE_KEY = 'search_type';
    protected const SEARCH_NAME_KEY = 'search_name';

    /**
     * The collection to internally hold all filter fields.
     *
     * @var Collection
     */
    private $filterQuery;

    /**
     * Initializes the filterQuery collection. More filters can be appended to it later.
     *
     * @param string $cloudSearchType The type to search for.
     */
    protected function initializeForType(string $cloudSearchType): void
    {
        $this->filterQuery = new Collection();

        $this->addField(self::SEARCH_TYPE_KEY, $cloudSearchType);
    }

    /**
     * Add a field to the filter query
     *
     * @param string $key The name of the field to filter on.
     * @param string $value The value to filter on.
     *
     * @return static
     */
    public function addField(string $key, string $value): self
    {
        $this->filterQuery->put($key, $value);

        return $this;
    }

    /**
     * Concatenate all fields into a single string.
     *
     * @param string $glue Used to concat the different filter fields.
     */
    public function toString(string $glue = ' '): string
    {
        return $this->filterQuery->map(function ($item, $key) {
            return is_string($key) ? $key . ":'$item'" : $item;
        })->implode($glue);
    }

    /**
     * Concat the filter query to a string that can be used to query with.
     *
     * @param null|string $operator The logical operator used in the query.
     */
    public function toQueryString(string $operator = 'and'): string
    {
        return sprintf(
            '(%s %s)',
            $operator,
            $this->toString(),
        );
    }

    /**
     * Returns the base filter fields as an array.
     *
     * @see CloudSearchService::getFieldsCleaned()
     */
    public function getBaseKeyValuePairs(): array
    {
        return $this->filterQuery->only([self::SEARCH_TYPE_KEY, self::SEARCH_NAME_KEY])->all();
    }
}
