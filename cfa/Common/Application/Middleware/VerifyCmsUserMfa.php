<?php

namespace Cfa\Common\Application\Middleware;

use App\Constants\SessionKeys;
use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Fortify\Actions\DisableTwoFactorAuthentication;
use Symfony\Component\HttpFoundation\Response;

use function app;
use function in_array;

class VerifyCmsUserMfa
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if ($user === null || $this->isIgnoredRoute($request) || $this->isMfaDisabled()) {
            return $next($request);
        }

        if (!$user->hasEnabledTwoFactorAuthentication() && !$request->wantsJson()) {
            // This allows the user to start the MFA setup wizard, without it, it could be shown as finished.
            app(DisableTwoFactorAuthentication::class)($user);

            return redirect()->route('nova.pages.user-security');
        }

        if ($request->session()->get(SessionKeys::CMS_MFA_VERFIED, false) === $user->id) {
            return $next($request);
        }

        return redirect()->route('nova.pages.login');
    }

    private function isIgnoredRoute(Request $request): bool
    {
        return $request->is(
            'nova/login',
            'nova/password/*',
            'nova/logout',
            'nova/mfa/2fa/qr',
            'nova/user-security',
            'nova/user-security/two-factor-authentication',
            'nova/user-security/two-factor-challenge',
            'nova/user-security/confirm-password',
            'nova/user-security/two-factor-secret-key',
            'nova/user-security/two-factor-recovery-codes',
            'nova/user-security/two-factor-qr-code',
            'nova/user-security/confirmed-two-factor-authentication',
        );
    }

    private function isMfaDisabled(): bool
    {
        return in_array(app()->environment(), ['local', 'testing']);
    }
}
