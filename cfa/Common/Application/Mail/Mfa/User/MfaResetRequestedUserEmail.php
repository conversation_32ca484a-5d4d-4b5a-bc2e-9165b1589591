<?php

namespace Cfa\Common\Application\Mail\Mfa\User;

use App\Constants\Queues;
use Cfa\Common\Domain\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

use function config;
use function trans;

class MfaResetRequestedUserEmail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public User $user;

    public function __construct(User $user)
    {
        $this->queue = Queues::MEDIUM_PRIORITY;
        $this->user = $user;
    }

    public function build(): Mailable
    {
        return $this->from(config('mail.from.address'), config('mail.from.name'))
            ->subject(trans('emails.mfa.user.reset-requested.subject'))
            ->view('mail.mfa.user.reset-requested');
    }
}
