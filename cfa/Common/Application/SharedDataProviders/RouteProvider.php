<?php

namespace Cfa\Common\Application\SharedDataProviders;

use Illuminate\Contracts\Support\Arrayable;
use Override;

use function route;
use function tenant;

class RouteProvider implements Arrayable
{
    #[Override]
    public function toArray(): array
    {
        return [
            'web.common.profile' => route('web.common.profile'),
            'web.common.users.data-transfer' => route('web.common.users.data-transfer'),
            'web.common.user-settings' => route('web.common.user-settings'),
            'web.common.schools' => route('web.common.schools'),
            'web.common.help' => route('web.common.help'),
            'web.common.mfa.management' => route('web.common.mfa.management'),
            'logout' => route('tms.logout'),
            'return' => config('tenants.' . tenant()->uid . '.externalUrls')['return'],
        ];
    }
}
