<?php

namespace Cfa\Common\Application\Controllers\Dashboard;

use App\Controllers\Controller;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalStructureResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class FollowUpSystemStructureController extends Controller
{
    public function __invoke(
        FollowUpSystemGoalRepositoryInterface $followUpSystemGoalRepository,
        Group $group,
        FollowUpSystem $followUpSystem,
    ): JsonResponse {
        $goalTree = $this->flattenTree(
            $followUpSystemGoalRepository->getGoalTreeStructureForFollowUpSystem($followUpSystem),
        );

        return $this->respond(FollowUpSystemGoalStructureResource::collection($goalTree));
    }

    private function flattenTree(Collection $tree): Collection
    {
        $result = collect();
        $tree->each(function ($treeChild) use (&$result): void {
            $result->push($treeChild);
            $result = $result->concat($this->flattenTree($treeChild->descendants)->all());
        });

        return $result;
    }
}
