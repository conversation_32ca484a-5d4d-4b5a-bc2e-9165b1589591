<?php

namespace Cfa\Common\Application\Controllers\Settings\Report\Period;

use App\Http\Requests\FormRequest;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Override;

class ReportPeriodSaveRequest extends FormRequest
{
    #[Override]
    public function rules(): array
    {
        $rules = [
            'periods' => [
                'required',
                'array',
            ],
            'periods.*.id' => [
                'required',
                'string',
                'uuid',
            ],
            'periods.*.name' => [
                'required',
                'string',
            ],
            'periods.*.short_name' => [
                'required',
                'string',
                'max:' . ReportPeriod::MAX_SHORT_NAME_LENGTH,
            ],
            'periods.*.end' => [
                'date',
                'required',
            ],
        ];

        return [...parent::rules(), ...$rules];
    }
}
