<?php

namespace Cfa\Common\Application\Controllers\Settings\Report\Overview;

use App\Http\Requests\FormRequest;
use Override;

class ReportOverviewSaveRequest extends FormRequest
{
    public const REPORT_NAME = 'report_name';
    public const TARGET_AUDIENCES = 'target_audiences';

    #[Override]
    public function rules(): array
    {
        return [
            self::REPORT_NAME => [
                'string',
                'required',
                'max:255',
            ],
            self::TARGET_AUDIENCES => [
                'present',
                'array',
            ],
        ];
    }

    public function getTargetAudiences(): array
    {
        return $this->get(self::TARGET_AUDIENCES);
    }

    public function getName(): string
    {
        return $this->get(self::REPORT_NAME);
    }
}
