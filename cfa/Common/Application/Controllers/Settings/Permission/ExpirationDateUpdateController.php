<?php

namespace Cfa\Common\Application\Controllers\Settings\Permission;

use App\Controllers\Controller;
use Cfa\Common\Application\Services\Permission\PermissionService;
use Cfa\Common\Domain\User\User;
use Illuminate\Http\JsonResponse;

class ExpirationDateUpdateController extends Controller
{
    public function __invoke(
        User $userToUpdate,
        ExpirationDateUpdateRequest $request,
        PermissionService $permissionService,
    ): JsonResponse {
        $permissionService->updateExpirationDateForUser($request->getExpirationDate(), $userToUpdate, school());

        return $this->respondNoContent();
    }
}
