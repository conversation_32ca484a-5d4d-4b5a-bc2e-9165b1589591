<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\Goal;

use App\Controllers\Controller;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoalRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\Level\FollowUpSystemLevel;
use DB;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

use function collect;

class FollowUpSystemGoalMoveController extends Controller
{
    public function __invoke(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoalMoveRequest $moveRequest,
        FollowUpSystemGoalRepositoryInterface $followUpSystemGoalRepository,
    ): JsonResponse {
        DB::transaction(
            fn() => $this->moveGoals(
                $followUpSystem,
                $moveRequest->getGoalToMove(),
                $moveRequest->getTargetGoal(),
            ),
        );

        $followUpSystemGoalRepository->flushCacheForFollowUpSystem($followUpSystem);

        return $this->respondNoContent();
    }

    protected function moveGoals(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoal $goalToMove,
        ?FollowUpSystemGoal $targetGoal,
    ): void {
        $levels = FollowUpSystemLevel::withTrashed()
            ->where('follow_up_system_id', $followUpSystem->id)
            ->orderBy('order')
            ->get(['id', 'order', 'deleted_at'])
            ->keyBy('order');

        /** @var null|FollowUpSystemLevel $nextLevel */
        $nextLevel = ($targetGoal === null) ? $levels->get(0) : $levels
            ->get($targetGoal->followUpSystemLevel->order + 1);

        $this->restoreLevelIfTrashed($nextLevel);

        $goalToMove->parent_id = $targetGoal?->id;
        $goalToMove->order = -1;
        $goalToMove->follow_up_system_level_id = $nextLevel->id;
        $goalToMove->save();

        $this->updateLevelsOfGoalChildren($followUpSystem, collect($goalToMove->id), $nextLevel, $levels);

        FollowUpSystemLevel::where('follow_up_system_levels.follow_up_system_id', $followUpSystem->id)
            ->leftJoin(
                'follow_up_system_goals',
                fn(JoinClause $joinClause): JoinClause => $joinClause
                    ->whereColumn(
                        'follow_up_system_levels.id',
                        'follow_up_system_goals.follow_up_system_level_id',
                    )
                    ->whereNull('follow_up_system_goals.deleted_at'),
            )
            ->whereNull('follow_up_system_goals.id')
            ->delete();
    }

    protected function updateLevelsOfGoalChildren(
        FollowUpSystem $followUpSystem,
        Collection $parentGoalIds,
        FollowUpSystemLevel $parentLevel,
        Collection $levels,
    ): void {
        $childrenGoalIds = FollowUpSystemGoal::whereIn('parent_id', $parentGoalIds)
            ->where('follow_up_system_id', $followUpSystem->id)
            ->pluck('id');

        /** @var null|FollowUpSystemLevel $nextLevel */
        $nextLevel = $levels->get($parentLevel->order + 1);
        if ($nextLevel !== null) {
            $this->restoreLevelIfTrashed($nextLevel);
            FollowUpSystemGoal::whereIn('id', $childrenGoalIds)
                ->where('follow_up_system_id', $followUpSystem->id)
                ->update(['follow_up_system_level_id' => $nextLevel->id]);
            $this->updateLevelsOfGoalChildren($followUpSystem, $childrenGoalIds, $nextLevel, $levels);
        }
    }

    private function restoreLevelIfTrashed(FollowUpSystemLevel $level): void
    {
        if ($level->trashed()) {
            FollowUpSystemLevel::withTrashed()
                ->where('id', $level->id)
                ->update(['deleted_at' => null]);
            $level->deleted_at = null;
        }
    }
}
