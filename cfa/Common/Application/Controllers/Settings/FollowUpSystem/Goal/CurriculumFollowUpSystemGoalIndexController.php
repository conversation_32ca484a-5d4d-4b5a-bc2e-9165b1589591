<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\Goal;

use Cfa\Evaluation\Application\Services\FollowUpSystem\Level\FollowUpSystemLevelService;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemLevelDepth;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Level\FollowUpSystemLevel;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Illuminate\Support\Collection;
use Illuminate\View\View;

class CurriculumFollowUpSystemGoalIndexController
{
    private ?int $maxNumberOfFollowUpSystemLevels;

    /**
     * Returns a tree of the curriculumnodes nodes and an array of the selected curriculum node uids.
     */
    public function __invoke(FollowUpSystem $followUpSystem, FollowUpSystemLevelService $service): View
    {
        $this->maxNumberOfFollowUpSystemLevels = $service->getMaximum($followUpSystem->type);

        $followUpSystemLevels = $followUpSystem->levels()->withTrashed()->get();
        $levelCount = $followUpSystemLevels->count();
        for ($i = $levelCount; $i < $this->maxNumberOfFollowUpSystemLevels; $i++) {
            $followUpSystemLevel = new FollowUpSystemLevel();
            $followUpSystemLevel->is_commentable = true;
            $followUpSystemLevel->is_evaluable = true;
            $followUpSystemLevel->order = $i;
            $followUpSystemLevel->follow_up_system_id = $followUpSystem->id;
            $followUpSystemLevel->setRelation('followUpSystem', $followUpSystem);
            $followUpSystemLevel->save();
        }

        $selectedCurriculumNodeUids = FollowUpSystemGoal::withoutArchived()
            ->select('curriculumnodes.uid')
            ->join('curriculumnodes', 'curriculumnodes.id', '=', 'follow_up_system_goals.curriculumnode_id')
            ->where('follow_up_system_goals.follow_up_system_id', $followUpSystem->id)
            ->orderBy('follow_up_system_goals.order')
            ->pluck('uid');

        $curriculumNodes = $this->getCurriculumNodesForFollowUpSystem($followUpSystem, $selectedCurriculumNodeUids);

        $saveUrl = route('web.settings.common.follow-up-systems.curriculum.goals.save', [
            'followUpSystem' => $followUpSystem->uid,
        ]);

        $maximumLevel = $this->maxNumberOfFollowUpSystemLevels;

        return view('settings.follow-up-systems.curriculum-follow-up-system-goals', compact(
            'followUpSystem',
            'curriculumNodes',
            'selectedCurriculumNodeUids',
            'saveUrl',
            'maximumLevel',
        ));
    }

    /**
     * Returns all the curriculumnodes needed for this follow up system.
     * It only goes as deep as the amount of care levels.
     */
    private function getCurriculumNodesForFollowUpSystem(
        FollowUpSystem $followUpSystem,
        Collection $selectedCurriculumNodeUids,
    ): array {
        $curriculumNodes = CurriculumNode::getRepository()->getTreeForCurriculumTypeAndEducationalNetwork(
            $followUpSystem->curriculum_type,
            $followUpSystem->educationalNetwork,
            $this->getConditionsForCurriculumTypeAndEducationalNetworkTree($followUpSystem->type),
        );

        $followUpSystemLevelDepth = FollowUpSystemLevelDepth::INITIAL_DEPTH;

        return $this->getCurriculumNodesWithChildren(
            $curriculumNodes,
            $selectedCurriculumNodeUids->flip(),
            $followUpSystemLevelDepth,
        );
    }

    private function getConditionsForCurriculumTypeAndEducationalNetworkTree(
        FollowUpSystemType $followUpSystemType,
    ): array {
        return $followUpSystemType === FollowUpSystemType::OvsgLeerLokaalObservationGoals
            ? ['is_observation' => true]
            : [];
    }

    /**
     * Get the curriculumnodes with their children.
     * Don't get any children if they are located deeper than the selected amount of care levels.
     * Add a 'selected' flag to every curriculumnode if its uid is present in the given collection.
     */
    private function getCurriculumNodesWithChildren(
        array $curriculumNodes,
        Collection $selectedCurriculumNodeUids,
        int $followUpSystemLevelDepth,
    ): array {
        $total = count($curriculumNodes);

        // If the evaluation level depth is smaller than the maximum number of follow up system levels,
        // it will add 1 to that depth. If it is not smaller, it will be untouched so that every goal and
        // children of the deepest evaluation level will be shown.
        if ($followUpSystemLevelDepth < $this->maxNumberOfFollowUpSystemLevels) {
            $followUpSystemLevelDepth++;
        }

        for ($i = 0; $i < $total; $i++) {
            $curriculumNodes[$i]['selected'] = $selectedCurriculumNodeUids->has($curriculumNodes[$i]['uid']);

            // If the evaluation level depth is greater than the amount of follow up system levels for this
            // follow up system, it should not return any children.
            if ($followUpSystemLevelDepth > $this->maxNumberOfFollowUpSystemLevels) {
                $curriculumNodes[$i]['children'] = [];

                continue;
            }

            if (!empty($curriculumNodes[$i]['children'])) {
                $curriculumNodes[$i]['children'] = $this->getCurriculumNodesWithChildren(
                    $curriculumNodes[$i]['children'],
                    $selectedCurriculumNodeUids,
                    $followUpSystemLevelDepth,
                );
            }
        }

        return $curriculumNodes;
    }
}
