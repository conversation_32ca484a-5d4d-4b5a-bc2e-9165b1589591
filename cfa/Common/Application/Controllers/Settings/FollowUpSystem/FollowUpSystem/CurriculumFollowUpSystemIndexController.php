<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\FollowUpSystem;

use App\Controllers\Controller;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Illuminate\View\View;

class CurriculumFollowUpSystemIndexController extends Controller
{
    /**
     * List the curriculum follow up systems for given school.
     */
    public function __invoke(): View
    {
        return view('settings.follow-up-systems.curriculum', [
            'title' => trans('titles.follow-up-systems.add-system'),
            'followUpSystemTypes' => FollowUpSystemType::getCurriculum(),
        ]);
    }
}
