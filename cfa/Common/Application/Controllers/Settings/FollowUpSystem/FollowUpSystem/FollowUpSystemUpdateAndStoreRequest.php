<?php

declare(strict_types=1);

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\FollowUpSystem;

use Cfa\Common\Domain\Icon\Icon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

final class FollowUpSystemUpdateAndStoreRequest extends FormRequest
{
    private const ICON = 'icon';

    public function rules(): array
    {
        return [
            self::ICON . '.uid' => [
                'uuid',
                Rule::exists('icons', 'uid')
                    ->whereNull('deleted_at'),
            ],
            self::ICON . '.handle' => [
                'string',
            ],
        ];
    }

    public function getIconId(): ?int
    {
        return $this->get('icon') ? Icon::whereUid($this->get('icon')['uid'])->value('id') : null;
    }
}
