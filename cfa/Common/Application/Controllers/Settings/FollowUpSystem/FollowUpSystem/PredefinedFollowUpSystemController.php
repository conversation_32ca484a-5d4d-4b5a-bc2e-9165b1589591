<?php

namespace Cfa\Common\Application\Controllers\Settings\FollowUpSystem\FollowUpSystem;

use App\Controllers\Controller;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

use function school;
use function trans;

class PredefinedFollowUpSystemController extends Controller
{
    /**
     * List follow up systems for given school.
     *
     * @throws NoActiveSchoolsException
     */
    public function index(): View
    {
        $activatedFollowUpSystems = FollowUpSystem::query()
            ->where('school_id', school()->id)
            ->where('source', FollowUpSystemSource::Predefined)
            ->pluck('type')
            ->toArray();

        return view('settings.follow-up-systems.predefined', [
            'title' => trans('titles.follow-up-systems.add-system'),
            'followUpSystems' => FollowUpSystemType::getActivatableTypes(),
            'activatedFollowUpSystems' => $activatedFollowUpSystems,
            'saveUrl' => route('web.settings.common.follow-up-systems.predefined.store'),
        ]);
    }

    /**
     * Activate a predefined follow up system.
     *
     * @throws NoActiveSchoolsException
     */
    public function store(PredefinedFollowUpSystemStoreRequest $request): RedirectResponse
    {
        $followUpSystemType = FollowUpSystemType::from($request->get('followUpSystem'));

        $attributes = [
            'school_id' => school()->id,
            'source' => FollowUpSystemSource::Predefined,
            'type' => $followUpSystemType,
            'structure_type' => StructureType::Table,
        ];

        $followUpSystem = FollowUpSystem::withTrashed()->where($attributes)->first() ?? new FollowUpSystem();
        $followUpSystem->forceFill($attributes);
        $followUpSystem->name = trans($followUpSystemType->getTranslationKey());
        $followUpSystem->deleted_at = null;

        $followUpSystem->save();

        $targetAudiences = config('follow-up-systems.' . $followUpSystemType->value . '.general.targetAudiences');

        if ($targetAudiences) {
            $targetAudienceIdsToSync = collect();

            foreach ($targetAudiences as $type => $naturalStudyYears) {
                $targetAudienceIdsToSync = $targetAudienceIdsToSync->merge(
                    TargetAudience::whereType($type)->whereIn('natural_study_year', $naturalStudyYears)->pluck('id'),
                );
            }

            $followUpSystem->targetAudiences()->sync($targetAudienceIdsToSync);
        }

        return redirect(route('web.settings.common.follow-up-systems.index'));
    }
}
