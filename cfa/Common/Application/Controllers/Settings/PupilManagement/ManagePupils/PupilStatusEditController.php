<?php

namespace Cfa\Common\Application\Controllers\Settings\PupilManagement\ManagePupils;

use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Common\Domain\User\Gender;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\PupilChange\PupilChange;
use Cfa\Common\Domain\User\PupilChange\PupilChangeResource;
use Illuminate\View\View;

use function route;

class PupilStatusEditController
{
    public function __invoke(Pupil $pupil): View
    {
        $pupilChange = null;
        if ($pupil->care_info->pupil_status === PupilStatus::SuspiciousNameChangeException) {
            $pupilChangeRecord = PupilChange::whereUserId($pupil->id)->orderByDesc('created_at')->firstOrFail();
            $pupilChange = new PupilChangeResource($pupilChangeRecord);
        }

        return view(
            'settings.care.pupil-management.manage-pupils.edit',
            [
                'pupil' => $pupil,
                'careInfo' => $pupil->care_info,
                'pupilChange' => optional($pupilChange)->resolve(),
                'genders' => Gender::getSelectFields(),
                'cancelUrl' => route('web.settings.common.pupil-management.manage-pupils-overview'),
                'standAloneUrl' => route(
                    'web.settings.common.pupil-management.manage-pupils.pupil.make-standalone',
                    [
                        'pupil' => $pupil,
                    ],
                ),
                'activeUrl' => route(
                    'web.settings.common.pupil-management.manage-pupils.pupil.make-active',
                    [
                        'pupil' => $pupil,
                    ],
                ),
                'clearUrl' => route(
                    'web.settings.common.pupil-management.manage-pupils.pupil.clear-national-register-number',
                    [
                        'pupil' => $pupil,
                    ],
                ),
            ],
        );
    }
}
