<?php

namespace Cfa\Common\Application\Controllers\Settings\PupilManagement\ManagePupils;

use Carbon\Carbon;
use Cfa\Common\Application\Services\Permission\PermissionService;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\Schoolyear\SchoolyearRepositoryInterface;
use Cfa\Common\Domain\User\Pupil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;

class PupilFetchController
{
    public function __construct(
        private readonly SchoolyearRepositoryInterface $schoolyearRepository,
        private readonly PermissionService $permissionService,
    ) {}

    public function __invoke(PupilFetchRequest $request): JsonResponse
    {
        $school = school();
        $user = Auth::user();
        $schoolHasCareLicense = $user->hasPermission(PermissionName::HasAccessToCare, $school);
        $careAccessGroups = $this->permissionService->getGroupsWithCareDataAccess($user, $school);
        $careerGroups = $user->activeCareerGroups($school->id);
        $accessToAllGroups = $user->hasAccessToAllGroups($school);

        $schoolyear = $this->schoolyearRepository->findSchoolyearByYear($request->get('schoolyearStartYear'));
        $start = $schoolyear?->start ?? Carbon::now();
        $end = $schoolyear?->end ?? Carbon::now();

        $pupils = Pupil::with(['careers.groupWithTrashed', 'careers.group', 'careInfos'])
            ->where('school_id', $school->id)
            ->where('startdate', '<=', $end)
            ->where(fn(Builder $query): Builder => $query->where('enddate', '>', $start)->orWhereNull('enddate'))
            ->get()
            ->map(
                function (Pupil $pupil) use (
                    $schoolyear,
                    $school,
                    $careAccessGroups,
                    $careerGroups,
                    $accessToAllGroups,
                    $schoolHasCareLicense,
                ) {
                    $activeGroups = $pupil->activeCareerGroups($school->id)->filter->is_classgroup;
                    $activeGroupNames = $activeGroups->pluck('name');
                    $group = $pupil->lastClassGroupOfSchoolyear($school->id, $schoolyear)?->name;
                    $careInfo = $pupil->care_info;
                    $pupilStatus = $careInfo->pupil_status;

                    $currentGroup = $activeGroupNames->implode(', ') ?: trans('labels.has-no-group');
                    $hasInfoSheetAccess =
                    ($currentGroup === trans('labels.has-no-group') ||
                        $careAccessGroups->intersect($activeGroups)->isNotEmpty());
                    $hasDashboardAccess = ($accessToAllGroups || $hasInfoSheetAccess ||
                    $careerGroups->intersect($activeGroups)->isNotEmpty());
                    $hasExportAccess = (!$schoolHasCareLicense && $hasDashboardAccess) || $hasInfoSheetAccess;

                    return [
                        'uid' => $pupil->uid,
                        'firstname' => $pupil->firstname,
                        'lastname' => $pupil->lastname,
                        'group' => $group,
                        'currentGroup' => $currentGroup,
                        'status' => $careInfo->has_wisa_connection ?
                            trans('labels.managed-by-wisa') :
                            $pupilStatus->getHumanReadableName(),
                        'editable' => !$careInfo->has_wisa_connection,
                        'hasInfoSheetAccess' => $hasInfoSheetAccess,
                        'hasDashboardAccess' => $hasDashboardAccess,
                        'hasExportAccess' => $hasExportAccess,
                    ];
                },
            )->values();

        return Response::json($pupils);
    }
}
