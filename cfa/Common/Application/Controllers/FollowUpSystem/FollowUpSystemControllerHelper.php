<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem;

use Cfa\Common\Application\Handlers\GroupSwitcherDataHandler;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemRepositoryInterface;
use Illuminate\Support\Collection;

use function app;

class FollowUpSystemControllerHelper
{
    private FollowUpSystemRepositoryInterface $followUpSystemRepository;

    public function __construct(FollowUpSystemRepositoryInterface $followUpSystemRepository)
    {
        $this->followUpSystemRepository = $followUpSystemRepository;
    }

    public function setGroupSwitcherGroups(): void
    {
        app(GroupSwitcherDataHandler::class)->setRouteName('web.common.follow-up-systems.redirect');
    }

    public function getFollowUpSystems(Group $group, FollowUpSystem $followUpSystem): Collection
    {
        [$selectedFollowUpSystem, $otherFollowUpSystems] = $this->followUpSystemRepository
            ->getAllForGroup($group)
            ->partition(fn(FollowUpSystem $item): bool => $item->id === $followUpSystem->id);

        return $selectedFollowUpSystem
            ->merge($otherFollowUpSystems)
            ->reject(fn(FollowUpSystem $item): bool => $item->archived())
            ->values();
    }
}
