<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\Avi;

use Cfa\Common\Application\Controllers\FollowUpSystem\FollowUpSystemControllerHelper;
use Cfa\Common\Application\Controllers\FollowUpSystem\Predefined\PredefinedFollowUpSystemIndexRequest;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Illuminate\Support\Collection;
use Inertia\Inertia;
use Inertia\Response;

class AviIndexController
{
    /**
     * Show the results of the pupils of an AVI predefined follow up system.
     */
    public function __invoke(
        PredefinedFollowUpSystemIndexRequest $request,
        PredefinedFollowUpSystem $followUpSystem,
        FollowUpSystemControllerHelper $followUpSystemControllerHelper,
    ): Response {
        $followUpSystemControllerHelper->setGroupSwitcherGroups();

        $followUpSystemType = $followUpSystem->type->value;
        $schoolyear = $request->getSchoolyear();
        $baseUrl = route('web.common.follow-up-systems.avi.zone', [
            'followUpSystem' => $followUpSystem->uid,
            'pupil' => '#pupilUid#',
        ]);

        $commentBaseUrl = route('web.common.follow-up-systems.predefined.comment', [
            'followUpSystem' => $followUpSystem->uid,
            'followUpSystemSubType' => FollowUpSystemSubType::Default->value,
            'pupil' => '#pupilUid#',
        ]);

        $pupils = Group::getRepository()->getPupilsOfGroup(group(), $schoolyear);
        $pupils = PredefinedFollowUpSystemScore::getRepository()->getPupilsWithScores(
            $pupils,
            group(),
            $followUpSystem,
            FollowUpSystemSubType::Default,
            $schoolyear,
        );

        $followUpSystems = $followUpSystemControllerHelper->getFollowUpSystems(group(), $followUpSystem);

        return Inertia::render(
            'Evaluation/PredefinedFollowUpSystems/Avi/Index',
            [
                'followUpSystem' => $followUpSystem,
                'followUpSystems' => $followUpSystems,
                'pupils' => $pupils,
                'zones' => config('follow-up-systems.' . $followUpSystemType . '.general.zones'),
                'testMoments' => config('follow-up-systems.' . $followUpSystemType . '.general.testMoments'),
                'baseUrl' => $baseUrl,
                'commentBaseUrl' => $commentBaseUrl,
                'editable' => $schoolyear->isCurrent(),
                'warning' => $this->getWarning($pupils),
            ],
        );
    }

    private function getWarning(
        Collection $pupils,
    ): string {
        if ($pupils->contains('disabled', true)) {
            return trans('labels.follow-up-systems.predefined.inactive-pupils-found');
        }

        return '';
    }
}
