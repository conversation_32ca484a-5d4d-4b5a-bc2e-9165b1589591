<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Predefined;

use App\Controllers\Controller;
use Cfa\Common\Application\Exceptions\NoActiveSchoolsException;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment\PredefinedFollowUpSystemComment;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;

abstract class AbstractPredefinedFollowUpSystemSaveController extends Controller
{
    /**
     * Get the existing follow up system score if it exist, otherwise it returns an empty score.
     *
     * @throws NoActiveSchoolsException
     */
    public function getExistingPredefinedFollowUpSystemScore(
        PredefinedFollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        Pupil $pupil,
        TestMoment $testMoment,
        Schoolyear $schoolyear,
    ): PredefinedFollowUpSystemScore {
        $attributes = [
            'school_id' => school()->id,
            'pupil_id' => $pupil->id,
            'schoolyear_id' => $schoolyear->id,
            'test_moment' => $testMoment,
            'subtype' => $followUpSystemSubType,
            'predefined_follow_up_system_id' => $followUpSystem->id,
        ];

        $predefinedFollowUpSystemScore = PredefinedFollowUpSystemScore::withTrashed()->where($attributes)->first();

        if ($predefinedFollowUpSystemScore === null) {
            $predefinedFollowUpSystemScore = new PredefinedFollowUpSystemScore()->forceFill($attributes);
        }

        $predefinedFollowUpSystemScore->restoreIfTrashed();

        return $predefinedFollowUpSystemScore;
    }

    public function getExistingPredefinedFollowUpSystemComment(
        PredefinedFollowUpSystem $followUpSystem,
        FollowUpSystemSubType $followUpSystemSubType,
        Pupil $pupil,
        TestMoment $testMoment,
        Schoolyear $schoolyear,
    ): PredefinedFollowUpSystemComment {
        $attributes = [
            'school_id' => school()->id,
            'pupil_id' => $pupil->id,
            'schoolyear_id' => $schoolyear->id,
            'test_moment' => $testMoment,
            'subtype' => $followUpSystemSubType->value,
            'predefined_follow_up_system_id' => $followUpSystem->id,
        ];

        $predefinedFollowUpSystemComment = PredefinedFollowUpSystemComment::withTrashed()->where($attributes)->first();

        if ($predefinedFollowUpSystemComment === null) {
            $predefinedFollowUpSystemComment = new PredefinedFollowUpSystemComment()->forceFill($attributes);
        }

        $predefinedFollowUpSystemComment->restoreIfTrashed();

        return $predefinedFollowUpSystemComment;
    }
}
