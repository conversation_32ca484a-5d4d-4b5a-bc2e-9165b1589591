<?php

namespace Cfa\Common\Application\Controllers\FollowUpSystem\Standard\Goal;

use App\Controllers\Controller;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalComment;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Comment\FollowUpSystemGoalCommentRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\FollowUpSystemGoal;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotation;
use Cfa\Evaluation\Domain\FollowUpSystem\Goal\Quotation\FollowUpSystemGoalQuotationRepositoryInterface;
use Cfa\Evaluation\Domain\FollowUpSystem\InputMoment\FollowUpSystemInputMoment;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class FollowUpSystemGoalByGoalFetchController extends Controller
{
    public function __invoke(
        FollowUpSystem $followUpSystem,
        FollowUpSystemGoal $followUpSystemGoal,
        FollowUpSystemInputMoment $followUpSystemInputMoment,
        FollowUpSystemGoalByGoalFetchRequest $request,
        FollowUpSystemGoalQuotationRepositoryInterface $quotationRepository,
        FollowUpSystemGoalCommentRepositoryInterface $commentRepository,
    ): JsonResponse {
        $pupils = Pupil::whereIn('users.uid', $request->get(FollowUpSystemGoalByGoalFetchRequest::PUPILS))->get();

        $historyQuotations = $quotationRepository->getHistoryByPupilsForSingleGoal(
            $followUpSystemGoal,
            $followUpSystemInputMoment,
            $pupils,
        );

        $historyComments = $commentRepository->getHistoryByPupilsForSingleGoal(
            $followUpSystemGoal,
            $followUpSystemInputMoment,
            $pupils,
        );

        $currentFollowUpSystemGoalQuotations = $quotationRepository->getQuotationsByPupilsForSingleGoal(
            $followUpSystemGoal,
            $followUpSystemInputMoment,
            $pupils,
        );

        $currentFollowUpSystemGoalComments = $commentRepository->getCommentsByPupilForSingleGoal(
            $followUpSystemGoal,
            $followUpSystemInputMoment,
            $pupils,
        );

        return $this->respond([
            'history' => $this->mergeHistory($pupils, $historyQuotations, $historyComments),
            'quotations' => $currentFollowUpSystemGoalQuotations,
            'comments' => $currentFollowUpSystemGoalComments,
        ]);
    }

    private function mergeHistory(Collection $pupils, Collection $quotations, Collection $comments): array
    {
        return $pupils->keyBy('uid')
            ->map(function (Pupil $pupil) use ($quotations, $comments) {
                $quotationsOfPupil = $quotations->get($pupil->uid, collect())
                    ->keyBy(fn(FollowUpSystemGoalQuotation $item): string => $item->followUpSystemInputMoment->uid);
                $commentsOfPupil = $comments->get($pupil->uid, collect())
                    ->keyBy(fn(FollowUpSystemGoalComment $item): string => $item->followUpSystemInputMoment->uid);

                return $quotationsOfPupil->keys()->concat($commentsOfPupil->keys())->unique()
                    ->map(function (string $uid) use ($quotationsOfPupil, $commentsOfPupil) {
                        $quotation = $quotationsOfPupil->get($uid, new FollowUpSystemGoalQuotation());
                        $comment = $commentsOfPupil->get($uid, new FollowUpSystemGoalComment());
                        $inputMoment = $comment->followUpSystemInputMoment ?? $quotation->followUpSystemInputMoment;

                        return [
                            'quotation' => $quotation->quotation,
                            'comment' => $comment->comment,
                            'name' => $inputMoment->name,
                            'date' => $inputMoment->date->toDateString(),
                        ];
                    })
                    ->sortByDesc('date')
                    ->values();
            })
            ->toArray();
    }
}
