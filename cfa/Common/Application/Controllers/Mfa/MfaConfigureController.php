<?php

namespace Cfa\Common\Application\Controllers\Mfa;

use App\Controllers\Controller;
use Cfa\Common\Domain\User\Mfa\Status\MfaStatus;
use Cfa\Common\Domain\User\Mfa\UserMfa;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class MfaConfigureController extends Controller
{
    public function __invoke(): JsonResponse
    {
        $user = Auth::user();

        if ($user->hasMfaEnabled()) {
            abort(Response::HTTP_FORBIDDEN);
        }

        $mfa = new UserMfa();
        $mfa->user_id = $user->id;
        $mfa->status = MfaStatus::MfaRequested;
        $mfa->save();

        return $this->respondOk();
    }
}
