<?php

namespace Cfa\Common\Application\Controllers\Mfa\Qr;

use Cfa\Common\Domain\User\Mfa\Status\MfaStatus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Override;

class QrVerifyRequest extends QrRequest
{
    public const IS_ACTIVE = 'is_active';

    #[Override]
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            self::IS_ACTIVE => [
                'boolean',
                Rule::in([true]),
            ],
        ]);
    }

    #[Override]
    public function validationData(): array
    {
        $data = parent::validationData();

        $data[self::IS_ACTIVE] = optional(Auth::user()->userMfa)->status === MfaStatus::Active;

        return $data;
    }
}
