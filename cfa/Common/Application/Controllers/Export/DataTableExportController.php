<?php

namespace Cfa\Common\Application\Controllers\Export;

use App\Controllers\Controller;
use Cfa\Common\Application\Jobs\Export\ExportPdfDataTable;
use Illuminate\Http\JsonResponse;

class DataTableExportController extends Controller
{
    public function __invoke(DataTableExportRequest $request): JsonResponse
    {
        $job = new ExportPdfDataTable(...$request->getData());
        // Job handled synchronously until print is available.
        $this->dispatchSync($job);

        return $this->respond($job->getTemporaryUrl());
    }
}
