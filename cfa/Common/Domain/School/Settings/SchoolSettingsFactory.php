<?php

namespace Cfa\Common\Domain\School\Settings;

use App\Factories\Factory;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Override;

/**
 * SchoolSettingsFactory
 *
 * @codingStandardsIgnoreStart
 * @method Collection|SchoolSettings[]|SchoolSettings create($attributes = [], Model|null $parent = null)
 * @method Collection|SchoolSettings[]|SchoolSettings createWithEvents(array $attributes = [], Model|null $parent = null)
 * @codingStandardsIgnoreEnd
 */
class SchoolSettingsFactory extends Factory
{
    /** @var string */
    protected $model = SchoolSettings::class;

    public function inSchool(School $school): self
    {
        return $this->state(fn(): array => [
            'school_id' => $school->id,
        ]);
    }

    public function forType(SchoolSettingsType $schoolSettingsType): self
    {
        return $this->state(fn(): array => [
            'type' => $schoolSettingsType,
        ]);
    }

    public function setValue(mixed $value): self
    {
        return $this->state(fn(): array => [
            'value' => $value,
        ]);
    }

    #[Override]
    public function definition(): array
    {
        return [
            'school_id' => School::randomId(),
            'type' => $this->faker->randomElement(SchoolSettingsType::cases()),
        ];
    }
}
