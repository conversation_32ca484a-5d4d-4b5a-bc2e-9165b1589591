<?php

namespace Cfa\Common\Domain\School;

use App\Infrastructure\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Model;
use App\Models\SMD\SmdExternalType;
use Carbon\Carbon;
use Cfa\Admin\Application\Nova\Traits\NovaSchoolRelations;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Application\Traits\UpdatedBySMD;
use Cfa\Common\Domain\Address\Address;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\Permission\PermissionSchool;
use Cfa\Common\Domain\Preference\Preference;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Institute\Institute;
use Cfa\Common\Domain\School\Settings\SchoolSettings;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Cfa\Common\Domain\User\Staff;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Roster\Roster;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Nova\Actions\Actionable;
use Laravel\Nova\Actions\ActionEvent;
use Override;

/**
 * Cfa\Common\Domain\School\School
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $educationalnetwork_id
 * @property string $name
 * @property string $original_name
 * @property Carbon|null $start_date
 * @property Carbon|null $end_date
 * @property string|null $remark
 * @property string|null $website
 * @property string|null $partner_number
 * @property string|null $school_number
 * @property string|null $api_token
 * @property bool $is_api_token_mailed
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $address_id
 * @property Carbon|null $smd_updated_at
 * @property int|null $smd_incremental_event_id
 * @property string|null $sol_uid The original SOL uid.
 * @property string|null $smd_external_uid
 * @property string|null $phone_number
 * @property bool $is_active
 * @property SmdExternalType $smd_external_type
 * @property string|null $collections_export_status
 * @property-read Collection<int, ActionEvent> $actions
 * @property-read int|null $actions_count
 * @property-read Collection<int, Career> $activeCareers
 * @property-read int|null $active_careers_count
 * @property-read Collection<int, Role> $activeRoles
 * @property-read int|null $active_roles_count
 * @property-read Address|null $address
 * @property-read Collection<int, Role> $allRoles
 * @property-read int|null $all_roles_count
 * @property-read Collection<int, Career> $careers
 * @property-read int|null $careers_count
 * @property-read Collection<int, Group> $classgroups
 * @property-read int|null $classgroups_count
 * @property-read EducationalNetwork $educationalnetwork
 * @property-read array $validation_rules
 * @property-read Collection<int, Group> $groups
 * @property-read int|null $groups_count
 * @property-read Collection<int, Group> $groupsInCurrentSchoolyear
 * @property-read int|null $groups_in_current_schoolyear_count
 * @property-read Collection<int, Institute> $institutes
 * @property-read int|null $institutes_count
 * @property-read Collection<int, Pupil> $novaPupils
 * @property-read int|null $nova_pupils_count
 * @property-read Collection<int, User> $novaStaff
 * @property-read int|null $nova_staff_count
 * @property-read Collection<int, PermissionSchool> $permissions
 * @property-read int|null $permissions_count
 * @property-read Collection<int, Preference> $preferences
 * @property-read int|null $preferences_count
 * @property-read Collection<int, Pupil> $pupils
 * @property-read int|null $pupils_count
 * @property-read Collection<int, Roster> $rosters
 * @property-read int|null $rosters_count
 * @property-read Collection<int, SchoolUserAccess> $schoolUserAccess
 * @property-read int|null $school_user_access_count
 * @property-read Collection<int, SchoolSettings> $settings
 * @property-read int|null $settings_count
 * @property-read Collection<int, Staff> $staff
 * @property-read int|null $staff_count
 * @property-read Collection<int, Subject> $subjects
 * @property-read int|null $subjects_count
 * @property-read Collection<int, User> $users
 * @property-read int|null $users_count
 * @method static SchoolFactory factory($count = null, $state = [])
 * @method static SchoolRepositoryInterface getRepository()
 * @method static Builder|School newModelQuery()
 * @method static Builder|School newQuery()
 * @method static Builder|School onlyTrashed()
 * @method static Builder|School query()
 * @method static int|null randomAddressId()
 * @method static string|null randomApiToken()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomEducationalnetworkId()
 * @method static Carbon|null randomEndDate()
 * @method static int randomId()
 * @method static bool randomIsActive()
 * @method static bool randomIsApiTokenMailed()
 * @method static string randomName()
 * @method static string randomOriginalName()
 * @method static string|null randomPartnerNumber()
 * @method static string|null randomPhoneNumber()
 * @method static string|null randomRemark()
 * @method static string|null randomSchoolNumber()
 * @method static SmdExternalType randomSmdExternalType()
 * @method static string|null randomSmdExternalUid()
 * @method static int|null randomSmdIncrementalEventId()
 * @method static Carbon|null randomSmdUpdatedAt()
 * @method static string|null randomSolUid()
 * @method static Carbon|null randomStartDate()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static string|null randomWebsite()
 * @method static Builder|School whereAddressId($value)
 * @method static Builder|School whereApiToken($value)
 * @method static Builder|School whereCreatedAt($value)
 * @method static Builder|School whereDeletedAt($value)
 * @method static Builder|School whereEducationalnetworkId($value)
 * @method static Builder|School whereEndDate($value)
 * @method static Builder|School whereId($value)
 * @method static Builder|School whereIsActive($value)
 * @method static Builder|School whereIsApiTokenMailed($value)
 * @method static Builder|School whereName($value)
 * @method static Builder|School whereOriginalName($value)
 * @method static Builder|School wherePartnerNumber($value)
 * @method static Builder|School wherePhoneNumber($value)
 * @method static Builder|School whereRemark($value)
 * @method static Builder|School whereSchoolNumber($value)
 * @method static Builder|School whereSmdExternalType($value)
 * @method static Builder|School whereSmdExternalUid($value)
 * @method static Builder|School whereSmdIncrementalEventId($value)
 * @method static Builder|School whereSmdUpdatedAt($value)
 * @method static Builder|School whereSolUid($value)
 * @method static Builder|School whereStartDate($value)
 * @method static Builder|School whereUid($value)
 * @method static Builder|School whereUpdatedAt($value)
 * @method static Builder|School whereWebsite($value)
 * @method static Builder|School withTrashed()
 * @method static Builder|School withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 *
 * @SuppressWarnings(PHPMD.TooManyPublicMethods)
 */
class School extends Model
{
    use Actionable;
    use NovaSchoolRelations {
        pupils as novaPupils;
        staff as novaStaff;
    }
    use SoftDeletes;
    use Uid;
    use UpdatedBySMD;

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'saved' => SchoolSaved::class,
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];

    /** @var array */
    protected $casts = [
        'educationalnetwork_id' => 'integer',
        'is_active' => 'bool',
        'is_api_token_mailed' => 'bool',
        'partner_number' => 'string',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    protected array $enums = [
        'collections_export_status' => ExportJobStatus::class,
        'smd_external_type' => SmdExternalType::class,
    ];
    private Carbon $today;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
            ],
        );
    }

    /**
     * Get the address associated with the school.
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }

    /**
     * Get the educational network the school belongs to.
     */
    public function educationalnetwork(): BelongsTo
    {
        return $this->belongsTo(EducationalNetwork::class, 'educationalnetwork_id');
    }

    /**
     * Get the careers that are part of the school.
     */
    public function careers(): HasMany
    {
        return $this->hasMany(Career::class);
    }

    /**
     * Get the active careers that are part of the school.
     */
    public function activeCareers(): HasMany
    {
        return $this->careers()
            ->where('startdate', '<=', Carbon::now())
            ->where(function (Builder $query): void {
                $query->where('enddate', '>', Carbon::now())
                    ->orWhereNull('enddate');
            });
    }

    /**
     * Get all the roles of the school.
     */
    public function allRoles(): HasManyThrough
    {
        return $this->hasManyThrough(Role::class, Career::class, 'school_id');
    }

    /**
     * Get the active roles of the school.
     */
    public function activeRoles(): HasManyThrough
    {
        return $this->allRoles()
            ->where('startdate', '<=', Carbon::now())
            ->where(function (Builder $query): void {
                $query->where('enddate', '>', Carbon::now())
                    ->orWhereNull('enddate');
            });
    }

    /**
     * Get the groups linked to the school.
     */
    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    /**
     * Get the classgroups linked to the school.
     */
    public function classgroups(): HasMany
    {
        return $this->hasMany(Group::class)->isClassgroup();
    }

    /**
     * Get the groups linked to the school, in the current schoolyear.
     */
    public function groupsInCurrentSchoolyear(): HasMany
    {
        return $this->hasMany(Group::class)
            ->where('schoolyear_id', Schoolyear::getRepository()->getCurrent()->id);
    }

    /**
     * Get the rosters for the school.
     */
    public function rosters(): HasMany
    {
        return $this->hasMany(Roster::class);
    }

    /**
     * Get the subjects for the school.
     */
    public function subjects(): HasMany
    {
        return $this->hasMany(Subject::class);
    }

    /**
     * Institutes belonging to this school.
     */
    public function institutes(): BelongsToMany
    {
        return $this->belongsToMany(Institute::class);
    }

    /**
     * Return the user's access linked to school.
     */
    public function schoolUserAccess(): HasMany
    {
        return $this->hasMany(SchoolUserAccess::class, 'school_id');
    }

    public function permissions(): HasMany
    {
        return $this->hasMany(PermissionSchool::class, 'school_id')
            ->where('startdate', '<=', $this->today())
            ->where(fn(Builder $subQuery): Builder => $subQuery
                ->whereNull('enddate')
                ->orWhere('enddate', '>=', $this->today()));
    }

    /**
     * Return the user's with access to school.
     */
    public function users(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            SchoolUserAccess::class,
            'school_id',
            'id',
            'id',
            'user_id',
        );
    }

    public function preferences(): MorphMany
    {
        return $this->morphMany(Preference::class, 'model');
    }

    public function settings(): HasMany
    {
        return $this->hasMany(SchoolSettings::class);
    }

    public function collectionLicenses(): HasMany
    {
        return $this->hasMany(CollectionLicense::class);
    }

    /**
     * Return the pupils with access to school.
     */
    public function pupils(): HasMany
    {
        return $this->newHasMany(
            $this->newRelatedInstance(Pupil::class)->newQuery(),
            $this,
            'careers.school_id',
            'id',
        );
    }

    /**
     * Return the staff with access to school.
     */
    public function staff(): HasMany
    {
        return $this->newHasMany(
            $this->newRelatedInstance(Staff::class)->newQuery(),
            $this,
            'careers.school_id',
            'id',
        );
    }

    /**
     * Remove a permission from a school.
     *
     * @param PermissionName $permissionName The permission to remove.
     */
    public function removePermission(PermissionName $permissionName): void
    {
        PermissionSchool::where('school_id', $this->id)
            ->where('name', $permissionName)
            ->delete();

        foreach ($this->users as $user) {
            User::getRepository()->flushFullUserCache($user->id);
        }
    }

    private function today(): Carbon
    {
        if (!isset($this->today)) {
            $this->today = Carbon::now();
        }

        return $this->today;
    }
}
