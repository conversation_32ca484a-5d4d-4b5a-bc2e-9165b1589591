<?php

namespace Cfa\Common\Domain\School\Institute;

use App\Infrastructure\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Application\Traits\UpdatedBySMD;
use Cfa\Common\Domain\Address\Address;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Override;

/**
 * Cfa\Common\Domain\School\Institute\Institute
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string $institute_number
 * @property string|null $school_title
 * @property string|null $long_name
 * @property string|null $short_name
 * @property string|null $nis_code
 * @property string|null $crab_code
 * @property string|null $im_number
 * @property string|null $school_community_number
 * @property string|null $admin_first_name
 * @property string|null $admin_last_name
 * @property Carbon|null $start_date
 * @property Carbon|null $end_date
 * @property int|null $address_id
 * @property string|null $email
 * @property string|null $fax
 * @property string|null $phone
 * @property string|null $website
 * @property Carbon|null $foundation_date
 * @property Carbon|null $closedown_date
 * @property bool|null $active
 * @property Carbon|null $smd_updated_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property int|null $smd_incremental_event_id
 * @property-read Address|null $address
 * @property-read array $validation_rules
 * @property-read Collection<int, School> $schools
 * @property-read int|null $schools_count
 * @method static Builder|Institute newModelQuery()
 * @method static Builder|Institute newQuery()
 * @method static Builder|Institute onlyTrashed()
 * @method static Builder|Institute query()
 * @method static bool|null randomActive()
 * @method static int|null randomAddressId()
 * @method static string|null randomAdminFirstName()
 * @method static string|null randomAdminLastName()
 * @method static Carbon|null randomClosedownDate()
 * @method static string|null randomCrabCode()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomEmail()
 * @method static Carbon|null randomEndDate()
 * @method static string|null randomFax()
 * @method static Carbon|null randomFoundationDate()
 * @method static int randomId()
 * @method static string|null randomImNumber()
 * @method static string randomInstituteNumber()
 * @method static string|null randomLongName()
 * @method static string|null randomNisCode()
 * @method static string|null randomPhone()
 * @method static string|null randomSchoolCommunityNumber()
 * @method static string|null randomSchoolTitle()
 * @method static string|null randomShortName()
 * @method static int|null randomSmdIncrementalEventId()
 * @method static Carbon|null randomSmdUpdatedAt()
 * @method static Carbon|null randomStartDate()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static string|null randomWebsite()
 * @method static Builder|Institute whereActive($value)
 * @method static Builder|Institute whereAddressId($value)
 * @method static Builder|Institute whereAdminFirstName($value)
 * @method static Builder|Institute whereAdminLastName($value)
 * @method static Builder|Institute whereClosedownDate($value)
 * @method static Builder|Institute whereCrabCode($value)
 * @method static Builder|Institute whereCreatedAt($value)
 * @method static Builder|Institute whereDeletedAt($value)
 * @method static Builder|Institute whereEmail($value)
 * @method static Builder|Institute whereEndDate($value)
 * @method static Builder|Institute whereFax($value)
 * @method static Builder|Institute whereFoundationDate($value)
 * @method static Builder|Institute whereId($value)
 * @method static Builder|Institute whereImNumber($value)
 * @method static Builder|Institute whereInstituteNumber($value)
 * @method static Builder|Institute whereLongName($value)
 * @method static Builder|Institute whereNisCode($value)
 * @method static Builder|Institute wherePhone($value)
 * @method static Builder|Institute whereSchoolCommunityNumber($value)
 * @method static Builder|Institute whereSchoolTitle($value)
 * @method static Builder|Institute whereShortName($value)
 * @method static Builder|Institute whereSmdIncrementalEventId($value)
 * @method static Builder|Institute whereSmdUpdatedAt($value)
 * @method static Builder|Institute whereStartDate($value)
 * @method static Builder|Institute whereUid($value)
 * @method static Builder|Institute whereUpdatedAt($value)
 * @method static Builder|Institute whereWebsite($value)
 * @method static Builder|Institute withTrashed()
 * @method static Builder|Institute withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class Institute extends Model
{
    use SoftDeletes;
    use Uid;
    use UpdatedBySMD;

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'institute_number' => [
                    'required',
                    'integer',
                ],
                'school_title' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'long_name' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'short_name' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'nis_code' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'crab_code' => [
                    'required',
                    'string',
                    'max:191',
                ],
                'email' => [
                    'nullable',
                    'email',
                ],
            ],
        );
    }

    /** @var array */
    protected $casts = [
        'active' => 'bool',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'foundation_date' => 'datetime',
        'closedown_date' => 'datetime',
    ];

    /**
     * Schools belonging to this institute.
     */
    public function schools(): BelongsToMany
    {
        return $this->belongsToMany(School::class);
    }

    /**
     * Get the address associated with the institute.
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class);
    }
}
