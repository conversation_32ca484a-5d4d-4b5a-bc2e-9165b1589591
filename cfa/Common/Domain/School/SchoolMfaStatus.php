<?php

namespace Cfa\Common\Domain\School;

enum SchoolMfaStatus: int
{
    case NotEnforced = 0;
    case Pending = 1;
    case Active = 2;

    public function isNotEnforced(): bool
    {
        return $this === self::NotEnforced;
    }

    public function isPending(): bool
    {
        return $this === self::Pending;
    }

    public function isActive(): bool
    {
        return $this === self::Active;
    }
}
