<?php

namespace Cfa\Common\Domain\School;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin School
 * @extends JsonResource<School>
 */
class SchoolResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'original_name' => $this->original_name,
            'name' => $this->name,
        ];
    }
}
