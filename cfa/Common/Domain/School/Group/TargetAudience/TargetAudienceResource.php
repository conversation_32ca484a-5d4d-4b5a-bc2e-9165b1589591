<?php

namespace Cfa\Common\Domain\School\Group\TargetAudience;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin TargetAudience
 * @extends JsonResource<TargetAudience>
 */
class TargetAudienceResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'id' => $this->uid,
            'name' => $this->name,
        ];
    }
}
