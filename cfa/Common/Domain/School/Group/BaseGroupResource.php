<?php

namespace Cfa\Common\Domain\School\Group;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Group
 * @extends JsonResource<Group>
 */
class BaseGroupResource extends JsonResource
{
    /** {@inheritdoc} */
    #[Override]
    public function toArray(Request $request): array
    {
        return [
            ...parent::toArray($request),
            ...[
                'name' => $this->name,
                'id' => $this->uid,
                'uid' => $this->uid,
            ],
        ];
    }
}
