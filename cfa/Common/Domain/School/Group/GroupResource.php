<?php

namespace Cfa\Common\Domain\School\Group;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin Group
 * @extends JsonResource<Group>
 */
class GroupResource extends BaseGroupResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            ...parent::toArray($request),
            ...[
                'institute' => $this->institute_code,
                'recently_accessed' => $this->additional['recentlyAccessedGroupsIds']->contains($this->id),
                'is_own_group' => $this->is_own_group,
                'is_classgroup' => $this->is_classgroup,
            ],
        ];
    }
}
