<?php

namespace Cfa\Common\Domain\School\EducationalNetwork;

use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

use function array_key_exists;
use function collect;

/**
 * Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property string $name
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property-read Collection<int, CurriculumNode> $curriculumnodes
 * @property-read int|null $curriculumnodes_count
 * @property-read array $validation_rules
 * @method static EducationalNetworkRepositoryInterface getRepository()
 * @method static Builder|EducationalNetwork newModelQuery()
 * @method static Builder|EducationalNetwork newQuery()
 * @method static Builder|EducationalNetwork onlyTrashed()
 * @method static Builder|EducationalNetwork query()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static int randomId()
 * @method static string randomName()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|EducationalNetwork whereCreatedAt($value)
 * @method static Builder|EducationalNetwork whereDeletedAt($value)
 * @method static Builder|EducationalNetwork whereId($value)
 * @method static Builder|EducationalNetwork whereName($value)
 * @method static Builder|EducationalNetwork whereUid($value)
 * @method static Builder|EducationalNetwork whereUpdatedAt($value)
 * @method static Builder|EducationalNetwork withTrashed()
 * @method static Builder|EducationalNetwork withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class EducationalNetwork extends Model
{
    use SoftDeletes;
    use Uid;

    /**
     * Uuids for the different networks.
     */
    public const VVKBAO_UID = '160f5535-1b2d-4404-8bf7-a438610556ad';
    public const OVSG_UID = '4cb01b07-b281-4599-95c2-3495a83594ea';
    public const GO_UID = '51704c81-595a-427e-90cf-74b2f50c13f5';
    public const POV_UID = '696f41cc-a79b-43bb-bc85-2ed31d41541a';
    public const ANDERE_UID = 'da75fcf6-05fc-11e5-82cd-b8f6b11b7fe';

    /**
     * {@inheritdoc}
     * @var string
     */
    protected $table = 'educationalnetworks';

    /**
     * Map SMD network name to an educational network.
     *
     * @param string|null $name The educational network name from SMD.
     */
    public static function getFromSMDName(?string $name): self
    {
        switch ($name) {
            case 'Gemeenschapsonderwijs':
                $networkUid = self::GO_UID;
                break;
            case 'Vrij gesubsidieerd onderwijs':
                $networkUid = self::VVKBAO_UID;
                break;
            case 'Gemeentelijk onderwijs':
                $networkUid = self::OVSG_UID;
                break;
            case 'Provinciaal onderwijs':
                $networkUid = self::POV_UID;
                break;
            default:
                $networkUid = self::ANDERE_UID;
                break;
        }

        return self::whereUid($networkUid)->first();
    }

    public static function getFromOlfType(string $type): ?string
    {
        if ($type === 'GO') {
            return self::GO_UID;
        }

        if ($type === 'VVKBAO') {
            return self::VVKBAO_UID;
        }

        if ($type === 'OVSG') {
            return self::OVSG_UID;
        }

        return null;
    }

    /**
     * Get the curriculumnodes for the network.
     */
    public function curriculumnodes(): HasMany
    {
        return $this->hasMany(CurriculumNode::class, 'educationalnetwork_id');
    }

    /**
     * Get the configuration.
     */
    public function getConfiguration(): array
    {
        return tenant()->getEducationalNetworkConfiguration()[$this->uid] ?? [];
    }

    /** @return Collection<int, CurriculumType> */
    public function getCurriculumTypes(): Collection
    {
        $configuration = $this->getConfiguration();
        if ($configuration === [] || !array_key_exists('curriculumTypes', $configuration)) {
            return collect();
        }

        return collect($configuration['curriculumTypes'])
            ->when(
                !FeatureToggle::isActive(Feature::OVSGLeerlokaal),
                fn(Collection $collection): Collection =>
                    $collection->reject(fn(int $curriculumTypeValue): bool =>
                        $curriculumTypeValue === CurriculumType::LeerLokaal->value),
            )
            ->map(fn(int $curriculumTypeValue): CurriculumType => CurriculumType::from($curriculumTypeValue));
    }
}
