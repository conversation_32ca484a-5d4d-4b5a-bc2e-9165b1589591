<?php

namespace Cfa\Common\Domain\School\EducationalNetwork;

use Illuminate\Support\Collection;

interface EducationalNetworkRepositoryInterface
{
    /**
     * Fetches all supported curriculum types of a network.
     *
     * @param EducationalNetwork $educationalnetwork The network to fetch the curriculum types for.
     *
     * @return Collection The curriculum types.
     */
    public function getSupportedCurriculumTypes(EducationalNetwork $educationalnetwork): Collection;

    public function getUsedEducationalNetworksWithSupportedCurriculumTypes(): Collection;
}
