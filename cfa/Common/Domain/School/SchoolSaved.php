<?php

namespace Cfa\Common\Domain\School;

use Illuminate\Queue\SerializesModels;

class SchoolSaved
{
    use SerializesModels;

    /**
     * The school model that was saved.
     *
     * @var School School
     */
    public $school;

    /**
     * Create a new event instance.
     *
     * @param School $school The created school.
     */
    public function __construct(School $school)
    {
        $this->school = $school;
    }
}
