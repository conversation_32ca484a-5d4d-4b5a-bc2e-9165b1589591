<?php

namespace Cfa\Common\Domain\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Override;

/**
 * @mixin User
 * @extends JsonResource<User>
 */
class CloudsearchUserResource extends JsonResource
{
    /**
     * {@inheritdoc}
     *
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        return [
            'user_uid' => $this->uid,
            'fullname' => $this->fullname,
        ];
    }
}
