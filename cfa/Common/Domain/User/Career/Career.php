<?php

namespace Cfa\Common\Domain\User\Career;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Scopes\ScopeWatcher;
use Cfa\Common\Application\Traits\StartAndEnddates;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Application\Traits\UpdatedBySMD;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * Cfa\Common\Domain\User\Career\Career
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $school_id
 * @property int $user_id
 * @property string|null $email
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon $startdate
 * @property Carbon|null $enddate
 * @property string|null $legacy_uid The old SOL-UID of the teacher
 * @property Carbon|null $smd_updated_at
 * @property int|null $role_id Link to database role.
 * @property int|null $group_id Link to a group.
 * @property string|null $group_name
 * @property int|null $smd_incremental_event_id
 * @property int|null $class_number The class number of the pupil in the group.
 * @property int $repeating_year
 * @property-read Collection<int, FollowUpSystem> $followUpSystems
 * @property-read int|null $follow_up_systems_count
 * @property-read string|null $fullname
 * @property-read string|null $user_uid
 * @property-read array $validation_rules
 * @property-read Group|null $group
 * @property-read Group|null $groupWithTrashed
 * @property-read Role|null $role
 * @property-read School $school
 * @property-read User $user
 * @method static Builder|Career active(?Carbon $date = null)
 * @method static CareerFactory factory($count = null, $state = [])
 * @method static CareerRepositoryInterface getRepository()
 * @method static Builder|Career newModelQuery()
 * @method static Builder|Career newQuery()
 * @method static Builder|Career onlyTrashed()
 * @method static Builder|Career query()
 * @method static int|null randomClassNumber()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static string|null randomEmail()
 * @method static Carbon|null randomEnddate()
 * @method static int|null randomGroupId()
 * @method static string|null randomGroupName()
 * @method static int randomId()
 * @method static string|null randomLegacyUid()
 * @method static int randomRepeatingYear()
 * @method static int|null randomRoleId()
 * @method static int randomSchoolId()
 * @method static int|null randomSmdIncrementalEventId()
 * @method static Carbon|null randomSmdUpdatedAt()
 * @method static Carbon randomStartdate()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int randomUserId()
 * @method static Builder|Career whereClassNumber($value)
 * @method static Builder|Career whereCreatedAt($value)
 * @method static Builder|Career whereDeletedAt($value)
 * @method static Builder|Career whereEmail($value)
 * @method static Builder|Career whereEnddate($value)
 * @method static Builder|Career whereGroupId($value)
 * @method static Builder|Career whereGroupName($value)
 * @method static Builder|Career whereId($value)
 * @method static Builder|Career whereLegacyUid($value)
 * @method static Builder|Career whereRepeatingYear($value)
 * @method static Builder|Career whereRoleId($value)
 * @method static Builder|Career whereSchoolId($value)
 * @method static Builder|Career whereSmdIncrementalEventId($value)
 * @method static Builder|Career whereSmdUpdatedAt($value)
 * @method static Builder|Career whereStartdate($value)
 * @method static Builder|Career whereUid($value)
 * @method static Builder|Career whereUpdatedAt($value)
 * @method static Builder|Career whereUserId($value)
 * @method static Builder|Career withRole(RoleName $role)
 * @method static Builder|Career withTrashed()
 * @method static Builder|Career withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class Career extends Model
{
    use SoftDeletes;
    use Uid;
    use UpdatedBySMD;
    use StartAndEnddates;
    use ScopeWatcher;

    public const int REPEATING_YEAR_MAX_VALUE = 255;

    /**
     * {@inheritdoc}
     * Career at this point can only be changed using the SMS calls, so we use guarded at this point.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'registration_number' => 'integer',
        'school_id' => 'integer',
        'user_id' => 'integer',
        'role_id' => 'integer',
        'group_id' => 'integer',
        'class_number' => 'integer',
    ];

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'saved' => CareerSaved::class,
        'created' => CareerCreated::class,
    ];

    /**
     * The attributes that should be visible in serialization.
     *
     * @var array
     */
    protected $visible = [
        'user',
    ];

    /**
     * Get the school the career belongs to.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the follow up systems this career has access to.
     */
    public function followUpSystems(): BelongsToMany
    {
        return $this->belongsToMany(FollowUpSystem::class);
    }

    /**
     * Get the User this career belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The group the career belongs to.
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function groupWithTrashed(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'group_id')->withoutGlobalScopes();
    }

    /**
     * The role the career belongs to.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Check if a career has a specific role.
     *
     * @param RoleName $role The role to check.
     */
    public function hasRole(RoleName $role): bool
    {
        return $this->role && $this->role->role_name === $role;
    }

    /**
     * Check if a career has at least one of the given roles.
     *
     * @param array $roles The roles to check.
     */
    public function hasAtLeastOneRole(array $roles): bool
    {
        return $this->role && in_array($this->role->role_name->name, $roles);
    }

    /**
     * Set the career's role.
     *
     * @param RoleName $roleName The role to assign.
     */
    public function setRole(RoleName $roleName): self
    {
        if ($role = Role::where('role_name_enum', $roleName)->first()) {
            $this->role_id = $role->id;
        }

        return $this;
    }

    /**
     * Remove the role from a career.
     */
    public function removeRole(): self
    {
        $this->role_id = null;

        return $this;
    }

    /**
     * Set the career's group.
     *
     * @param Group $group The group to assign.
     */
    public function setGroup(Group $group): self
    {
        $this->group_id = $group->id;

        return $this;
    }

    /**
     * Remove the group from a career.
     */
    public function removeGroup(): self
    {
        $this->group_id = null;

        return $this;
    }

    /**
     * Tests if this career is currently in use.
     */
    public function isActive(?Carbon $date = null): bool
    {
        $date ??= Carbon::now();

        return $this->startdate <= $date &&
            (is_null($this->enddate) || $this->enddate > $date);
    }

    /**
     * Get the full name of the career.
     */
    public function getFullnameAttribute(): ?string
    {
        return $this->user ? $this->user->fullname : null;
    }

    /**
     * Get the user uid of the career.
     */
    public function getUserUidAttribute(): ?string
    {
        return $this->user ? $this->user->uid : null;
    }

    /**
     * Scope to return all the users with a career containing the given role.
     *
     * @param Builder $query The query.
     * @param RoleName $role The role the users should have.
     */
    public function scopeWithRole(Builder $query, RoleName $role): Builder
    {
        $query
            ->select('careers.*')
            ->distinct()
            ->join('roles', 'careers.role_id', '=', 'roles.id')
            ->where('roles.name', $role->name);

        return $query;
    }

    public function scopeActive(Builder $query, ?Carbon $date = null): Builder
    {
        $date ??= Carbon::now();

        return $query->where('startdate', '<=', $date)
            ->where(function (Builder $query) use ($date): void {
                $query->whereNull('enddate')
                    ->orWhere('enddate', '>', $date);
            });
    }
}
