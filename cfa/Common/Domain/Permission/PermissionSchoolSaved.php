<?php

namespace Cfa\Common\Domain\Permission;

use Illuminate\Queue\SerializesModels;

class PermissionSchoolSaved
{
    use SerializesModels;

    /**
     * @var PermissionSchool
     */
    public $permissionSchool;

    /**
     * Create a new event instance.
     */
    public function __construct(PermissionSchool $permissionSchool)
    {
        $this->permissionSchool = $permissionSchool;
    }
}
