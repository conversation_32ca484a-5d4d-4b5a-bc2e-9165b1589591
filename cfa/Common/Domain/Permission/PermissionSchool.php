<?php

namespace Cfa\Common\Domain\Permission;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\PruneSoftDeletes;
use Cfa\Common\Application\Traits\RestoreSoftDeletes;
use Cfa\Common\Application\Traits\StartAndEnddates;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Validation\Rule;
use Override;

use function array_merge;
use function today;

/**
 * Cfa\Common\Domain\Permission\PermissionSchool
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property int $school_id
 * @property PermissionName $name
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon $startdate
 * @property Carbon|null $enddate
 * @property TargetAudienceType $target_audience_type
 * @property Carbon|null $licenses_updated_at
 * @property string|null $permission_hash
 * @property-read array $validation_rules
 * @property-read School $school
 * @method static PermissionSchoolFactory factory($count = null, $state = [])
 * @method static Builder|PermissionSchool newModelQuery()
 * @method static Builder|PermissionSchool newQuery()
 * @method static Builder|PermissionSchool onlyTrashed()
 * @method static Builder|PermissionSchool query()
 * @method static Carbon|null randomCreatedAt()
 * @method static Carbon|null randomDeletedAt()
 * @method static Carbon|null randomEnddate()
 * @method static int randomId()
 * @method static Carbon|null randomLicensesUpdatedAt()
 * @method static PermissionName randomName()
 * @method static string|null randomPermissionHash()
 * @method static int randomSchoolId()
 * @method static Carbon randomStartdate()
 * @method static TargetAudienceType randomTargetAudienceType()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|PermissionSchool whereCreatedAt($value)
 * @method static Builder|PermissionSchool whereDeletedAt($value)
 * @method static Builder|PermissionSchool whereEnddate($value)
 * @method static Builder|PermissionSchool whereId($value)
 * @method static Builder|PermissionSchool whereLicensesUpdatedAt($value)
 * @method static Builder|PermissionSchool whereName($value)
 * @method static Builder|PermissionSchool wherePermissionHash($value)
 * @method static Builder|PermissionSchool whereSchoolId($value)
 * @method static Builder|PermissionSchool whereStartdate($value)
 * @method static Builder|PermissionSchool whereTargetAudienceType($value)
 * @method static Builder|PermissionSchool whereUpdatedAt($value)
 * @method static Builder|PermissionSchool withTrashed()
 * @method static Builder|PermissionSchool withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class PermissionSchool extends Model implements LicenceInterface
{
    use PruneSoftDeletes;
    use RestoreSoftDeletes;
    use SoftDeletes;
    use StartAndEnddates {
        rules as protected startAndEnddatesRules;
    }

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'saved' => PermissionSchoolSaved::class,
        'created' => PermissionSchoolCreated::class,
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            $this->startAndEnddatesRules(),
            [
                'name' => [
                    'required',
                    Rule::in(PermissionName::getSchoolPermissions()),
                ],
                'target_audience_type' => [
                    'required',
                    Rule::in([TargetAudienceType::Ko, TargetAudienceType::Lo]),
                ],
                'school_id' => [
                    'required',
                ],
            ],
        );
    }

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];

    protected array $enums = [
        'name' => PermissionName::class,
        'target_audience_type' => TargetAudienceType::class,
    ];

    /** @var array */
    protected $casts = [
        'school_id' => 'integer',
        'licenses_updated_at' => 'datetime',
    ];

    /**
     * Get the schoolUserAccess linked to the permission.
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Creates a new permission or restores the permission if it already exists.
     *
     * @param int $schoolId The Id of the School that gets the Permission.
     * @param PermissionName $permissionName The PermissionName he needs to get.
     */
    public static function createOrRestorePermissionSchool(
        int $schoolId,
        PermissionName $permissionName,
        TargetAudienceType $audienceType,
        ?Carbon $startDate = null,
        ?Carbon $endDate = null,
    ): self {
        if ($startDate === null) {
            $startDate = today()->startOfDay();
        }
        /* @var PermissionSchool $schoolPermission */
        $schoolPermission = self::withTrashed()
            ->whereSchoolId($schoolId)
            ->whereName($permissionName)
            ->whereTargetAudienceType($audienceType)
            ->first();

        if ($schoolPermission) {
            $schoolPermission->startdate = $startDate;
            $schoolPermission->enddate = $endDate;

            return $schoolPermission->restoreIfTrashed();
        }

        $schoolPermission = new static(['name' => $permissionName]);
        $schoolPermission->school_id = $schoolId;
        $schoolPermission->startdate = $startDate;
        $schoolPermission->enddate = $endDate;
        $schoolPermission->target_audience_type = $audienceType;
        $schoolPermission->save();

        return $schoolPermission;
    }
}
