<?php

namespace Cfa\Common\Domain\Permission;

enum LicensePortalProductType: string
{
    case Yearbook = 'yearbook';
    case Chapter = 'chapter';
    case Planner = 'planner';
    case ReadingBox = 'reading_box';
    case Module = 'module';

    /**
     * @return self[]
     */
    public static function getPermissionSchoolProductTypes(): array
    {
        return [self::Planner, self::Module];
    }

    /**
     * @return self[]
     */
    public static function getCollectionLicenseProductTypes(): array
    {
        return [self::Yearbook, self::Chapter, self::ReadingBox];
    }
}
