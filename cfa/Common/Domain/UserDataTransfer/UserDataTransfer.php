<?php

namespace Cfa\Common\Domain\UserDataTransfer;

use App\Models\Model;
use Carbon\Carbon;
use Cfa\Common\Application\Traits\Uid;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Override;

use function array_merge;

/**
 * Cfa\Common\Domain\UserDataTransfer\UserDataTransfer
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid
 * @property int $from_user_id
 * @property int $to_user_id
 * @property string $ip
 * @property Carbon|null $confirmed_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property Carbon|null $expires_at
 * @property-read User|null $fromUser
 * @property-read array $validation_rules
 * @property-read User|null $toUser
 * @method static Builder|UserDataTransfer newModelQuery()
 * @method static Builder|UserDataTransfer newQuery()
 * @method static Builder|UserDataTransfer query()
 * @method static Carbon|null randomConfirmedAt()
 * @method static Carbon|null randomCreatedAt()
 * @method static string|null randomDeletedAt()
 * @method static Carbon|null randomExpiresAt()
 * @method static int randomFromUserId()
 * @method static int randomId()
 * @method static string randomIp()
 * @method static int randomToUserId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static Builder|UserDataTransfer whereConfirmedAt($value)
 * @method static Builder|UserDataTransfer whereCreatedAt($value)
 * @method static Builder|UserDataTransfer whereDeletedAt($value)
 * @method static Builder|UserDataTransfer whereExpiresAt($value)
 * @method static Builder|UserDataTransfer whereFromUserId($value)
 * @method static Builder|UserDataTransfer whereId($value)
 * @method static Builder|UserDataTransfer whereIp($value)
 * @method static Builder|UserDataTransfer whereToUserId($value)
 * @method static Builder|UserDataTransfer whereUid($value)
 * @method static Builder|UserDataTransfer whereUpdatedAt($value)
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class UserDataTransfer extends Model
{
    use Uid;
    use MassPrunable;

    /** @var array */
    protected $casts = [
        'from_user_id' => 'integer',
        'to_user_id' => 'integer',
        'expires_at' => 'datetime',
        'confirmed_at' => 'datetime',
    ];

    /**
     * {@inheritdoc}
     */
    #[Override]
    public function rules(): array
    {
        return array_merge(
            parent::rules(),
            [
                'from_user_id' => ['required', 'integer'],
                'to_user_id' => ['required', 'integer'],
                'confirmed_at' => ['nullable', 'date'],
                'expires_at' => ['required', 'date'],
                'ip' => ['required', 'ip'],
            ],
        );
    }

    /**
     * The User to transfer the data.
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id')
            ->withTrashed();
    }

    /**
     * The User to get the data.
     */
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Check if this UserDataTransfer is still active.
     */
    public function isActive(): bool
    {
        return Carbon::now()->lessThan($this->expires_at);
    }

    public function prunable(): Builder
    {
        return static::where('expires_at', '<=', Carbon::now()->subYear());
    }
}
