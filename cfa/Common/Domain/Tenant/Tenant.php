<?php

namespace Cfa\Common\Domain\Tenant;

use Cfa\Admin\Domain\CmsUser\CmsUser;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Cfa\Planner\Domain\Settings\Activity\ActivitySettings;
use Cfa\Planner\Domain\Settings\Activity\ActivityType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use ValueError;

use function asset;
use function config;

/**
 * App\Models\Tenant
 *
 * @property-read array $educationalNetworkMapping
 * @property-read TenantId $id
 * @property-read Collection|School[] $schools
 */
class Tenant
{
    /**
     * The configuration array.
     *
     * @var array
     */
    public $config;

    /**
     * Tenant's identifier.
     *
     * @var string
     */
    public $uid;

    final private function __construct()
    {
        // Use getFromUid to construct a tenant instead.
    }

    /**
     * Find tenant in config using the Tenant's id.
     *
     * @param string $uid Tenant's uid.
     */
    public static function getFromUid(string $uid): ?self
    {
        if ($config = config('tenants.' . $uid)) {
            $tenant = new static();
            $tenant->config = $config;
            $tenant->uid = $uid;

            return $tenant;
        }

        return null;
    }

    /**
     * Find tenant in config using the database id.
     *
     * @param int $tenantId Tenant's id as used in database.
     */
    public static function getFromId(int $tenantId): ?self
    {
        foreach (config('tenants') as $uid => $config) {
            if ($config['id'] == $tenantId) {
                return static::getFromUid($uid);
            }
        }

        return null;
    }

    /**
     * Get the preferred language linked to the tenant.
     */
    public function preferredLanguage(): string
    {
        return $this->config['localeConfig']['preferredLocale'];
    }

    /**
     * Get the full preferred language (including region suffix) linked to the tenant.
     */
    public function fullPreferredLanguage(): string
    {
        return $this->getFullLanguage('preferred');
    }

    /**
     * Get the fallback language linked to the tenant.
     */
    public function fallbackLanguage(): string
    {
        return $this->config['localeConfig']['fallbackLocale'];
    }

    /**
     * Get the full fallback language (including region suffix) linked to the tenant.
     */
    public function fullFallbackLanguage(): string
    {
        return $this->getFullLanguage('fallback');
    }

    /**
     * Get the full language (including region suffix) linked to the tenant.
     *
     * @param string $language Language to retrieve (preferred or fallback).
     */
    protected function getFullLanguage(string $language): string
    {
        return $this->config['localeConfig'][$language . 'Locale']
            . '-'
            . $this->config['localeConfig'][$language . 'Suffix'];
    }

    /**
     * Getter for help_url_base.
     */
    public function getHelpUrlBaseAttribute(): string
    {
        return config('app.helpUrlBase');
    }

    /**
     * Get the analytics for the tenant.
     */
    public function getAnalytics(): array
    {
        return $this->config['analytics'];
    }

    /**
     * Get the educational network configuration for the tenant.
     */
    public function getEducationalNetworkConfiguration(): array
    {
        return $this->config['educationalNetworkConfig'];
    }

    /**
     * Get the external urls configuration for the tenant.
     */
    public function getExternalUrls(): array
    {
        return $this->config['externalUrls'];
    }

    /**
     * Get the internal urls configuration for the tenant.
     */
    public function getInternalUrls(): array
    {
        return $this->config['internalUrls'];
    }

    /**
     * Gets the default calendar activity settings for the tenant.
     */
    public function getDefaultActivitySettings(): SupportCollection
    {
        return collect($this->config['activitySettings'])->map(function ($activitySettings) {
            $activitySettings['activity_type'] = ActivityType::from($activitySettings['activity_type']);

            return new ActivitySettings($activitySettings);
        });
    }

    /**
     * Get the AWS configuration for the tenant.
     */
    public function getAwsConfiguration(): array
    {
        return [
            's3Url' => $this->getS3Endpoint(
                config('filesystems.disks.s3.bucket'),
                config('filesystems.disks.s3.region'),
            ),
            's3Prefix' => config('filesystems.disks.s3.root'),
            'useCdn' => config('app.cdn'),
            'assetUrl' => config('app.asset_url'),
        ];
    }

    /**
     * Get the URL to the learning trail resource.
     *
     * @param string $educationalNetworkUid Educational network for which to retrieve the learning trail URL.
     */
    public function getLearningTrailUrl(string $educationalNetworkUid): string
    {
        return  asset('/learningTrail/' . $educationalNetworkUid);
    }

    /**
     * Get the S3 endpoint based on the bucket and the region.
     *
     * @param string $bucket Bucket for the S3 endpoint.
     * @param string $region Region for the S3 endpoint.
     */
    private function getS3Endpoint(string $bucket, string $region): string
    {
        return 'https://' . $bucket . '.s3-' . $region . '.amazonaws.com';
    }

    /**
     * Get a magic property using the tenant's config.
     *
     * @param string $name The config property.
     */
    public function __get(string $name): mixed
    {
        if ($name === 'id') {
            try {
                return TenantId::from($this->config['id']);
            } catch (ValueError $exception) {
                return $this->config['id'];
            }
        }

        if (method_exists($this, $name)) {
            // Return collection result.
            return $this->{$name}(false);
        }

        return $this->config[$name] ?? null;
    }

    /**
     * Get the AppSwitcher configuration.
     */
    public function getAppSwitcherConfiguration(): array
    {
        return $this->config['appSwitcherConfig'];
    }

    /**
     * Get the AppSwitcher configuration, pass a user model to filter based on permission.
     */
    public function getAppSwitcherConfigurationForUser(User|CmsUser|null $user): array
    {
        $appSwitcher = collect($this->getAppSwitcherConfiguration());

        return $appSwitcher->filter(function (array $app) use ($user) {
            if (isset($app['permission']) && $user instanceof User) {
                return $user->hasPermission(PermissionName::from($app['permission']), school());
            }

            if (isset($app['gate']) && $user instanceof User) {
                $inverseCheck = Str::startsWith($app['gate'] ?? '', 'not:');
                $gate = Str::replaceFirst('not:', '', $app['gate']);

                return $inverseCheck ? Gate::forUser($user)->denies($gate) : Gate::forUser($user)->allows($gate);
            }

            return true;
        })->toArray();
    }
}
