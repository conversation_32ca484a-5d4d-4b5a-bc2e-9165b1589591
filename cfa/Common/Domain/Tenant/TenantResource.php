<?php

namespace Cfa\Common\Domain\Tenant;

use App\Constants\SessionKeys;
use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use Cfa\Common\Domain\User\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Override;

use function config;
use function school;

/**
 * @mixin Tenant
 * @extends JsonResource<Tenant>
 */
class TenantResource extends JsonResource
{
    /**
     * {@inheritdoc}
     * @param Request $request The request.
     */
    #[Override]
    public function toArray($request): array
    {
        $permissions = null;
        if (Auth::user() instanceof User) {
            $school = school();
            $permissions = Auth::user()->getPermissionsForUserInSchool($school);
        }

        return [
            'tenant' => $this->uid,
            'i18n' => [
                'preferred' => $this->getLocale(),
                'fullPreferred' => $this->getFullLocale(),
                'fallback' => $this->fallbackLanguage(),
                'fullFallback' => $this->fullFallbackLanguage(),
            ],
            'timezone' => config('app.timezone'),
            'educationalNetworkConfig' => $this->getEducationalNetworkConfiguration(),
            'externalUrls' => $this->getExternalUrls(),
            'internalUrls' => $this->getInternalUrls(),
            'features' => $this->getFeatureFlags(),
            'permissions' => $permissions,
            'aws' => $this->getAwsConfiguration(),
            'bingelBaseUrl' => config('cfa.url.bingel'),
            'appSwitcherConfig' => $this->getAppSwitcherConfiguration(),
            'securityExtensionWhitelist' => config('security-extension-whitelist'),
        ];
    }

    /**
     * Get all of the feature flags with their active state;
     */
    private function getFeatureFlags(): array
    {
        return collect(Feature::getFrontEndFlags())->flatMap(
            fn(Feature $feature): array => [$feature->value => FeatureToggle::isActive($feature)],
        )->all();
    }

    private function getLocale(): string
    {
        return Session::get(SessionKeys::SESSION_LOCALE) ?? $this->preferredLanguage();
    }

    private function getFullLocale(): string
    {
        if ($this->getLocale() === 'en') {
            return 'en-gb';
        }

        return $this->fullPreferredLanguage();
    }
}
