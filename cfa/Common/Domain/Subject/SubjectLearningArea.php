<?php

namespace Cfa\Common\Domain\Subject;

use Carbon\Carbon;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Overwrite\SubjectOverwrite;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\Roster\Timeslot\Timeslot;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Override;

/**
 * Cfa\Common\Domain\Subject\SubjectLearningArea
 *
 * @codingStandardsIgnoreStart
 * @property int $id
 * @property string $uid Unique identifier for the subject
 * @property int|null $school_id The School it belongs to
 * @property int|null $curriculumnode_id The Curriculumnode it belongs to
 * @property string $name Name of the subject
 * @property int $color_index
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 * @property Carbon|null $archived_at
 * @property CalendarType $calendar_type
 * @property int|null $parent_id The parent subject.
 * @property int|null $educationalnetwork_id
 * @property int|null $icon_id
 * @property-read Collection<int, CalendarItem> $calendaritems
 * @property-read int|null $calendaritems_count
 * @property-read User|null $creator
 * @property-read CurriculumNode|null $curriculumnode
 * @property-read EducationalNetwork|null $educationalnetwork
 * @property-read Collection<int, EvaluationSubjectPermission> $evaluationSubjectPermissions
 * @property-read int|null $evaluation_subject_permissions_count
 * @property-read Collection<int, EvaluationTest> $evaluationTests
 * @property-read int|null $evaluation_tests_count
 * @property-read bool $can_edit
 * @property-read string|null $full_name
 * @property-read bool $has_relations
 * @property-read array $validation_rules
 * @property-read Subject|null $parent
 * @property-read Collection<int,Redicodi> $redicodis
 * @property-read int|null $redicodis_count
 * @property-read School|null $school
 * @property-read Icon|null $selectedIcon
 * @property-read Collection<int, SubjectOverwrite> $subjectOverwrites
 * @property-read int|null $subject_overwrites_count
 * @property-read Collection<int,SubjectQuotationSettings> $subjectQuotationSettings
 * @property-read int|null $subject_quotation_settings_count
 * @property-read Collection<int, Timeslot> $timeslots
 * @property-read int|null $timeslots_count
 * @property-read User|null $updater
 * @method static SubjectLearningAreaFactory factory($count = null, $state = [])
 * @method static Builder|Subject forSchool(?School $school = null)
 * @method static Builder|SubjectLearningArea newModelQuery()
 * @method static Builder|SubjectLearningArea newQuery()
 * @method static Builder|SubjectLearningArea onlyTrashed()
 * @method static Builder|SubjectLearningArea query()
 * @method static Carbon|null randomArchivedAt()
 * @method static CalendarType randomCalendarType()
 * @method static int randomColorIndex()
 * @method static Carbon|null randomCreatedAt()
 * @method static int|null randomCreatorId()
 * @method static int|null randomCurriculumnodeId()
 * @method static Carbon|null randomDeletedAt()
 * @method static int|null randomEducationalnetworkId()
 * @method static int|null randomIconId()
 * @method static int randomId()
 * @method static string randomName()
 * @method static int|null randomParentId()
 * @method static int|null randomSchoolId()
 * @method static string randomUid()
 * @method static Carbon|null randomUpdatedAt()
 * @method static int|null randomUpdaterId()
 * @method static Builder|SubjectLearningArea whereArchivedAt($value)
 * @method static Builder|SubjectLearningArea whereCalendarType($value)
 * @method static Builder|SubjectLearningArea whereColorIndex($value)
 * @method static Builder|SubjectLearningArea whereCreatedAt($value)
 * @method static Builder|SubjectLearningArea whereCreatorId($value)
 * @method static Builder|SubjectLearningArea whereCurriculumnodeId($value)
 * @method static Builder|SubjectLearningArea whereDeletedAt($value)
 * @method static Builder|SubjectLearningArea whereEducationalnetworkId($value)
 * @method static Builder|SubjectLearningArea whereIconId($value)
 * @method static Builder|SubjectLearningArea whereId($value)
 * @method static Builder|SubjectLearningArea whereName($value)
 * @method static Builder|SubjectLearningArea whereParentId($value)
 * @method static Builder|SubjectLearningArea whereSchoolId($value)
 * @method static Builder|SubjectLearningArea whereUid($value)
 * @method static Builder|SubjectLearningArea whereUpdatedAt($value)
 * @method static Builder|SubjectLearningArea whereUpdaterId($value)
 * @method static Builder|SubjectLearningArea withTrashed()
 * @method static Builder|SubjectLearningArea withoutTrashed()
 * @mixin \Eloquent
 * @codingStandardsIgnoreEnd
 */
class SubjectLearningArea extends Subject
{
    #[Override]
    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope('subjectLearningArea', fn(Builder $query): Builder => $query->whereNull('parent_id'));
    }

    #[Override]
    public function getHasRelationsAttribute(): bool
    {
        return parent::getHasRelationsAttribute() || SubjectDomain::whereParentId($this->id)->exists();
    }
}
