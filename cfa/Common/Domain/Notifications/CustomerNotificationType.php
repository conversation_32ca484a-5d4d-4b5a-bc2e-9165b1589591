<?php

namespace Cfa\Common\Domain\Notifications;

enum CustomerNotificationType: string
{
    case PLANNER = 'planner';
    case CARE = 'care';
    case EVALUATION = 'evaluation';
    case SETTINGS_CARE = 'settings_care';
    case SETTINGS_PLANNER = 'settings_planner';
    case SETTINGS_EVALUATION = 'settings_evaluation';
    case SETTINGS_COMMON = 'settings_common';

    public function getModelType(): string
    {
        return 'show_in_' . $this->value;
    }
}
