<?php

namespace Tests\Traits;

use Cfa\Common\Domain\School\Group\Group;
use Illuminate\Support\Collection;

trait GroupSelectorData
{
    protected function getGroupSelectorDataStructure(Collection $groups, string $redirectLink): array
    {
        $groups->each->refresh();

        return $groups->values()
            ->map(function (Group $group) use ($redirectLink) {
                return [
                    'redirectLink' => route(
                        $redirectLink,
                        ['group' => $group->uid],
                    ),
                    'uid' => $group->uid,
                    'name' => $group->name,
                    'target_audience_type' => optional($group->target_audience_type)->toArray(),
                    'natural_study_year' => $group->natural_study_year,
                ];
            })->toArray();
    }
}
