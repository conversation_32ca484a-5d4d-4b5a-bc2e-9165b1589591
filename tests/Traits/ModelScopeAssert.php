<?php

namespace Tests\Traits;

use App\Events\ModelScopeCalled;
use Illuminate\Support\Facades\Event;

trait ModelScopeAssert
{
    /** Use the method after calling factories, because model event listeners won't trigger */
    public function expectScopeCall(): void
    {
        Event::fake([ModelScopeCalled::class]);
    }

    public function assertScopeCalled(string $scope, string $model): void
    {
        $triggeredScopes = [];

        Event::assertDispatched(ModelScopeCalled::class);
        Event::assertDispatched(function (ModelScopeCalled $event) use (&$triggeredScopes) {
            $triggeredScopes[$event->model][] = $event->scope;

            return true;
        });

        $this->assertContains($scope, $triggeredScopes[$model]);
    }
}
