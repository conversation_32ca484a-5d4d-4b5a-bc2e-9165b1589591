<?php

namespace Tests\Traits;

use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTest;
use Cfa\Evaluation\Domain\EvaluationTest\Score\EvaluationTestScore;
use Cfa\Evaluation\Domain\QuotationSystem\Quotation\Quotation;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;

trait QuotationSystemSeeding
{
    use WithSeeding;

    /**
     * Create EvaluationTestScore with all its dependencies.
     */
    public function createEvaluationTestScore(): void
    {
        $group = $this->createGroupWithPupils();

        $quotation = Quotation::factory()
            ->setQuotationSystemId($this->model->id)
            ->create();

        $subject = Subject::factory()->create();
        $reportPeriod = ReportPeriod::factory()
            ->forReportSettings(ReportSettings::factory()->forGroup($group)->create())
            ->create(
                [
                    'schoolyear_id' => $this->schoolyear->id,
                ],
            );
        $evaluationTest = factory(EvaluationTest::class)->create([
            'school_id' => $this->school->id,
            'group_id' => $group->id,
            'subject_id' => $subject->id,
            'report_period_id' => $reportPeriod->id,
        ]);

        EvaluationTestScore::factory()
            ->forQuotation($quotation)
            ->forEvaluationTest($evaluationTest)
            ->forPupil(Pupil::first())
            ->create();
    }
}
