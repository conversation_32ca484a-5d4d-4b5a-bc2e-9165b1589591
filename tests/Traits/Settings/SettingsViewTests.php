<?php

namespace Tests\Traits\Settings;

use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;

trait SettingsViewTests
{
    /**
     * {@inheritdoc}
     */
    protected function setUpSettingsViewTests(): void
    {
        $this->method = Request::METHOD_GET;
    }

    /**
     * Tests if the create view is returned correctly.
     *
     * @param string $routeName The name of the route we want to test.
     */
    #[DataProvider('routeNameDataProvider')]
    #[Test]
    public function it_shows_the_correct_view(string $routeName, ?string $redirectName = null): void
    {
        $response = $this->setRouteName($routeName)
            ->setAuthenticatedUser($this->ictAdmin)
            ->jsonWithRequiredHeaders();
        $response->assertResponseOk()->assertViewIs($this->viewName);
    }
}
