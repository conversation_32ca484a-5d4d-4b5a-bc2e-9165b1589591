<?php

namespace Tests\Traits\Smd;

use App\Models\SMD\SmdExternalType;
use App\Models\SMD\SmdOperation;
use Error;
use PHPUnit\Framework\Attributes\Test;

use function factory;
use function uuid;

trait SmdExternalUidTrait
{
    /**
     * Test if the model to create is found with one of the given external uid's.
     * If this is the case, the model is updated.
     */
    #[Test]
    public function it_updates_model_when_operation_create_and_external_uid_bingel_is_found(): void
    {
        $uid = uuid();

        $model = $this->modelClass::factory()->create(['smd_external_uid' => $uid]);

        $this->payload['externalUids'] = ['BINGEL' => $uid];
        $originalCount = $this->model->count();

        $this->operation = SmdOperation::create;
        $this->runJob();

        $newCount = $this->model->count();
        self::assertEquals($originalCount, $newCount);
        $this->assertModelFields($model->fresh());
    }

    /**
     * Test if the model to create is found with one of the given external uid's.
     * If this is the case, the model is updated.
     */
    #[Test]
    public function it_updates_model_when_operation_create_and_external_ethos_uid_is_found(): void
    {
        $uid = uuid();

        $model = $this->modelClass::factory()->create(['smd_external_uid' => $uid]);

        $this->payload['externalUids'] = ['ethos' => $uid];
        $originalCount = $this->model->count();

        $this->operation = SmdOperation::create;
        $this->runJob();

        $newCount = $this->model->count();
        self::assertEquals($originalCount, $newCount);
        $this->assertModelFields($model->fresh());
    }

    /**
     * Test if a new model is created when there are no models found matching the given external uid, because
     * smd_external_uid is empty on existing model.
     */
    #[Test]
    public function it_creates_a_new_entity_if_model_is_not_found_because_smd_external_uid_is_null_on_model(): void
    {
        $this->modelClass::factory()->create(['smd_external_uid' => null]);

        $this->payload['externalUids'] = ['BINGEL' => uuid()];
        $originalCount = $this->model->count();

        $this->operation = SmdOperation::create;
        $this->runJob();

        $newCount = $this->model->count();
        self::assertEquals($originalCount + 1, $newCount);
        $model = $this->model->orderByDesc('id')->first();
        $this->assertModelFields($model);
    }

    /**
     * Test if a new model is created when there are no models found matching the given external uid's.
     */
    #[Test]
    public function it_creates_a_new_entity_if_model_is_not_found_with_given_external_uids(): void
    {
        $this->modelClass::factory()->create(['uid' => uuid()]);

        $this->payload['externalUids'] = ['BINGEL' => uuid()];
        $originalCount = $this->model->count();

        $this->operation = SmdOperation::create;
        $this->runJob();

        $newCount = $this->model->count();
        self::assertEquals($originalCount + 1, $newCount);
        $model = $this->model->orderByDesc('id')->first();
        $this->assertModelFields($model);
    }

    #[Test]
    public function it_adds_the_smd_external_uid_when_its_uppercase(): void
    {
        $this->payload['externalUids']['BINGEL'] = 'bingel-internal-id';

        $this->operation = SmdOperation::create;
        $this->runJob();

        $model = $this->model->orderByDesc('id')->first();

        self::assertSame('bingel-internal-id', $model->smd_external_uid);
        self::assertSame(SmdExternalType::Bingel, $model->smd_external_type);
    }

    #[Test]
    public function it_adds_the_smd_external_uid_when_its_lowercase(): void
    {
        $this->payload['externalUids']['bingel'] = 'bingel-internal-id';

        $this->operation = SmdOperation::create;
        $this->runJob();

        $model = $this->model->orderByDesc('id')->first();

        self::assertSame('bingel-internal-id', $model->smd_external_uid);
        self::assertSame(SmdExternalType::Bingel, $model->smd_external_type);
    }

    #[Test]
    public function it_adds_the_ethos_smd_external_uid_if_multiple_are_available(): void
    {
        $this->payload['externalUids'] = [
            'bingel' => 'bingel-internal-id',
            'ethos' => 'ethos-internal-id',
        ];

        $this->operation = SmdOperation::create;
        $this->runJob();

        $model = $this->model->orderByDesc('id')->first();

        self::assertSame('ethos-internal-id', $model->smd_external_uid);
        self::assertSame(SmdExternalType::Ethos, $model->smd_external_type);
    }

    #[Test]
    public function it_adds_the_bingel_uid_when_ethos_uid_is_empty(): void
    {
        $this->payload['externalUids']['ethos'] = '';
        $this->payload['externalUids']['bingel'] = 'bingel-internal-id';

        $this->operation = SmdOperation::create;
        $this->runJob();

        $model = $this->model->orderByDesc('id')->first();

        $this->assertSame('bingel-internal-id', $model->smd_external_uid);
        $this->assertSame(SmdExternalType::Bingel, $model->smd_external_type);
    }

    #[Test]
    public function it_empties_smd_external_uid_if_the_type_is_not_known(): void
    {
        $this->payload['externalUids'] = ['bart' => 'bart-internal-id-for-person'];

        try {
            $model = $this->modelClass::factory()->create([
                'uid' => $this->payload['uid'],
                'smd_external_uid' => uuid(),
                'smd_external_type' => SmdExternalType::Bingel,
            ]);
        } catch (Error $error) {
            $model = factory($this->modelClass)->create([
                'uid' => $this->payload['uid'],
                'smd_external_uid' => uuid(),
                'smd_external_type' => SmdExternalType::Bingel,
            ]);
        }

        $this->operation = SmdOperation::update;
        $this->runJob();

        $model = $model->fresh();

        self::assertNull($model->smd_external_uid);
        self::assertNull($model->smd_external_type);
    }

    #[Test]
    public function it_ignores_smd_external_uid_if_the_type_is_not_known(): void
    {
        $this->payload['externalUids'] = [
            'bart' => 'bart-internal-id-for-person',
            'bingel' => 'bingel-internal-id-for-person',
        ];

        $this->operation = SmdOperation::create;
        $this->runJob();

        $model = $this->model->orderByDesc('id')->first();

        self::assertSame('bingel-internal-id-for-person', $model->smd_external_uid);
        self::assertSame(SmdExternalType::Bingel, $model->smd_external_type);
    }
}
