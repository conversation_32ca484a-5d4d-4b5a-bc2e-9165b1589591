<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="email">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9!#-'\*\+\-/=\?\^_`\{-~]+(\.[A-Za-z0-9!#-'\*\+\-/=\?\^_`\{-~]+)*@[A-Za-z0-9!#-'\*\+\-/=\?\^_`\{-~]+(\.[A-Za-z0-9!#-'\*\+\-/=\?\^_`\{-~]+)*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="isni">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{15}[0-9X]{1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="OLFMessage">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Header">
					<xs:complexType>
						<xs:all>
							<xs:element name="Sender" type="xs:string"/>
							<xs:element name="SentDateTime" type="xs:string"/>
						</xs:all>
					</xs:complexType>
				</xs:element>
				<xs:element name="Publishers">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded">
							<xs:element name="Publisher">
								<xs:complexType>
									<xs:all>
										<xs:element name="PublisherIdentifier" type="isni"/>
										<xs:element name="PublisherName" type="xs:string"/>
										<xs:element name="PublisherEmail" type="xs:string"/>
										<xs:element name="Methods">
											<xs:complexType>
												<xs:choice maxOccurs="unbounded">
													<xs:element name="Method">
														<xs:complexType>
															<xs:all>
																<xs:element name="MethodIdentifier" type="xs:string" />
																<xs:element name="MethodName" type="xs:string" minOccurs="0" />
																<xs:element name="TargetAudiences" minOccurs="0">
																	<xs:complexType>
																		<xs:choice minOccurs="0" maxOccurs="unbounded">
																			<xs:element name="TargetAudience">
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:enumeration value="KO-J0"/>
																						<xs:enumeration value="KO-J1"/>
																						<xs:enumeration value="KO-J2"/>
																						<xs:enumeration value="KO-J3"/>
																						<xs:enumeration value="LO-J1"/>
																						<xs:enumeration value="LO-J2"/>
																						<xs:enumeration value="LO-J3"/>
																						<xs:enumeration value="LO-J4"/>
																						<xs:enumeration value="LO-J5"/>
																						<xs:enumeration value="LO-J6"/>
																						<xs:enumeration value="SO-J1"/>
																						<xs:enumeration value="SO-J2"/>
																						<xs:enumeration value="SO-J3"/>
																						<xs:enumeration value="SO-J4"/>
																						<xs:enumeration value="SO-J5"/>
																						<xs:enumeration value="SO-J6"/>
																						<xs:enumeration value="SO-G1"/>
																						<xs:enumeration value="SO-G2"/>
																						<xs:enumeration value="SO-G3"/>
																						<xs:enumeration value="SO-ASTR"/>
																						<xs:enumeration value="SO-BSTR"/>
																						<xs:enumeration value="SO-ASO"/>
																						<xs:enumeration value="SO-BSO"/>
																						<xs:enumeration value="SO-TSO"/>
																						<xs:enumeration value="SO-KSO"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																		</xs:choice>
																	</xs:complexType>
																</xs:element>
																<xs:element name="LearningObjectives">
																	<xs:complexType>
																		<xs:choice maxOccurs="unbounded">
																			<xs:element name="LearningObjective">
																				<xs:complexType>
																					<xs:all>
																						<xs:element name="LearningObjectiveIdentifier" type="xs:string"/>
																						<xs:element name="LearningObjectiveDescription" type="xs:string" minOccurs="0"/>
																						<xs:element name="LearningObjectiveCategories" minOccurs="1">
																							<xs:complexType>
																								<xs:choice minOccurs="1" maxOccurs="unbounded">
																									<xs:element name="LearningObjectiveCategory">
																										<xs:complexType>
																											<xs:simpleContent>
																												<xs:extension base="xs:string">
																													<xs:attribute name="type" use="required">
																														<xs:simpleType>
																															<xs:restriction base="xs:string">
																																<xs:enumeration value="CHAPTER"/>
																																<xs:enumeration value="LEARNINGAREA"/>
																																<xs:enumeration value="LEARNINGDOMAIN"/>
																																<xs:enumeration value="LESSONNUMBER"/>
																																<xs:enumeration value="LESSON"/>
																																<xs:enumeration value="THEME"/>
																																<xs:enumeration value="BLOCK"/>
																																<xs:enumeration value="OBJECTIVEPRIORITY"/>
																															</xs:restriction>
																														</xs:simpleType>
																													</xs:attribute>
																												</xs:extension>
																											</xs:simpleContent>
																										</xs:complexType>
																									</xs:element>
																								</xs:choice>
																							</xs:complexType>
																						</xs:element>
																						<xs:element name="LearningObjectiveCodes" minOccurs="0">
																							<xs:complexType>
																								<xs:choice minOccurs="0" maxOccurs="unbounded">
																									<xs:element name="LearningObjectiveCode">
																										<xs:complexType>
																											<xs:simpleContent>
																												<xs:extension base="xs:string">
																													<xs:attribute name="type" use="required">
																														<xs:simpleType>
																															<xs:restriction base="xs:string">
																																<xs:enumeration value="GO"/>
																																<xs:enumeration value="OVSG"/>
																																<xs:enumeration value="VVKBAO"/>
																																<xs:enumeration value="VVKSO"/>
																																<xs:enumeration value="POV"/>
																																<xs:enumeration value="VSKO"/>
																															</xs:restriction>
																														</xs:simpleType>
																													</xs:attribute>
																													<xs:attribute name="codetype">
																														<xs:simpleType>
																															<xs:restriction base="xs:string">
																																<xs:enumeration value="short"/>
																																<xs:enumeration value="full"/>
																															</xs:restriction>
																														</xs:simpleType>
																													</xs:attribute>
																												</xs:extension>
																											</xs:simpleContent>
																										</xs:complexType>
																									</xs:element>
																								</xs:choice>
																							</xs:complexType>
																						</xs:element>
																						<xs:element name="Qualifications" minOccurs="0">
																							<xs:complexType>
																								<xs:choice minOccurs="0" maxOccurs="unbounded">
																									<xs:element name="Qualification" type="xs:string"/>
																								</xs:choice>
																							</xs:complexType>
																						</xs:element>
																					</xs:all>
																				</xs:complexType>
																			</xs:element>
																		</xs:choice>
																	</xs:complexType>
																</xs:element>
															</xs:all>
														</xs:complexType>
													</xs:element>
												</xs:choice>
											</xs:complexType>
										</xs:element>
									</xs:all>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="version" use="required">
				<xs:simpleType>
					<xs:restriction base="xs:decimal">
						<xs:minExclusive value="0"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
