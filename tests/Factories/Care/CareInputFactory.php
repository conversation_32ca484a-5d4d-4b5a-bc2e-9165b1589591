<?php

namespace Tests\Factories\Care;

use Carbon\Carbon;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareTheme\CareTheme;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\CareInput\VisibilityType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Faker\Generator;
use Illuminate\Support\Collection;
use Override;
use Tests\Factories\Factory;

class CareInputFactory extends Factory
{
    protected string $uid;

    /** @var Collection<int, Pupil> */
    protected Collection $pupils;

    /** @var Collection<int, CareInfo> */
    protected Collection $careInfos;

    /** @var Collection<int, CareTheme> */
    protected Collection $careThemes;

    protected int $careThemeAmount = 0;

    protected CareType $careType;

    protected Carbon $date;

    protected School $school;

    protected User $creator;

    private VisibilityType $visibilityType = VisibilityType::Everyone;

    public function __construct(Generator $faker)
    {
        parent::__construct($faker);

        $this->pupils = collect();
        $this->careInfos = collect();
        $this->careThemes = collect();
    }

    public function setUid(string $uid): self
    {
        $this->uid = $uid;

        return $this;
    }

    public function setVisibilityType(VisibilityType $visibilityType): self
    {
        $this->visibilityType = $visibilityType;

        return $this;
    }

    public function setCreator(User $user): self
    {
        $this->creator = $user;

        return $this;
    }

    public function addPupil(Pupil $pupil): self
    {
        $this->pupils->push($pupil);

        return $this;
    }

    public function setPupils(Collection $pupils): self
    {
        $this->pupils = $pupils;

        return $this;
    }

    public function addCareTheme(CareTheme $careTheme): self
    {
        $this->careThemes->push($careTheme);

        return $this;
    }

    public function setCareThemes(Collection $careThemes): self
    {
        $this->careThemes = $careThemes;

        return $this;
    }

    public function withCareThemes(int $amount = 1): self
    {
        $this->careThemeAmount = $amount;

        return $this;
    }

    public function setCareType(CareType $careType): self
    {
        $this->careType = $careType;

        return $this;
    }

    public function setSchool(School $school): self
    {
        $this->school = $school;

        return $this;
    }

    public function setDate(?int $year = null, ?int $month = null, ?int $day = null): self
    {
        $this->date = Carbon::create($year, $month, $day);

        return $this;
    }

    #[Override]
    public function create(): CareInput
    {
        if (!isset($this->school)) {
            $this->school = School::first();
        }

        if ($this->careInfos->isEmpty()) {
            $this->careInfos = $this->pupils
                ->map(
                    fn(Pupil $pupil): CareInfo => $pupil->careInfos->where('school_id', $this->school->id)->first(),
                );
        }

        if ($this->careThemeAmount > 0) {
            $this->careThemes = $this->careThemes->merge(
                CareTheme::factory()
                    ->count($this->careThemeAmount)
                    ->forSchool($this->school)
                    ->create(),
            );

            $this->careThemeAmount = 0;
        }

        if (!isset($this->careType)) {
            $this->careType = CareType::factory()
                ->forSchool($this->school)
                ->create();
        }
        $careInputFactory = CareInput::factory()
            ->setUid($this->uid ?? $this->faker->uuid())
            ->forCareType($this->careType)
            ->forVisibilityType($this->visibilityType)
            ->forSchool($this->school)
            ->setDate($this->date ?? $this->faker->dateTimeBetween('-6 months', 'now'));
        if (isset($this->creator)) {
            $careInputFactory = $careInputFactory->forUser($this->creator);
        }
        $careInput = $careInputFactory->create();

        $careInput->careInfos()->sync($this->careInfos->pluck('id'));
        $careInput->careThemes()->sync($this->careThemes->pluck('id'));

        return $careInput;
    }
}
