<?php

namespace Tests\Unit;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Common\Application\Services\Helpers\GroupHelperService;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Application\Services\Helpers\TenantHelperService;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Tenant\Tenant;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Illuminate\Routing\Route as RoutingRoute;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View as ViewAlias;
use Mockery;
use Mockery\MockInterface;
use Override;
use Route;
use View;

class ViewComposerTestCase extends UnitTestCase
{
    protected User $user;

    protected School $school;

    protected Collection $groups;

    protected Group $group;

    protected User $pupil;

    protected Collection $pupils;

    protected ViewAlias $view;

    protected string $viewName;

    protected MockInterface $routeMock;

    protected string $route = 'test/checkViewComposer';

    protected MockInterface $schoolHelperMock;

    protected MockInterface $groupHelperMock;

    protected MockInterface $tenantHelperMock;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $index = 0;

        $schools = School::factory()->count(2)
            ->create()
            ->each(function (School $school): void {
                Career::factory()
                    ->count(4)
                    ->forUser($this->user)
                    ->inSchool($school)
                    ->withRole(RoleName::Teacher)
                    ->create()
                    ->each(function (Career $career) use ($school, &$index): void {
                        $group = Group::factory()
                            ->create([
                                'school_id' => $school->id,
                                'natural_study_year' => $index++,
                                'is_classgroup' => false,
                                'target_audience_type' => TargetAudienceType::Lo,
                            ]);

                        $career->setGroup($group)->save();
                    });
            });

        $this->school = $schools->first();
        $this->groups = $this->user
            ->activeCareers()
            ->where('school_id', $this->school->id)
            ->map
            ->group;

        $this->group = $this->groups->first();
        $this->setUpTestEnvironment();
    }

    /**
     * Sets up the test environment by mocking a route and creating a test view.
     */
    protected function setUpTestEnvironment(): void
    {
        view()->addNamespace('test_view', base_path('tests/resources/views'));

        $routes = Route::getRoutes();

        $this->routeMock = Mockery::mock(RoutingRoute::class)->makePartial();
        $this->routeMock->shouldReceive('parameters')->andReturn($this->getParameters());

        Route::shouldReceive('current')->andReturn($this->routeMock);
        Route::shouldReceive('getRoutes')->andReturn($routes);
        Route::makePartial();

        Auth::shouldReceive('user')->andReturn($this->user);

        $this->view = View::make('test_view::view-composer');
    }

    /**
     * Sets up the test environment by creating a new route that returns an existing view.
     */
    protected function setUpTestRoute(): void
    {
        Route::get(
            $this->route,
            function () {
                return response()->view(
                    $this->viewName,
                    [
                        'redirectRoute' => 'web.tms.home',
                    ],
                );
            },
        );

        $this->actingAs($this->user);
    }

    /**
     * Will create a mock for the RequestHelperService and expecting the school call.
     */
    protected function initRequestHelperWithSchool(): self
    {
        $this->schoolHelperMock = Mockery::mock(SchoolHelperService::class);
        $this->schoolHelperMock->shouldReceive('school')
            ->andReturn($this->school);
        app()->instance(SchoolHelperService::class, $this->schoolHelperMock);

        return $this;
    }

    /**
     * Will create a mock for the RequestHelperService and expecting the school and group call.
     */
    protected function addGroupToRequestHelper(): self
    {
        $this->groupHelperMock = Mockery::mock(GroupHelperService::class);
        $this->groupHelperMock->shouldReceive('group')
            ->andReturn($this->group);
        app()->instance(GroupHelperService::class, $this->groupHelperMock);

        return $this;
    }

    /**
     * Will create a mock for the RequestHelperService and expecting the school and group call.
     */
    protected function addTenantToRequestHelper(): self
    {
        $this->tenantHelperMock = Mockery::mock(TenantHelperService::class);
        $this->tenantHelperMock->shouldReceive('tenant')
            ->andReturn(Tenant::getFromUid(config('app.defaultTenant')));
        app()->instance(TenantHelperService::class, $this->tenantHelperMock);

        return $this;
    }

    /**
     * Sets the pupils for the group.
     */
    protected function setUpPupils(): void
    {
        $this->pupils = User::factory()->times(10)
            ->create()
            ->each(function (User $user): void {
                Career::factory()
                    ->forUser($user)
                    ->inGroup($this->group)
                    ->withRole(RoleName::Pupil)
                    ->create([
                        'class_number' => $this->faker->boolean() ? $this->faker->numberBetween(0, 20) : null,
                    ]);

                CareInfo::factory()
                    ->forPupil($user)
                    ->inSchool($this->school)
                    ->withStatus(PupilStatus::Active)
                    ->create();
            });

        $this->pupil = $this->pupils->first();
    }

    /**
     * Returns the parameters needed for the route.
     */
    protected function getParameters(): array
    {
        return [
            'school' => $this->school,
        ];
    }
}
