<?php

namespace Tests\Unit\Cfa\Care\Services\Statistics\Graphs\Group;

use Cfa\Care\Application\Services\Statistics\Graphs\Group\RedicodisCharts;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Services\Statistics\ChartsTestCase;

use function factory;

class RedicodisChartsTest extends ChartsTestCase
{
    #[Test]
    public function it_returns_the_correct_redicodi_group_chart_data_for_top_level(): void
    {
        $this->setUpData();
        $parentRedicodi = Redicodi::whereName('Parent Redicodi')->firstOrFail();

        $expectedSeriesData = [
            'name' => 'Parent Redicodi',
            // 3 Pupils x 2 child redicodis.
            'y' => 6,
            'drilldown' => $parentRedicodi->uid,
            'tooltip' => 6 . '<br />' . $this->getPupilNames(),
        ];
        $result = app(RedicodisCharts::class)->getRedicodisGroupChartData($this->group);

        $this->assertEquals($expectedSeriesData, $result['series'][0]['data'][0]);
        $this->assertCount(1, $result['series'][0]['data']);
    }

    #[Test]
    public function it_returns_the_correct_redicodi_group_chart_data_for_drilldown(): void
    {
        $this->setUpData();

        $parentRedicodi = Redicodi::whereName('Parent Redicodi')->firstOrFail();
        $subcategoryRedicodi = Redicodi::whereName('Subcategory Redicodi')->firstOrFail();
        $expectedSeriesData = [
            'name' => 'Subcategory Redicodi',
            // 3 Pupils x 2 child redicodis.
            'y' => 6,
            'drilldown' => $subcategoryRedicodi->uid,
            'tooltip' => 6 . '<br />' . $this->getPupilNames(),
        ];
        $result = app(RedicodisCharts::class)->getRedicodisGroupChartDrillDownData($this->group, $parentRedicodi->id);

        $this->assertEquals($expectedSeriesData, $result[0]);
        $this->assertCount(1, $result);
    }

    private function setUpData(): void
    {
        $parentRedicodi = factory(Redicodi::class)->create([
            'name' => 'Parent Redicodi',
            'school_id' => $this->school->id,
            'archived_at' => null,
        ]);
        $subcategoryRedicodi = factory(Redicodi::class)->create([
            'name' => 'Subcategory Redicodi',
            'school_id' => $this->school->id,
            'parent_redicodi_id' => $parentRedicodi->id,
            'archived_at' => null,
        ]);
        $childRedicodi1 = factory(Redicodi::class)->create([
            'name' => 'Child Redicodi 1',
            'school_id' => $this->school->id,
            'parent_redicodi_id' => $subcategoryRedicodi->id,
            'archived_at' => null,
        ]);
        $childRedicodi2 = factory(Redicodi::class)->create([
            'name' => 'Child Redicodi2',
            'school_id' => $this->school->id,
            'parent_redicodi_id' => $subcategoryRedicodi->id,
            'archived_at' => null,
        ]);

        $this->careInfos->each(function (CareInfo $careInfo) use ($childRedicodi1, $childRedicodi2): void {
            $careInfo->redicodis()->save($childRedicodi1);
            $careInfo->redicodis()->save($childRedicodi2);
            $careInfo->save();
        });
    }
}
