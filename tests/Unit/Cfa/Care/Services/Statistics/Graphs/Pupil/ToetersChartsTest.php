<?php

namespace Tests\Unit\Cfa\Care\Services\Statistics\Graphs\Pupil;

use Cfa\Care\Application\Services\Statistics\Graphs\Pupil\ToetersCharts;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\ToetersZone;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Services\Statistics\ChartsTestCase;

use function app;
use function factory;

class ToetersChartsTest extends ChartsTestCase
{
    private FollowUpSystem $followUpSystem;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->followUpSystem = factory(FollowUpSystem::class)->create([
            'structure_type' => StructureType::Table,
            'school_id' => $this->school->id,
            'source' => FollowUpSystemSource::Predefined,
            'type' => FollowUpSystemType::Toeters,
            'name' => 'Toeters',
        ]);
    }

    #[Test]
    public function it_does_not_return_graph_data_for_toeters_without_scores(): void
    {
        $this->assertEmpty(app(ToetersCharts::class)->getGraphForPupil($this->pupils->first(), $this->school));
    }

    #[Test]
    public function it_returns_graph_data_for_toeters_with_scores(): void
    {
        $this->setUpScores();
        $data = app(ToetersCharts::class)->getGraphForPupil($this->pupils->first(), $this->school);
        $this->assertSame(1, count($data));
        $chart = $data[0];

        $this->assertArrayContainsFragment(
            ['chart' => ['type' => 'gauge']],
            $chart,
        );
        $this->assertArrayContainsFragment(
            ['series' => [['name' => 'Score', 'data' => [80]]]],
            $chart,
        );
        $this->assertEquals([
            [
                'from' => 0,
                'to' => 54,
                'thickness' => '50%',
            ],
            [
                'from' => 54,
                'to' => 60,
                'thickness' => '50%',
            ],
            [
                'from' => 60,
                'to' => 80,
                'thickness' => '50%',
            ],
        ], $chart['yAxis']['plotBands']);
    }

    private function setUpScores(): void
    {
        PredefinedFollowUpSystemScore::factory()->create([
            'pupil_id' => $this->pupil->id,
            'school_id' => $this->school->id,
            'schoolyear_id' => $this->schoolyear->id,
            'predefined_follow_up_system_id' => $this->followUpSystem->id,
            'test_moment' => TestMoment::Once,
            'test_audience' => TestAudience::SeptemberDecember,
            'subtype' => FollowUpSystemSubType::Default,
            'score' => 80,
            'zone' => ToetersZone::RiskZone1,
            'zone_nn' => ToetersZone::RiskZone2,
        ]);
    }
}
