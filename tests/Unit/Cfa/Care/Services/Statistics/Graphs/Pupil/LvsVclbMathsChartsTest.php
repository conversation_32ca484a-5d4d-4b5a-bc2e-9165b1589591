<?php

namespace Tests\Unit\Cfa\Care\Services\Statistics\Graphs\Pupil;

use Cfa\Care\Application\Services\Statistics\Graphs\Pupil\LvsVclbMathsCharts;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Services\Statistics\ChartsTestCase;

use function app;
use function factory;

class LvsVclbMathsChartsTest extends ChartsTestCase
{
    private FollowUpSystem $followUpSystem;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->followUpSystem = factory(FollowUpSystem::class)->create([
            'structure_type' => StructureType::Table,
            'school_id' => $this->school->id,
            'source' => FollowUpSystemSource::Predefined,
            'type' => FollowUpSystemType::LvsVclbMaths,
            'name' => 'LVS-VCLB Wiskunde',
        ]);
    }

    #[Test]
    public function it_does_not_return_graph_data_for_lvs_vclb_spelling_without_scores(): void
    {
        $this->assertEmpty(app(LvsVclbMathsCharts::class)->getGraphForPupil($this->pupils->first(), $this->school));
    }

    #[Test]
    public function it_returns_graph_data_for_lvs_vclb_spelling_with_scores(): void
    {
        $this->setUpScores();
        $data = app(LvsVclbMathsCharts::class)->getGraphForPupil($this->pupils->first(), $this->school);
        $this->assertSame(1, count($data));
        $chart = $data[0];

        $this->assertArrayContainsFragment(
            ['title' => ['text' => 'LVS-VCLB Wiskunde'], 'isVCLB' => true],
            $chart,
        );
        $this->assertCount(7, $chart['series']);
        $this->assertSame('Recentste score', $chart['series'][0]['name']);
        $this->assertNull($chart['series'][0]['color']);
        $this->assertFalse($chart['series'][0]['showInLegend']);
        $this->assertCount(17, $chart['series'][0]['data']);
        $this->assertContains(15, $chart['series'][0]['data']);

        $this->assertSame('Vorige score', $chart['series'][1]['name']);
        $this->assertSame('orange', $chart['series'][1]['color']);
        $this->assertTrue($chart['series'][1]['showInLegend']);
        $this->assertCount(17, $chart['series'][1]['data']);
        $this->assertContains(25, $chart['series'][1]['data']);
    }

    private function setUpScores(): void
    {
        PredefinedFollowUpSystemScore::factory()->create([
            'pupil_id' => $this->pupil->id,
            'school_id' => $this->school->id,
            'schoolyear_id' => $this->schoolyear->id,
            'predefined_follow_up_system_id' => $this->followUpSystem->id,
            'test_moment' => TestMoment::Middle,
            'test_audience' => TestAudience::LoJ3,
            'subtype' => FollowUpSystemSubType::Default,
            'repeating_year' => 1,
            'score' => 15,
        ]);

        PredefinedFollowUpSystemScore::factory()->create([
            'pupil_id' => $this->pupil->id,
            'school_id' => $this->school->id,
            'schoolyear_id' => Schoolyear::getRepository()->getPrevious()->id,
            'predefined_follow_up_system_id' => $this->followUpSystem->id,
            'test_moment' => TestMoment::Middle,
            'test_audience' => TestAudience::LoJ3,
            'subtype' => FollowUpSystemSubType::Default,
            'repeating_year' => 0,
            'score' => 25,
        ]);
    }
}
