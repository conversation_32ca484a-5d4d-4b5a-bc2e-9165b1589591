<?php

namespace Tests\Unit\Cfa\Care\Services\Statistics\Graphs\Pupil;

use Cfa\Care\Application\Services\Statistics\Graphs\Pupil\TalentCharts;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSource;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\StructureType;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Services\Statistics\ChartsTestCase;

use function app;
use function factory;

class TalentChartsTest extends ChartsTestCase
{
    private FollowUpSystem $followUpSystem;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->followUpSystem = factory(FollowUpSystem::class)->create([
            'structure_type' => StructureType::Table,
            'school_id' => $this->school->id,
            'source' => FollowUpSystemSource::Predefined,
            'type' => FollowUpSystemType::Talent,
            'name' => 'Talent',
        ]);
    }

    #[Test]
    public function it_does_not_return_graph_data_without_scores(): void
    {
        $this->assertEmpty(
            app(TalentCharts::class)->getGraphForPupil($this->pupils->first(), $this->school),
        );
    }

    #[Test]
    public function it_returns_graph_data_with_scores(): void
    {
        $this->setUpScores();
        $data = app(TalentCharts::class)->getGraphForPupil($this->pupils->first(), $this->school);
        $this->assertSame(1, count($data));
        $chart = $data[0];

        $this->assertArrayContainsFragment(
            ['title' => ['text' => 'Talent']],
            $chart,
        );
        $this->assertCount(6, $chart['series']);
        $this->assertSame('Score', $chart['series'][0]['name']);
        $this->assertNull($chart['series'][0]['color']);
        $this->assertFalse($chart['series'][0]['showInLegend']);
        $this->assertCount(15, $chart['series'][0]['data']);
        $this->assertSame(15, $chart['series'][0]['data'][0]);
        $this->assertSame(null, $chart['series'][0]['data'][1]);
    }

    private function setUpScores(): void
    {
        PredefinedFollowUpSystemScore::factory()->create([
            'pupil_id' => $this->pupil->id,
            'school_id' => $this->school->id,
            'schoolyear_id' => $this->schoolyear->id,
            'predefined_follow_up_system_id' => $this->followUpSystem->id,
            'test_moment' => TestMoment::DictationRevision1,
            'test_audience' => TestAudience::LoJ2,
            'subtype' => FollowUpSystemSubType::Default,
            'repeating_year' => 1,
            'score' => 15,
        ]);
    }
}
