<?php

namespace Tests\Unit\Cfa\Care\Services\Redicodi;

use Cfa\Care\Application\Services\Redicodi\RedicodiService;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystem;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ServiceTestCase;

class RedicodiServiceTest extends ServiceTestCase
{
    /** @var RedicodiService */
    protected $service;

    protected string $serviceName = RedicodiService::class;

    private School $school;

    private Pupil $pupil;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->school = School::factory()
            ->inKathOndVla()
            ->create();
        $this->pupil = Pupil::factory()
            ->withActiveCareer($this->school)
            ->withCareInfo($this->school, PupilStatus::New)
            ->create();
    }

    #[Test]
    public function it_creates_the_redicodi_tree(): void
    {
        $careInfo = $this->pupil->careInfos->first();
        $expectedTree = $this->createRedicodiTree($careInfo);
        $this->assertEquals($expectedTree, $this->service->getRedicodiTree($careInfo));
    }

    #[Test]
    public function it_creates_the_redicodi_tree_with_comments(): void
    {
        /** @var CareInfo $careInfo */
        $careInfo = $this->pupil->careInfos->first();
        $expectedTree = $this->createRedicodiTree($careInfo);

        $redicodiWiskunde = Redicodi::whereName('Wiskunde')->first();
        $redicodiKanTellen = Redicodi::whereName('Kan tellen')->first();
        $this->assertEquals('Kan tellen', $expectedTree[$redicodiWiskunde->uid]['children'][0]['name']);
        $expectedTree[$redicodiWiskunde->uid]['children'][0]['comment'] = 'Newest Comment';
        $redicodiKanTellen->careInfos()->updateExistingPivot($careInfo->id, ['comment' => 'Newest Comment']);

        $redicodiDyslexie = Redicodi::whereName('Dyslexie')->first();
        $redicodiDyslexie->careInfos()->updateExistingPivot($careInfo->id, ['comment' => 'Dictionary']);
        $expectedTree[$redicodiDyslexie->uid]['comment'] = 'Dictionary';

        $careInfo->load(
            ['redicodis' => fn(BelongsToMany $belongsToMany): BelongsToMany => $belongsToMany->withPivot('comment')],
        );
        $serviceTree = $this->service->getRedicodiTree($careInfo);
        $this->assertEquals($expectedTree, $serviceTree);
    }

    private function createRedicodiTree(CareInfo $careInfo): array
    {
        $redicodis = [];
        $category = Redicodi::forceCreate([
            'name' => 'Algemeen',
            'school_id' => $this->school->id,
        ])->fresh(['icon']);
        $subject = Subject::factory()->create(['uid' => 'subject-uid', 'name' => 'subject']);
        $category->subjects()->syncWithPivotValues([$subject->id], ['care_info_id' => $careInfo->id]);
        $subCategory = Redicodi::forceCreate([
            'name' => 'Vaardigheden',
            'parent_redicodi_id' => $category->id,
            'school_id' => $this->school->id,
        ])->fresh(['icon']);
        $followUpSystem = factory(FollowUpSystem::class)->create(
            ['uid' => 'fus-uid', 'name' => 'followUpSystem', 'school_id' => $this->school->id],
        );
        $subCategory->followUpSystems()->syncWithPivotValues([$followUpSystem->id], ['care_info_id' => $careInfo->id]);
        $subCategory->subjects()->syncWithPivotValues([$subject->id], ['care_info_id' => $careInfo->id]);
        $anotherCategory = Redicodi::forceCreate([
            'name' => 'Wiskunde',
            'school_id' => $this->school->id,
        ])->fresh(['icon']);

        $redicodis[] = Redicodi::forceCreate([
            'name' => 'calculator',
            'parent_redicodi_id' => $subCategory->id,
            'school_id' => $this->school->id,
            'order' => 0,
        ])->fresh(['icon']);
        $redicodis[] = Redicodi::forceCreate([
            'name' => 'iPod',
            'parent_redicodi_id' => $subCategory->id,
            'school_id' => $this->school->id,
            'order' => 1,
        ])->fresh(['icon']);
        foreach ($redicodis as $redicodi) {
            $redicodi->followUpSystems()->syncWithPivotValues([$followUpSystem->id], ['care_info_id' => $careInfo->id]);
        }
        $redicodis[] = Redicodi::forceCreate([
            'name' => 'Kan tellen',
            'parent_redicodi_id' => $anotherCategory->id,
            'school_id' => $this->school->id,
        ])->fresh(['icon']);
        // Root redicodi.
        $redicodis[] = $dyslexie = Redicodi::forceCreate([
            'name' => 'Dyslexie',
            'school_id' => $this->school->id,
        ])->fresh(['icon']);

        foreach ($redicodis as $redicodi) {
            $careInfo->redicodis()->save($redicodi);
        }

        return [
            $category->uid => [
                ...$category->toArray(),
                'subjects_follow_up_systems' => collect([
                    [
                        'uid' => 'subject-uid',
                        'name' => 'subject',
                        'parent_name' => null,
                    ],
                ]),
                'children' => [
                    [
                        ...$subCategory->toArray(),
                        'subjects_follow_up_systems' => collect([
                            [
                                'uid' => 'fus-uid',
                                'name' => 'followUpSystem',
                                'parent_name' => null,
                            ],
                            [
                                'uid' => 'subject-uid',
                                'name' => 'subject',
                                'parent_name' => null,
                            ],
                        ]),
                        'children' => $subCategory->children
                            ->map(fn(Redicodi $child) => [
                                ...$child->toArray(),
                                'subjects_follow_up_systems' => collect([
                                    [
                                        'uid' => 'fus-uid',
                                        'name' => 'followUpSystem',
                                        'parent_name' => null,
                                    ],
                                ]),
                                'comment' => null,
                            ])
                            ->toArray(),
                        'comment' => null,
                    ],
                ],
                'comment' => null,
            ],
            $anotherCategory->uid => [
                ...$anotherCategory->toArray(),
                'subjects_follow_up_systems' => [],
                'children' => $anotherCategory->children
                    ->map(
                        fn(Redicodi $child) => [
                            ...$child->toArray(),
                            'subjects_follow_up_systems' => [],
                            'comment' => null,
                        ],
                    )
                    ->toArray(),
                'comment' => null,
            ],
            $dyslexie->uid => [...$dyslexie->toArray(), 'subjects_follow_up_systems' => [], 'comment' => null],
        ];
    }
}
