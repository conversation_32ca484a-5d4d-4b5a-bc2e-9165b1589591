<?php

namespace Tests\Unit\Cfa\Care\Jobs\CareImport\Authors;

use Cfa\Care\Application\Jobs\ProcessCareImport;
use Cfa\Care\Domain\Import\ImportJobStatus;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\Teacher;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Jobs\CareImport\ProcessCareImportTestCase;

class ProcessCareImportAuthorsTest extends ProcessCareImportTestCase
{
    /**
     * The name of the csv file.
     *
     * @var string
     */
    protected $fileName = 'test-completed-with-author-names.csv';

    /**
     * In this test, the author is a teacher with a career in the school.
     * The teacher is linked as an author for the care input.
     */
    #[Test]
    public function it_stores_the_author_names_correctly_as_creator(): void
    {
        $job = app(ProcessCareImport::class, [
            'careImportJobId' => $this->careImportJob->id,
        ]);

        $job->handle();

        $teacher = Teacher::where('firstname', '=', 'Christian')
            ->where('lastname', '=', 'Quintero')
            ->first();

        $teacherCareer = $teacher->hasActiveCareersForSchool($this->school->id);

        $pupil = Pupil::where('firstName', '=', 'Sharna')
            ->where('lastName', '=', 'Sullivan')
            ->first();

        $careInfo = $pupil->careInfos()->first();

        $this->assertDatabaseHas('care_inputs', [
            'creator_id' => $teacher->id,
            'description' => 'Not the test you\'re looking for',
        ]);

        $this->assertEquals(ImportJobStatus::Completed, $this->careImportJob->fresh()->status);
        $this->assertTrue($teacherCareer);
    }

    /**
     * In this test, the author is a teacher with no career in the school.
     * The author is created as a care giver for the care input.
     */
    #[Test]
    public function it_stores_author_names_as_care_giver_when_the_author_is_a_teacher_without_an_active_career(): void
    {
        $job = app(ProcessCareImport::class, [
            'careImportJobId' => $this->careImportJob->id,
        ]);

        $job->handle();

        $teacher = Teacher::where('firstname', '=', 'Joske')
            ->where('lastname', '=', 'Vermeulen')
            ->first();

        $this->assertFalse($teacher->hasActiveCareersForSchool($this->school->id));

        $pupil = Pupil::where('firstName', '=', 'Lynda')
            ->where('lastName', '=', 'Véng')
            ->first();

        $careInfo = $pupil->careInfos()->first();
        $careInput = $careInfo->careInputs()
            ->where('creator_id', '=', null)
            ->where('description', '=', 'To be or not to be')
            ->first();

        $careGiverCount = $careInput->careGivers()
            ->where('name', '=', 'Joske Vermeulen')
            ->count();

        $this->assertEquals(ImportJobStatus::Completed, $this->careImportJob->fresh()->status);
        $this->assertEquals(1, $careGiverCount);
    }

    /**
     * In this test, the author is no user.
     * The author is created as a care giver for the care input.
     */
    #[Test]
    public function it_stores_author_names_as_care_giver_when_the_author_is_no_user(): void
    {
        $job = app(ProcessCareImport::class, [
            'careImportJobId' => $this->careImportJob->id,
        ]);

        $job->handle();

        $this->assertDatabaseMissing('users', [
            'firstName' => 'Jantje',
            'lastName' => 'Smit',
        ]);

        $pupil = Pupil::where('firstName', '=', 'Lynda')
            ->where('lastName', '=', 'Véng')
            ->first();

        $careInfo = $pupil->careInfos()->first();
        $careInput = $careInfo->careInputs()
            ->where('creator_id', '=', null)
            ->where('description', '=', 'This is a third test.')
            ->first();

        $careGiverCount = $careInput->careGivers()
            ->where('name', '=', 'Jantje Smit')
            ->count();

        $this->assertEquals(ImportJobStatus::Completed, $this->careImportJob->fresh()->status);
        $this->assertEquals(1, $careGiverCount);
    }
}
