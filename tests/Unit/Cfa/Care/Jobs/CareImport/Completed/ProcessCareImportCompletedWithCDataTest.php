<?php

namespace Tests\Unit\Cfa\Care\Jobs\CareImport\Completed;

use Cfa\Care\Application\Jobs\ProcessCareImport;
use Cfa\Care\Domain\CareImportJob\CareImportMapping\CareImportJobDestination;
use Cfa\Care\Domain\CareImportJob\Enclosure;
use Cfa\Care\Domain\CareImportJob\Separator;
use Cfa\Care\Domain\CareInput\CareInput;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\Cfa\Care\Jobs\CareImport\ProcessCareImportTestCase;

use function app;

class ProcessCareImportCompletedWithCDataTest extends ProcessCareImportTestCase
{
    /** @var string */
    protected $fileName = 'test-completed-with-cdata.csv';

    /** @var int */
    protected $separator = Separator::Comma->value;

    /** @var int */
    protected $enclosure = Enclosure::DoubleQuotes->value;

    /** @var bool */
    protected $hasHeaders = true;

    /** @var array */
    protected $mapping = [
        0 => CareImportJobDestination::Firstname->value,
        1 => CareImportJobDestination::Lastname->value,
        2 => CareImportJobDestination::DateOfBirth->value,
        4 => CareImportJobDestination::Description->value,
        5 => CareImportJobDestination::CareInputDate->value,
        6 => CareImportJobDestination::CareType->value,
        7 => CareImportJobDestination::CareGiverFirstname->value,
        8 => CareImportJobDestination::CareGiverLastname->value,
        9 => CareImportJobDestination::CareTheme->value,
    ];

    #[Test]
    public function it_runs_the_job_and_creates_the_valid_care_inputs(): void
    {
        $job = app(ProcessCareImport::class, [
            'careImportJobId' => $this->careImportJob->id,
        ]);

        $job->handle();

        $careInputs = CareInput::with('careInfos.pupil', 'careType', 'careThemes', 'careGivers')
            ->orderBy('id')
            ->get();
        $this->assertCount(9, $careInputs);
        $careInput = $careInputs->get(0);
        $expectedDescription = 'This is a test


<p> </p>';

        $this->assertEquals($expectedDescription, $careInput->description);
    }
}
