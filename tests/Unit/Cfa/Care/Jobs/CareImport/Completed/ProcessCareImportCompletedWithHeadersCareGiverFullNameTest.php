<?php

namespace Tests\Unit\Cfa\Care\Jobs\CareImport\Completed;

use Cfa\Care\Domain\CareImportJob\CareImportMapping\CareImportJobDestination;
use Cfa\Care\Domain\CareImportJob\Enclosure;
use Cfa\Care\Domain\CareImportJob\Separator;

/**
 * Class ProcessCareImportCompletedWithHeadersCareGiverFullNameTest
 * Test complements the ProcessCareImportCompletedWithHeadersTest
 * In that test we focus on separate Care Giver first and last name.
 * In this test we focus on grouped first and last into full name.
 * Same tests run and should result in exactly same behavior
 */
class ProcessCareImportCompletedWithHeadersCareGiverFullNameTest extends ProcessCareImportCompletedTestCase
{
    /**
     * {@inheritdoc}
     *
     * @var string
     */
    protected $fileName = 'test-completed-with-column-names-care-giver-full-name.csv';

    /**
     * {@inheritdoc}
     *
     * @var int
     */
    protected $separator = Separator::Semicolon->value;

    /**
     * {@inheritdoc}
     *
     * @var int
     */
    protected $enclosure = Enclosure::DoubleQuotes->value;

    /**
     * {@inheritdoc}
     *
     * @var bool
     */
    protected $hasHeaders = true;

    /**
     * {@inheritdoc}
     * Column 3 is skipped in this csv file (birth place).
     *
     * @var array
     */
    protected $mapping = [
        0 => CareImportJobDestination::Firstname->value,
        1 => CareImportJobDestination::Lastname->value,
        2 => CareImportJobDestination::DateOfBirth->value,
        4 => CareImportJobDestination::Description->value,
        5 => CareImportJobDestination::CareInputDate->value,
        6 => CareImportJobDestination::CareType->value,
        7 => CareImportJobDestination::CareGiverFullname->value,
        8 => CareImportJobDestination::CareTheme->value,
    ];
}
