<?php

namespace Tests\Unit\Cfa\Care\Jobs\CareImport;

use Carbon\Carbon;
use Cfa\Care\Application\Jobs\ProcessCareImport;
use Cfa\Care\Domain\CareImportJob\CareImportMapping\CareImportJobDestination;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\Import\ImportJobStatus;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Override;
use PHPUnit\Framework\Attributes\Test;

use function app;

class ProcessCareImportDuplicatePupilTest extends ProcessCareImportTestCase
{
    /** @var string */
    protected $fileName = 'test-completed-with-column-names.csv';

    /** @var array */
    protected $mapping = [
        0 => CareImportJobDestination::Firstname->value,
        1 => CareImportJobDestination::Lastname->value,
        2 => CareImportJobDestination::DateOfBirth->value,
        4 => CareImportJobDestination::Description->value,
        5 => CareImportJobDestination::CareInputDate->value,
        6 => CareImportJobDestination::CareType->value,
        7 => CareImportJobDestination::CareGiverFirstname->value,
        8 => CareImportJobDestination::CareGiverLastname->value,
        9 => CareImportJobDestination::CareTheme->value,
    ];

    #[Override]
    protected function setUp(): void
    {
        Carbon::setTestNow('2020-09-15');

        parent::setUp();
    }

    #[Test]
    public function it_does_not_warn_about_duplicate_pupils_if_they_have_an_expired_career(): void
    {
        // Create a duplicate user.
        $user = User::factory()
            ->withActiveCareer($this->school, RoleName::Pupil)
            ->create([
                'firstname' => 'Sharna',
                'lastname' => 'Sullivan',
            ]);

        $user->forceFill(['date_of_birth' => null]);
        $user->save();

        $user->careers()->update([
            'startdate' => '2019-09-01 00:00',
            'enddate' => '2020-08-01 00:00',
        ]);

        app(
            ProcessCareImport::class,
            [
                'careImportJobId' => $this->careImportJob->id,
            ],
        )->handle();

        $careImportJob = $this->careImportJob->fresh();

        $this->assertSame('{"errors":[],"warnings":[]}', $careImportJob->status_message);
        $this->assertEquals(ImportJobStatus::Completed, $careImportJob->status);
        $this->assertSame(9, CareInput::count());
    }

    #[Test]
    public function it_does_warn_about_a_duplicate_in_the_current_schoolyear(): void
    {
        // Create a duplicate user.
        $user = User::factory()
            ->withActiveCareer($this->school, RoleName::Pupil)
            ->create([
                'firstname' => 'Sharna',
                'lastname' => 'Sullivan',
            ]);

        $user->forceFill(['date_of_birth' => null]);
        $user->save();

        $user->careers()->update([
            'startdate' => '2020-09-01 00:00',
            'enddate' => '2020-09-14 00:00',
        ]);

        app(
            ProcessCareImport::class,
            [
                'careImportJobId' => $this->careImportJob->id,
            ],
        )->handle();

        $careImportJob = $this->careImportJob->fresh();

        $this->assertSame(
            '{"errors":[],"warnings":[{"rows":"4, 10","name":"enums.care-import-exception-code.duplicate_pupil"}]}',
            $careImportJob->status_message,
        );
        $this->assertEquals(ImportJobStatus::Completed, $careImportJob->status);
        $this->assertSame(8, CareInput::count());
    }
}
