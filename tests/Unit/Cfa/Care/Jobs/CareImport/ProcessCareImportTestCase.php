<?php

namespace Tests\Unit\Cfa\Care\Jobs\CareImport;

use Carbon\Carbon;
use Cfa\Care\Application\Services\CareImportJob\CareImportJobService;
use Cfa\Care\Domain\CareImportJob\CareImportJob;
use Cfa\Care\Domain\CareImportJob\CareImportMapping\CareImportJobDestination;
use Cfa\Care\Domain\CareImportJob\CareImportMapping\CareImportMapping;
use Cfa\Care\Domain\CareImportJob\CareImportType;
use Cfa\Care\Domain\CareImportJob\Enclosure;
use Cfa\Care\Domain\CareImportJob\Separator;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareTheme\CareTheme;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Illuminate\Contracts\Filesystem\FileNotFoundException as FileNotFoundExceptionAlias;
use Illuminate\Support\Collection;
use Override;
use Tests\Filesystem\Filesystem;
use Tests\Filesystem\TestFilesystem;
use Tests\Unit\UnitTestCase;

use function app;
use function config;

abstract class ProcessCareImportTestCase extends UnitTestCase
{
    /**
     * The school.
     *
     * @var School
     */
    protected $school;

    /**
     * The care import job.
     *
     * @var CareImportJob
     */
    protected $careImportJob;

    /**
     * The name of the csv file.
     *
     * @var string
     */
    protected $fileName;

    /**
     * The separator used in the csv file.
     *
     * @var int
     */
    protected $separator = Separator::Semicolon->value;

    /**
     * The enclosure used in the csv file.
     *
     * @var int
     */
    protected $enclosure = Enclosure::DoubleQuotes->value;

    /**
     * Does the csv file contain headers?
     *
     * @var bool
     */
    protected $hasHeaders = true;

    /**
     * Does the import automatically remove the previously imported care inputs?
     *
     * @var bool
     */
    protected $removeEarlierImportedCareInputs = true;

    /**
     * {@inheritdoc}
     *
     * @var array
     */
    protected $mapping = [
        0 => CareImportJobDestination::Firstname->value,
        1 => CareImportJobDestination::Lastname->value,
        2 => CareImportJobDestination::DateOfBirth->value,
        3 => CareImportJobDestination::Description->value,
        4 => CareImportJobDestination::CareInputDate->value,
        5 => CareImportJobDestination::CareType->value,
        6 => CareImportJobDestination::CareGiverFirstname->value,
        7 => CareImportJobDestination::CareGiverLastname->value,
        8 => CareImportJobDestination::CareTheme->value,
        9 => CareImportJobDestination::AuthorFirstname->value,
        10 => CareImportJobDestination::AuthorLastname->value,
        11 => CareImportJobDestination::Attachments->value,
    ];

    /**
     * The zip file with path.
     *
     * @var string
     */
    protected $zipFile = 'care_import_jobs' . DIRECTORY_SEPARATOR .
        'attachments' . DIRECTORY_SEPARATOR .
        'attachments.zip';

    protected string $zipFileNested = 'care_import_jobs' . DIRECTORY_SEPARATOR .
        'attachments' . DIRECTORY_SEPARATOR .
        'nested-attachments.zip';

    protected string $zipFileExtensions = 'care_import_jobs' . DIRECTORY_SEPARATOR .
        'attachments' . DIRECTORY_SEPARATOR .
        'file-extensions-attachments.zip';

    /**
     * The filesystem.
     *
     * @var TestFilesystem
     */
    protected $filesystem;

    /**
     * @var CareImportType
     */
    protected $careImportType;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $file = 'care_import_jobs' . DIRECTORY_SEPARATOR . $this->fileName;

        $this->filesystem = Filesystem::replace();
        Filesystem::replace('local');
        $this->filesystem->copyResource($file);

        $this->school = $this->createSchool();

        if ($this->careImportType) {
            $config = config('care-import-types')[$this->careImportType->getLegacyName()];
            $this->enclosure = $config['enclosure']->value;
            $this->separator = $config['separator']->value;
            $this->hasHeaders = $config['has_headers'] === 'true';
        }

        $this->careImportJob = $this->getCareImportJob($file);

        if ($this->careImportType) {
            $this->useDefaultMapping($this->careImportType);
        }
        $this->createMapping();

        $this->setUpPupils();
        $this->setUpTeachers();
        $this->setUpThemes();
        $this->setUpTypes();

        $school = School::factory()->create();
        $this->teacher = $this->createUser(
            RoleName::Teacher,
            'Joske',
            'Vermeulen',
            null,
            $school,
        );
    }

    /**
     * Create CareImportMapping.
     */
    protected function createMapping(): void
    {
        collect($this->mapping)->each(function (int $destination, int $source): void {
            $mapping = new CareImportMapping();
            $mapping->origin_column = $source;
            $mapping->destination_column = $destination;
            $mapping->care_import_job_id = $this->careImportJob->id;

            $mapping->save();
        });
    }

    /**
     * Creates all the pupils.
     */
    private function setUpPupils(): void
    {
        $role = RoleName::Pupil;

        $pupil = $this->createUser($role, 'Lynda', 'Véng', '2000-01-19');
        $this->createCareInfo($pupil);
        $pupil = $this->createUser($role, 'Patsy', 'Frazier');
        $this->createCareInfo($pupil);
        $pupil = $this->createUser($role, 'Herbie', 'Craft');
        $this->createCareInfo($pupil, '2000-05-04');
        $this->createUser($role, 'Sharna', 'Sullivan');
        $this->createUser($role, 'Aimee', 'Aguilar', '2002-08-29');
    }

    /**
     * Creates all the teachers.
     */
    private function setUpTeachers(): void
    {
        $role = RoleName::Teacher;

        $this->createUser($role, 'Christian', 'Quintero');
        $this->createUser($role, 'Fredrick', 'Duffy');
        $this->createUser($role, 'Aariz', 'Macleod');
    }

    /**
     * Creates all the care themes.
     */
    private function setUpThemes(): void
    {
        $this->createTheme('Social Development');
        $this->createTheme('Language Development');
    }

    /**
     * Creates all the care types.
     */
    private function setUpTypes(): void
    {
        $this->createType('Parents Evening');
        $this->createType('Observation');
    }

    /**
     * Creates a user with the given firstname, lastname and date of birth.
     *
     * @param RoleName $role The given role.
     * @param null|string $firstname The given first name.
     * @param null|string $lastname The given last name.
     * @param null|string $dateOfBirth The given date of birth.
     * @param School $school The given school.
     */
    protected function createUser(
        RoleName $role,
        ?string $firstname = null,
        ?string $lastname = null,
        ?string $dateOfBirth = null,
        ?School $school = null,
    ): User {
        if ($dateOfBirth) {
            $dateOfBirth = Carbon::parse($dateOfBirth);
        }

        $user = User::factory()->create([
            'firstname' => $firstname ?? $this->faker->firstName,
            'lastname' => $lastname ?? $this->faker->lastName,
            'date_of_birth' => $dateOfBirth,
        ]);

        $this->createCareer($school ?? $this->school, $user, $role);

        return $user;
    }

    /**
     * Creates a care theme with the given name and archived at date.
     *
     * @param null|string $name The given theme name.
     * @param null|Carbon $archivedAt The archived at date.
     */
    private function createTheme(?string $name = null, ?Carbon $archivedAt = null): CareTheme
    {
        return CareTheme::factory()
            ->forSchool($this->school)
            ->setName($name ?? $this->faker->text(191))
            ->setArchivedAt($archivedAt)
            ->create();
    }

    /**
     * Creates a care type with the given name and archived at date.
     *
     * @param null|string $name The given type name.
     * @param null|Carbon $archivedAt The archived at date.
     */
    private function createType(?string $name = null, ?Carbon $archivedAt = null): CareType
    {
        return CareType::factory()
            ->forSchool($this->school)
            ->setName($name ?? $this->faker->text(191))
            ->setArchivedAt($archivedAt)
            ->create();
    }

    /**
     * Creates a career within the school for the given user having the given role.
     *
     * @param School $school The given school.
     * @param User $user The given user.
     * @param RoleName|null $role The given role.
     */
    private function createCareer(School $school, User $user, ?RoleName $role = null): Career
    {
        return Career::factory()
            ->forUser($user)
            ->inSchool($school)
            ->withRole($role)
            ->create();
    }

    /**
     * Creates a care info for the given pupil.
     *
     * @param User $pupil The pupil the care info is created for.
     */
    private function createCareInfo(User $pupil, ?string $dateOfBirth = null): CareInfo
    {
        CareInfo::unguardValidation();
        $careInfo = CareInfo::factory()
            ->forPupil($pupil)
            ->inSchool($this->school)
            ->create(['date_of_birth' => $dateOfBirth ? Carbon::parse($dateOfBirth) : null]);
        CareInfo::guardValidation();

        return $careInfo;
    }

    /**
     * Add the zip to the careImportJob in database.
     */
    protected function addZipToCareImportJob(): void
    {
        $this->filesystem->copyResource('care_import_jobs/attachments/attachments.zip');

        $this->careImportJob->attachments = $this->zipFile;
        $this->careImportJob->creator_id = $this->teacher->id;
        $this->careImportJob->save();
    }

    protected function addNestedZipToCareImportJob(): void
    {
        $this->filesystem->copyResource('care_import_jobs/attachments/nested-attachments.zip');

        $this->careImportJob->attachments = $this->zipFileNested;
        $this->careImportJob->creator_id = $this->teacher->id;
        $this->careImportJob->save();
    }

    protected function addFileExtensionsZipToCareImportJob(): void
    {
        $this->filesystem->copyResource('care_import_jobs/attachments/file-extensions-attachments.zip');

        $this->careImportJob->attachments = $this->zipFileExtensions;
        $this->careImportJob->creator_id = $this->teacher->id;
        $this->careImportJob->save();
    }

    /**
     * Set the default mapping for the given care import type (as if the user didn't change any of the fields).
     *
     * @throws FileNotFoundExceptionAlias
     */
    protected function useDefaultMapping(CareImportType $careImportType): void
    {
        $service = app(CareImportJobService::class);
        $mapping = $service->getMapping($this->careImportJob);
        $fields = $service->getFields($careImportType, $mapping);
        $this->mapping = $fields->pluck('value', 'destination')
            ->except('')
            ->toArray();
    }

    /**
     * Create a school.
     */
    protected function createSchool(): School
    {
        return School::factory()->inKathOndVla()->create([
            'created_at' => Carbon::create(2015, 8, 15),
        ]);
    }

    /**
     * Returns a CareImportJob base definition which you can overwrite in your children
     * Use case could for instance be adding a type
     */
    protected function getCareImportJob(string $file): CareImportJob
    {
        return CareImportJob::factory()
            ->setFile($file)
            ->setSeparator($this->separator)
            ->setEnclosure($this->enclosure)
            ->setHasHeaders($this->hasHeaders)
            ->setResetImport($this->removeEarlierImportedCareInputs)
            ->forType($this->careImportType ?? CareImportType::Other)
            ->create();
    }

    /**
     * Returns a collection of the names of the pupils linked to the care input.
     *
     * @param CareInput $careInput The care input.
     */
    protected function getPupilsOfCareInput(CareInput $careInput): Collection
    {
        return $careInput->careInfos->map->pupil->map->fullname;
    }

    /**
     * Returns a collection of the names of the care themes linked to the care input.
     *
     * @param CareInput $careInput The care input.
     */
    protected function getCareThemesOfCareInput(CareInput $careInput): Collection
    {
        return $careInput->careThemes->pluck('name');
    }

    /**
     * Returns a collection of the names of the care themes linked to the care input.
     *
     * @param CareInput $careInput The care input.
     */
    protected function getCareGiversOfCareInput(CareInput $careInput): Collection
    {
        return $careInput->careGivers->map->fullname;
    }
}
