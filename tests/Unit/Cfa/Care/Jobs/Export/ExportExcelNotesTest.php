<?php

namespace Tests\Unit\Cfa\Care\Jobs\Export;

use App\Constants\FilesystemDisks;
use Cfa\Care\Application\Jobs\Export\ExportExcelNotes;
use Illuminate\Support\Facades\Storage;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\WithSeeding;
use Tests\Unit\UnitTestCase;

class ExportExcelNotesTest extends UnitTestCase
{
    use WithSeeding;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpTeacherWithGroupsAndPupilsInCurrentSchoolyear();
        $this->setUpCareInfos();
        $this->setUpCareTypes();
        $this->setUpCareInputs();

        Storage::persistentFake('s3');
    }

    /**
     * Test that an excel is created in the temp storage.
     */
    #[Test]
    public function it_creates_an_excel_export(): void
    {
        $job = app(ExportExcelNotes::class, [
            'filter' => [],
            'school' => $this->school,
            'pupilIds' => collect([$this->pupil->id]),
        ]);

        $job->handle();

        Storage::disk(FilesystemDisks::S3)->assertExists($job->getFilename());
    }
}
