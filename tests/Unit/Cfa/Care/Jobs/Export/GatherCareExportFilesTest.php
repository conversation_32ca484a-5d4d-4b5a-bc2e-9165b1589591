<?php

namespace Tests\Unit\Cfa\Care\Jobs\Export;

use App\Models\Lambda\LambdaResult;
use App\Models\Lambda\LambdaResultType;
use App\Services\Lambda\Domain\ZipPath;
use App\Services\Lambda\Domain\ZipPathCollection;
use App\Services\Lambda\LambdaZipService;
use Carbon\Carbon;
use Cfa\Care\Application\Jobs\Export\ExportExcelNotes;
use Cfa\Care\Application\Jobs\Export\ExportPdfInfoSheet;
use Cfa\Care\Application\Jobs\Export\GatherCareExportFiles;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Diagnosis\DiagnosisAttachment\DiagnosisAttachment;
use Cfa\Care\Domain\CareInput\CareFile\CareFile;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\Export\CareExportJob;
use Cfa\Common\Application\Exports\ExportJobStatus;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Cfa\Evaluation\Domain\Report\EvaluationPrintJob\EvaluationPrintJob;
use Cfa\Evaluation\Domain\Settings\Report\Period\ReportPeriod;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Support\Str;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Care\CareInputFactory;
use Tests\Unit\UnitTestCase;

use function app;
use function array_merge;
use function collect;
use function dispatch;

class GatherCareExportFilesTest extends UnitTestCase
{
    private CareExportJob $careExportJob;
    private LambdaResult $lambdaResult;
    private School $school;
    private Pupil $pupil;
    private CareInfo $careInfo;
    private Pupil $otherPupil;
    private CareInfo $otherCareInfo;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->create();
        $this->pupil = Pupil::factory()->withActiveCareer($this->school)->create([
            'firstname' => 'Thorgan',
            'lastname' => 'Hazard',
        ]);
        $this->otherPupil = Pupil::factory()->withActiveCareer($this->school)->create([
            'firstname' => 'Eden',
            'lastname' => 'Hazard',
        ]);
        $this->careInfo = CareInfo::factory()
            ->forPupil($this->pupil)
            ->inSchool($this->school)
            ->create(['uid' => 'd8c2a17c-83cd-43f3-a037-5fc33c4a66f1']);
        $this->otherCareInfo = CareInfo::factory()->forPupil($this->otherPupil)->inSchool($this->school)->create();
        $this->careExportJob = CareExportJob::forceCreate([
            'status' => ExportJobStatus::Queued,
            'care_info_id' => $this->careInfo->id,
            'school_id' => $this->careInfo->school_id,
            'creator_id' => User::factory()->create()->id,
        ]);
        $this->lambdaResult = LambdaResult::forceCreate([
            'type' => LambdaResultType::ZipService,
            'status' => ExportJobStatus::Completed,
        ]);
    }

    #[Test]
    public function it_gathers_the_info_sheet_export(): void
    {
        $infosheetExport = new ExportPdfInfoSheet($this->careInfo);

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                $zipPath = $zipPathCollection->first()->jsonSerialize();

                return $fileName === 'd8c2a17c_Hazard_Thorgan'
                    && $zipPathCollection->count() === 1
                    && Str::startsWith($zipPath['key'], 'app/exports/leerlingfiche-')
                    && Str::endsWith($zipPath['key'], '.pdf')
                    && $zipPath['name'] === 'leerlingfiche.pdf';
            })
            ->andReturn($this->lambdaResult);

        dispatch(new GatherCareExportFiles($this->careExportJob, collect([$infosheetExport])));

        $this->assertEquals($this->lambdaResult->id, $this->careExportJob->fresh()->lambda_result_id);
    }

    #[Test]
    public function it_gathers_the_care_inputs_export(): void
    {
        $careInputExport = new ExportExcelNotes([], $this->school, collect([$this->careInfo->pupil_id]));

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                $zipPath = $zipPathCollection->first()->jsonSerialize();

                return $fileName === 'd8c2a17c_Hazard_Thorgan'
                    && $zipPathCollection->count() === 1
                    && Str::startsWith($zipPath['key'], 'app/exports/zorgnotities-')
                    && Str::endsWith($zipPath['key'], '.xlsx')
                    && $zipPath['name'] === 'zorgnotities.xlsx';
            })
            ->andReturn($this->lambdaResult);

        dispatch(new GatherCareExportFiles($this->careExportJob, collect([$careInputExport])));

        $this->assertEquals($this->lambdaResult->id, $this->careExportJob->fresh()->lambda_result_id);
    }

    #[Test]
    public function it_adds_care_files(): void
    {
        $careInput = app(CareInputFactory::class)
            ->setUid('3210b586-7c4c-4bf4-8e3d-e3732100d357')
            ->setPupils(collect([$this->pupil]))
            ->create();
        $combinedCareInput = app(CareInputFactory::class)
            ->setUid('d3dfe344-d41a-45c2-a2c4-fe01bc45e5ae')
            ->setPupils(collect([$this->pupil, $this->otherPupil]))
            ->create();
        $careInputOtherPupil = app(CareInputFactory::class)
            ->setPupils(collect([$this->otherPupil]))
            ->create();

        collect([$careInput, $combinedCareInput, $careInputOtherPupil])->each(
            function (CareInput $careInput, int $index): void {
                CareFile::factory()
                    ->forCareInput($careInput)
                    ->setFile('test-file%20+' . $index . '.jpg')
                    ->setName('mytest.jpg')
                    ->create();
            },
        );

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                $zipPathCollection = $zipPathCollection->sortBy(
                    fn(ZipPath $path): mixed => $path->jsonSerialize()['name'],
                );
                $firstPath = $zipPathCollection->first()->jsonSerialize();
                $secondPath = $zipPathCollection->last()->jsonSerialize();

                return $fileName === 'd8c2a17c_Hazard_Thorgan'
                    && $zipPathCollection->count() === 2
                    && $firstPath['key'] === 'app/test-file +0.jpg'
                    && $firstPath['name'] === 'bijlagen/zorgnotities/3210b586-7c4c-4bf4-8e3d-e3732100d357_mytest.jpg'
                    && $secondPath['key'] === 'app/test-file +1.jpg'
                    && $secondPath['name'] === 'bijlagen/zorgnotities/d3dfe344-d41a-45c2-a2c4-fe01bc45e5ae_mytest.jpg';
            })
            ->andReturn($this->lambdaResult);

        dispatch(new GatherCareExportFiles($this->careExportJob, collect()));
    }

    #[Test]
    public function it_adds_the_diagnosis_attachments(): void
    {
        DiagnosisAttachment::forceCreate([
            'file' => 'test/test%20+.pdf',
            'care_info_id' => $this->careInfo->id,
        ]);
        DiagnosisAttachment::forceCreate([
            'file' => 'test/test2.pdf',
            'care_info_id' => $this->otherCareInfo->id,
        ]);

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                $zipPath = $zipPathCollection->first()->jsonSerialize();

                return $fileName === 'd8c2a17c_Hazard_Thorgan'
                    && $zipPathCollection->count() === 1
                    && $zipPath['key'] === 'app/test/test +.pdf'
                    && $zipPath['name'] === 'bijlagen/diagnoses/test +.pdf';
            })
            ->andReturn($this->lambdaResult);
        dispatch(new GatherCareExportFiles($this->careExportJob, collect()));
    }

    #[Test]
    public function it_adds_the_reports(): void
    {
        $now = Carbon::now();
        $user = User::factory()->create();
        $group = Group::factory()->withNaturalStudyYear(5)->inSchool($this->school)->setName('5A')->create();
        $reportSettings = ReportSettings::factory()->forGroup($group)->create();
        $yearReport = ReportPeriod::factory()
            ->isYearReport()
            ->forReportSettings($reportSettings)
            ->create();
        $winterPeriod = ReportPeriod::factory()
            ->inSchool($this->school)
            ->setYearReport($yearReport)
            ->forReportSettings($reportSettings)
            ->setEndDate(Carbon::create(2015, 12, 10))
            ->create(['name' => 'Winter']);
        $summerPeriod = ReportPeriod::factory()
            ->inSchool($this->school)
            ->setYearReport($yearReport)
            ->forReportSettings($reportSettings)
            ->setEndDate(Carbon::create(2016, 5, 10))
            ->create(['name' => 'Summer']);

        $evaluationPrintJobProperties = [
            'pupil_id' => $this->careInfo->pupil_id,
            'school_id' => $this->careInfo->school_id,
            'group_id' => $group->id,
            'creator_id' => $user->id,
            'created_at' => $now,
            'report_period_id' => $winterPeriod->id,
            'status' => ExportJobStatus::Completed,
            'file' => 'prints/my-report-1.pdf',
        ];
        EvaluationPrintJob::forceCreate($evaluationPrintJobProperties);
        EvaluationPrintJob::forceCreate(array_merge(
            $evaluationPrintJobProperties,
            [
                'status' => ExportJobStatus::Failed,
                'report_period_id' => $summerPeriod->id,
                'file' => 'prints/my-report-2.pdf',
            ],
        ));
        EvaluationPrintJob::forceCreate(array_merge(
            $evaluationPrintJobProperties,
            [
                'pupil_id' => $this->otherPupil->id,
                'file' => 'prints/my-report-3.pdf',
            ],
        ));

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                $zipPath = $zipPathCollection->first()->jsonSerialize();

                return $fileName === 'd8c2a17c_Hazard_Thorgan'
                    && $zipPathCollection->count() === 1
                    && $zipPath['key'] === 'app/prints/my-report-1.pdf'
                    && $zipPath['name'] === 'rapporten/2015-12-10_Winter_5A_my-report-1.pdf';
            })
            ->andReturn($this->lambdaResult);
        dispatch(new GatherCareExportFiles($this->careExportJob, collect()));
    }

    #[Test]
    public function it_uses_the_group_name_of_the_pupil_career_for_report_file_names(): void
    {
        $group = Group::factory()->withNaturalStudyYear(5)->inSchool($this->school)->setName('5A')->create();
        Career::factory()
            ->forUser($this->pupil)
            ->inGroup($group)
            ->setStartDate('2015-11-14')
            ->setEndDate('2015-11-15')
            ->create(['group_name' => 'old-name']);
        $winterPeriod = ReportPeriod::factory()
            ->inSchool($this->school)
            ->withYearReportAndReportSettings(ReportSettings::factory()->forGroup($group)->create())
            ->setStartDate(Carbon::create(2015, 11, 10))
            ->setEndDate(Carbon::create(2015, 12, 10))
            ->create(['name' => 'Winter']);

        EvaluationPrintJob::forceCreate([
            'pupil_id' => $this->careInfo->pupil_id,
            'school_id' => $this->careInfo->school_id,
            'group_id' => $group->id,
            'creator_id' => User::factory()->create()->id,
            'created_at' => Carbon::now(),
            'report_period_id' => $winterPeriod->id,
            'status' => ExportJobStatus::Completed,
            'file' => 'prints/my-report-1.pdf',
        ]);

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                return $zipPathCollection->first()->jsonSerialize()['name'] ===
                    'rapporten/2015-12-10_Winter_old-name_my-report-1.pdf';
            })
            ->andReturn($this->lambdaResult);
        dispatch(new GatherCareExportFiles($this->careExportJob, collect()));
    }

    #[Test]
    public function it_does_not_use_the_wrong_group_name_in_report_file_names(): void
    {
        $group = Group::factory()->withNaturalStudyYear(5)->inSchool($this->school)->setName('5A')->create();
        Career::factory()
            ->forUser($this->pupil)
            ->inGroup($group)
            ->setStartDate('2015-11-01')
            ->setEndDate('2015-11-09')
            ->create(['group_name' => 'old-name']);
        Career::factory()
            ->forUser($this->pupil)
            ->inGroup($group)
            ->setStartDate('2015-12-11')
            ->setEndDate('2015-12-13')
            ->create(['group_name' => 'old-name']);
        Career::factory()
            ->forUser($this->pupil)
            ->inGroup(Group::factory()->inSchool($this->school)->create())
            ->setStartDate('2015-11-14')
            ->setEndDate('2015-11-15')
            ->create(['group_name' => 'old-name']);
        $winterPeriod = ReportPeriod::factory()
            ->inSchool($this->school)
            ->withYearReportAndReportSettings(ReportSettings::factory()->forGroup($group)->create())
            ->setStartDate(Carbon::create(2015, 11, 10))
            ->setEndDate(Carbon::create(2015, 12, 10))
            ->create(['name' => 'Winter']);

        EvaluationPrintJob::forceCreate([
            'pupil_id' => $this->careInfo->pupil_id,
            'school_id' => $this->careInfo->school_id,
            'group_id' => $group->id,
            'creator_id' => User::factory()->create()->id,
            'created_at' => Carbon::now(),
            'report_period_id' => $winterPeriod->id,
            'status' => ExportJobStatus::Completed,
            'file' => 'prints/my-report-1.pdf',
        ]);

        $this->mock(LambdaZipService::class)
            ->shouldReceive('trigger')
            ->once()
            ->withArgs(function (string $fileName, ZipPathCollection $zipPathCollection) {
                return $zipPathCollection->first()->jsonSerialize()['name'] ===
                    'rapporten/2015-12-10_Winter_5A_my-report-1.pdf';
            })
            ->andReturn($this->lambdaResult);
        dispatch(new GatherCareExportFiles($this->careExportJob, collect()));
    }

    #[Test]
    public function it_works_with_soft_deleted_pupils(): void
    {
        $infosheetExport = new ExportPdfInfoSheet($this->careInfo);

        $this->mock(LambdaZipService::class)->shouldReceive('trigger')->once()->andReturn($this->lambdaResult);
        $this->careInfo->pupil->delete();
        dispatch(new GatherCareExportFiles($this->careExportJob, collect([$infosheetExport])));

        $this->assertEquals($this->lambdaResult->id, $this->careExportJob->fresh()->lambda_result_id);
    }
}
