<?php

namespace Tests\Unit\Cfa\Care\Jobs;

use Cfa\Care\Application\Jobs\ProcessPupilImport;
use Cfa\Care\Application\Services\PupilImportJob\PupilImportException;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\PupilStatus;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\Import\ImportJobStatus;
use Cfa\Care\Domain\PupilImportJob\Mapping\PupilImportJobDestination;
use Cfa\Care\Domain\PupilImportJob\Mapping\PupilImportMapping;
use Cfa\Care\Domain\PupilImportJob\PupilImportJob;
use Cfa\Common\Application\Services\NationalRegisterNumber\NationalRegisterNumber;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\Gender;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Override;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Filesystem\Filesystem;
use Tests\Filesystem\TestFilesystem;
use Tests\Unit\UnitTestCase;

use function app;
use function array_keys;
use function collect;
use function config;
use function hash_hmac;
use function json_decode;

use const DIRECTORY_SEPARATOR;

class ProcessPupilImportTest extends UnitTestCase
{
    protected School $school;

    protected User $teacher;

    protected PupilImportJob $pupilImportJob;

    protected Collection $pupils;

    protected Group $group;

    protected TestFilesystem $filesystem;

    protected string $fileWithHeadersAndNrn;

    protected ProcessPupilImport $processPupilImport;

    protected Collection $pupilData;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        // Its not a joke, but validation would fail otherwise in a few years on date of birth.
        Carbon::setTestNow(Carbon::create(2020, 4, 1));
        config(['app.locale' => 'en']);
        $this->setUpTheSchool();
    }

    #[Test]
    public function it_serializes_the_job(): void
    {
        $this->setUpFileAndFileSystem();
        $this->assertIsArray($this->processPupilImport->__serialize());
    }

    #[DataProvider('getFilesWithAndWithoutHeader')]
    #[Test]
    public function it_adds_the_data_to_the_care_infos(string $file, bool $hasHeader = true): void
    {
        $this->setUpFileAndFileSystem($file, $hasHeader);
        $this->setUpPupils();
        $this->processPupilImport->handle();
        $this->assertNotNull($this->pupilImportJob->creator_id);
        $this->pupilData->each(
            function ($data, $uid): void {
                /** @var Gender $gender */
                $gender = $data['gender'];
                /** @var Carbon $dateOfBirth */
                $dateOfBirth = $data['dateOfBirth'];
                $expectedNrn = new NationalRegisterNumber($gender, $dateOfBirth, $data['nrn'])->getHash();
                /** @var CareInfo $careInfo */
                $careInfo = Pupil::with('careInfos')->where('users.uid', $uid)->first()->careInfos->first();

                $this->assertEquals($expectedNrn, $careInfo->national_register_number);
                $this->assertTrue($dateOfBirth->isSameDay($careInfo->date_of_birth));
                $this->assertEquals($gender, $careInfo->gender);
                $this->assertEquals(Carbon::now(), $careInfo->updated_at);
                $this->assertEquals($this->pupilImportJob->creator_id, $careInfo->updater_id);
                $this->assertEquals(PupilStatus::Active, $careInfo->pupil_status);
            },
        );

        $this->assertTrue(true);
        $this->pupilImportJob->refresh();

        $this->assertEquals($this->pupilImportJob->rows_processed, 3);
        $this->assertEquals($this->pupilImportJob->rows_total, 3);
        $this->assertEquals($this->pupilImportJob->rows_failed, 0);
        $this->assertEquals(
            json_decode((string) $this->pupilImportJob->status_message, true),
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
        );
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Completed);
    }

    #[Test]
    public function it_merges_valid_matching_pupils(): void
    {
        $group = Group::factory()->inSchool($this->school)->withNaturalStudyYear(1)->create([
            'name' => '1B',
        ]);
        CareType::factory()->create();

        $pupilsToBeMerged = $this->pupilData->map(function ($item) use ($group): Pupil {
            /** @var Carbon $dateOfBirth */
            $dateOfBirth = $item['dateOfBirth'];
            /** @var Pupil $pupil */
            $pupil = Pupil::factory()
                ->has(Career::factory()->inGroup($group)->expired()->state(fn(): array => [
                    'class_number' => $item['classNumber'],
                ]))
                ->has(CareInfo::factory()->inSchool($this->school)->state(fn(): array => [
                    'date_of_birth' => $dateOfBirth->format('Y-m-d'),
                ]))
                ->create([
                    'firstname' => $item['firstName'],
                    'lastname' => $item['lastName'],
                    'gender' => $item['gender'],
                    'date_of_birth' => $dateOfBirth->format('Y-m-d'),
                ]);
            /** @var CareInfo $careInfo */
            $careInfo = $pupil->careInfos->first();
            $careInfo->gender = $item['gender'];
            $careInfo->pupil_status = PupilStatus::Active;
            $careInfo->setRawAttributes(
                array_merge($careInfo->getAttributes(), ['national_register_number' => $item['nrn_hash']]),
            );
            $careInfo->save();
            $careInputs = CareInput::factory()
                ->count(2)
                ->forSchool($this->school)
                ->create();
            $careInputs->each(function (CareInput $careInput) use ($careInfo): void {
                $careInput->careInfos()->save($careInfo);
            });

            return $pupil;
        });

        // We assert the data is still being processed correctly.
        $this->it_adds_the_data_to_the_care_infos($this->getFilesWithAndWithoutHeader()['withHeader']['file']);
        $pupilsToBeMerged->each->refresh();
        $this->pupils->each(function (Pupil $pupil) use ($group, $pupilsToBeMerged): void {
            // Assert the pupilToBeMerged got the Uid of the existing pupil.
            /** @var Pupil $mergedPupil */
            $mergedPupil = $pupilsToBeMerged->where('uid', $pupil->uid)->first();
            $this->assertNotNull($mergedPupil);
            $this->assertEquals($pupil->smd_external_uid, $mergedPupil->smd_external_uid);
            $this->assertNull(Pupil::find($pupil->id));
            $this->assertTrue($mergedPupil->careers()->where('group_id', $group->id)->exists());
            $this->assertTrue($mergedPupil->careers()->where('group_id', $this->group->id)->exists());
            $this->assertCount(1, $mergedPupil->careInfos);
            $this->assertCount(2, $mergedPupil->careInfos->first()->careInputs);
        });
    }

    #[Test]
    public function it_throws_an_exception_if_pupil_is_in_multiple_schools(): void
    {
        $extraSchool = School::factory()->inOvsg()->create();
        $this->setUpPupils();
        /** @var Pupil $pupil */
        $pupil = $this->pupils->first();
        Career::factory()->inSchool($extraSchool)->forUser($pupil)->withRole(RoleName::Pupil)->create();

        $nationalRegisterNumber = $this->pupilData->first()['nrn_hash'];
        $matchingPupil =
            Pupil::factory()->withActiveCareer($this->school)->create(
                ['firstname' => $pupil->firstname, 'lastname' => $pupil->lastname],
            );
        $careInfo =
            CareInfo::factory()->forPupil($matchingPupil)->inSchool($this->school)->make(['pupil_status' => 'active']);
        $careInfo->setNationalRegisterNumberWithoutValidation($nationalRegisterNumber);
        $careInfo->saveQuietly();

        $careInfo = CareInfo::factory()->forPupil($pupil)->inSchool($extraSchool)->make(['pupil_status' => 'active']);
        $careInfo->setNationalRegisterNumberWithoutValidation($nationalRegisterNumber);
        $careInfo->saveQuietly();

        Log::shouldReceive('error')->once()->withArgs(function (string $message, array $context) {
            $this->assertEquals(
                'Deze leerlingen kunnen niet worden samengevoegd, omdat ze ongeldige schooldata hebben.',
                $message,
            );
            $this->assertEquals(
                array_keys($context),
                ['row', 'pupilImportJobId', 'trace', 'file', 'line', 'dd.trace_id', 'dd.span_id'],
            );
            // Import has header so first pupil is on row 2.
            $this->assertEquals($context['row'], 2);
            $this->assertEquals($context['pupilImportJobId'], $this->pupilImportJob->id);

            return true;
        });
        $pupilImportExceptionThrown = false;
        try {
            $this->setUpFileAndFileSystem($this->getFilesWithAndWithoutHeader()['withHeader']['file']);
            $this->processPupilImport->handle();
        } catch (PupilImportException $exception) {
            $pupilImportExceptionThrown = true;
            $this->assertEquals('import_failed', $exception->getMessage());
        }
        $this->assertTrue($pupilImportExceptionThrown);
        $this->pupilImportJob->refresh();
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Failed);
    }

    #[Test]
    public function it_logs_an_error_if_pupil_has_no_care_info(): void
    {
        $this->setUpPupils();
        /** @var Pupil $pupil */
        $pupil = $this->pupils->first();
        $pupil->careInfos()->forceDelete();
        $this->setUpFileAndFileSystem();

        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Completed);
        $errors = json_decode((string) $this->pupilImportJob->status_message, true);
        $this->assertEquals([
            'validation_errors' => [
                'Leerling niet gevonden op de geselecteerde school.' => [2],
            ],
            'invalid_matching_pupils' => [],
        ], $errors);
    }

    #[Test]
    public function it_ignores_when_pupil_has_wisa_uid(): void
    {
        $this->setUpPupils();
        /** @var CareInfo $pupil */
        $careInfo = $this->pupils->first()->careInfos()->first();
        $careInfo->wisa_uid = uuid();
        $careInfo->save();

        $this->setUpFileAndFileSystem();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Completed);
        $this->assertStringContainsString(
            'De leerling wordt beheerd door Wisa en kan niet verwerkt worden',
            $this->pupilImportJob->status_message,
        );
    }

    #[Test]
    public function it_fails_when_a_pupil_has_a_suspicious_name_change_within_the_school(): void
    {
        Pupil::factory()
            ->withActiveCareer($this->school)
            ->withCareInfo($this->school, PupilStatus::SuspiciousNameChangeException)
            ->create();

        $pupilImportExceptionThrown = false;
        try {
            $this->setUpFileAndFileSystem();
            $this->processPupilImport->handle();
        } catch (PupilImportException $exception) {
            $pupilImportExceptionThrown = true;
            $this->assertEquals('suspicious_name_changes_found', $exception->getMessage());
        }
        $this->assertTrue($pupilImportExceptionThrown);
        $this->pupilImportJob->refresh();
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Failed);
        $this->assertStringContainsString('onverwachte naamswijziging', $this->pupilImportJob->status_message);
    }

    #[Test]
    public function it_does_not_fail_if_a_pupil_in_another_school_has_a_suspicious_name_change(): void
    {
        $secondSchool = School::factory()->inKathOndVla()->create();
        Pupil::factory()
            ->withActiveCareer($secondSchool)
            ->withCareInfo($secondSchool, PupilStatus::SuspiciousNameChangeException)
            ->create();

        $this->setUpFileAndFileSystem();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();
        $this->assertEquals($this->pupilImportJob->status, ImportJobStatus::Completed);
    }

    #[Test]
    public function it_skips_and_records_valid_and_invalid_duplicates_on_nrn(): void
    {
        $this->setUpFileAndFileSystem();

        $names = [
            ['firstName' => 'Covid', 'lastName' => '19'],
            ['firstName' => 'Quarantaine', 'lastName' => 'Cabin Fever'],
        ];
        $index = 0;

        $duplicatePupilWithActiveCareer = Pupil::factory()
            ->withActiveCareer($this->group)
            ->withCareInfo($this->school, PupilStatus::Active)
            ->create([
                'firstname' => 'Ilse',
                'lastname' => 'Bevegem',
            ]);
        CareInfo::wherePupilId($duplicatePupilWithActiveCareer->id)
            ->update(['national_register_number' => $this->pupilData->first()['nrn_hash']]);

        $invalidPupilMatches =
            $this->pupilData->slice(1)->mapWithKeys(function ($item) use ($names, &$index) {
                $pupil = Pupil::factory()
                    ->withActiveCareer($this->group)
                    ->withCareInfo($this->school)
                    ->create([
                        'firstname' => $names[$index]['firstName'],
                        'lastname' => $names[$index]['lastName'],
                    ]);
                /** @var CareInfo $careInfo */
                $careInfo = $pupil->careInfos()->first();
                $careInfo->date_of_birth = $item['dateOfBirth'];
                $careInfo->gender = $item['gender'];
                $careInfo->pupil_status = PupilStatus::Active;
                $careInfo->setRawAttributes(
                    array_merge($careInfo->getAttributes(), ['national_register_number' => $item['nrn_hash']]),
                );
                $careInfo->save();

                return [++$index => $pupil];
            });

        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertNotEmpty($this->pupilImportJob->status_message);
        $errors = json_decode((string) $this->pupilImportJob->status_message, true);

        // Key is the row, the values are the id.
        $this->assertEquals([
            'validation_errors' => [],
            'invalid_matching_pupils' => [
                2 => $duplicatePupilWithActiveCareer->id,
                3 => $invalidPupilMatches->get(1)->id,
                4 => $invalidPupilMatches->get(2)->id,
            ],
        ], $errors);
    }

    #[Test]
    public function it_skips_invalid_rows_and_saves_the_errors(): void
    {
        $this->setUpPupils();
        Pupil::where('users.uid', '5343ef02-66b4-49da-9bc1-0da47ab37efb')
            ->first()
            ->careInfos()
            ->update(['pupil_status' => PupilStatus::StandAlone]);
        $this->setUpFileAndFileSystem('pupils_with_invalid_data.xlsx');
        $this->processPupilImport->handle();
        $this->pupilImportJob->refresh();
        $this->assertNotEmpty($this->pupilImportJob->status_message);
        $errors = json_decode((string) $this->pupilImportJob->status_message, true);
        $standaloneStatus = 'Leerling heeft de status "Rijksregisternummer niet gekend"'
            . ', dit kan niet meer aangepast worden.';
        $this->assertEquals([
            'validation_errors' => [
                'Geboortedatum is verplicht.' => [10],
                'Unieke identificatie is verplicht.' => [10],
                'Geslacht is verplicht.' => [10],
                'Rijksregisternummer is verplicht.' => [10],
                'Unieke identificatie bestaat niet.' => [5, 11],
                'Het rijksregisternummer bevat een ongeldig controlegetal.' => [6],
                'De leeftijd van deze leerling ligt buiten bereik.' => [7, 8],
                $standaloneStatus => [3],
            ],
            'invalid_matching_pupils' => [],
        ], $errors);
    }

    #[Test]
    public function it_works_with_dates_indicated_as_date_in_excel(): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR . 'pupils_with_date_as_date.xls';
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport(true);

        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertEquals(
            json_decode((string) $this->pupilImportJob->status_message, true),
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
        );
        $this->assertEquals($this->pupils->first()->careInfos()->first()->date_of_birth->toDateString(), '2016-01-27');
    }

    #[Test]
    public function it_works_with_different_row_lengths(): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR . 'test-different-header-length.xlsx';
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport(true);

        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertEquals(
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
            json_decode((string) $this->pupilImportJob->status_message, true),
        );
        $this->assertEquals($this->pupils->first()->careInfos()->first()->date_of_birth->toDateString(), '2014-06-08');
    }

    #[Test]
    public function it_works_with_national_register_numbers_starting_with_zero(): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR . 'nrn_with_leading_zero.xls';
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport(true);

        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertEquals(
            json_decode((string) $this->pupilImportJob->status_message, true),
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
        );
        $this->assertEquals($this->pupils->first()->careInfos()->first()->date_of_birth->toDateString(), '2009-03-24');
    }

    #[Test]
    public function it_works_with_dates_indicated_as_date_in_other_format_in_excel(): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR
            . 'pupils_with_date_in_other_format.xls';
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport(true);

        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertEquals(
            json_decode((string) $this->pupilImportJob->status_message, true),
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
        );
        $this->assertEquals($this->pupils->first()->careInfos()->first()->date_of_birth->toDateString(), '2016-01-27');
    }

    #[Test]
    public function it_works_with_empty_rows_in_excel(): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR
            . 'pupils_with_empty_row_in_between.xlsx';
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport(true);
        $this->setUpPupils();
        $this->processPupilImport->handle();

        $this->pupilImportJob->refresh();

        $this->assertEquals(
            json_decode((string) $this->pupilImportJob->status_message, true),
            ['validation_errors' => [], 'invalid_matching_pupils' => []],
        );

        $this->assertSame(3, CareInfo::whereNotNull('pupil_status')->count());
    }

    protected function setUpFileAndFileSystem(string $file = 'pupils_with_nrn.xlsx', bool $hasHeader = true): void
    {
        $this->filesystem = Filesystem::replace();
        $this->fileWithHeadersAndNrn = 'pupil_import_jobs' . DIRECTORY_SEPARATOR . $file;
        $this->filesystem->copyResource($this->fileWithHeadersAndNrn);
        $this->createProcessPupilImport($hasHeader);
    }

    protected function setUpTheSchool(): void
    {
        $this->school = School::factory()->inKathOndVla()->create();
        $this->group = Group::factory()
            ->inSchool($this->school)
            ->withNaturalStudyYear(1)
            ->create(['name' => '1A']);
        $this->teacher = User::factory()
            ->withActiveCareer($this->group)
            ->create();
        $hashingKey = config('national-register-number.hashing-keys.default');

        $this->pupilData = collect([
            '4c8cba7e-d917-4441-864d-07c9f60932ec' => [
                'classNumber' => 1,
                'firstName' => 'Ilse',
                'lastName' => 'Bevergem',
                'gender' => Gender::Female,
                'nrn' => '14060864463',
                'nrn_hash' => hash_hmac('sha3-512', '14060864463', (string) $hashingKey),
                'dateOfBirth' => Carbon::create(2014, 6, 8),
            ],
            '5343ef02-66b4-49da-9bc1-0da47ab37efb' => [
                'classNumber' => 2,
                'firstName' => 'Bobby',
                'lastName' => 'Doyle',
                'gender' => Gender::Male,
                'nrn' => '14121009710',
                'nrn_hash' => hash_hmac('sha3-512', '14121009710', (string) $hashingKey),
                'dateOfBirth' => Carbon::create(2014, 12, 10),
            ],
            'bc2542c5-0fe3-4dd8-8caa-e3c83266fec1' => [
                'classNumber' => 3,
                'firstName' => 'Carmela',
                'lastName' => 'O\'Reilly',
                'gender' => Gender::Female,
                'nrn' => '14050693222',
                'nrn_hash' => hash_hmac('sha3-512', '14050693222', (string) $hashingKey),
                'dateOfBirth' => Carbon::create(2014, 5, 6),
            ],
        ]);
    }

    private function setUpPupils(): void
    {
        $this->pupils = $this->pupilData->map(function ($item, $uid): Pupil {
            return Pupil::factory()
                ->has(Career::factory()->inGroup($this->group)->withRole(RoleName::Pupil)->state(fn(): array => [
                    'class_number' => $item['classNumber'],
                ]))
                ->withCareInfo($this->school)
                ->create([
                    'uid' => $uid,
                    'firstname' => $item['firstName'],
                    'lastname' => $item['lastName'],
                    'gender' => Gender::Neutral,
                    'date_of_birth' => '2015-01-01',
                ]);
        });
    }

    protected function createProcessPupilImport(bool $hasHeader): void
    {
        $this->pupilImportJob = PupilImportJob::factory()
            ->setSchoolId($this->school->id)
            ->setFile($this->fileWithHeadersAndNrn)
            ->setHasHeaders($hasHeader)
            ->setCreatorId($this->teacher->id)
            ->create();

        collect([
            0 => PupilImportJobDestination::Uid,
            7 => PupilImportJobDestination::Nrn,
            5 => PupilImportJobDestination::Gender,
            6 => PupilImportJobDestination::DateOfBirth,
        ])->each(function (PupilImportJobDestination $fieldName, int $columnIndex): void {
            $mapping = new PupilImportMapping();
            $mapping->origin_column = $columnIndex;
            $mapping->destination_column = $fieldName;
            $mapping->pupil_import_job_id = $this->pupilImportJob->id;
            $mapping->save();
        });
        $this->processPupilImport = app(ProcessPupilImport::class, ['pupilImportJobId' => $this->pupilImportJob->id]);
    }

    public static function getFilesWithAndWithoutHeader(): array
    {
        return [
            'withHeader' => ['file' => 'pupils_with_nrn.xlsx', 'hasHeader' => true],
            'withStringDates' => ['file' => 'pupils_with_date_as_string.xlsx', 'hasHeader' => true],
            'withoutHeader' => ['file' => 'pupils_with_nrn_no_header.xlsx', 'hasHeader' => false],
        ];
    }
}
