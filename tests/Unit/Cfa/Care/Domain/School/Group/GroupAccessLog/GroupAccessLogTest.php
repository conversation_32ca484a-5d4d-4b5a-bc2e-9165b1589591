<?php

namespace Tests\Unit\Cfa\Care\Domain\School\Group\GroupAccessLog;

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\Group\GroupAccessLog\GroupAccessLog;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\Artisan;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function config;

class GroupAccessLogTest extends UnitTestCase
{
    private Group $group;
    private User $user;

    #[Override]
    protected function setUp(): void
    {
        $this->setTestNow('2021-07-27 14:25:25');
        parent::setUp();

        $this->user = User::factory()->create();
        $this->group = Group::factory()->inSchool(School::factory()->create())->create();
    }

    #[Test]
    public function it_deletes_access_logs_older_than_two_months(): void
    {
        GroupAccessLog::forceCreate([
            'user_id' => $this->user->id,
            'group_id' => $this->group->id,
            'accessed_at' => '2021-05-27 14:25:24',
        ]);
        Artisan::call('model:prune', ['--model' => config('model-cleanup.models')]);
        $this->assertSame(0, GroupAccessLog::count());
    }

    #[Test]
    public function it_does_not_delete_more_recently_accessed_logs(): void
    {
        GroupAccessLog::forceCreate([
            'user_id' => $this->user->id,
            'group_id' => $this->group->id,
            'accessed_at' => '2021-05-27 14:25:26',
        ]);
        Artisan::call('model:prune', ['--model' => config('model-cleanup.models')]);
        $this->assertSame(1, GroupAccessLog::count());
    }
}
