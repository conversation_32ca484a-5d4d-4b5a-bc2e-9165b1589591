<?php

namespace Tests\Unit\Cfa\Care\Models;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Illuminate\Support\Collection;
use LogicException;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Traits\WithSeeding;
use Tests\Unit\UnitTestCase;

use function factory;

class RedicodiTest extends UnitTestCase
{
    use WithSeeding;

    private const NUMBER_OF_REDICODIS = 4;
    private const NUMBER_OF_CHILDREN = 3;

    /**
     * The redicodis.
     *
     * @var Collection|Redicodi[]
     */
    protected $redicodis;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpTeacher();
        $this->setUpRedicodis();
    }

    /**
     * Set up the redicodis.
     */
    protected function setUpRedicodis(): void
    {
        $this->redicodis = collect();
        $numberOfRedicodi = 1;

        for ($i = self::NUMBER_OF_REDICODIS; $i > 0; $i--) {
            $this->redicodis->add(
                factory(Redicodi::class)->create([
                    'name' => 'Redicodi ' . $numberOfRedicodi++,
                    'order' => $i,
                    'archived_at' => null,
                    'parent_redicodi_id' => null,
                    'is_folder' => true,
                ]),
            );
        }

        $parentRedicodiId = $this->redicodis->first()->id;

        for ($i = self::NUMBER_OF_CHILDREN; $i > 0; $i--) {
            factory(Redicodi::class)->create([
                'name' => 'Redicodi ' . $numberOfRedicodi++,
                'order' => $i,
                'archived_at' => null,
                'parent_redicodi_id' => $parentRedicodiId,
                'is_folder' => false,
            ]);
        }
    }

    #[Test]
    public function it_returns_the_redicodis_in_the_correct_order(): void
    {
        $redicodis = Redicodi::whereNull('parent_redicodi_id')->get();

        $expectedResult = ['Redicodi 4', 'Redicodi 3', 'Redicodi 2', 'Redicodi 1'];
        $this->assertCount(self::NUMBER_OF_REDICODIS, $redicodis);
        $this->assertEquals($expectedResult, $redicodis->pluck('name')->toArray());
    }

    #[Test]
    public function it_returns_the_children_in_the_correct_order(): void
    {
        $children = $this->redicodis->first()->children;

        $expectedResult = ['Redicodi 7', 'Redicodi 6', 'Redicodi 5'];
        $this->assertCount(self::NUMBER_OF_CHILDREN, $children);
        $this->assertEquals($expectedResult, $children->pluck('name')->toArray());
    }

    #[Test]
    public function it_returns_the_correct_number_op_parents(): void
    {
        /* @var Redicodi $redicodi */
        $redicodi = factory(Redicodi::class)->create(['is_folder' => true]);
        $redicodiSecondLevel = factory(Redicodi::class)->create(
            ['is_folder' => true, 'parent_redicodi_id' => $redicodi->id],
        );
        $redicodiThirdLevel = factory(Redicodi::class)->create(
            ['is_folder' => false, 'parent_redicodi_id' => $redicodiSecondLevel->id],
        );
        $this->assertEquals(0, $redicodi->getNumberOfParents());
        $this->assertEquals(1, $redicodiSecondLevel->getNumberOfParents());
        $this->assertEquals(2, $redicodiThirdLevel->getNumberOfParents());
    }

    #[Test]
    public function it_number_op_parents_has_failsafe_on_recursion(): void
    {
        // Recursion check embedded, can only happen when DB data is corrupt.
        // Validation protects against such use cases.
        Redicodi::unguardValidation();
        $redicodi = factory(Redicodi::class)->create(['is_folder' => true]);
        /* @var Redicodi $redicodiSecondLevel */
        $redicodiSecondLevel = factory(Redicodi::class)->create(
            ['is_folder' => true, 'parent_redicodi_id' => $redicodi->id],
        );
        $redicodi->parent_redicodi_id = $redicodiSecondLevel->id;
        $redicodi->save();
        $errorFound = false;
        try {
            $redicodiSecondLevel->getNumberOfParents();
        } catch (LogicException $e) {
            $this->assertEquals('Recursion set on parent-child', $e->getMessage());
            $errorFound = true;
        }
        $this->assertTrue($errorFound);
        Redicodi::guardValidation();
    }

    #[Test]
    public function it_returns_has_relations_correctly_when_it_has_care_inputs(): void
    {
        $school = School::factory()->create();
        $pupil = $this->createUsersWithRole($school, RoleName::Pupil);
        $redicodi = $this->getFreshRedicodiWithHasRelations($this->redicodis->last());

        $this->assertFalse($redicodi->has_relations);

        $careInfo = CareInfo::factory()->forPupil($pupil)->inSchool($school)->create();
        $redicodi->careInfos()->attach($careInfo->id);

        $this->assertTrue($this->getFreshRedicodiWithHasRelations($redicodi)->has_relations);
    }

    #[Test]
    public function it_returns_has_relations_correctly_when_it_has_at_least_one_child_that_has_a_relation(): void
    {
        $school = School::factory()->create();
        $pupil = $this->createUsersWithRole($school, RoleName::Pupil);
        $parentRedicodi = $this->getFreshRedicodiWithHasRelations($this->redicodis->first());

        $this->assertTrue($parentRedicodi->children->isNotEmpty());
        $this->assertFalse($parentRedicodi->has_relations);

        // Attach a careInfo to a child.
        $childRedicodi = $parentRedicodi->children->first();
        $careInfo = CareInfo::factory()->forPupil($pupil)->inSchool($school)->create();
        $childRedicodi->careInfos()->attach($careInfo->id);

        // As one of its children now has a relation to a CareInfo, the parent should also be marked with has_relations.
        $this->assertTrue($this->getFreshRedicodiWithHasRelations($parentRedicodi)->has_relations);
    }

    #[Test]
    public function it_returns_has_relations_correctly_when_it_has_a_child_on_the_second_level_with_a_relation(): void
    {
        $school = School::factory()->create();
        $pupil = $this->createUsersWithRole($school, RoleName::Pupil);

        $parentRedicodi = $this->redicodis->first();
        $childRedicodi = $parentRedicodi->children->first();
        $childRedicodi->is_folder = true;
        $childRedicodi->save();
        $childChildRedicodi = factory(Redicodi::class)->create([
            'parent_redicodi_id' => $childRedicodi->id,
            'is_folder' => false,
        ]);

        $this->assertTrue($parentRedicodi->children->isNotEmpty());
        $this->assertFalse($this->getFreshRedicodiWithHasRelations($parentRedicodi)->has_relations);
        $this->assertFalse($this->getFreshRedicodiWithHasRelations($childRedicodi)->has_relations);
        $this->assertFalse($this->getFreshRedicodiWithHasRelations($childChildRedicodi)->has_relations);

        // Attach a careInfo to a child.
        $careInfo = CareInfo::factory()->forPupil($pupil)->inSchool($school)->create();
        $childChildRedicodi->careInfos()->attach($careInfo->id);

        // As one of its children now has a relation to a CareInfo, the parent should also be marked with has_relations.
        $this->assertTrue($this->getFreshRedicodiWithHasRelations($parentRedicodi)->has_relations);
        $this->assertTrue($this->getFreshRedicodiWithHasRelations($childRedicodi)->has_relations);
        $this->assertTrue($this->getFreshRedicodiWithHasRelations($childChildRedicodi)->has_relations);
    }

    private function getFreshRedicodiWithHasRelations(Redicodi $redicodi): Redicodi
    {
        return Redicodi::withHasCareInfos()->find($redicodi->id);
    }
}
