<?php

namespace Tests\Unit\Cfa\Care\Listeners;

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Language\Language;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Pupil;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class SaveHomeLanguageOfCareInfoTest extends UnitTestCase
{
    protected School $school;

    protected Pupil $pupil;

    protected CareInfo $careInfo;

    protected Language $dutch;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->create();
        $this->pupil = Pupil::factory()->withActiveCareer($this->school)->create();

        $this->dutch = Language::where('code', 'dut')->first();

        $this->careInfo = CareInfo::factory()->forPupil($this->pupil)->inSchool($this->school)->create([
            'home_language_id' => $this->dutch->id,
            'has_dutch_as_home_language' => true,
        ]);
    }

    #[Test]
    public function it_saves_false_as_the_has_dutch_as_home_language_field_correctly(): void
    {
        $this->careInfo->home_language_id = Language::where('code', 'spa')->first()->id;
        $this->careInfo->save();

        $this->assertFalse($this->careInfo->has_dutch_as_home_language);
        $this->assertTrue($this->careInfo->wasChanged(['home_language_id']));
        $this->assertTrue($this->careInfo->wasChanged(['has_dutch_as_home_language']));
    }

    #[Test]
    public function it_saves_true_as_the_has_dutch_as_home_language_field_correctly(): void
    {
        $this->careInfo->home_language_id = Language::where('code', 'spa')->first()->id;
        $this->careInfo->has_dutch_as_home_language = false;
        $this->careInfo->saveQuietly();

        $this->careInfo->home_language_id = $this->dutch->id;
        $this->careInfo->save();

        $this->assertTrue($this->careInfo->has_dutch_as_home_language);
        $this->assertTrue($this->careInfo->wasChanged(['home_language_id']));
        $this->assertTrue($this->careInfo->wasChanged(['has_dutch_as_home_language']));
    }

    #[Test]
    public function it_saves_true_as_the_has_dutch_as_home_language_field_when_no_home_language_was_provided(): void
    {
        $this->careInfo->home_language_id = Language::where('code', 'spa')->first()->id;
        $this->careInfo->has_dutch_as_home_language = false;
        $this->careInfo->saveQuietly();

        $this->careInfo->home_language_id = null;
        $this->careInfo->save();

        $this->assertTrue($this->careInfo->has_dutch_as_home_language);
        $this->assertTrue($this->careInfo->wasChanged(['home_language_id']));
        $this->assertTrue($this->careInfo->wasChanged(['has_dutch_as_home_language']));
    }

    #[Test]
    public function it_keeps_the_has_dutch_as_home_language_field_when_home_language_does_not_change(): void
    {
        $this->careInfo->extra_info = 'Dit is extra info';
        $this->careInfo->save();

        $this->assertTrue($this->careInfo->has_dutch_as_home_language);
        $this->assertFalse($this->careInfo->wasChanged(['home_language_id', 'has_dutch_as_home_language']));
    }

    #[Test]
    public function it_saves_true_for_has_dutch_as_home_language_based_on_wisa_care_data(): void
    {
        WisaCareData::factory()
            ->inSchool($this->school)
            ->create([
                'care_info_id' => $this->careInfo,
                'home_language' => 'Nederlands',
            ]);

        $this->careInfo->home_language_id = Language::where('code', 'spa')->first()->id;
        $this->careInfo->save();

        $this->assertTrue($this->careInfo->has_dutch_as_home_language);
    }

    #[Test]
    public function it_saves_false_for_has_dutch_as_home_language_based_on_wisa_care_data(): void
    {
        WisaCareData::factory()
            ->inSchool($this->school)
            ->create([
                'care_info_id' => $this->careInfo,
                'home_language' => 'Frans',
            ]);

        $this->careInfo->home_language_id = Language::where('code', 'dut')->first()->id;
        $this->careInfo->save();

        $this->assertFalse($this->careInfo->has_dutch_as_home_language);
    }
}
