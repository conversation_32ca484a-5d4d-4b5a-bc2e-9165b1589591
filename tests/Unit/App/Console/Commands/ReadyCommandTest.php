<?php

namespace Tests\Unit\App\Console\Commands;

use App\Console\Commands\Migrations\ReadyCommand;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function env;

class ReadyCommandTest extends UnitTestCase
{
    #[Test]
    public function it_indicates_everything_is_migrated(): void
    {
        $this->artisan(ReadyCommand::class, ['--database' => env('DB_CONNECTION')])->assertExitCode(0);
    }

    #[Test]
    public function it_indicates_pending_migrations(): void
    {
        DB::table('migrations')
            ->where('migration', '2017_09_07_112745_add_learning_areas_and_domains_goal_book')
            ->delete();
        $this->artisan(ReadyCommand::class, ['--database' => env('DB_CONNECTION')])->assertExitCode(1);
    }

    #[Test]
    public function it_indicates_pending_anonymous_migrations(): void
    {
        DB::table('migrations')
            ->where('migration', '2024_03_22_161803_anonymous_migration')
            ->delete();
        $this->artisan(ReadyCommand::class, ['--database' => env('DB_CONNECTION')])->assertExitCode(1);
    }
}
