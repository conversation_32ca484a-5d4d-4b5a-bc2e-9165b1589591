<?php

namespace Tests\Unit\App\Console\Commands;

use App\Console\Commands\Cache\WarmUpCache;
use App\Models\Feature\Feature;
use App\Models\Feature\FeatureToggle;
use App\Repositories\Feature\FeatureToggleCacheRepository;
use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Planner\Application\Repositories\CurriculumNodeCacheRepository;
use Cfa\Planner\Application\Repositories\PublisherCollectionCacheRepository;
use Cfa\Planner\Domain\Collection\PublisherCollection\PublisherCollection;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollection;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNode;
use Cfa\Planner\Domain\CurriculumNode\CurriculumNodeType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelClassification;
use Cfa\Planner\Domain\CurriculumNode\GradeLevelConfiguration\GradeLevelConfiguration;
use Cfa\Planner\Domain\CurriculumNode\Zill\ZillVersion;
use Illuminate\Support\Facades\Artisan;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Cache\CacheTester;
use Tests\Unit\CommandTestCase;

use function factory;

class WarmUpCacheTest extends CommandTestCase
{
    public const TARGET_AUDIENCE_TYPE = 'LO';
    public const NATURAL_STUDY_YEAR = '1';
    public const DEFAULT_APPLICATION = 'schoolonline';

    /**
     * The publishercollection
     *
     * @var UserCollection
     */
    private $publisherCollection;

    /**
     * The goal
     *
     * @var CurriculumNode
     */
    private $curriculumnode;

    /**
     * The educational network
     *
     * @var EducationalNetwork
     */
    private $network;

    /**
     * Setup of CacheWarmupCommandTest
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Setup warmup publishercollections.
        $this->publisherCollection = factory(PublisherCollection::class)
            ->states('nonArchived')
            ->create();

        // Setup warmup negative goals.
        $this->network = EducationalNetwork::whereUid(EducationalNetwork::VVKBAO_UID)->first();
        $networkConfiguration = $this->network->getConfiguration();
        $levels = collect($networkConfiguration['gradeLevelClassification'])->flip();

        $learningArea = CurriculumNode::factory()
            ->forType(CurriculumNodeType::LearningArea)
            ->setIsActive(true)
            ->forCurriculumType(CurriculumType::Default)
            ->forEducationalNetwork($this->network)
            ->create();

        $this->curriculumnode = CurriculumNode::factory()
            ->forType(CurriculumNodeType::Goal)
            ->setIsActive(true)
            ->forCurriculumType(CurriculumType::Default)
            ->forEducationalNetwork($this->network)
            ->create();

        $this->curriculumnode->parents()->save($learningArea);

        GradeLevelConfiguration::create([
            'target_audience_type' => TargetAudienceType::fromName(static::TARGET_AUDIENCE_TYPE),
            'natural_study_year' => static::NATURAL_STUDY_YEAR,
            'level' => $levels[GradeLevelClassification::Mandatory->value] ??
                $levels[GradeLevelClassification::Continuous->value] ?? null,
            'curriculumnode_id' => $this->curriculumnode->id,
        ]);
    }

    /**
     * Test if the warmup caches the publishercollections and check if the needed data is available in the cache.
     */
    #[Test]
    public function it_caches_the_publishercollections_on_a_warmup(): void
    {
        Artisan::call(WarmUpCache::class);

        // Test warmup of publishercollections.
        $collections = CacheTester::create(PublisherCollectionCacheRepository::class)
            ->setKey('publishercollections')
            ->getContents();

        $this->assertEquals($this->publisherCollection->uid, $collections->first()->uid);
    }

    /**
     * Test if the warmup caches the negative goals and check if the needed data is available in the cache.
     */
    #[Test]
    public function it_caches_the_negative_goals_on_a_warmup(): void
    {
        Artisan::call(WarmUpCache::class);

        // Test warmup of negative goals.
        $key = 'negativeGoalsByEducationalNetworkId:' . $this->network->id;
        $negativeGoals = CacheTester::create(CurriculumNodeCacheRepository::class)
            ->setKey('negativeGoalsByEducationalNetworkId:' . $this->network->id)
            ->getContents();

        $this->assertEquals(1, $negativeGoals->first()->goalsTotal);
    }

    #[Test]
    public function it_caches_the_curriculum_node_tree_zill_vvkbao_on_a_warmup(): void
    {
        $curriculumNode = CurriculumNode::factory()
            ->setIsActive(true)
            ->forCurriculumType(CurriculumType::Zill)
            ->forZillVersion(ZillVersion::whereIsCurrent(true)->first())
            ->forEducationalNetwork($this->network)
            ->create();

        $childCurriculumNode = CurriculumNode::factory()
            ->setIsActive(true)
            ->forCurriculumType(CurriculumType::Zill)
            ->forZillVersion(ZillVersion::whereIsCurrent(true)->first())
            ->forEducationalNetwork($this->network)
            ->create();

        $childCurriculumNode->parents()->attach($curriculumNode);

        Artisan::call(WarmUpCache::class);

        $key = 'curriculumNodeTreeByCurriculumType:' . CurriculumType::Zill->value . '&';

        CacheTester::create(CurriculumNodeCacheRepository::class)
            ->addTag('educationalNetworkAndCurriculumType:' . $this->network->id)
            ->setKey($key)
            ->assertFilled();
    }

    #[Test]
    public function it_caches_the_feature_toggles_on_a_warmup(): void
    {
        Artisan::call(WarmUpCache::class);

        CacheTester::create(FeatureToggleCacheRepository::class)
            ->setKey('KEY_FEATURE_ALL')
            ->assertFilled();

        $this->assertCount(FeatureToggle::count(), Feature::cases());
    }
}
