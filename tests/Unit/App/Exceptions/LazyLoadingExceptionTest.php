<?php

namespace Tests\Unit\App\Exceptions;

use App\Exceptions\System\LazyLoadingException;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class LazyLoadingExceptionTest extends UnitTestCase
{
    #[Test]
    public function it_doesnt_use_a_custom_renderer_on_web_requests(): void
    {
        $this->assertFalse(new LazyLoadingException('tst')->render(request()));
    }

    #[Test]
    public function it_uses_a_custom_renderer_on_api_requests(): void
    {
        $request = request();
        $request->headers->set('Accept', '/json');

        $response = json_decode(new LazyLoadingException('test')->render($request)->content());
        $this->assertEquals(LazyLoadingException::class, $response->Exception);
        $this->assertEquals('test', $response->message);
        $this->assertIsArray($response->trace);
    }
}
