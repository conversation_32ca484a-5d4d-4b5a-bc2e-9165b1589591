<?php

namespace Tests\Unit\App\Exceptions;

use App\Exceptions\Handler;
use App\Exceptions\Job\JobException;
use Illuminate\Contracts\Queue\Job;
use Illuminate\Database\UniqueConstraintViolationException;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Monolog\Level;
use PHPUnit\Framework\Attributes\Test;
use Route;
use RuntimeException;
use Tests\Unit\UnitTestCase;

use function app;

class HandlerTest extends UnitTestCase
{
    #[Test]
    public function it_reports_validation_messages_for_job_exceptions(): void
    {
        Log::shouldReceive('error')->once()->withArgs(function ($message, $context) {
            $this->assertSame('test', $message);
            $this->assertSame(['enum' => ['test']], $context['validation_messages']);
            $this->assertSame($context['level_overwrite'], Level::Error);

            return true;
        });
        app(Handler::class)->report(
            new JobException(
                Mockery::mock(Job::class),
                ValidationException::withMessages(['enum' => 'test']),
                Level::Error,
            ),
        );
    }

    #[Test]
    public function it_translate_the_error_message(): void
    {
        Route::get(
            'my-test-url',
            ['uses' => fn() => throw new TokenMismatchException('CSRF token mismatch.'), 'as' => 'testUrl'],
        );
        $response = $this->getJson(route('testUrl'))->getOriginalContent();
        $this->assertArrayHasKey('message', $response);
        $this->assertEquals(
            'Je sessie is verlopen, gelieve opnieuw in te loggen in een nieuwe tab of venster.',
            $response['message'],
        );
    }

    #[Test]
    public function it_doesnt_return_database_exceptions_to_the_user(): void
    {
        config(['app.debug' => false]);
        Route::get(
            'my-test-url',
            [
                'uses' => fn() => throw new UniqueConstraintViolationException(
                    'database',
                    'sql',
                    [],
                    new RuntimeException(),
                ),
                'as' => 'testUrl',
            ],
        );
        $response = $this->getJson(route('testUrl'))->getOriginalContent();
        $this->assertArrayHasKey('message', $response);
        $this->assertEquals('Server Error', $response['message']);
    }
}
