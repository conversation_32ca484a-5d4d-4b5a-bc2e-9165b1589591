<?php

namespace Tests\Unit\App\Mixins;

use App\Mixins\StrMixin;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function mb_strlen;

class StrMixinTest extends UnitTestCase
{
    #[DataProvider('getDataWithSorting')]
    #[Test]
    public function strings_are_correctly_normalized_with_sorting(
        string $firstName,
        string $lastName,
        string $expected,
    ): void {
        $mixin = new StrMixin();
        $closure = $mixin->cleanAndNormalizeInputWithSorting();
        $this->assertSame($expected, $closure($firstName, $lastName));
    }

    #[DataProvider('getDataWithoutSorting')]
    #[Test]
    public function strings_are_correctly_normalized_without_sorting(
        string $firstName,
        string $lastName,
        string $expected,
    ): void {
        $mixin = new StrMixin();
        $closure = $mixin->cleanAndNormalizeInput();
        $this->assertSame($expected, $closure($firstName, $lastName));
    }

    #[Test]
    public function it_cleans_zero_width_spaces_for_given_string(): void
    {
        $mixin = new StrMixin();

        $closure = $mixin->cleanZeroWidthSpaces();
        $string = mb_chr(0xFEFF) . mb_chr(0x200B) . mb_chr(0x200C) . mb_chr(0x200D) . 'test' . mb_chr(0xFEFF);
        $this->assertGreaterThan(4, mb_strlen($string));
        $cleanedString = $closure($string);
        $this->assertSame('test', $cleanedString);
        $this->assertSame(4, mb_strlen((string) $cleanedString));
    }

    #[Test]
    public function mixins_are_correctly_set_on_Str(): void
    {
        $this->assertSame('demaxvanvijver', Str::cleanAndNormalizeInputWithSorting('Van de Vijver', 'Max'));
        $this->assertSame('test', Str::cleanZeroWidthSpaces('test' . mb_chr(0xFEFF)));
    }

    #[Test]
    public function it_matches_two_strings(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Matie Selleslagh';

        $this->assertTrue(Str::matchWithDistance($first, $second));
    }

    #[Test]
    public function it_does_not_match_two_strings(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Matie Selleslag';

        $this->assertFalse(Str::matchWithDistance($first, $second));
    }

    #[Test]
    public function it_matches_two_strings_with_greater_distance(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Matie Seleslag';

        $this->assertTrue(Str::matchWithDistance($first, $second, 4));
    }

    #[Test]
    public function it_does_not_match_two_strings_with_greater_distance(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Matie Seeslag';

        $this->assertFalse(Str::matchWithDistance($first, $second, 4));
    }

    #[Test]
    public function it_matches_two_strings_with_smaller_distance(): void
    {
        $first = 'Ward Hachi';
        $second = 'Ward Hache';

        $this->assertTrue(Str::matchWithDistance($first, $second, 1));
    }

    #[Test]
    public function it_does_not_match_two_strings_with_smaller_distance_and_special_character(): void
    {
        // Careful, "é" vs "e" returns a levenshtein distance of 2.
        $first = 'Ward Haché';
        $second = 'Ward Hache';

        $this->assertFalse(Str::matchWithDistance($first, $second, 1));
    }

    #[Test]
    public function it_does_not_match_two_strings_with_smaller_distance(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Matie Sellaslach';

        $this->assertFalse(Str::matchWithDistance($first, $second, 1));
    }

    #[Test]
    public function it_matches_two_strings_with_no_distance(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Mattie Selleslach';

        $this->assertTrue(Str::matchWithDistance($first, $second, 0));
    }

    #[Test]
    public function it_does_not_match_two_strings_with_no_distance(): void
    {
        $first = 'Mattie Selleslach';
        $second = 'Mattie Seleslach';

        $this->assertFalse(Str::matchWithDistance($first, $second, 0));
    }

    #[DataProvider('alphaNumericSnakeDataProvider')]
    #[Test]
    public function it_applies_alpha_numeric_snake_case_correctly(string $input, string $expectedOutput): void
    {
        $this->assertSame($expectedOutput, Str::alphaNumericSnake($input));
    }

    public static function getDataWithSorting(): array
    {
        return [
            ['Stéphanie', 'Boots', 'bootsstephanie'],
            ['Boots', 'Stéphanie', 'bootsstephanie'],
            ['Van de Vijver', 'Max', 'demaxvanvijver'],
            ['Van De Vijver', 'Max', 'demaxvanvijver'],
            ['VandeVijver', 'Max', 'maxvandevijver'],
            ['Max', 'Van De Vijver', 'demaxvanvijver'],
            ['Ali-zou', 'bail-iza', 'alizoubailiza'],
            ['Zou Ali', 'Bail', 'alibailzou'],
            ['Zou', 'Ali Bail', 'alibailzou'],
        ];
    }

    public static function getDataWithoutSorting(): array
    {
        return [
            ['Stéphanie', 'Boots', 'stephanieboots'],
            ['Boots', 'Stéphanie', 'bootsstephanie'],
            ['Van de Vijver', 'Max', 'vandevijvermax'],
            ['Van De Vijver', 'Max', 'vandevijvermax'],
            ['VandeVijver', 'Max', 'vandevijvermax'],
            ['Max', 'Van De Vijver', 'maxvandevijver'],
            ['Ali-zou', 'bail-iza', 'alizoubailiza'],
            ['Zou Ali', 'Bail', 'zoualibail'],
            ['Zou', 'Ali Bail', 'zoualibail'],
        ];
    }

    public static function alphaNumericSnakeDataProvider(): array
    {
        return [
            'testMoment1' => ['testMoment1', 'test_moment_1'],
            'testMoment11' => ['testMoment11', 'test_moment_11'],
            'Foo Bar' => ['Foo Bar', 'foo_bar'],
            'fooBar' => ['fooBar', 'foo_bar'],
            '--FOO-BAR--' => ['--FOO-BAR--', 'foo_bar'],
            'foo2bar' => ['foo2bar', 'foo_2_bar'],
            'LaravelPHPFramework' => ['LaravelPHPFramework', 'laravel_php_framework'],
            'LaravelPhpFramework' => ['LaravelPhpFramework', 'laravel_php_framework'],
            'Laravel Php Framework' => ['Laravel Php Framework', 'laravel_php_framework'],
            'Laravel    Php      Framework   ' => ['Laravel    Php      Framework   ', 'laravel_php_framework'],
            'laravel php Framework' => ['laravel php Framework', 'laravel_php_framework'],
            'laravel php FrameWork' => ['laravel php FrameWork', 'laravel_php_frame_work'],
            'foo-bar' => ['foo-bar', 'foo_bar'],
            'Foo-Bar' => ['Foo-Bar', 'foo_bar'],
            'Foo_Bar' => ['Foo_Bar', 'foo_bar'],
            'ŻółtaŁódka' => ['ŻółtaŁódka', 'zolta_lodka'],
            '5+6' => ['5+6', '5_6'],
            'loJ1' => ['loJ1', 'lo_j_1'],
        ];
    }
}
