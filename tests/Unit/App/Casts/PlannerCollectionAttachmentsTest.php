<?php

namespace Tests\Unit\App\Casts;

use App\Casts\PlannerCollectionAttachments;
use Cfa\Planner\Domain\CollectionV2\Attachment\Attachment;
use Cfa\Planner\Domain\CollectionV2\PlannerCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\UnitTestCase;

class PlannerCollectionAttachmentsTest extends UnitTestCase
{
    #[Test]
    public function it_accepts_a_json_value_and_returns_an_attachments_collection(): void
    {
        $cast = new PlannerCollectionAttachments();
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $result = $cast->get($plannerCollection, 'attachments', '{"blaat"}', []);
        $this->assertCount(0, $result);
        $this->assertInstanceOf(Collection::class, $result);
    }

    private function getValidFiles(): Collection
    {
        $userUid = uuid();
        $fileUid = uuid();
        $s3Path = '/' . tenant()->uid . '/collections/users/' . $userUid . '/' . $fileUid . '/';

        return collect([
            $s3Path . 'file1.jpg',
            $s3Path . 'file2.jpg',
            $s3Path . 'file3.jpg',
        ]);
    }

    #[Test]
    public function it_accepts_attachment_files_as_json_value_and_returns_an_attachments_collection(): void
    {
        $cast = new PlannerCollectionAttachments();
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $files = $this->getValidFiles();

        $result = $cast->get(
            $plannerCollection,
            'attachments',
            json_encode($files),
            [],
        );
        $this->assertCount(3, $result);
        $this->assertInstanceOf(Attachment::class, $result[0]);
        $this->assertInstanceOf(Attachment::class, $result[1]);
        $this->assertInstanceOf(Attachment::class, $result[2]);
        $this->assertSame($files[0], $result[0]->getFile());
        $this->assertSame($files[1], $result[1]->getFile());
        $this->assertSame($files[2], $result[2]->getFile());
    }

    #[Test]
    public function it_accepts_invalid_file_as_json_value_and_silently_ignores_it_but_logs_away(): void
    {
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        Log::shouldReceive('warning')->once()->withArgs(function ($message) use ($plannerCollection) {
            $this->assertSame(
                'corrupt attachment file location found in planner collection ' . $plannerCollection->id,
                $message,
            );

            return true;
        });

        $cast = new PlannerCollectionAttachments();
        $files = collect(['invalid file']);

        $result = $cast->get(
            $plannerCollection,
            'attachments',
            json_encode($files),
            [],
        );
        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_accepts_invalid_file_and_logs_away_without_id_on_fresh_collection(): void
    {
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = new PlannerCollection();
        Log::shouldReceive('warning')->once()->withArgs(function ($message) use ($plannerCollection) {
            $this->assertSame('corrupt attachment file location found in planner collection ', $message);

            return true;
        });

        $cast = new PlannerCollectionAttachments();
        $files = collect(['invalid file']);

        $result = $cast->get(
            $plannerCollection,
            'attachments',
            json_encode($files),
            [],
        );
        $this->assertCount(0, $result);
    }
}
