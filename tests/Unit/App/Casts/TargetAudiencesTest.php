<?php

namespace Tests\Unit\App\Casts;

use App\Casts\TargetAudiences;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudience;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\Factories\Planner\PlannerCollectionFactory;
use Tests\Unit\UnitTestCase;

class TargetAudiencesTest extends UnitTestCase
{
    #[Test]
    public function it_accepts_a_json_value_and_returns_a_target_audience_collection(): void
    {
        $cast = new TargetAudiences();
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $result = $cast->get($plannerCollection, 'target_audiences', '{"blaat"}', []);
        $this->assertCount(0, $result);
        $this->assertInstanceOf(Collection::class, $result);
    }

    #[Test]
    public function it_accepts_target_audiences_uids_as_json_value_and_returns_a_target_audience_collection(): void
    {
        $cast = new TargetAudiences();
        // Just some model, we will use a plannercollection as that makes sense.
        $plannerCollection = app(PlannerCollectionFactory::class)->create();
        $targetAudiences = TargetAudience::whereIn('id', [1, 3, 5])->get();

        $result = $cast->get(
            $plannerCollection,
            'target_audiences',
            json_encode($targetAudiences->pluck('uid')),
            [],
        )
            ->sortBy('id')
            ->values();
        $this->assertCount(3, $result);
        $this->assertInstanceOf(TargetAudience::class, $result[0]);
        $this->assertInstanceOf(TargetAudience::class, $result[1]);
        $this->assertInstanceOf(TargetAudience::class, $result[2]);
        $this->assertSame(1, $result[0]->id);
        $this->assertSame(3, $result[1]->id);
        $this->assertSame(5, $result[2]->id);
    }
}
