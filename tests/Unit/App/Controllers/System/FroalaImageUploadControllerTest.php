<?php

namespace Tests\Unit\App\Controllers\System;

use App\Models\File\FileUsage;
use App\Services\File\FileUploadService;
use Cfa\Common\Application\Services\CloudFront\CloudFrontUrlService;
use Cfa\Common\Domain\User\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ControllerTestCase;

class FroalaImageUploadControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'api.system.froala-image-upload';

    #[Test]
    public function it_calls_the_upload_service_with_the_path_in_the_query_parameters(): void
    {
        $this->mock(FileUploadService::class)
            ->shouldReceive('uploadFile')
            ->withArgs(fn(User $user, FileUsage $usage, string $path): bool =>
                $user->uid === $this->owner->uid &&
                $usage === FileUsage::Froala &&
                $path === 'original%20Path')
            ->once()
            ->andReturn('test');

        $this->mock(CloudFrontUrlService::class)
            ->shouldR<PERSON>eive('getUrl')
            ->with('test')
            ->andReturn('https://cloudfront.com/test')
            ->once();

        $this->setUrlParameters(['path' => 'app/original%20Path'])
            ->getJsonWithRequiredHeaders()
            ->assertExactJson(['url' => 'https://cloudfront.com/test']);
    }
}
