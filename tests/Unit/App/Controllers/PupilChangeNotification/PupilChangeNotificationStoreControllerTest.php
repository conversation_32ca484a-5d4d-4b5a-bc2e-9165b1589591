<?php

namespace Tests\Unit\App\Controllers\PupilChangeNotification;

use Carbon\Carbon;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\PupilChange\PupilChangeNotification;
use Cfa\Common\Domain\User\PupilChange\PupilChangeNotificationType;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ControllerTestCase;

class PupilChangeNotificationStoreControllerTest extends ControllerTestCase
{
    protected static ?string $routeName = 'web.pupil-changes-notifications.store';

    protected string $redirectTo = 'web.settings.care.home';

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();
        $this->mock(SchoolHelperService::class)->makePartial()
            ->shouldReceive('school')
            ->andReturn($this->school);
    }

    #[Test]
    public function it_deletes_the_pupil_change_notifications(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $pupilChangeNotificationWithData = $this->createPupilChangeNotification(
            PupilChangeNotificationType::WithData,
        );
        $pupilChangeNotificationWithoutData = $this->createPupilChangeNotification(
            PupilChangeNotificationType::WithData,
        );

        $this->postJsonWithRequiredHeaders(['confirmed' => true])
            ->assertRedirect(route($this->redirectTo));
        $this->assertTrue($pupilChangeNotificationWithData->refresh()->trashed());
        $this->assertTrue($pupilChangeNotificationWithoutData->refresh()->trashed());
    }

    #[Test]
    public function it_does_not_deletes_the_pupil_change_notifications_with_confirmed_false(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $pupilChangeNotificationWithData = $this->createPupilChangeNotification(
            PupilChangeNotificationType::WithData,
        );
        $this->postJsonWithRequiredHeaders(['confirmed' => false])
            ->assertRedirect(route($this->redirectTo));
        $this->assertFalse($pupilChangeNotificationWithData->refresh()->trashed());
    }

    #[Test]
    public function it_increases_the_count_for_existing_pupil_change_notifications(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $pupilChangeNotificationWithData = $this->createPupilChangeNotification(
            PupilChangeNotificationType::WithData,
        );
        $pupilChangeNotificationWithData->count = 5;
        $pupilChangeNotificationWithData->save();
        $this->postJsonWithRequiredHeaders(['confirmed' => true]);
        $this->assertEquals(6, $pupilChangeNotificationWithData->refresh()->count);
    }

    #[Test]
    public function it_uses_409_and_sets_x_inertia_location_header_with_inertia_header_in_request(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $this->postJsonWithRequiredHeaders(['confirmed' => true], ['X-Inertia' => true])
            ->assertStatus(409)
            ->assertHeader('X-Inertia-Location', route($this->redirectTo));
    }

    #[Test]
    public function it_denies_access_for_non_ict_administrators(): void
    {
        $this->postJsonWithRequiredHeaders(['confirmed' => true])
            ->assertForbidden();
    }

    #[Test]
    public function it_returns_error_without_any_parameters(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $this->postJsonWithRequiredHeaders([])
            ->assertJsonValidationErrors(['confirmed' => 'Confirmed is verplicht']);
    }

    #[Test]
    public function it_returns_error_with_invalid_parameter(): void
    {
        $this->createIctAdministratorCareerForOwner();
        $this->postJsonWithRequiredHeaders(['confirmed' => 'Crazy hacker!'])
            ->assertJsonValidationErrors(['confirmed' => 'Confirmed moet ‘ja’ of ‘nee’ zijn.']);
    }

    private function createIctAdministratorCareerForOwner(): void
    {
        Career::factory()
            ->forUser($this->owner)
            ->inSchool($this->school)
            ->withRole(RoleName::SchoolIctAdministrator)
            ->create();
    }

    private function createPupilChangeNotification(PupilChangeNotificationType $type): PupilChangeNotification
    {
        return PupilChangeNotification::forceCreate(
            [
                'school_id' => $this->school->id,
                'user_id' => $this->owner->id,
                'type' => $type,
                'blocked_at' => Carbon::now(),
            ],
        );
    }
}
