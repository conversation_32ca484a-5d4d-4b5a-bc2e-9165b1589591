<?php

namespace Tests\Unit\App\Traits;

use Cfa\Common\Application\Traits\QueryRaceCondition;
use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Evaluation\Domain\Settings\EvaluationSubjectPermission\EvaluationSubjectPermission;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Override;
use PHPUnit\Framework\Attributes\Test;
use RuntimeException;
use Tests\Unit\UnitTestCase;

class QueryRaceConditionTest extends UnitTestCase
{
    /**
     * @var QueryRaceCondition
     */
    private $trait;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();
        $this->trait = Mockery::mock(QueryRaceCondition::class)->makePartial();
    }

    #[Test]
    public function it_returns_true_if_race_condition_is_found_upon_inserting(): void
    {
        $school = School::factory()->create();
        $group = Group::factory()->inSchool($school)->create();
        $subject = Subject::factory()->create();
        EvaluationSubjectPermission::factory()
            ->forSubject($subject)
            ->forGroup($group)
            ->create();

        $this->assertTrue(
            $this->trait->hasRaceConditionOccurred(
                fn(): EvaluationSubjectPermission|Collection => EvaluationSubjectPermission::factory()
                    ->forSubject($subject)
                    ->forGroup($group)
                    ->create(),
            ),
        );
        $this->assertSame(1, EvaluationSubjectPermission::all()->count());
    }

    #[Test]
    public function it_returns_false_if_race_condition_is_not_found_upon_inserting(): void
    {
        $school = School::factory()->create();
        $group = Group::factory()->inSchool($school)->create();
        $subject = Subject::factory()->create();
        EvaluationSubjectPermission::factory()
            ->forSubject($subject)
            ->forGroup($group)
            ->create();

        $this->assertFalse(
            $this->trait->hasRaceConditionOccurred(
                fn(): EvaluationSubjectPermission|Collection => EvaluationSubjectPermission::factory()
                    ->forSubject(Subject::factory()->create())
                    ->forGroup(Group::factory()->inSchool($school)->create())
                    ->create(),
            ),
        );
        $this->assertSame(2, EvaluationSubjectPermission::all()->count());
    }

    #[Test]
    public function it_bubbles_up_non_constraints_exceptions(): void
    {
        $this->expectException(RuntimeException::class);
        $this->trait->hasRaceConditionOccurred(function (): void {
            throw new RuntimeException('blaat');
        });
    }

    #[Test]
    public function it_catches_constraints_exceptions(): void
    {
        $this->trait->hasRaceConditionOccurred(function (): void {
            $this->throwDuplicatePDOException();
        });
    }

    #[Test]
    public function it_retries_when_a_race_condition_is_found_and_returns_false_on_failure(): void
    {
        $raceConditions = 0;
        $tries = 3;
        $result = $this->trait->retryWhenRaceConditionOccurs(
            function () use (&$raceConditions): void {
                $raceConditions++;
                $this->throwDuplicatePDOException();
            },
            $tries,
        );

        $this->assertFalse($result);
        $this->assertSame(3, $raceConditions);
    }

    #[Test]
    public function it_retries_when_a_race_condition_is_found_and_returns_true_on_success(): void
    {
        $raceConditions = 0;
        $result = $this->trait->retryWhenRaceConditionOccurs(
            function () use (&$raceConditions): void {
                $raceConditions++;

                if ($raceConditions === 1) {
                    $this->throwDuplicatePDOException();
                }
            },
        );

        $this->assertTrue($result);
        $this->assertSame(2, $raceConditions);
    }

    private function throwDuplicatePDOException(): void
    {
        $school = School::factory()->create();
        $group = Group::factory()->inSchool($school)->create();
        $subject = Subject::factory()->create();
        EvaluationSubjectPermission::factory()
            ->count(2)
            ->forSubject($subject)
            ->forGroup($group)
            ->create();
    }
}
