<?php

namespace Tests\Unit\App\Infrastructure\Queue;

use App\Infrastructure\Queue\Jobs\SmdExternalSqsJob;
use App\Models\SMD\EventLogStatus;
use App\Models\SMD\SmdOperation;
use App\Models\SQS\SqsEventLog;
use Aws\Sqs\SqsClient;
use Carbon\Carbon;
use Cfa\Common\Application\Jobs\Smd\SchoolEventJob;
use Illuminate\Container\Container;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Mockery;
use Override;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Queue;
use Tests\Unit\UnitTestCase;

use function json_encode;

class SmdExternalSqsJobTest extends UnitTestCase
{
    /**
     * The mocked SQS client.
     *
     * @var SqsClient
     */
    protected $mockedSqsClient;

    /**
     * The mocked IoC container.
     *
     * @var Container
     */
    protected $mockedContainer;

    /**
     * The contents of the SMD event message.
     *
     * @var array
     */
    protected $mockedMessage;

    /**
     * SQS message body.
     *
     * @var array
     */
    protected $mockedData;

    /**
     * Adds necessary data to the database before every test.
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Get a mock of the SqsClient.
        $this->mockedSqsClient = Mockery::mock(SqsClient::class)
            ->shouldAllowMockingMethod('deleteMessage')
            ->makePartial();

        // Use Mockery to mock the IoC Container.
        $this->mockedContainer = Mockery::mock(Container::class);

        // The smd event message.
        $this->mockedMessage = [
            'event' => [
                'uid' => '1a33c6e3-a2df-4f44-b9f2-448ba981b969',
                'timestamp' => '2018-06-26T12:00:00.000Z',
                'type' => 'SCHOOL',
                'operation' => 'CREATE',
                'version' => '1',
                'incrementalId' => 1,
            ],
            'payload' => [
                'uid' => 'a72d22c2-f09c-4707-b0b7-868614a3c704',
                'name' => 'GO! internaat MPI Neder-over-heembeek',
                'businessPartnerId' => 'BusinessPartnerId',
                'active' => true,
                'externalUids' => [
                    'BINGEL' => 'bingel-internal-id-for-school',
                ],
                'instituteUids' => [],
                'address' => [
                    'streetName' => 'Koning Albertlaan',
                    'houseNumber' => '181',
                    'bus' => '',
                    'postalCode' => '1120',
                    'town' => 'Neder-Over-Heembeek',
                    'city' => 'Brussel',
                    'country' => 'BE',
                ],
            ],
        ];

        // The SQS message body.
        $this->mockedData = [
            'Type' => 'Notification',
            'MessageId' => '39738b26-69f5-51d9-a335-d887dfb9053b',
            'Subject' => 'SchoolCreatedEvent',
            'Message' => json_encode($this->mockedMessage),
            'Timestamp' => '2018-06-18T13:30:52',
        ];
    }

    /**
     * No handling setup yet, test that the expected data is logged.
     */
    #[Test]
    public function it_processes_the_external_sqs_queue_event(): void
    {
        Queue::fake();

        // Message deleted from queue after processing.
        $this->mockedSqsClient
            ->shouldReceive('deleteMessage')
            ->once();

        $job = $this->getJob();
        $job->fire();

        Queue::assertPushed(SchoolEventJob::class, function (SchoolEventJob $job) {
            return $job->getPayload() === $this->mockedMessage['payload'] &&
                $job->getOperation() === SmdOperation::from($this->mockedMessage['event']['operation']) &&
                Carbon::parse($this->mockedMessage['event']['timestamp'])->equalTo($job->getTimestamp());
        });

        $this->assertStatusLogged(EventLogStatus::Processed);
    }

    #[Test]
    public function it_logs_version_mismatch_if_the_message_is_invalid(): void
    {
        Queue::fake();

        $this->mockedMessage['payload']['name'] = null;
        $this->mockedData['Message'] = json_encode($this->mockedMessage);

        Log::shouldReceive('info')
            ->times(2)
            ->withArgs(fn($message): bool =>
                Str::startsWith($message, 'JSON validation failed')
                || Str::startsWith($message, 'SMD schema mismatch'));
        Log::shouldReceive('error')->once();
        $this->expectException(InvalidArgumentException::class);

        $this->mockedSqsClient->shouldNotReceive('deleteMessage');

        $job = $this->getJob();
        $job->fire();

        $this->assertStatusLogged(EventLogStatus::Failed);
    }

    /**
     * Test that an error is thrown when the event type can't be linked to an internal job.
     */
    #[Test]
    public function it_throws_error_when_message_type_not_found(): void
    {
        Queue::fake();

        $this->mockedMessage['event']['type'] = 'INVALID_TYPE';
        $this->mockedData['Message'] = json_encode($this->mockedMessage);
        $this->expectException(InvalidArgumentException::class);

        // Message not deleted from queue after processing.
        $this->mockedSqsClient->shouldNotReceive('deleteMessage');

        $job = $this->getJob();
        $job->fire();

        Queue::assertNotPushed(SchoolEventJob::class);

        $this->assertStatusLogged(EventLogStatus::Failed);
    }

    #[Test]
    public function it_ignores_the_message_when_internal_job_is_set_to_null(): void
    {
        Queue::fake();

        config(['smd-event-mapping.MERGE_PERSON' => null]);

        $this->mockedMessage['event']['type'] = 'MERGE_PERSON';
        $this->mockedMessage['payload'] = [
            'from' => [
                [
                    'uid' => '54d1845d-c4dc-4f82-91a4-9eddcdd74f66',
                    'externalUids' => ['bingel' => '54d1845d-c4dc-4f82-91a4-9eddcdd74f66'],
                ],
            ],
            'to' => [
                'uid' => '54d1845d-c4dc-4f82-91a4-9eddcdd74f66',
                'externalUids' => ['bingel' => '54d1845d-c4dc-4f82-91a4-9eddcdd74f66'],
            ],
        ];

        $this->mockedData['Message'] = json_encode($this->mockedMessage);

        // Message not deleted from queue after processing.
        $this->mockedSqsClient
            ->shouldReceive('deleteMessage')
            ->once();

        $job = $this->getJob();
        $job->fire();

        Queue::assertNothingPushed();

        $this->assertStatusLogged(EventLogStatus::Processed);
    }

    /**
     * An exception is thrown if the json can't be validated.
     */
    #[DataProvider('getCorruptMessagesDataProvider')]
    #[Test]
    public function it_wont_process_corrupt_json_messages(?string $message): void
    {
        Queue::fake();

        $this->mockedData['Message'] = $message;
        $this->expectException(InvalidArgumentException::class);

        // Message not deleted from queue after processing.
        $this->mockedSqsClient->shouldNotReceive('deleteMessage');

        $job = $this->getJob();
        $job->fire();

        Queue::assertNotPushed(SchoolEventJob::class);

        $this->assertStatusLogged(EventLogStatus::Failed);
    }

    #[Test]
    public function it_gets_the_name(): void
    {
        $this->assertSame('SmdExternalSqsJob', $this->getJob()->getName());
    }

    public static function getCorruptMessagesDataProvider(): array
    {
        return [
            [''],
            ['some random string'],
            [null],
        ];
    }

    protected function assertStatusLogged(EventLogStatus $eventLogStatus): void
    {
        $sqsEventLogs = SqsEventLog::all();
        $this->assertCount(1, $sqsEventLogs);
        $sqsEventLog = $sqsEventLogs->first();
        $this->assertEquals($sqsEventLog->source, 'SmdExternalSqsJob');
        $this->assertEquals($sqsEventLog->status, $eventLogStatus->value);
        $this->assertEquals($sqsEventLog->payload, json_encode($this->mockedData));
    }

    /**
     * Return a job using mocked container and SQS client.
     */
    protected function getJob(): SmdExternalSqsJob
    {
        // Fake queue url.
        $queueUrl = 'https://sqs.someregion.amazonaws.com/**********/testqueue';

        return new SmdExternalSqsJob(
            $this->mockedContainer,
            $this->mockedSqsClient,
            $this->getMockedJobData(),
            'external-sqs',
            $queueUrl,
        );
    }

    /**
     * Return an array for the SQS message response.
     */
    protected function getMockedJobData(): array
    {
        $mockedPayload = json_encode($this->mockedData);

        return [
            'Body' => $mockedPayload,
            'MD5OfBody' => md5($mockedPayload),
            'ReceiptHandle' => '0NNAq8PwvXuWv5gMtS9DJ8qEdyiUwbAjpp45w2m6M4SJ1Y+PxCh7R930NRB8ylSa',
            'MessageId' => 'e3cd03ee-59a3-4ad8-b0aa-ee2e3808ac81',
            'Attributes' => ['ApproximateReceiveCount' => 1],
        ];
    }
}
