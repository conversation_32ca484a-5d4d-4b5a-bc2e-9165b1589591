<?php

namespace Tests\Unit\App\Infrastructure\Queue;

use App\Constants\CacheStores;
use App\Infrastructure\Queue\ExternalSqsQueue;
use App\Infrastructure\Queue\Jobs\BingelResultsExternalSqsJob;
use App\Infrastructure\Queue\Jobs\LambdaResultsExternalSqsJob;
use App\Infrastructure\Queue\Jobs\SmdExternalSqsJob;
use Aws\Command;
use Aws\Result;
use Aws\Sqs\Exception\SqsException;
use Aws\Sqs\SqsClient;
use Cache;
use Illuminate\Container\Container;
use Mockery;
use Override;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class ExternalSqsQueueTest extends UnitTestCase
{
    private const CACHE_KEY = 'external_sqs_disabled';

    /**
     * The mocked SQS client.
     *
     * @var SqsClient
     */
    protected $mockedSqsClient;

    /**
     * The mocked IoC container.
     *
     * @var Container
     */
    protected $mockedContainer;

    /**
     * {@inheritdoc}
     */
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        // Get a mock of the SqsClient.
        $this->mockedSqsClient = Mockery::mock(SqsClient::class)
            ->shouldAllowMockingMethod('receiveMessage')
            ->shouldAllowMockingMethod('getQueueAttributes')
            ->makePartial();
        ExternalSqsQueue::enable();

        // Use Mockery to mock the IoC Container.
        $this->mockedContainer = Mockery::mock(Container::class);

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'default');
        $queue->setContainer($this->mockedContainer);

        config()->set('queue.queue-names.smd', 'smd-queue');
        config()->set('queue.queue-names.bingel-results', 'bingel-results-queue');
        config()->set('queue.queue-names.lambda-results', 'lambda-results-queue');
    }

    #[DataProvider('getExpectedQueues')]
    #[Test]
    public function it_can_receive_sqs_messages_when_enabled(string $queueName, string $className): void
    {
        $this->mockedSqsClient
            ->shouldReceive('receiveMessage')
            ->once()
            ->andReturn([
                'Messages' => [
                    ['Body' => 'Boo'],
                ],
            ]);

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'smd-queue,bingel-results-queue,lambda-results-queue');
        $queue->setContainer($this->mockedContainer);

        $sqsJob = $queue->pop($queueName);
        $this->assertInstanceOf($className, $sqsJob);
        $this->assertEquals('Boo', $sqsJob->getRawBody());
        $this->assertEquals('/' . $queueName, $sqsJob->getQueue());
    }

    #[Test]
    public function it_can_process_jobs_from_different_queues(): void
    {
        $this->mockedSqsClient
            ->shouldReceive('receiveMessage')
            ->once()
            ->andReturn([
                'Messages' => [
                    ['Body' => 'Boo'],
                    ['Body' => 'Boo2'],
                ],
            ]);

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'smd-queue,bingel-results-queue,lambda-results-queue');
        $queue->setContainer($this->mockedContainer);

        $sqsJob = $queue->pop('bingel-results-queue');
        $this->assertInstanceOf(BingelResultsExternalSqsJob::class, $sqsJob);
        $this->assertEquals('Boo', $sqsJob->getRawBody());
        $this->assertEquals('/bingel-results-queue', $sqsJob->getQueue());

        $sqsJob2 = $queue->pop();
        $this->assertInstanceOf(BingelResultsExternalSqsJob::class, $sqsJob2);
        $this->assertEquals('Boo2', $sqsJob2->getRawBody());
        $this->assertEquals('/bingel-results-queue', $sqsJob2->getQueue());
    }

    public static function getExpectedQueues(): array
    {
        return [
            'smd' => ['smd-queue', SmdExternalSqsJob::class],
            'bingel-results' => ['bingel-results-queue', BingelResultsExternalSqsJob::class],
            'lambda-results' => ['lambda-results-queue', LambdaResultsExternalSqsJob::class],
        ];
    }

    /**
     * It doesn't get a message from the queue when disabled.
     */
    #[Test]
    public function it_doenst_receive_sqs_message_when_disabled(): void
    {
        // Disable queue processing.
        Cache::store(CacheStores::MAINTENANCE)->forever(self::CACHE_KEY, true);

        $this->mockedSqsClient->shouldNotReceive('receiveMessage');

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'default');
        $queue->setContainer($this->mockedContainer);

        $this->assertNull($queue->pop());
    }

    #[Test]
    public function it_retrieves_the_size_of_multiple_queues(): void
    {
        $this->mockedSqsClient
            ->shouldReceive('getQueueAttributes')
            ->twice()
            ->andReturn(new Result(['Attributes' => [
                'ApproximateNumberOfMessages' => 5,
                'ApproximateNumberOfMessagesNotVisible' => 0,
                'ApproximateNumberOfMessagesDelayed' => 0,
            ]]));

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'default');
        $queue->setContainer($this->mockedContainer);

        $this->assertEquals(10, $queue->readyNow('default,other'));
    }

    #[Test]
    public function it_retrieves_the_size_of_one_queue(): void
    {
        $this->mockedSqsClient
            ->shouldReceive('getQueueAttributes')
            ->once()
            ->andReturn(new Result(['Attributes' => [
                'ApproximateNumberOfMessages' => 5,
                'ApproximateNumberOfMessagesDelayed' => 0,
                'ApproximateNumberOfMessagesNotVisible' => 0,
            ]]));

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'default');
        $queue->setContainer($this->mockedContainer);

        $this->assertEquals(5, $queue->readyNow('default'));
    }

    #[Test]
    public function it_returns_size_0_when_the_sqs_sdk_throws_an_exception(): void
    {
        $this->mockedSqsClient
            ->shouldReceive('getQueueAttributes')
            ->once()
            ->andThrow(new SqsException('exception', new Command('sqs')));

        $queue = new ExternalSqsQueue($this->mockedSqsClient, 'default');
        $queue->setContainer($this->mockedContainer);

        $this->assertEquals(0, $queue->readyNow('default'));
    }
}
