<?php

namespace Tests\Unit\App\Infrastructure\Csp\Policies;

use App\Constants\HttpHeaders;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class CommonPolicyTest extends UnitTestCase
{
    /**
     * Tests if the CSP header is properly set
     */
    #[Test]
    public function it_sets_the_csp_header(): void
    {
        config(
            [
                'csp.policy' => 'App\Infrastructure\Csp\Policies\ProductionPolicy',
                'csp.enabled' => true,
            ],
        );

        $response = $this->get('/');

        $response->assertHeader(HttpHeaders::CONTENT_SECURITY_POLICY);
    }
}
