<?php

namespace Tests\Unit\App\Gates;

use Cfa\Common\Application\Repositories\UserAccessRepository;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\Permission\PermissionSchool;
use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\Gate;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class CanAccessEvaluationGateTest extends UnitTestCase
{
    private School $school;

    private User $user;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->withoutPermissions()->create();
        $this->user = User::factory()->withActiveCareer($this->school, RoleName::Teacher)->create();

        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($this->school);
        $this->mock(UserAccessRepository::class)->shouldReceive('hasAccessToSchool')->andReturnTrue();
    }

    #[Test]
    public function it_denies_a_school_without_evaluation_permission(): void
    {
        $this->assertFalse(Gate::forUser($this->user)->allows(PermissionName::HasAccessToEvaluation->name));
    }

    #[Test]
    public function it_allows_a_school_with_evaluation_permission(): void
    {
        PermissionSchool::factory()
            ->inSchool($this->school)
            ->forTargetAudienceType(TargetAudienceType::Lo)
            ->setPermission(PermissionName::HasAccessToEvaluation)
            ->create();

        $this->assertTrue(Gate::forUser($this->user)->allows(PermissionName::HasAccessToEvaluation->name));
    }
}
