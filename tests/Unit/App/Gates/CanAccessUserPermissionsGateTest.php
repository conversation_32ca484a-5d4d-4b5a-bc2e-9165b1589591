<?php

namespace Tests\Unit\App\Gates;

use Cfa\Common\Application\Repositories\UserAccessRepository;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\Permission\PermissionName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\Gate;
use Override;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

class CanAccessUserPermissionsGateTest extends UnitTestCase
{
    private School $school;

    private User $user;

    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->withoutPermissions()->create();
        $this->user = User::factory()->withActiveCareer($this->school, RoleName::Teacher)->create();

        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($this->school);
        $this->mock(UserAccessRepository::class)->shouldReceive('hasAccessToSchool')->andReturnTrue();
    }

    #[DataProvider('getPermissionNames')]
    #[Test]
    public function it_denies_without_the_permission(PermissionName $permissionName): void
    {
        $this->assertFalse(Gate::forUser($this->user)->allows($permissionName->name));
    }

    #[DataProvider('getPermissionNames')]
    #[Test]
    public function it_allows_with_the_permission(PermissionName $permissionName): void
    {
        $schoolUserAccess = $this->user->schoolUserAccess->first();
        $schoolUserAccess->addPermission($permissionName);

        $this->assertTrue(Gate::forUser($this->user)->allows($permissionName->name));
    }

    public static function getPermissionNames(): array
    {
        return [
            'can access history' => [PermissionName::from(PermissionName::CanAccessHistory->value)],
        ];
    }
}
