<?php

namespace Tests\Unit\App\Gates;

use Cfa\Common\Application\Repositories\UserAccessRepository;
use Cfa\Common\Application\Services\Helpers\SchoolHelperService;
use Cfa\Common\Domain\Gate\GateName;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\User;
use Illuminate\Support\Facades\Gate;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ControllerTestCase;

class CanAccessCareAndSettingsGateTest extends ControllerTestCase
{
    #[Override]
    public function setUp(): void
    {
        parent::setUp();

        $this->school = School::factory()->inKathOndVla()->create();

        $this->mock(SchoolHelperService::class)->shouldReceive('school')->andReturn($this->school);
        $this->mock(UserAccessRepository::class)->shouldReceive('hasAccessToSchool')->andReturnTrue();
    }

    #[Test]
    public function it_denies_a_user_without_permissions(): void
    {
        $pupil = User::factory()->withActiveCareer($this->school, RoleName::Pupil)->create();
        $this->assertTrue(Gate::forUser($pupil)->denies(GateName::CanAccessCareAndSettings->value));
    }

    #[Test]
    public function it_denies_a_user_with_only_care_permission(): void
    {
        $teacher = User::factory()->withActiveCareer($this->school, RoleName::Teacher)->create();
        $this->assertTrue(Gate::forUser($teacher)->denies(GateName::CanAccessCareAndSettings->value));
    }

    #[Test]
    public function it_allows_a_user_with_care_and_settings_permission(): void
    {
        $ictAdmin = User::factory()->withActiveCareer($this->school, RoleName::SchoolIctAdministrator)->create();
        $this->assertTrue(Gate::forUser($ictAdmin)->allows(GateName::CanAccessCareAndSettings->value));
    }
}
