<?php

namespace Tests\Unit\App\Logging;

use App\Logging\DataDogJsonFormatter;
use Illuminate\Log\Logger as IlluminateLogger;
use Illuminate\Support\Arr;
use Monolog\Handler\TestHandler;
use Monolog\Level;
use Monolog\Logger;
use Monolog\LogRecord;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function app;
use function json_decode;

class DatadogJsonFormatterTest extends UnitTestCase
{
    private IlluminateLogger $logger;

    private TestHandler $handler;

    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->handler = new TestHandler();
        $this->handler->setFormatter(app()->make(DataDogJsonFormatter::class, ['applicationName' => 'bingel-tms']));
        $this->logger = new IlluminateLogger(new Logger('logger', [$this->handler]));
    }

    #[Test]
    public function it_contains_severity_and_timestamp_for_datadog(): void
    {
        $this->logger->error('Test');

        $logContext = $this->handler->getRecords()[0];
        $this->assertStringContainsString('"severity":"ERROR"', $logContext['formatted']);
        $this->assertStringContainsString('"@timestamp":"', $logContext['formatted']);
    }

    #[Test]
    public function it_sets_the_exception_data(): void
    {
        $this->handler->pushProcessor(function (LogRecord $record) {
            $record->extra['exception'] = 'added exception';
            $record->extra['previous_exceptions'] = 'added previous exception';

            return $record;
        });
        $this->logger->error('Test', ['exception' => 'first exception']);

        $logData = $this->getFormattedLogData();
        $this->assertEmpty($logData['extra']);
        $this->assertNotEmpty($logData['context']['dd.trace_id']);
        $this->assertNotEmpty($logData['context']['dd.span_id']);
        $this->assertSame(Arr::except($logData['context'], ['dd.trace_id', 'dd.span_id']), [
            'exception' => 'added exception',
            'previous_exceptions' => 'added previous exception',
        ]);
    }

    #[Test]
    public function it_overwrites_the_level_info(): void
    {
        $this->handler->pushProcessor(function (LogRecord $record) {
            $record->extra['level'] = Level::Debug;
            $record->extra['level_name'] = Level::Notice->getName();

            return $record;
        });
        $this->logger->error('Test');

        $logData = $this->getFormattedLogData();
        $this->assertEmpty($logData['extra']);
        $this->assertSame($logData['level'], Level::Debug->value);
        $this->assertSame($logData['level_name'], Level::Notice->getName());
    }

    private function getFormattedLogData(): array
    {
        return json_decode((string) $this->handler->getRecords()[0]['formatted'], true);
    }
}
