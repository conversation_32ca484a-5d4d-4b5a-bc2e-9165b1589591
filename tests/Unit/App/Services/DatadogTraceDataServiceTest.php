<?php

namespace Tests\Unit\App\Services;

use App\Services\DatadogTraceDataService;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function app;

class DatadogTraceDataServiceTest extends UnitTestCase
{
    #[Test]
    public function it_returns_the_trace_id(): void
    {
        $this->assertGreaterThanOrEqual(1, Str::length(app(DatadogTraceDataService::class)->getTraceID()));
    }

    #[Test]
    public function it_returns_the_span_id(): void
    {
        $this->assertGreaterThanOrEqual(1, Str::length(app(DatadogTraceDataService::class)->getSpanID()));
    }
}
