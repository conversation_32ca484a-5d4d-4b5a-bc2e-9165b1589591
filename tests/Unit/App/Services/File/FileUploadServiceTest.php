<?php

namespace Tests\Unit\App\Services\File;

use App\Exceptions\File\InvalidFileException;
use App\Models\File\FileUsage;
use App\Services\File\FileUploadService;
use App\Services\File\FileValidationService;
use Cfa\Common\Domain\User\User;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Mockery;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function app;

class FileUploadServiceTest extends UnitTestCase
{
    #[DataProvider('expectedPaths')]
    #[Test]
    public function it_returns_the_path_of_the_uploaded_file(FileUsage $fileUsage, string $expectedPath): void
    {
        $this->mock(FileValidationService::class)->shouldReceive('validateFile')
            ->once()
            ->withArgs(function ($usage, $filePath) use ($fileUsage) {
                $this->assertSame('tmp/upload-zone/b/image%20%2B%201.jpg', $filePath);
                $this->assertSame($fileUsage, $usage);

                return true;
            })
            ->andReturn([]);
        $mockStorage = Mockery::mock(FilesystemAdapter::class);
        $mockStorage->shouldReceive('move')
            ->once()
            ->withArgs(function ($from, $to) use ($expectedPath) {
                $this->assertSame('tmp/upload-zone/b/image + 1.jpg', $from);
                $this->assertSame($expectedPath, $to);

                return true;
            })
            ->andReturnTrue();
        $user = User::factory()->create(['uid' => 'uid-user-a']);
        $actualPath = app(FileUploadService::class, ['filesystemAdapter' => $mockStorage])
            ->uploadFile($user, $fileUsage, 'tmp/upload-zone/b/image%20%2B%201.jpg');
        $this->assertSame($expectedPath, $actualPath);
    }

    #[Test]
    public function it_throws_an_exception_if_an_invalid_file_is_uploaded(): void
    {
        $this->mock(FileValidationService::class)->shouldReceive('validateFile')->once()->andReturn(['error']);
        $this->expectException(InvalidFileException::class);
        $mockStorage = Mockery::mock(FilesystemAdapter::class);
        $mockStorage->shouldReceive('move')->never();
        app(FileUploadService::class, ['filesystemAdapter' => $mockStorage])
            ->uploadFile(User::factory()->create(), FileUsage::Default, 'image.jpg');
    }

    #[Test]
    public function it_uploads_a_local_file_without_validation(): void
    {
        $this->mock(FileValidationService::class)->shouldReceive('validateFile')->never();
        $this->mock(FileValidationService::class)->shouldReceive('extensionIsAllowed')->once()->andReturnTrue();
        $mockStorage = Mockery::mock(FilesystemAdapter::class);
        $mockStorage->shouldReceive('putFileAs')
            ->once()
            ->withArgs(function ($destinationDirectory, $file, $fileName) {
                $this->assertStringStartsWith('sol-fl/uploads/uid-user-a/', $destinationDirectory);
                $this->assertSame('300x300.jpg', $fileName);

                return true;
            })
            ->andReturn('newPath');
        $actualPath = app(FileUploadService::class, ['filesystemAdapter' => $mockStorage])
            ->uploadLocalFile('uid-user-a', FileUsage::Default, 'tests/resources/covers/300x300.jpg');
        $this->assertSame('newPath', $actualPath);
    }

    #[Test]
    public function it_logs_when_file_not_found_in_s3(): void
    {
        Log::shouldReceive('error')->once()->withArgs(function ($message) {
            $this->assertSame(
                'File ' . 'sol-fl/uploads/image + 1.jpg' . ' not found',
                $message,
            );

            return true;
        });
        app(FileUploadService::class)->deleteFiles(['sol-fl/uploads/image%20%2B%201.jpg']);
    }

    #[Test]
    public function it_removes_the_files_found_in_s3(): void
    {
        Storage::fake();
        Storage::cloud()->put('sol-fl/uploads/image + 1.jpg', 'test');
        Storage::cloud()->put('sol-fl/uploads/image + 2.jpg', 'test');
        app(FileUploadService::class)->deleteFiles([
            'sol-fl/uploads/image%20%2B%201.jpg',
            'sol-fl/uploads/image%20%2B%202.jpg',
        ]);
        Storage::cloud()->assertMissing(urldecode('sol-fl/uploads/image + 1.jpg'));
        Storage::cloud()->assertMissing(urldecode('sol-fl/uploads/image + 2.jpg'));
    }

    public static function expectedPaths(): array
    {
        return [
            'default' => [FileUsage::Default, 'sol-fl/uploads/uid-user-a/b/image + 1.jpg'],
            'care' => [FileUsage::Care, 'sol-fl/uploads/uid-user-a/b/image + 1.jpg'],
            'collectionAttachment' => [
                FileUsage::CollectionAttachment,
                'sol-fl/collections/users/uid-user-a/b/image + 1.jpg',
            ],
            'diagnosisAttachments' => [
                FileUsage::DiagnosisAttachments,
                'sol-fl/diagnosis-attachments/b/image + 1.jpg',
            ],
            'careImportFile' => [
                FileUsage::CareImportFile,
                '/tmp/care_import_jobs/b/image + 1.jpg',
            ],
            'careImportAttachments' => [
                FileUsage::CareImportAttachments,
                '/tmp/care_import_jobs/b/image + 1.jpg',
            ],
            'careImportPupils' => [
                FileUsage::CareImportPupils,
                '/tmp/care_import_pupils/b/image + 1.jpg',
            ],
        ];
    }
}
