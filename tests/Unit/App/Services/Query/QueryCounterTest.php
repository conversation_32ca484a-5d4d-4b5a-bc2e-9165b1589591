<?php

namespace Tests\Unit\App\Services\Query;

use App\Services\Query\QueryCounter;
use Cfa\Common\Domain\User\User;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\ServiceTestCase;

class QueryCounterTest extends ServiceTestCase
{
    /** @var QueryCounter */
    protected $service;

    protected string $serviceName = QueryCounter::class;

    #[Test]
    public function it_counts_the_number_of_queries(): void
    {
        $this->service->resetCounter();
        User::first();
        $this->assertEquals(1, $this->service->getQueryCount());
    }

    #[Test]
    public function it_resets_the_counter(): void
    {
        $this->it_counts_the_number_of_queries();
        $this->assertEquals(1, $this->service->getQueryCount());
        $this->service->resetCounter();
        $this->assertEquals(0, $this->service->getQueryCount());
    }
}
