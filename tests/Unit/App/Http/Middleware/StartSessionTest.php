<?php

namespace Tests\Unit\App\Http\Middleware;

use App\Constants\Cookies;
use App\Constants\Subdomain;
use App\Http\Middleware\StartSession;
use PHPUnit\Framework\Attributes\Test;
use Route;
use Tests\Unit\UnitTestCase;

use function config;

class StartSessionTest extends UnitTestCase
{
    #[Test]
    public function it_puts_the_session_cookie_on_the_main_domain(): void
    {
        config(['session.driver' => 'file']);

        Route::get('my-test-url', ['as' => 'testUrl', 'uses' => fn(): string => '', 'domain' => Subdomain::CARE])
            ->middleware([StartSession::class]);

        $this->get(route('testUrl'))
            ->assertCookieOnMainDomain(Cookies::session());
    }
}
