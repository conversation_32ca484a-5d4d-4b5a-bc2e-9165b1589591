<?php

namespace Tests\Unit\App\Rules;

use App\Models\File\FileUsage;
use App\Rules\FileUploadAllowedRule;
use App\Services\File\FileValidationService;
use PHPUnit\Framework\Attributes\Test;
use Tests\Unit\UnitTestCase;

use function app;

class FileUploadAllowedRuleTest extends UnitTestCase
{
    #[Test]
    public function it_returns_true_if_validate_file_returns_an_empty_array(): void
    {
        $this->mock(FileValidationService::class)
            ->shouldReceive('validateFile')
            ->once()
            ->withArgs(
                fn($usage, $filePath): bool => $usage === FileUsage::Default && $filePath === 'tmp/uploads/uid/1.jpg',
            )
            ->andReturn([]);
        $rule = new FileUploadAllowedRule(app(FileValidationService::class), FileUsage::Default);
        $this->assertTrue($rule->passes('some attribute', 'tmp/uploads/uid/1.jpg'));
    }

    #[Test]
    public function it_returns_false_if_validate_file_returns_a_non_empty_array(): void
    {
        $this->mock(FileValidationService::class)
            ->shouldReceive('validateFile')
            ->once()
            ->withArgs(
                fn($usage, $filePath): bool => $usage === FileUsage::Default && $filePath === 'tmp/uploads/uid/1.jpg',
            )
            ->andReturn(['not nothing']);
        $rule = new FileUploadAllowedRule(app(FileValidationService::class), FileUsage::Default);
        $this->assertFalse($rule->passes('some attribute', 'tmp/uploads/uid/1.jpg'));
    }
}
