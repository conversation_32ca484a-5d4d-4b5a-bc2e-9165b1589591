# Frontend

## Table of contents

<!-- TOC -->
* [Frontend](#frontend)
  * [Table of contents](#table-of-contents)
* [Linting and Code Style Guide](#linting-and-code-style-guide)
  * [Directory Structure](#directory-structure)
  * [Linting Tools](#linting-tools)
  * [Pre-commit Hooks](#pre-commit-hooks)
    * [Setting up Husky](#setting-up-husky)
    * [Running Linters Manually](#running-linters-manually)
    * [Configuration Files](#configuration-files)
    * [Customizing Rules](#customizing-rules)
    * [Troubleshooting](#troubleshooting)
    * [Continuous Integration](#continuous-integration)
<!-- TOC -->

# Linting and Code Style Guide

Linting and code style practices for the frontend in Bingel TMS.

## Directory Structure

Our frontend code is primarily located in the `resources/assets` directory:

- `resources/assets/js`: JavaScript and Vue files
- `resources/assets/css`: CSS and SCSS files
- `resources/assets/icons`: Icon files
- `resources/assets/fonts`: Font files
- `resources/assets/cms`: CMS-related files

## Linting Tools

We use the following tools to maintain code quality and consistency:

1. `ESLint`: For JavaScript and Vue files
2. `Prettier`: For overall code formatting

## Pre-commit Hooks

We use <PERSON>sky to run pre-commit hooks that automatically lint and format your code before each commit.

### Setting up Husky

After cloning the repository, run:

```bash
npm install
npm run prepare
```

This will install Husky and set up the pre-commit hooks.

### Running Linters Manually

You can run the linters manually using the following npm scripts:

```bash
# Run ESLint
npm run lint

# Run Prettier
npm run format
```

### Configuration Files

- ESLint: `.eslintrc.cjs`
- Prettier: `.prettierrc.json`

### Customizing Rules

If you need to customize any linting rules:

1. Discuss the changes with the team
2. Update the appropriate configuration file
3. Update this documentation to reflect any significant changes

### Troubleshooting

If you encounter any issues with the linting process:

1. Ensure all dependencies are installed: `npm install`
2. Clear the lint cache: `npm run lint:clear-cache`
3. If problems persist, reach out to the team for assistance

### Continuous Integration

Our Bitbucket Pipelines configuration includes steps to run these linters on every pull request. Make sure your code passes all checks before requesting a review.