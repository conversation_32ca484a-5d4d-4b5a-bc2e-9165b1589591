<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('group_school_user_access', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('school_user_access_id');
            $table->unsignedInteger('group_id');

            $table->foreign('school_user_access_id', 'group_school_user_access_school_user_access_id')
                ->references('id')
                ->on('school_user_access')
                ->onDelete('cascade');

            $table->foreign('group_id', 'group_school_user_access_group_id')
                ->references('id')
                ->on('groups')
                ->onDelete('cascade');

            $table->unique(
                [
                    'school_user_access_id',
                    'group_id',
                ],
                'group_school_user_access_unique',
            );
        });
    }
};
