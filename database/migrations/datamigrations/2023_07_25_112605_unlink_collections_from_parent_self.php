<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up()
    {
        $now = Carbon::now();
        DB::table('plannercollections')
            ->select('childCollection.*')
            ->from('plannercollections as childCollection')
            ->join(
                'plannercollections as parentCollection',
                fn(JoinClause $joinClause): JoinClause => $joinClause
                    ->on('childCollection.parent_collection_id', '=', 'parentCollection.id')
                    ->on('childCollection.owner_id', '=', 'parentCollection.owner_id')
                    ->whereNotNull('childCollection.owner_id')
                    ->whereNotNull('childCollection.parent_collection_id'),
            )
            ->whereNull('childCollection.archived_at')
            ->update(['childCollection.parent_collection_id' => null, 'childCollection.updated_at' => $now]);
    }
};
