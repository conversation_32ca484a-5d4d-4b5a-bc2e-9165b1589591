<?php

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\User\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Query\JoinClause;

return new class extends Migration {
    public function up(): void
    {
        $schoolId = School::where('partner_number', 1150861)->firstOrFail(['id'])->id;
        $pupil = ['id' => 1834439, 'firstname' => 'Sam', 'lastname' => 'Claus'];
        $careTypesIds = CareType::where('school_id', $schoolId)
            ->where('name', 'LIKE', '%Observatie%')->pluck('id');

        $pupilId = User::where('id', $pupil['id'])
            ->where('firstname', $pupil['firstname'])
            ->where('lastname', $pupil['lastname'])
            ->firstOrFail(['id'])
            ->id;

        $careInfoId = CareInfo::where('pupil_id', $pupilId)
            ->where('school_id', $schoolId)
            ->firstOrFail(['id'])
            ->id;
        CareInput::withTrashed()
            ->join(
                'care_info_care_input',
                fn(JoinClause $joinClause): JoinClause
                    => $joinClause
                        ->whereColumn('care_inputs.id', 'care_input_id')
                        ->where('care_info_id', $careInfoId),
            )
            ->whereNotNull('deleted_at')
            ->whereBetween('deleted_at', ['2024-11-07 00:00:00', '2024-11-08 00:00:00'])
            ->whereIn('care_type_id', $careTypesIds)
            ->where('school_id', $schoolId)
            ->where('date', '2024-10-15')
            ->update(['deleted_at' => null]);
    }
};
