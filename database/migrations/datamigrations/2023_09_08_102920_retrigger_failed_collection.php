<?php

use Cfa\Planner\Application\Jobs\Collections\MigrateLegacyCollection;
use Cfa\Planner\Domain\Collection\MigrationStatus;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $failedCollections = PlannerCollection::query()
            ->where('migration_status', MigrationStatus::FAILED)
            ->get();
        PlannerCollection::query()
            ->whereIn('id', $failedCollections->pluck('id'))
            ->update([
                'migration_status' => MigrationStatus::QUEUED,
            ]);
        $failedCollections->each(function (PlannerCollection $failedCollection): void {
            dispatch(new MigrateLegacyCollection($failedCollection));
        });
    }
};
