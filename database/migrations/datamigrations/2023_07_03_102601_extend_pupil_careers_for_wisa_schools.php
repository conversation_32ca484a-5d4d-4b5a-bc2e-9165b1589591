<?php

use Carbon\Carbon;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $wisaSchools = WisaCareData::query()
            ->select('school_id')
            ->distinct()
            ->where('created_at', '>', Carbon::create(2022, 9, 1))
            ->pluck('school_id');
        $fromDate = Carbon::create(2023, 6, 29, 0, 0, 0);
        Career::withTrashed()
            ->whereIn('school_id', $wisaSchools)
            ->where('enddate', '>', $fromDate)
            ->where('role_id', Role::getRepository()->findRoleByName(RoleName::Pupil)->id)
            ->update(['enddate' => Carbon::create(2023, 7, 15, 6, 6, 6)]);
    }
};
