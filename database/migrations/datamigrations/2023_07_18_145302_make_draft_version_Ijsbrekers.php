<?php

use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Collection\PublisherCollection\Publisher\Publisher;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Record;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up()
    {
        // IJsbrekers inspiratiemap oudste kleuters
        $plannerCollection = PlannerCollection::query()
            ->with([
                'chapters.records.curriculumnodes',
                'chapters.records.allMaterials',
            ])
            ->whereNull('owner_id')
            ->where('uid', '78c2dad8-fbdf-4a6f-9271-29f2d67f537d')
            ->firstOrFail();

        $newName = 'Reken Maar! inspiratiemap oudste kleuters';

        //Duplicate as publisher collection, with a new name
        /** @var PlannerCollection $copyCollection */
        $copyCollection = $plannerCollection->replicate();
        $copyCollection->uid = uuid();
        $copyCollection->published_parent_id = null;
        $copyCollection->published_at = null;
        $copyCollection->name = $newName;
        $copyCollection->saveQuietly();

        $this->createData($plannerCollection, $copyCollection, false);

        //Duplicate as DRAFT publisher collection, with a new name
        $draftPublisherId = Publisher::whereUid(Publisher::VAN_IN_DRAFT)->value('id');

        /** @var PlannerCollection $draftCollection */
        $draftCollection = $plannerCollection->replicate();
        $draftCollection->uid = uuid();
        $draftCollection->published_parent_id = $copyCollection->id;
        $draftCollection->publisher_id = $draftPublisherId;
        $draftCollection->published_at = null;
        $draftCollection->name = $newName . ' (Draft)';
        $draftCollection->saveQuietly();

        $this->createData($plannerCollection, $draftCollection, true);
    }

    private function createData(
        PlannerCollection $originalPlannerCollection,
        PlannerCollection $newCollection,
        bool $isDraft,
    ): void {
        $originalPlannerCollection->chapters->each(function (Chapter $chapter) use ($newCollection, $isDraft): void {
            /** @var Chapter $draftChapter */
            $copyChapter = $chapter->replicate();
            $copyChapter->uid = uuid();
            $copyChapter->published_parent_id = $isDraft ? $chapter->id : null;
            $copyChapter->plannercollection_id = $newCollection->id;
            $copyChapter->setTouchedRelations([]);
            $copyChapter->saveQuietly();

            $chapter->records->each(function (Record $record) use ($copyChapter, $isDraft): void {
                /** @var Record $copyRecord */
                $copyRecord = $record->replicate();
                $copyRecord->uid = uuid();
                $copyRecord->published_parent_id = $isDraft ? $record->id : null;
                $copyRecord->chapter_id = $copyChapter->id;
                $copyRecord->setTouchedRelations([]);
                $copyRecord->saveQuietly();

                $curriculumNodeRecords = $record->curriculumnodes
                    ->pluck('id')
                    ->map(fn(int $curriculumnodeId) => [
                        'record_id' => $copyRecord->id,
                        'curriculumnode_id' => $curriculumnodeId,
                    ]);
                DB::table('curriculumnode_record')->insert($curriculumNodeRecords->all());

                $record->allMaterials->each(function (Material $material) use ($copyRecord, $isDraft): void {
                    /** @var Material $copyMaterial */
                    $copyMaterial = $material->replicate();
                    $copyMaterial->uid = uuid();
                    $copyMaterial->published_parent_id = $isDraft ? $material->id : null;
                    $copyMaterial->record_id = $copyRecord->id;
                    $copyMaterial->saveQuietly();
                });
            });
        });
    }
};
