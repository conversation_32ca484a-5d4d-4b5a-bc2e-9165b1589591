<?php

use Cfa\Care\Domain\CareInfo\Diagnosis\DiagnosisAttachment\DiagnosisAttachment;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        DB::table('care_info_diagnosis')->insert($this->getDeletedDiagnoses());
        DiagnosisAttachment::withTrashed()
            ->whereIn('uid', $this->getDeletedDiagnosisAttachmentUids())
            ->where('deleted_at', '>', '2024-08-01 00:00:00')
            ->update(['deleted_at' => null]);
    }

    /**
     * Retrieved from the 28/07 backup using this query:
     *   SELECT CONCAT("['care_info_id' => ", care_info_id, ", 'diagnosis_id' => ", diagnosis_id, '],')
     *      FROM care_info_diagnosis WHERE care_info_id IN (...);
     * The care info ids were retrieved using the follow query on the PRD database:
     *  SELECT care_infos.id FROM users
     *    JOIN care_infos ON care_infos.pupil_id = users.id
     *    WHERE users.uid IN (
     *      SELECT JSON_EXTRACT(parameters, '$.pupil') FROM activity_logs WHERE route = 'web.common.care-info.save'
     *        AND method = 'POST'
     *        AND id > 125643000 AND id < 134000000 -- 2024-07-24 10:28:34 -> 2024-09-03 13:08:20
     *        AND `data` not like '%diagn%'
     *        AND `data` like '%pupil_status%"value": "new"%**redacted**%'
     *    );
     */
    private function getDeletedDiagnoses(): array
    {
        return [
            ['care_info_id' => 471151, 'diagnosis_id' => 11242],
            ['care_info_id' => 641012, 'diagnosis_id' => 18834],
            ['care_info_id' => 641017, 'diagnosis_id' => 18821],
            ['care_info_id' => 641019, 'diagnosis_id' => 20605],
            ['care_info_id' => 641022, 'diagnosis_id' => 18831],
            ['care_info_id' => 641025, 'diagnosis_id' => 20605],
            ['care_info_id' => 647384, 'diagnosis_id' => 18826],
            ['care_info_id' => 647384, 'diagnosis_id' => 18831],
            ['care_info_id' => 654902, 'diagnosis_id' => 18820],
            ['care_info_id' => 655097, 'diagnosis_id' => 20605],
            ['care_info_id' => 655097, 'diagnosis_id' => 20747],
            ['care_info_id' => 655100, 'diagnosis_id' => 20605],
            ['care_info_id' => 875126, 'diagnosis_id' => 21176],
            ['care_info_id' => 885626, 'diagnosis_id' => 18820],
        ];
    }

    /**
     * Retrieved from the 28/07 backup using this query:
     *  SELECT uid FROM diagnosis_attachments WHERE diagnosis_attachments.deleted_at IS NULL AND care_info_id IN (...);
     * The care info ids were retrieved from the PRD database in the same way as the method above.
     */
    private function getDeletedDiagnosisAttachmentUids(): array
    {
        return [
            '63c8eb28-b5ba-44c0-a3bf-4e012cacd406',
            'cb1fd12b-9d3e-4ebe-9b94-d062a2d5210f',
            'c812c010-1ae7-4d93-8d2c-eaee642f6289',
            '4bfa2364-dff7-4325-91ef-e3c42eeffc94',
            '64801fdc-ac49-4223-98e5-c3ebde741433',
            'c0a1ca17-4bd3-48cf-aa1b-f07f4ba71385',
            '6dd9b5e3-5c78-4b2b-816f-88e7b2d6aaa0',
            'b7fe666c-d421-4faa-b91c-532dd74fd723',
            'b24c42ec-711d-47d7-8edd-7e99e262e735',
            '6ac8477f-3621-4bc3-88a0-9fa6ab27c68e',
            '9d4a566e-6e42-4865-bb4c-bda8658d5a7d',
            '8a22525c-96e4-4635-8da6-05692f3e2fcc',
            '990a3f0f-b061-477e-b25c-2a0d371902fb',
            '145fd29b-964c-4763-9bc5-5c519e1a1385',
            '642d9002-22f4-40b5-a008-ee692a8d6ffa',
            '4c34629a-3252-4f83-845d-d11a5d9d4780',
            '9c2222d9-7d82-473b-8e8c-258dab6e4e62',
            '1392153c-4630-45b0-8ac1-68b324132b05',
            'c1b3a697-dd85-4ff1-b5e9-ea73c5a85418',
            'd07d237d-5b01-4426-9809-22244163ddca',
            'd1440d27-9e25-4d2d-b51d-4c97ddeb3597',
            'd9dacf26-d5fd-41f1-b197-dc95902cde5e',
            '7e6976e1-fae1-4014-a32f-6fdff89fd0bf',
            '358f1a1f-221d-4118-a59f-903b19b92945',
            '889dec17-dc61-474d-8e10-14817c0c068d',
            '860c00d4-9a11-47a0-929a-2981746ac5cb',
            'c585d908-9b77-4844-a190-a4e45c3cf738',
            'b79192bd-5353-4fd0-89a2-56f37c1f1325',
            '42718048-397b-43a3-ac78-e9986f256c92',
            '38f03b81-020d-4f27-ab3b-8a2aa6d93181',
            'c67aac5d-4b79-4889-a018-b2bce2d12cbb',
            'b2838b0d-aed4-4b7c-b2a9-b5f13062cd93',
            'bb067658-208c-4871-96ff-dc3b6bb693ed',
            'cd2e8e61-c775-4cf4-ad23-366737246727',
            'd851c3e3-de39-4e3c-9369-087ad0bea9c5',
            '952205ad-70f5-40f0-ba24-8cc18c1c969d',
            '73544e99-8450-49dd-990d-c521de88d96e',
            '6854779a-ed28-4d2c-8f40-d2aec46aeace',
            '931a9a44-8d15-40bb-9cd4-6e8c4e5cf4fe',
            '60b75875-d242-45e4-9981-46d5654e2d0f',
            '130d56f3-8288-4e07-8987-d4fb20b17d76',
            '9686d1c0-2ed8-4624-8f2b-7e9caa276afb',
            'b32cd7ee-2ab5-4a3a-b00c-ab001c0daab5',
            '19aa248f-54d4-4f77-a509-a89f7a9397f3',
            '08a8b2c0-76b4-4961-8cee-93c305c4e8c1',
            '396bebb2-fcd8-48b2-aee6-f832fbdb6b1d',
            '5237eaf0-c18b-40b9-938f-db06d1c4781d',
            'e03890d8-63e2-43b3-8afe-690e1c0940c0',
            'c6de4218-f1c8-4819-998c-68187b0bc9da',
            '62f354bf-d566-4389-8087-dfbdd6313ca7',
            '191e7b70-d040-4228-9ac0-f314ec6ce973',
            '3882a09c-18fb-4f51-92f3-9ae3d80300c7',
            'fdf71adf-4308-4d2b-b81a-ef923478d26a',
            'f22f52d1-84c3-4bc7-81ba-d97cbaa848ab',
            '792c1038-f292-49a0-90fa-13ef28705165',
            '9ad0f235-aa19-4a16-ae6e-1eabb8ca62b4',
            '9350077e-b437-4684-87a7-46feabfdebc9',
            '9e6f8b4a-27b4-43dc-94a2-cec9fdca714e',
            'd267971f-9a5c-4ab4-9cca-14ba653bf80f',
            '9dcb4eec-21de-4c68-812f-d9cecbc962ca',
            '14f026a7-589e-40cb-997d-01c3ce234145',
            '09f7f135-dfbd-458d-9f1f-948a2ee046a3',
            '1e7c5818-dcf9-49c2-8d34-b77293a08a3d',
            '49f97ccd-6aeb-4825-bc23-ca179e537b40',
            '226d2159-61f0-4efb-83cc-8698486b5595',
            '9c314004-21c5-4bc1-a3ec-064e2ca63f2d',
            'cc93a8f2-c7e0-4c09-a65e-f7ee2511faf2',
            'a5b0d90f-c52c-4e61-8b90-a3629acaa9f1',
            '9db13edc-1458-4934-ab5c-20aaad945a09',
            '7a71d255-bfdd-4eb4-b238-b8b8cdfee9ce',
            '719766e2-30da-44b3-9345-8cbdecd3adab',
            'fe89b1f8-4526-44ea-a27e-980caec154a7',
            'a2e3f284-b33c-4c5f-ae08-5cbdec8e27dd',
            'b792210d-599e-47a0-8b53-acde36130146',
            'ac3fb0cc-d6e3-449d-b378-953daec34561',
            'fdd47c0f-c4a1-4483-979a-ffe4306809ad',
            'f5d3ec0d-da65-42ee-bb0b-add28c53063b',
            '0bd58015-18e2-4658-89d6-868606b368e8',
            '49d54c80-980c-4362-8442-4b74ebe44954',
            'f0f42216-244a-4b66-91dc-893cf9f92998',
            '428964bb-863a-49f2-95e9-fd1d9b2c8af1',
            'db2aba84-bd67-438a-b45c-ad9a73c01dd5',
            '3a064f9a-75e0-45b8-b53b-678678fd812e',
            '07b61394-a49a-4356-930a-761785a58d81',
            'c2599e71-429f-4a20-8382-89a44d0a494c',
            'dd95859f-7826-461e-a904-f99da70f001b',
            '562bfeb1-fe2a-4a74-bb63-6c2d8e82f2bd',
            '2bd0bb05-652c-4bf2-945d-e755863a617d',
            'fd4fb64b-3262-4647-b78b-3d76cb7ba0c0',
            '90bb0d91-61f3-4f28-9ea6-91b7e5ee5dfd',
            '0da545ad-6a15-455b-893a-e508c2a00b72',
            '5e220fff-5748-4b5e-bc65-002db796e5bb',
            '5e6670f9-e0ba-4f49-a9d0-4b21b5d587d2',
            'cbd6bfa4-a439-4bed-b5cb-63e9e318fd9e',
            'b5348de5-d70f-4b1d-9d81-75b498af1251',
            '146e1057-49f3-4ed7-b60a-dad18b3c320b',
            'bcf8eae5-4817-4033-9d53-43909d4b82c4',
            'b1a581d3-5ebb-4bad-b45b-ceec2874ef47',
            'f17f0728-5b3c-4393-ae44-f9f8ae09dced',
            'ef1187f4-f055-4430-8f2e-c60de673b472',
            'def81e9a-92ab-4dc6-9a82-ac48f920e2c9',
            'fba3d6b5-bd6a-4876-8b2f-e56d4e611afd',
            'b6ed9559-eddb-44d5-b455-e7ba417940c5',
            '6c687ad8-e79f-42ec-bcf0-f9be59fb4544',
            '578ea35e-717d-4ede-a003-62527e5d2abb',
            '873df440-980e-4b64-b0c4-54e71f3d4f50',
            '9b65f835-4653-487f-bb71-76b03e75cbad',
            'b98be91d-b5e5-47f3-beab-c67a57151f69',
            'f02285cf-d91c-4e8c-b853-1800522c5df2',
            'a5a16f4d-e515-4772-bd73-51901a65e111',
            '3e69556a-e279-47db-b271-e452e137d187',
            '0510db17-d390-4fdf-a316-8e23c46f6b17',
            'aff19626-238f-4dd6-838e-2625f133d62d',
            '07b18baa-23e6-429f-8fa7-d2d75f16d6d4',
            '40d8d5b3-e997-4532-8b90-4d534f1a09b0',
            '19c33924-7435-40fd-ab29-afde8e58b06a',
            'c9f40e39-08e3-4cb4-80da-b7e98d1fd6c3',
            '821082db-c9f5-4e3a-a737-97d59dec799c',
            '5c9504f2-b1a9-437e-b91b-9209886e8fde',
            'e6cdf8f0-316a-408e-849a-7f337bb60582',
            '58eb6690-2f1c-49dc-9854-5e1dbfa7fd02',
            '5b7c5560-397f-4d87-8492-934a9ea98b24',
            '36ec9f11-20c2-493f-93bf-9ffad15b11cd',
            'c13b3079-30a3-42e9-8b4b-84ec8a8f899e',
            '19a2c3a7-cc47-43c1-b82f-ecdf2118d3ab',
            '81ec4563-73d9-4420-90a8-cc3b4c2d7c9f',
            '0acc48b9-a40e-427c-a169-76fa6f4e02eb',
            '8f3458f5-7455-4256-b292-afd9a9782983',
            'd3a21d6e-5e87-4de1-a210-039f377932e6',
            'c96c03c9-882c-4fe0-a6dc-142e7361d43d',
            '72a5d646-62f2-4c2d-afac-90c6bb4cad1d',
            '6f8fc9e0-8032-4e24-9d13-f7d389d11b92',
            '3e814656-08b6-45e3-becd-38fb01440da9',
            'a93e1600-55e4-43d6-ab12-9ce63f6d3112',
            '21351e95-fcf1-4b47-9163-41d18e9f9e9e',
            '42d271f2-86b3-476e-b565-f50b7f5110c3',
            '9e119a4a-3014-482d-9bca-959b5646c5a6',
        ];
    }
};
