<?php

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Application\Services\Smd\SmdApiService;
use Cfa\Common\Domain\School\School;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use Database\migrations\datacommands\FillHasDutchAsHomeLanguageField;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $partnerNumbers = [
            '1151696',
            '1151456',
            '1151431',
            '1151432',
            '1155809',
            '1211737',
            '1205321',
            '1151448',
        ];
        $smdApiService = app(SmdApiService::class);

        foreach ($partnerNumbers as $partnerNumber) {
            $school = School::query()->where('partner_number', $partnerNumber)->sole();
            CareInfo::where('school_id', $school->id)->update(['wisa_uid' => null]);
            WisaCareData::where('school_id', $school->id)->forceDelete();
            $smdApiService->publishFullBingelSchool($school)->wait();
            Artisan::call(FillHasDutchAsHomeLanguageField::class, ['schoolUid' => $school->uid]);
        }
    }
};
