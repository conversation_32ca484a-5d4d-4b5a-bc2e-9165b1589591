<?php

use Cfa\Common\Domain\School\Group\Group;
use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\Role;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Cfa\Common\Domain\User\SchoolUserAccess\SchoolUserAccess;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    public function up(): void
    {
        $roles = Role::query()
            ->whereIn('role_name_enum', [RoleName::Management->value, RoleName::TeacherForAllClasses->value])
            ->pluck('id');

        $teacherAllClassCareers = Career::query()
            ->selectRaw("role_id, CONCAT(user_id, '-', school_id) as user_school")
            ->whereIn('role_id', $roles)
            ->pluck('user_school')
            ->toArray();

        $relevantSchoolUserAccess = SchoolUserAccess::query()
            ->with(['school', 'user'])
            ->whereIn(DB::raw("CONCAT(user_id, '-', school_id)"), $teacherAllClassCareers)
            ->get();

        $relevantSchoolUserAccess->each(function (SchoolUserAccess $schoolUserAccess) {
            $hasCareDataAccess = DB::table('care_data_access')
                ->where('school_user_access_id', $schoolUserAccess->id)
                ->exists();

            if ($hasCareDataAccess || !$schoolUserAccess->user || !$schoolUserAccess->school) {
                return;
            }

            $groups = Group::getRepository()
                ->getGroupsForUserAndSchool($schoolUserAccess->user, $schoolUserAccess->school);
            DB::table('care_data_access')->insert($groups->map(fn(Group $group): array => [
                'school_user_access_id' => $schoolUserAccess->id,
                'group_id' => $group->id,
            ])->toArray());
        });
    }
};
