<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $views = [
            'actieve_plannerscholen_contactgegevens_2023-2024',
            'evaluatiescholen_contactgegevens_admins_management',
            'evaluatiescholen_contactgegevens_alle_gebruikers',
            'export_pupils_differing_names_between_wisa_bingel',
            'export_pupils_without_care_data',
            'export_wisa_care_data_without_pupils',
            'licenses_by_school',
            'number_of_users_using_ovsg',
            'VerdachteNaamsWijzigingen',
            'zorgscholen_contactgegevens_admins_management',
            'zorgscholen_contactgegevens_alle_gebruikers',
            'zorgscholen_contactgegevens_zorgfiche_bewerkers',
        ];
        foreach ($views as $view) {
            DB::statement("DROP VIEW IF EXISTS `$view`");
        }
    }
};
