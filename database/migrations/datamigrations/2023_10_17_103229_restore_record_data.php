<?php

use App\Constants\FilesystemDisks;
use App\Models\ActivityLog\ActivityLog;
use Carbon\Carbon;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\Record;
use Cfa\Planner\Domain\CollectionV2\Chapter\Record\RecordOverwrite;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    private array $headerMapping;

    private array $fieldsToUpdate = [
        'is_duration_applicable',
        'duration_minutes',
        'order',
        'start_situation',
        'teacher_goals',
        'differentiation',
        'lesson_steps',
        'tips',
        'work_methods',
        'zill',
        'curriculumnodes',
        'material_files',
        'material_physicals',
        'material_links',
        'experience_situation_types',
    ];

    public function up()
    {
        $this->restoreOwnRecordData();
        $this->restoreRecordOverwrites();
    }

    private function restoreRecordOverwrites(): void
    {
        RecordOverwrite::setQueryingEnabled();
        $fromActivityLog = ActivityLog::where('id', 325194165)->first();
        $toActivityLog = ActivityLog::where('id', 327598846)->first();

        ActivityLog::getQuery()
            ->select(['records_v2.id AS record_id', 'activated_planner_collections.id as collection_id'])
            ->distinct()
            ->join(
                'records_v2',
                'records_v2.uid',
                DB::raw('JSON_UNQUOTE(JSON_EXTRACT(parameters, "$.recordv2"))'),
            )
            ->join(
                'activated_planner_collections',
                'activated_planner_collections.uid',
                DB::raw('JSON_UNQUOTE(JSON_EXTRACT(parameters, "$.collectionv2"))'),
            )
            ->where('activity_logs.id', '>=', $fromActivityLog->id)
            ->where('activity_logs.id', '<=', $toActivityLog->id)
            ->where('route', 'v2.api.collections.records.update')
            ->where('method', 'PUT')
            ->where('data', 'not like', '%lesson_steps%')
            ->where('data', 'like', '%experienceSituations%')
            ->whereNull('records_v2.deleted_at')
            ->whereNull('records_v2.activated_planner_collection_id')
            ->get()
            ->each(function (stdClass $result) use ($fromActivityLog, $toActivityLog): void {
                $this->restorePreviousOverwrites(
                    $result->record_id,
                    $result->collection_id,
                    $fromActivityLog->created_at,
                    $toActivityLog->created_at,
                );
            });

        RecordOverwrite::setQueryingDisabled();
    }

    private function restorePreviousOverwrites(int $recordId, int $collectionId, string $from, string $to): void
    {
        RecordOverwrite::query()
            ->where('record_id', $recordId)
            ->where('activated_planner_collection_id', $collectionId)
            ->whereIn('column', $this->fieldsToUpdate)
            ->where('created_at', '>=', $from)
            ->where('created_at', '<=', $to)
            ->where(
                fn(Builder $builder): Builder => $builder
                    ->where('value', 'like', '[]')
                    ->orWhere('value', 'like', 'null')
                    ->orWhereNull('value'),
            )
            ->update(['deleted_at' => Carbon::now()]);

        $idsToRestore = RecordOverwrite::onlyTrashed()
            ->selectRaw('MAX(id) AS id')
            ->where('record_id', $recordId)
            ->where('activated_planner_collection_id', $collectionId)
            ->whereIn('column', $this->fieldsToUpdate)
            ->where('deleted_at', '>=', $from)
            ->where('deleted_at', '<=', $to)
            ->groupBy('column')
            ->pluck('id');

        $columnsWithValues = RecordOverwrite::query()
            ->where('record_id', $recordId)
            ->where('activated_planner_collection_id', $collectionId)
            ->pluck('column');

        RecordOverwrite::onlyTrashed()
            ->whereIn('id', $idsToRestore)
            ->whereNotIn('column', $columnsWithValues)
            ->update(['deleted_at' => null]);
    }

    private function restoreOwnRecordData(): void
    {
        Record::setQueryingEnabled();
        $stream = Storage::disk(FilesystemDisks::TMP)->readStream('migrations/record-data-from-backup.csv');
        $this->headerMapping = array_flip(fgetcsv($stream, null, ';'));
        while (($row = fgetcsv($stream, null, ';')) !== false) {
            $record = Record::find($this->getValue($row, 'id'));
            foreach ($this->fieldsToUpdate as $field) {
                $value = $this->getValue($row, $field);
                if ($this->isEmpty($record->getRawOriginal($field)) && !$this->isEmpty($value)) {
                    $record->setRawAttributes([...$record->getAttributes(), $field => $value]);
                }
            }
            $record->saveQuietly();
        }
        fclose($stream);
        Record::setQueryingDisabled();
    }

    private function getValue(array $row, string $field): ?string
    {
        $rowValue = $row[$this->headerMapping[$field]];

        return $rowValue === 'NULL' ? null : $rowValue;
    }

    private function isEmpty(?string $value): bool
    {
        return in_array($value, ['[]', null]);
    }
};
