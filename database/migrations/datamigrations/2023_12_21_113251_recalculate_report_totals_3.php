<?php

use Cfa\Common\Domain\School\School;
use Cfa\Common\Domain\Schoolyear\Schoolyear;
use Cfa\Evaluation\Application\Services\Report\EvaluationReportJobsService;
use Cfa\Evaluation\Domain\Settings\Report\ReportSettings\ReportSettings;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $schoolIdsWithAveragesEnabled = ReportSettings::distinct()
            ->where('schoolyear_id', Schoolyear::getRepository()->getCurrent()->id)
            ->where('show_synthesis_average', true)
            ->pluck('school_id');

        School::whereIn('id', $schoolIdsWithAveragesEnabled)->each(function (School $school): void {
            app(EvaluationReportJobsService::class)->createForSchool($school);
        });
    }
};
