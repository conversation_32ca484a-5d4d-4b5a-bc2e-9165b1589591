<?php

use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Common\Domain\School\School;
use Cfa\Wisa\Domain\CareData\WisaCareData;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        $schoolId = School::query()
            ->where('school_number', 12319)
            ->first()->id;

        CareInfo::whereSchoolId($schoolId)->update([
            'wisa_uid' => null,
        ]);

        WisaCareData::query()
            ->where(
                [
                    'school_id' => $schoolId,
                ],
            )
            ->forceDelete();
    }
};
