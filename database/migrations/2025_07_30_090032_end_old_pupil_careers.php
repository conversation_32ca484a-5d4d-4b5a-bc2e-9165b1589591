<?php

use Cfa\Common\Domain\User\Career\Career;
use Cfa\Common\Domain\User\Career\Role\RoleName;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        Career::join('roles', 'careers.role_id', 'roles.id')
            ->where('roles.role_name_enum', RoleName::Pupil->value)
            ->where('careers.smd_updated_at', '<', '2025-07-30 00:00:00')
            ->whereNull('enddate')
            ->update(['enddate' => '2025-07-30 00:00:00']);
    }
};
