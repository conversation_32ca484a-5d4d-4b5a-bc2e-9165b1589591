<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('planner_collections_v2', function (Blueprint $table) {
            $table->string('bingel_uid')->nullable()->after('method_identifier');
            $table->string('bingel_dc_uid')->nullable()->after('bingel_uid');
            $table->string('digiboardware_uid')->nullable()->after('bingel_dc_uid');
        });

        Schema::table('chapters_v2', function (Blueprint $table) {
            $table->string('bingel_uid')->nullable()->after('deleted_at');
        });
    }
};
