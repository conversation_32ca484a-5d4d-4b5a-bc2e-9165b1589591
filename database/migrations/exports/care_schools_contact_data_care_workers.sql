select distinct `schools`.`partner_number`        as `school_partner_number`,
                `schools`.`name`                  as `school_name`,
                `users`.`firstname`               as `user_first_name`,
                `users`.`lastname`                as `user_last_name`,
                `users`.`email`                   as `user_email`,
                GROUP_CONCAT(distinct roles.name) as `role_names`,
                MAX(permission_schools.enddate)   as `license_end_date`
from `schools`
         inner join `careers` on `careers`.`school_id` = `schools`.`id`
         inner join `roles` on `careers`.`role_id` = `roles`.`id`
         inner join `users` on `careers`.`user_id` = `users`.`id`
         inner join `permission_schools` on `permission_schools`.`school_id` = `schools`.`id`
         inner join `school_user_access` on `school_user_access`.`user_id` = `users`.`id` and
                                            `school_user_access`.`school_id` = `schools`.`id`
         left join `permission_school_user_access`
                   on `permission_school_user_access`.`school_user_access_id` = `school_user_access`.`id`
where `users`.`deleted_at` is null
  and `careers`.`startdate` <= now()
  and (`careers`.`enddate` >= now() or `careers`.`enddate` is null)
  and `careers`.`deleted_at` is null
  and `schools`.`deleted_at` is null
  and `permission_schools`.`name` = '3'
  and `roles`.`role_name_enum` in (1, 3, 6, 7)
  and `permission_school_user_access`.`name` = 7
  and `permission_school_user_access`.`deleted_at` is null
  and `schools`.`deleted_at` is null
group by `schools`.`partner_number`, `schools`.`name`, `users`.`firstname`, `users`.`lastname`, `users`.`email`
order by `schools`.`partner_number` asc, `users`.`firstname`
