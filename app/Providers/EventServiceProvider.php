<?php

namespace App\Providers;

use App\Listeners\ClearCache;
use App\Listeners\LogExceptionOccurredJobs;
use App\Listeners\LogFailedJobs;
use App\Models\Lambda\LambdaResultUpdated;
use Cfa\Admin\Application\Cms\Listeners\ActivateLicensedCollectionListener;
use Cfa\Admin\Application\Cms\Listeners\ActivateLicensedCollectionsForSchoolTeachersListener;
use Cfa\Admin\Application\Cms\Listeners\Generate2FaSecret;
use Cfa\Admin\Domain\CmsUser\CmsUserCreatingEvent;
use Cfa\Admin\Domain\PlannerCollection\CollectionActivationTriggered;
use Cfa\Admin\Domain\PlannerCollection\SchoolTeachersCollectionActivationTriggered;
use Cfa\Care\Application\Listeners\AddPupilStatusToCareInfo;
use Cfa\Care\Application\Listeners\EvaluateSchoolExportCareJobFailure;
use Cfa\Care\Application\Listeners\LogChangesOnCareInfoSave;
use Cfa\Care\Application\Listeners\LogChangesOnCareLevelSave;
use Cfa\Care\Application\Listeners\LogRelationChangeOnCareInfoSave;
use Cfa\Care\Application\Listeners\SaveHomeLanguageOfCareInfo;
use Cfa\Care\Application\Listeners\SaveHomeLanguageOfWisaCareData;
use Cfa\Care\Application\Listeners\TriggerSchoolExportZipper;
use Cfa\Care\Application\Listeners\UpdateCareExportJobStatus;
use Cfa\Care\Domain\CareInfo\CareInfoSavedEvent;
use Cfa\Care\Domain\CareInfo\CareInfoSavingEvent;
use Cfa\Care\Domain\CareInfo\CareInfoUpdatingEvent;
use Cfa\Care\Domain\CareInfo\CareLevelSavingEvent;
use Cfa\Care\Domain\Export\CareExportJobUpdated;
use Cfa\Common\Application\Listeners\ActivateLicensedCollectionsForTeachersListener;
use Cfa\Common\Application\Listeners\CreateActionLogForUserMfa;
use Cfa\Common\Application\Listeners\CreateSchoolUserAccessOnCareerCreated;
use Cfa\Common\Application\Listeners\FlushFullUserCacheForSchool;
use Cfa\Common\Application\Listeners\FlushFullUserCacheForTwoFactorAuthenticationEvent;
use Cfa\Common\Application\Listeners\FlushFullUserCacheForUserMfa;
use Cfa\Common\Application\Listeners\FlushGroupCacheOnCareInfoSaved;
use Cfa\Common\Application\Listeners\FlushInactivePupilCache;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddDefaultCareThemes;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddDefaultCareTypes;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddDefaultDiagnoses;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddDefaultRedicodis;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddDefaultReportPeriods;
use Cfa\Common\Application\Listeners\PermissionSchoolCreated\AddPredefinedQuotationSystems;
use Cfa\Common\Application\Listeners\TriggerSmdPublish;
use Cfa\Common\Domain\Permission\LicenseProductIdActivationTriggered;
use Cfa\Common\Domain\Permission\PermissionSchoolCreated;
use Cfa\Common\Domain\Permission\PermissionSchoolSaved;
use Cfa\Common\Domain\School\Settings\SchoolSettingsSaved;
use Cfa\Common\Domain\User\Career\CareerCreated;
use Cfa\Common\Domain\User\Career\CareersSaved;
use Cfa\Common\Domain\User\Mfa\UserMfaSaved;
use Cfa\Evaluation\Application\Listeners\AddQuotationSettingsToEvaluationTestOnCreating;
use Cfa\Evaluation\Application\Listeners\FillRepeatingYearOnPredefinedFollowUpSystemComment;
use Cfa\Evaluation\Application\Listeners\FillRepeatingYearOnPredefinedFollowUpSystemScore;
use Cfa\Evaluation\Application\Listeners\TriggerBingelTestResultsCreation;
use Cfa\Evaluation\Domain\EvaluationTest\EvaluationTestCreating;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Comment\PredefinedFollowUpSystemCommentCreatingEvent;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScoreCreatingEvent;
use Cfa\Planner\Application\Listeners\ActivateLicensedCollectionsForProductIdListener;
use Cfa\Planner\Application\Listeners\HandleCollectionUpdated;
use Cfa\Planner\Application\Listeners\NotifySharedCollectionOwner;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionCreated;
use Cfa\Planner\Domain\Collection\UserCollection\UserCollectionDeleted;
use Cfa\Wisa\Domain\CareData\WisaCareDataSavingEvent;
use Illuminate\Database\Events\ModelsPruned;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Queue\Events\JobExceptionOccurred;
use Illuminate\Queue\Events\JobFailed;
use Laravel\Fortify\Events\TwoFactorAuthenticationDisabled;
use Laravel\Fortify\Events\TwoFactorAuthenticationEnabled;
use Override;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        UserCollectionDeleted::class => [NotifySharedCollectionOwner::class],
        UserCollectionCreated::class => [NotifySharedCollectionOwner::class],
        ModelsPruned::class => [ClearCache::class],
        CareerCreated::class => [CreateSchoolUserAccessOnCareerCreated::class],
        CareersSaved::class => [ActivateLicensedCollectionsForTeachersListener::class],
        EvaluationTestCreating::class => [AddQuotationSettingsToEvaluationTestOnCreating::class],
        JobExceptionOccurred::class => [LogExceptionOccurredJobs::class],
        JobFailed::class => [LogFailedJobs::class],
        PermissionSchoolSaved::class => [FlushFullUserCacheForSchool::class],
        PermissionSchoolCreated::class => [
            TriggerSmdPublish::class,
            AddDefaultCareThemes::class,
            AddDefaultCareTypes::class,
            AddDefaultRedicodis::class,
            AddDefaultDiagnoses::class,
            AddDefaultReportPeriods::class,
            AddPredefinedQuotationSystems::class,
        ],
        CareInfoUpdatingEvent::class => [LogChangesOnCareInfoSave::class],
        CareInfoSavingEvent::class => [
            AddPupilStatusToCareInfo::class,
            SaveHomeLanguageOfCareInfo::class,
        ],
        CareInfoSavedEvent::class => [
            FlushInactivePupilCache::class,
            FlushGroupCacheOnCareInfoSaved::class,
        ],
        CareLevelSavingEvent::class => [LogChangesOnCareLevelSave::class],
        UserMfaSaved::class => [FlushFullUserCacheForUserMfa::class, CreateActionLogForUserMfa::class],
        CmsUserCreatingEvent::class => [Generate2FaSecret::class],
        SchoolSettingsSaved::class => [TriggerBingelTestResultsCreation::class],
        LambdaResultUpdated::class => [UpdateCareExportJobStatus::class],
        CareExportJobUpdated::class => [EvaluateSchoolExportCareJobFailure::class, TriggerSchoolExportZipper::class],
        WisaCareDataSavingEvent::class => [SaveHomeLanguageOfWisaCareData::class],
        PredefinedFollowUpSystemScoreCreatingEvent::class => [FillRepeatingYearOnPredefinedFollowUpSystemScore::class],
        PredefinedFollowUpSystemCommentCreatingEvent::class => [
            FillRepeatingYearOnPredefinedFollowUpSystemComment::class,
        ],
        TwoFactorAuthenticationEnabled::class => [FlushFullUserCacheForTwoFactorAuthenticationEvent::class],
        TwoFactorAuthenticationDisabled::class => [FlushFullUserCacheForTwoFactorAuthenticationEvent::class],
        SchoolTeachersCollectionActivationTriggered::class => [
            ActivateLicensedCollectionsForSchoolTeachersListener::class,
        ],
        CollectionActivationTriggered::class => [ActivateLicensedCollectionListener::class],
        LicenseProductIdActivationTriggered::class => [ActivateLicensedCollectionsForProductIdListener::class],
    ];

    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        HandleCollectionUpdated::class,
        LogRelationChangeOnCareInfoSave::class,
    ];

    /**
     * Register any events for your application.
     */
    #[Override]
    public function boot(): void
    {
        parent::boot();
    }
}
