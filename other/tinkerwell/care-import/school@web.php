<?php

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Symfony\Component\Finder\SplFileInfo;

/**
 * brew install qpdf
 * brew install pdfgrep
 */

const APP_DIR = __DIR__ . '/../../../';
const IMPORT_DIRECTORY = '/Users/<USER>/Downloads/OneDrive_1_29-01-2024/handelingsplannen_Johannesschool';
const EXPORT_DIRECTORY = '/Users/<USER>/Downloads/OneDrive_1_29-01-2024/handelingsplannen_Johannesschool/output';

const DEBUG = true;

const DATE_FOR_NOTES = '22/01/2024';

// Boot laravel.
require APP_DIR . 'bootstrap/autoload.php';
$app = require APP_DIR . 'bootstrap/app.php';
$app->make(Kernel::class)->bootstrap();

dump("Start");
/** @var Pupil[] $allPupils */
$allPupils = createFiles(IMPORT_DIRECTORY, EXPORT_DIRECTORY);
debug($allPupils);
$csvData = createCSVData($allPupils);
debug($csvData);
createCSV(EXPORT_DIRECTORY . '/import.csv', $csvData);
dump("Ready");

class Pupil
{
    public function __construct(
        public readonly string $firstName,
        public readonly string $lastName,
        public readonly string $dateOfBirth,
        public readonly int $page,
        public string $path = '',
        public string $careType = '',

    ) {}
}

function debug($data): void
{
    if (DEBUG) {
        dump($data);
    }
}

/**
 * @return Pupil[]
 */
function createFiles(string $importLocation, string $outputLocation): array
{
    $files = collect(File::allFiles($importLocation));
    makeDir($outputLocation);
    $allPupils = [];

    $directories = $files->map(function (SplFileInfo $file) use (
        &$allPupils,
        $importLocation,
        $outputLocation,
    ): void {
        $fileName = $file->getFilename();
        $folder = Str::replace(" ", "", Str::beforeLast($fileName, ".pdf"));
        makeDir($outputLocation);
        $pupils = getPupilsWithPages($importLocation, $file);
        // Split all files.
        $pupilCount = $pupils->count();
        for ($i = 0; $i < $pupilCount; $i++) {
            $allPupils [] = getPupilFile(
                $importLocation,
                $outputLocation,
                $file,
                $pupils->get($i),
                $pupils->get($i + 1),
            );
        }
    });

    return $allPupils;
}

function getPupilFile(
    string $path,
    string $outputPath,
    SplFileInfo $file,
    Pupil $pupil,
    ?Pupil $nextPupil = null
): Pupil {
    $fileName = $file->getFilename();
    $pupil->careType = Str::substr($fileName, 0, -7);
    if(Str::contains($pupil->careType, 'contacten_deel 2')){
        $pupil->careType = 'contacten_deel 2';
    }
    if(!Str::contains($pupil->careType, ['contact', 'klasbespreking', 'mdo', 'observatie', 'premdo', 'individuele'])){
        $pupil->careType = 'handelingsplannen';
    }

    $className = Str::substr($fileName, -7, 3);
    if(!in_array($className[0], ['K', 'L']) && !in_array($className[1], ['1', '2', '3', '4', '5', '6'])  && !in_array($className[2], ['A', 'B', 'C', 'D', 'E', 'F']) ){
        $parts = explode('/', $file->getPath());
        $className = array_pop($parts);
    }

    $pages = '' . $pupil->page;
    if ($nextPupil !== null) {
        $pages .= '-' . $nextPupil->page - 1;
    } else {
        $pages .= '-z';
    }

    $transliterator = Transliterator::createFromRules(
        ":: Any-Latin; :: Latin-ASCII; :: NFD; :: [:Nonspacing Mark:] Remove; :: NFC;",
        Transliterator::FORWARD
    );

    $pupil->path =   $className . '-' . $transliterator->transliterate($pupil->firstName) . ' ' . $transliterator->transliterate($pupil->lastName) . '-' . $pupil->careType . '.pdf';
    $fullPathOutputFile = '"' . $outputPath . '/' . $pupil->path . '"';
    $command = 'qpdf "' . $fileName . '" --pages "' . $fileName . '" ' . $pages . ' -- ' . $fullPathOutputFile;
    chdir(getDirFromFile($file));
    $output = [];
    $resultCode = 0;
    exec($command, $output, $resultCode);
    debug($command);

    return $pupil;
}

function getDirFromFile(SplFileInfo $file): string
{
    return Str::substr($file->getRealPath(), 0, -Str::length($file->getFilename()) - 1);
}

// firstName, lastName, startPage, endPage
function getPupilsWithPages(string $path, SplFileInfo $file): Collection
{
    $fileName = $file->getFilename();
    $searchRegex = '(([\p{L} \'´&-]+)( )+)(\d{2}\-\d{2}\-\d{2,4})';
    $command = 'export LC_ALL=en_US.UTF-8;pdfgrep -n -P "' . $searchRegex . '" "' . $fileName . '"';
    chdir(getDirFromFile($file));
    $output = [];
    $resultCode = 0;
    exec($command, $output, $resultCode);
    debug($command);
    $pupils = collect($output)->reject(fn(string $row): bool => str_contains($row, 'Datum'));

    $pupils = $pupils->map(function (string $row) use ($fileName): ?Pupil {
        $row = explode(':', $row);
        $page = (int)$row[0];
        $pupilParts = preg_split('/(  )+/', $row[1]);
        if (count($pupilParts) < 3) {
            debug($fileName, $pupilParts);

            return null;
        }
        $dateOfBirth = (trim($pupilParts[2]) === '°') ? trim($pupilParts[3]) : trim($pupilParts[2]);
        if (!isValidDateFormat($dateOfBirth)) {
            dump("invalid data: ", $dateOfBirth);

            return null;
        }

        return new Pupil(
            firstName: trim($pupilParts[1]),
            lastName: trim($pupilParts[0]),
            dateOfBirth: $dateOfBirth,
            page: $page,
        );
    });

    /** @var Collection $pupils */
    return $pupils->filter()->values();
}

/**
 * Will validate string if it matches the given format.
 *
 * @param string $date The date string.
 * @param string $format The format, ex.: 'd-m-Y'.
 *
 * @return bool
 */
function isValidDateFormat(string $date, string $format = 'd-m-Y'): bool
{
    $dateTime = DateTime::createFromFormat($format, $date);

    return $dateTime && $dateTime->format($format) === $date;
}


function makeDir(string $path): void
{
    if (!is_dir($path)) {
        if (!mkdir($path) && !is_dir($path)) {
            throw new \RuntimeException('Directory "' . $path . '" was not created');
        }
    }
}

/**
 * @param Pupil[] $pupils
 *
 * @return Pupil[]
 */
function createCSVData(array $pupils): array
{
    $header = [
        'VOORNAAM',
        'FAMILIENAAM',
        'GEBOORTEDATUM',
        'DATUM',
        'BIJLAGE',
        'BESCHRIJVING',
        'ZORGTYPE',
    ];

    $rows = Arr::map($pupils, fn(Pupil $pupil): array => [
        $pupil->firstName,
        $pupil->lastName,
        $pupil->dateOfBirth,
        DATE_FOR_NOTES,
        $pupil->path,
        $pupil->careType,
        "School@web",
    ]);

    return [$header, ...$rows];
}

/**
 * @param string $csvFilePath location where you want the csv.
 * @param array $data Rows data.
 *
 * @return void
 */
function createCSV(string $csvFilePath, array $data): void
{
    $file = fopen($csvFilePath, 'w');
    foreach ($data as $row) {
        fputcsv($file, $row);
    }
    fclose($file);
}
