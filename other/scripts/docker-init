#!/usr/bin/env bash

set -eu

if [ ! -f "./.env.local-overwrites" ]; then
  echo "Create a .env.local-overwrites file (see README.md) before running docker-init." >&2

  exit 1
fi

if [ "$(grep -c 'IGNITION_LOCAL_SITES_PATH' './.env.local-overwrites')" -ne 1 ]; then
  echo "IGNITION_LOCAL_SITES_PATH='$(pwd)'" >> .env.local-overwrites
fi

if [ ! -f "./.env" ]; then
  cp .env.example .env
fi

./other/scripts/sail up fpm redis
./other/scripts/sail exec fpm rm -f /opt/local/bingeltms/bootstrap/cache/*.php
./other/scripts/sail exec fpm mkdir -p /opt/local/bingeltms/storage/framework/views
./other/scripts/sail exec fpm mkdir -p /opt/local/bingeltms/storage/framework/sessions
./other/scripts/sail exec fpm mkdir -p /opt/local/bingeltms/storage/framework/cache
./other/scripts/sail exec fpm mkdir -p /opt/local/bingeltms/storage/html-purifier
./other/scripts/sail exec -u root fpm touch .phpstorm.meta.php
./other/scripts/sail php composer install
./other/scripts/sail php artisan tms:aws:auth
./other/scripts/sail up horizon
./other/scripts/sail exec horizon mkdir -p /opt/local/bingeltms/storage/framework/views
./other/scripts/sail exec horizon mkdir -p /opt/local/bingeltms/storage/framework/sessions
./other/scripts/sail exec horizon mkdir -p /opt/local/bingeltms/storage/framework/cache
./other/scripts/sail exec horizon mkdir -p /opt/local/bingeltms/storage/html-purifier
./other/scripts/sail exec -u root fpm php artisan tms:aws:setup-env
./other/scripts/sail exec -u root fpm chown -R www-data:www-data storage
./other/scripts/sail exec -u root fpm chown -R www-data:www-data /home/<USER>/.npm

./other/scripts/sail npm run production

./other/scripts/sail up mysql

CONTAINER=$(docker compose ps -q mysql)
while [ "$(docker inspect -f "{{.State.Health.Status}}" "$CONTAINER")" != "healthy" ]; do
  echo "Waiting for mysql to start..."
  sleep 5
done

if ./other/scripts/sail php artisan migrate:status|grep -q "Migration table not found"; then
  ./other/scripts/sail php artisan migrate:fresh --seed
fi

./other/scripts/sail up
