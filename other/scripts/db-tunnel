#!/usr/bin/env bash
if [ $# -lt 1 ]; then
    echo "First follow instructions on: https://sanoma-learning.atlassian.net/wiki/spaces/SCHOOL/pages/90721898/Database"
    echo "Usage: db-tunnel [acc, dev, prd] [smd, tms] [write] or db-tunnel [acc, dev, prd] [endpoint]"
    echo "acc gets used as suffix for profile: vnn-bingel-acc"
    echo "Ex: db-tunnel acc smd"
    echo "Will open a read only connection to acc SMD rds database on the given port."
    echo "Ex: db-tunnel prd or db-tunnel prd tms"
    echo "Will open a read only connection to prd Bingel TMS rds database on the given port."
    echo "By default a read-only connection is opened, to open a write connection, add write as last argument."
    exit 1
fi
db_endpoint="";
profile=vnn-bingel-$1
if [[ $1 == "dev" ]]
then
   env='int'
else
   env=$1
fi

if [[ $2 == "smd" ]]
then
   db_name=aurora-smd-$env
   port=9990
elif [[ $2 =~ "amazonaws.com" ]]
then
   db_name=$2
   port=9980
else
   db_name=aurora-bingeltms-$env
   port=9997
fi

if [[ $3 == "write" ]]
then
   endpoint='Endpoint'
   connection='Write connection'
else
   endpoint='ReaderEndpoint'
   connection='Read only connection'
fi

case $env in acc) port=$(( port+1 ));;
prd) port=$(( port+2 ));;
*) ;;
esac

db_endpoint=$(aws rds describe-db-clusters --output text --query "DBClusters[?starts_with(Endpoint, '$db_name')].$endpoint" --profile $profile);
shared_bastion=$(aws ec2 describe-instances --profile $profile --filters "Name=tag:Name,Values=shared-bastion" "Name=instance-state-name,Values=running" --query "Reservations[*].Instances[*].[InstanceId]" --output text);
echo "$connection opened on port: $port to $db_endpoint";
export AWS_REGION=eu-west-1
export AWS_PROFILE=$profile
ssh -N -T -L $port:$db_endpoint:3306 ec2-user@$shared_bastion
