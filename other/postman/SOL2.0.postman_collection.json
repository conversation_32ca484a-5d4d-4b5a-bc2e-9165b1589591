{"variables": [], "info": {"name": "SOL2.0", "_postman_id": "ecb45776-9f80-05e6-5701-1952af43d517", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "Tests", "description": "", "item": [{"name": "Test CloudFront cookies", "request": {"url": "https://d1zb1tsiuhdp5t.cloudfront.net/sol-fl/help/003/help.css", "method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "CloudFront-Policy={{cookies.cloudfront.policy}}; CloudFront-Signature={{cookies.cloudfront.signature}}; CloudFront-Key-Pair-Id={{cookies.cloudfront.keyPairId}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}]}, {"name": "api/users", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = JSON.parse(responseBody);", "if(responseCode.code === 200 && json.length > 0) {", "    postman.setEnvironmentVariable(\"firstUser.id\", json[0].id);", "} else {", "    postman.setEnvironmentVariable(\"firstUser.id\", null);", "}"]}}], "request": {"url": "http://sol2.test/sol/planner/rest/users", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "description": "GET a list of all Users."}, "response": []}, {"name": "api/users/jwt", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["if(responseCode.code === 200 && responseBody.length > 0) {", "    postman.setEnvironmentVariable(\"firstUser.jwtToken\", responseBody);", "} else {", "    postman.setEnvironmentVariable(\"firstUser.jwtToken\", \"\");", "}"]}}], "request": {"url": "http://sol2.test/sol/planner/rest/users/jwt?userId={{firstUser.id}}", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "description": "GET the JWT-token for a User."}, "response": []}, {"name": "api/system/tenant-config", "request": {"url": "http://sol2.test/sol/planner/rest/system/tenant-config", "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firstUser.jwtToken}}", "description": ""}, {"key": "Tenant-Id", "value": "{{solFlTenant.uid}}", "description": ""}, {"key": "X-Application", "value": "bingel", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "GET the Configuration for a Tenant using a User's JWT-token."}, "response": []}, {"name": "api/system/cloudfront/get-cookies", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = JSON.parse(responseBody);", "if(responseCode.code === 200 && json) {", "    postman.setEnvironmentVariable(\"cookies.cloudfront.policy\", json.policy);", "    postman.setEnvironmentVariable(\"cookies.cloudfront.signature\", json.signature);", "    postman.setEnvironmentVariable(\"cookies.cloudfront.keyPairId\", \"APKAI3ZPBIGUCPZUBBGQ\");", "} else {", "    postman.setEnvironmentVariable(\"cookies.cloudfront.policy\", \"\");", "    postman.setEnvironmentVariable(\"cookies.cloudfront.signature\", \"\");", "    postman.setEnvironmentVariable(\"cookies.cloudfront.keyPairId\", \"\");", "}"]}}], "request": {"url": "http://sol2.test/sol/planner/rest/system/cloudfront/get-cookies", "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firstUser.jwtToken}}", "description": ""}, {"key": "Tenant-Id", "value": "{{solFlTenant.uid}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": "GET the CloudFront cookies for a Tenant using a User's JWT-token."}, "response": []}, {"name": "api/system/version", "request": {"url": "http://sol2.test/sol/planner/rest/system/version", "method": "GET", "header": [], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}, {"name": "api/school", "request": {"url": "http://sol2.test/sol/planner/rest/school/c037e01b-2679-4887-a6d7-7498110a497b", "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firstUser.jwtToken}}", "description": ""}, {"key": "Tenant-Id", "value": "{{solFlTenant.uid}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}, {"name": "api/school/{schoolid}/subjects", "request": {"url": "http://sol2.test/sol/planner/rest/school/c037e01b-2679-4887-a6d7-7498110a497b/subjects", "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firstUser.jwtToken}}", "description": ""}, {"key": "Tenant-Id", "value": "{{solFlTenant.uid}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}, {"name": "api/planner/rosters", "request": {"url": "http://sol2.test/sol/planner/rest/planner/rosters?schoolId=c037e01b-2679-4887-a6d7-7498110a497b", "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{firstUser.jwtToken}}", "description": ""}, {"key": "Tenant-Id", "value": "{{solFlTenant.uid}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}]}