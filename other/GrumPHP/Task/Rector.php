<?php

namespace Other\GrumPHP\Task;

use GrumPHP\Collection\FilesCollection;
use GrumPHP\Fixer\Provider\FixableProcessResultProvider;
use GrumPHP\Runner\TaskResult;
use <PERSON>rumP<PERSON>\Runner\TaskResultInterface;
use GrumP<PERSON>\Task\Context\ContextInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Process\Process;

class Rector extends AbstractExternalTaskWithFiles
{
    protected static function getOptionsResolver(): OptionsResolver
    {
        return parent::getOptionsResolver()->setDefault('triggered_by', ['php']);
    }

    protected function runForFiles(ContextInterface $context, FilesCollection $files): TaskResultInterface
    {
        $arguments = $this->processBuilder->createArgumentsForCommand('rector');
        $arguments->add('process');
        $arguments->add('--dry-run');

        $arguments->addFiles($files);

        $process = $this->processBuilder->buildProcess($arguments);
        $process->run();

        if (!$process->isSuccessful()) {
            return FixableProcessResultProvider::provide(
                TaskResult::createFailed($this, $context, $this->formatter->format($process)),
                function () use ($arguments): Process {
                    $arguments->removeElement('--dry-run');

                    return $this->processBuilder->buildProcess($arguments);
                }
            );
        }

        return TaskResult::createPassed($this, $context);
    }
}
