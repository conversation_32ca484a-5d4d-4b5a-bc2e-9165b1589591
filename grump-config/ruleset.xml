<?xml version="1.0"?>
<ruleset name="PHPMD rule set for SOL 2.0"
         xmlns="http://pmd.sf.net/ruleset/1.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
         xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
    <description>
        Custom PHPMD rule set for Bingel TMS.
    </description>

    <rule ref="rulesets/cleancode.xml">
        <exclude name="StaticAccess" />
        <exclude name="IfStatementAssignment" />
    </rule>
    <rule ref="rulesets/codesize.xml" />
    <rule ref="rulesets/controversial.xml" />
    <rule ref="rulesets/design.xml">
        <exclude name="CouplingBetweenObjects" />
    </rule>
    <rule ref="rulesets/naming.xml">
        <exclude name="ShortMethodName" />
        <exclude name="LongVariable" />
        <exclude name="BooleanGetMethodName" />
        <exclude name="LongClassName" />
    </rule>
    <rule ref="rulesets/unusedcode.xml">
        <exclude name="UnusedFormalParameter" />
    </rule>

    <!-- Specific options for previously excluded rules -->
    <rule ref="rulesets/naming.xml/ShortMethodName">
        <priority>1</priority>
        <properties>
            <property name="exceptions" value="up" />
        </properties>
    </rule>
    <rule ref="rulesets/naming.xml/LongVariable">
        <priority>1</priority>
        <properties>
            <property name="maximum" value="48" />
        </properties>
    </rule>
    <!-- No Enum Support -->
    <!-- See: https://github.com/phpmd/phpmd/issues/932 -->
    <!-- See: https://github.com/pdepend/pdepend/issues/556 -->
    <exclude-pattern>cfa/Common/Domain/Notifications/CustomerNotificationType.php</exclude-pattern>
</ruleset>
