apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ingress-cfa.yml
commonAnnotations:
  kubernetes.io/ingress.class: alb
  alb.ingress.kubernetes.io/scheme: internet-facing
  alb.ingress.kubernetes.io/target-type: ip
  alb.ingress.kubernetes.io/tags: Env=$(ENV),Platform=bingeltms,Tenant=fl
  alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
  alb.ingress.kubernetes.io/certificate-arn: $(AWS_CERTIFICATE_ARN)
  alb.ingress.kubernetes.io/ssl-redirect: '443'
  alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-Ext-2018-06
  alb.ingress.kubernetes.io/inbound-cidrs: $(WHITELIST_CFA)
  alb.ingress.kubernetes.io/group.name: bingeltms.$(ENV).main
  alb.ingress.kubernetes.io/load-balancer-name: bingeltms-main-$(ENV)
  alb.ingress.kubernetes.io/load-balancer-attributes: deletion_protection.enabled=false,access_logs.s3.enabled=true,access_logs.s3.bucket=$(AWS_S3_BUCKET_LOGS),access_logs.s3.prefix=alb
  alb.ingress.kubernetes.io/healthcheck-path: /ping
  alb.ingress.kubernetes.io/healthcheck-port: '8081'
