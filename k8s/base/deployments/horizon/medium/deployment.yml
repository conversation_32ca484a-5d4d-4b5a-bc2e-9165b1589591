apiVersion: apps/v1
kind: Deployment
metadata:
  name: tms
spec:
  template:
    spec:
      terminationGracePeriodSeconds: 900
      securityContext:
        runAsNonRoot: true
      containers:
        - name: main
          command: ['docker/run-scripts/horizon.sh']
          resources:
            limits:
              cpu: '2000m'
              memory: '2400Mi'
            requests:
              cpu: '1000m'
              memory: '550Mi'
          livenessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 10
            periodSeconds: 15
            exec:
              command:
                - php
                - artisan
                - horizon:status
          imagePullPolicy: Always
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
