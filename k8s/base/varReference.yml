varReference:
  - kind: ExternalSecret
    path: spec/data/remoteRef/key
  - kind: Deployment
    path: spec/template/spec/containers[]/image
  - kind: Deployment
    path: spec/template/spec/initContainers[]/image
  - kind: Deployment
    path: spec/metadata/labels[]/tags.datadoghq.com\/version
  - kind: Deployment
    path: spec/metadata/labels[]/tags.datadoghq.com\/env
  - kind: Deployment
    path: spec/template/metadata/labels[]/tags.datadoghq.com\/version
  - kind: Deployment
    path: spec/template/metadata/labels[]/tags.datadoghq.com\/env
  - kind: Job
    path: spec/template/spec/containers[]/image
  - kind: Job
    path: spec/template/spec/initContainers[]/image
  - kind: CronJob
    path: spec/jobTemplate/spec/template/spec/containers[]/image
  - kind: CronJob
    path: spec/jobTemplate/spec/template/spec/initContainers[]/image
  - kind: CronJob
    path: spec/jobTemplate/spec/template/metadata/labels[]/tags.datadoghq.com\/version
  - kind: CronJob
    path: spec/jobTemplate/spec/template/metadata/labels[]/tags.datadoghq.com\/env
  - kind: Ingress
    path: metadata/annotations[]/nginx.ingress.kubernetes.io\/whitelist-source-range
  - kind: Ingress
    path: metadata/annotations[]/alb.ingress.kubernetes.io\/certificate-arn
  - kind: Ingress
    path: metadata/annotations[]/alb.ingress.kubernetes.io\/inbound-cidrs
  - kind: Ingress
    path: metadata/annotations[]/alb.ingress.kubernetes.io\/load-balancer-attributes
  - kind: ConfigMap
    path: data
  - kind: ScaledObject
    path: spec/triggers/metadata/url
