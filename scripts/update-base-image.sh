#!/usr/bin/env bash

# This script updates the build image version used on Bitbucket, locally and in production to the latest one built by the Bingel-TMS-builder project.
# The script doesn't take any arguments.
# You can either use this script in Bitbucket by executing the custom 'update-base-image' pipeline (this will create a pull request) or execute it locally from the project dir.

# Catch all errors
set -e -o pipefail

if [ -z $AWS_PRD_ACCESS_KEY_ID ]; then
  IS_LOCAL=1
  AWS_COMMAND="aws --profile vnn-bingel-prd"
else
  IS_LOCAL=0
  AWS_COMMAND="docker run --rm -i -e AWS_ACCESS_KEY_ID=$AWS_PRD_ACCESS_KEY_ID -e AWS_SECRET_ACCESS_KEY=$AWS_PRD_SECRET_ACCESS_KEY -e AWS_DEFAULT_REGION=eu-west-1 amazon/aws-cli"
fi

IMAGE_TAG=$($AWS_COMMAND ecr list-images --repository-name bingeltms-tooling/builder --output text --query "imageIds[?contains(imageTag, 'builder-')]"|cut -d "-" -f2|sort -n|tail -n1)

sed -i'.update-base.bak' "s#\(dkr.ecr.eu-west-1.amazonaws.com.*-\).*#\1$IMAGE_TAG#g" ./bitbucket-pipelines.yml
sed -i'.update-base.bak' "s#\(dkr.ecr.eu-west-1.amazonaws.com.*-\).*#\1$IMAGE_TAG#g" ./docker-compose.yml
rm *.update-base.bak

if [[ "$IS_LOCAL" == 0 ]]; then
  BRANCH="feature/bitbucket/update-base-docker-$(date +%s)"
  git checkout -b "$BRANCH"
  git add ./bitbucket-pipelines.yml ./docker-compose.yml
  git commit -m "other - Update base docker image" --no-verify
  git push origin "$BRANCH" --no-verify

  wget \
    --header "Authorization: Bearer $BITBUCKET_PR_CREATE_AUTH" \
    --header "Content-Type: application/json" \
    --post-data '{
                    "title": "Update base image",
                    "source": {
                      "branch": {
                        "name": "'"$BRANCH"'"
                      }
                    },
                    "close_source_branch": "true"
                  }' \
    "https://api.bitbucket.org/2.0/repositories/sl-technology/bingel-tms/pullrequests" \
    -O -
  fi
