#!/usr/bin/env bash

# Catch all errors
set -e -o pipefail

# Allow empty globs (for *.yml, when there are no changes)
shopt -s nullglob

function increaseVersion() {
  PREVIOUS_VERSION_NUMBER=$1
  DIGIT_COUNT=$2

  # The 10# makes sure base 10 is used instead of using the octal representation.
  printf %0"$DIGIT_COUNT"d $((10#$PREVIOUS_VERSION_NUMBER + 1))
}

function getPreviousVersion() {
  perl -0777 -pe "s/.+?## \[(.+?)\].+?\n## \[.*/\1/gs" CHANGELOG.md
}

function getNewVersion() {
  PREVIOUS_MAJOR_VERSION=$(echo "$PREVIOUS_VERSION"|cut -d "." -f1)
  PREVIOUS_MINOR_VERSION=$(echo "$PREVIOUS_VERSION"|cut -d "." -f2)
  PREVIOUS_PATCH_VERSION=$(echo "$PREVIOUS_VERSION"|cut -d "." -f3)

  MAJOR_VERSION=$(date +%Y)
  if [[ "$PREVIOUS_MAJOR_VERSION" != "$MAJOR_VERSION" ]]; then
    MINOR_VERSION='001'
    PATCH_VERSION='01'
  elif [[ "$RELEASE_TYPE" == "minor" ]]; then
    MINOR_VERSION=$(increaseVersion "$PREVIOUS_MINOR_VERSION" 3)
    PATCH_VERSION='01'
  else
    MINOR_VERSION="$PREVIOUS_MINOR_VERSION"
    PATCH_VERSION=$(increaseVersion "$PREVIOUS_PATCH_VERSION" 2)
  fi

  echo "$MAJOR_VERSION"."$MINOR_VERSION"."$PATCH_VERSION"
}

function getUnreleasedChangelog() {
  FILES=(./changelogs/unreleased/*.yml)
  if [ ${#FILES[@]} -eq 0 ]; then
    echo "No changes"
    exit 0
  fi

  declare -A CATEGORIES
  for file in "${FILES[@]}"
  do
    TITLE=$(awk "/^title: /{flag=1}/^type: /{flag=0}flag" "$file"|sed 's/^title: //'|xargs -0)
    TYPE=$(grep "^type: " "$file"|sed 's/^type: //'|xargs -0)
    FEATURE_FLAG=$(grep "^feature-flag: " "$file"|sed 's/^feature-flag: //'|xargs -0)
    TICKET=$(grep "^ticket: " "$file"|sed 's/^ticket: //'|xargs -0)
    if [ "${TICKET}" != "false" ]; then
      TITLE="${TICKET} ${TITLE}"
    fi
    if [ "${FEATURE_FLAG}" == "true" ]; then
      TITLE="(FEATURE FLAG) ${TITLE}"
    fi
    CATEGORIES["$TYPE"]+="- ${TITLE^}\n"
  done

  for TYPE in "${!CATEGORIES[@]}"; do
    echo -e "### ${TYPE^}\n"
    echo -e "${CATEGORIES[$TYPE]}"
  done
}

function updateChangelog() {
  NEW_RELEASE_TEMPLATE="---

## [$NEW_VERSION] - $(date +%Y-%m-%d)\n\n$(getUnreleasedChangelog)"

  # Replace all newline characters with \n so we have a single line replacement for sed
  NEW_RELEASE_TEMPLATE=${NEW_RELEASE_TEMPLATE//$'\n'/\\n}
  # Escape & characters, because they have a special meaning in sed
  NEW_RELEASE_TEMPLATE=${NEW_RELEASE_TEMPLATE//&/\\&}
  # Escape / characters, because they have a special meaning in sed
  NEW_RELEASE_TEMPLATE=${NEW_RELEASE_TEMPLATE//\//\\/}

  sed -i "s/---/${NEW_RELEASE_TEMPLATE}/g" CHANGELOG.md
}

if [[ "$BITBUCKET_BRANCH" == "master" ]]; then
  RELEASE_TYPE="minor"
else
  RELEASE_TYPE="patch"
fi

PREVIOUS_VERSION=$(getPreviousVersion)
NEW_VERSION=$(getNewVersion "$RELEASE_TYPE")

updateChangelog

BRANCH="bitbucket/release/$NEW_VERSION"
git checkout -b "$BRANCH"
if compgen -G "./changelogs/unreleased/*.yml" > /dev/null; then
  rm ./changelogs/unreleased/*.yml
fi
git add .
git commit -m "release - $NEW_VERSION" --no-verify
git push origin "$BRANCH" -f --no-verify
git tag "$NEW_VERSION" -f
git push origin "$NEW_VERSION" -f --no-verify

wget \
  --header "Authorization: Bearer $BITBUCKET_PR_CREATE_AUTH" \
  --header "Content-Type: application/json" \
  --post-data '{
                  "title": "Release '"$NEW_VERSION"'",
                  "source": {
                    "branch": {
                      "name": "'"$BRANCH"'"
                    }
                  },
                  "destination": {
                    "branch": {
                      "name": "'"$BITBUCKET_BRANCH"'"
                    }
                  },
                  "close_source_branch": "true"
                }' \
  "https://api.bitbucket.org/2.0/repositories/sl-technology/bingel-tms/pullrequests" \
  -O -
