#!/usr/bin/env bash

# Source common-steps to be able to run reusable steps without creating duplicate code
. ${0%/*}/common-steps.sh

# Display company banner
banner

# Pause the build for 2 seconds
sleep 2

# Display Team banner
team_banner

# Pause the build for 5 seconds
sleep 5

# Catch all errors
set -e -o pipefail

TESTSUITE=$1
echo -e "\n${GREEN}Executing testsuite $TESTSUITE${NC}"

# Check for Pull Request
check-pull-request

cloudfront-key

create-environment

install-composer-dependencies

# Prepare test PRD
mkdir -p test-reports
./other/scripts/phpunit-init
export DB_CONNECTION=mysql_test
export APP_ENV=testing
export DD_TRACE_CLI_ENABLED=true
export DD_LOGS_INJECTION=true

# Copy PHP settingss
copy-php-settings

# Increase PHP memory limit
set-php-memory-limit

if [[ ${SKIP_TESTS} == true ]]; then
  echo "Skipping tests!"
else
  php artisan test \
    --log-junit test-reports/phpunit-$TESTSUITE.xml \
    --testsuite $TESTSUITE
fi

cat test-reports/phpunit-$TESTSUITE.xml|grep -v "<testcase"|grep -v "::"|sed 's/      \s*<\/testsuite>/      <\/testsuite>/g'|uniq > test-reports/phpunit-$TESTSUITE-fixed.xml
rm test-reports/phpunit-$TESTSUITE.xml
