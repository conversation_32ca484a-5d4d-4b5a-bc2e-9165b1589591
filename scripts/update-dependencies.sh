#!/usr/bin/env bash

# Catch all errors
set -e -o pipefail

npx npm-check-updates --target minor -u --cwd nova-components/Impersonate
npx npm-check-updates --target minor -u --cwd docker/lambda/zipservice

npm update
(cd nova-components/Impersonate && npm update)
(cd docker/lambda/zipservice && npm update)

composer config http-basic.nova.laravel.com "${NOVA_USERNAME}" "${NOVA_LICENSE_KEY}"
COMPOSER_ALLOW_SUPERUSER=1 ./composer update --ignore-platform-reqs
COMPOSER_ALLOW_SUPERUSER=1 ./composer bump
./composer self-update

# Test if the build still works
npm run production -- --base=__ASSET_URL__


BRANCH="feature/bitbucket/update-dependencies-$(date +%s)"
git checkout -b "$BRANCH"
git add .
git commit -m "other - Update dependencies" --no-verify
git push origin "$BRANCH" --no-verify

wget \
  --header "Authorization: Bearer $BITBUCKET_PR_CREATE_AUTH" \
  --header "Content-Type: application/json" \
  --post-data '{
                  "title": "Update dependencies",
                  "source": {
                    "branch": {
                      "name": "'"$BRANCH"'"
                    }
                  },
                  "close_source_branch": "true"
                }' \
  "https://api.bitbucket.org/2.0/repositories/sl-technology/bingel-tms/pullrequests" \
  -O -
