<?php

use Cfa\Common\Domain\School\EducationalNetwork\EducationalNetwork;
use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemType;
use Cfa\Planner\Domain\CurriculumNode\CurriculumType;

return [
    FollowUpSystemType::Zill->value => [
        'educationalNetwork' => EducationalNetwork::VVKBAO_UID,
        'curriculumType' => CurriculumType::Zill->value,
    ],
    FollowUpSystemType::Ovsg->value => [
        'educationalNetwork' => EducationalNetwork::OVSG_UID,
        'curriculumType' => CurriculumType::Default->value,
    ],
    FollowUpSystemType::OvsgGoalbook->value => [
        'educationalNetwork' => EducationalNetwork::OVSG_UID,
        'curriculumType' => CurriculumType::Goalbook->value,
    ],
    FollowUpSystemType::OvsgPreschoolGoalbook->value => [
        'educationalNetwork' => EducationalNetwork::OVSG_UID,
        'curriculumType' => CurriculumType::PreschoolGoalbook->value,
    ],
    FollowUpSystemType::OvsgLeerLokaal->value => [
        'educationalNetwork' => EducationalNetwork::OVSG_UID,
        'curriculumType' => CurriculumType::LeerLokaal->value,
    ],
    FollowUpSystemType::OvsgLeerLokaalObservationGoals->value => [
        'educationalNetwork' => EducationalNetwork::OVSG_UID,
        'curriculumType' => CurriculumType::LeerLokaal->value,
    ],
    FollowUpSystemType::Go->value => [
        'educationalNetwork' => EducationalNetwork::GO_UID,
        'curriculumType' => CurriculumType::Default->value,
    ],
];
