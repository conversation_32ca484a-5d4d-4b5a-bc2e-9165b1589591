<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;

// Tabs that should be displayed when editing a follow up system in TMS, this can be different per test audience.
return [
    TestAudience::LoJ1->value => [
        FollowUpSystemSubType::Default->value,
        FollowUpSystemSubType::SeparateWords->value,
        FollowUpSystemSubType::WordsInSentence->value,
        FollowUpSystemSubType::Sentences->value,
    ],
];
