<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;

return [
    'aftrekken tot 1 000' => FollowUpSystemSubType::SubtractionUpToThousand->value,
    'aftrekken tot 5' => FollowUpSystemSubType::SubtractionUpToFive->value,
    'aftrekken tot 10' => FollowUpSystemSubType::SubtractionUpToTen->value,
    'aftrekken tot 20' => FollowUpSystemSubType::SubtractionUpToTwenty->value,
    'aftrekken tot 100 000' => FollowUpSystemSubType::SubtractionUpToHundredThousand->value,
    'aftrekken tot 100' => FollowUpSystemSubType::SubtractionUpToHundred->value,
    'deeltafels' => FollowUpSystemSubType::DivisionTables->value,
    'maaltafels' => FollowUpSystemSubType::MultiplicationTables->value,
    'min tot 100' => [
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => FollowUpSystemSubType::MinusUpToHundred->value,
            TestMoment::Middle->value => FollowUpSystemSubType::MinusUpToHundred->value,
            TestMoment::End->value => FollowUpSystemSubType::MinusUpToHundred->value,
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => FollowUpSystemSubType::SubtractionUpToHundred->value,
            TestMoment::Middle->value => FollowUpSystemSubType::SubtractionUpToHundred->value,
            TestMoment::End->value => FollowUpSystemSubType::SubtractionUpToHundred->value,
        ],
    ],
    'optellen tot 1 000' => FollowUpSystemSubType::AdditionUpToThousand->value,
    'optellen tot 5' => FollowUpSystemSubType::AdditionUpToFive->value,
    'optellen tot 10' => FollowUpSystemSubType::AdditionUpToTen->value,
    'optellen tot 20' => FollowUpSystemSubType::AdditionUpToTwenty->value,
    'optellen tot 100 000' => FollowUpSystemSubType::AdditionUpToHundredThousand->value,
    'optellen tot 100' => FollowUpSystemSubType::AdditionUpToHundred->value,
    'plus tot 100' => [
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => FollowUpSystemSubType::PlusUpToHundred->value,
            TestMoment::Middle->value => FollowUpSystemSubType::PlusUpToHundred->value,
            TestMoment::End->value => FollowUpSystemSubType::PlusUpToHundred->value,
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => FollowUpSystemSubType::AdditionUpToHundred->value,
            TestMoment::Middle->value => FollowUpSystemSubType::AdditionUpToHundred->value,
            TestMoment::End->value => FollowUpSystemSubType::AdditionUpToHundred->value,
        ],
    ],
    'wiskunde' => FollowUpSystemSubType::Default->value,
];
