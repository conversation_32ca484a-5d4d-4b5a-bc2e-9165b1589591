<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;

return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 3,
                16 => 3,
                17 => 5,
                18 => 5,
                19 => 7,
                20 => 10,
                21 => 10,
                22 => 10,
                23 => 15,
                24 => 15,
                25 => 20,
                26 => 20,
                27 => 25,
                28 => 30,
                29 => 35,
                30 => 40,
                31 => 45,
                32 => 50,
                33 => 55,
                34 => 65,
                35 => 70,
                36 => 75,
                37 => 80,
                38 => 90,
                39 => 93,
                40 => 97,
                41 => 99,
                42 => 99,
                43 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 3,
                24 => 3,
                25 => 3,
                26 => 5,
                27 => 5,
                28 => 7,
                29 => 7,
                30 => 10,
                31 => 10,
                32 => 15,
                33 => 20,
                34 => 20,
                35 => 25,
                36 => 30,
                37 => 35,
                38 => 40,
                39 => 45,
                40 => 55,
                41 => 60,
                42 => 65,
                43 => 75,
                44 => 80,
                45 => 85,
                46 => 90,
                47 => 95,
                48 => 97,
                49 => 99,
                50 => 99,
                51 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 3,
                12 => 3,
                13 => 5,
                14 => 5,
                15 => 7,
                16 => 10,
                17 => 10,
                18 => 10,
                19 => 15,
                20 => 15,
                21 => 20,
                22 => 20,
                23 => 25,
                24 => 25,
                25 => 30,
                26 => 30,
                27 => 35,
                28 => 35,
                29 => 40,
                30 => 40,
                31 => 45,
                32 => 50,
                33 => 50,
                34 => 55,
                35 => 55,
                36 => 60,
                37 => 65,
                38 => 65,
                39 => 70,
                40 => 75,
                41 => 75,
                42 => 80,
                43 => 80,
                44 => 80,
                45 => 85,
                46 => 85,
                47 => 90,
                48 => 93,
                49 => 93,
                50 => 95,
                51 => 97,
                52 => 97,
                53 => 99,
                54 => 99,
                55 => 99,
                56 => 99,
                57 => 99,
                58 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 3,
                17 => 3,
                18 => 3,
                19 => 5,
                20 => 5,
                21 => 7,
                22 => 7,
                23 => 10,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 15,
                28 => 20,
                29 => 20,
                30 => 25,
                31 => 25,
                32 => 30,
                33 => 35,
                34 => 35,
                35 => 40,
                36 => 40,
                37 => 45,
                38 => 50,
                39 => 50,
                40 => 55,
                41 => 60,
                42 => 60,
                43 => 65,
                44 => 70,
                45 => 75,
                46 => 75,
                47 => 80,
                48 => 85,
                49 => 85,
                50 => 90,
                51 => 93,
                52 => 95,
                53 => 97,
                54 => 99,
                55 => 99,
                56 => 99,
                57 => 99,
                58 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 1,
                25 => 3,
                26 => 3,
                27 => 3,
                28 => 5,
                29 => 5,
                30 => 5,
                31 => 7,
                32 => 10,
                33 => 10,
                34 => 10,
                35 => 15,
                36 => 15,
                37 => 20,
                38 => 20,
                39 => 25,
                40 => 30,
                41 => 30,
                42 => 35,
                43 => 40,
                44 => 45,
                45 => 50,
                46 => 55,
                47 => 60,
                48 => 65,
                49 => 70,
                50 => 80,
                51 => 85,
                52 => 90,
                53 => 95,
                54 => 97,
                55 => 99,
                56 => 99,
                57 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 5,
                24 => 7,
                25 => 7,
                26 => 10,
                27 => 10,
                28 => 10,
                29 => 15,
                30 => 15,
                31 => 20,
                32 => 20,
                33 => 20,
                34 => 25,
                35 => 30,
                36 => 30,
                37 => 35,
                38 => 35,
                39 => 40,
                40 => 45,
                41 => 45,
                42 => 50,
                43 => 55,
                44 => 55,
                45 => 60,
                46 => 65,
                47 => 65,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 80,
                53 => 85,
                54 => 90,
                55 => 93,
                56 => 95,
                57 => 95,
                58 => 97,
                59 => 97,
                60 => 99,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 3,
                23 => 3,
                24 => 3,
                25 => 5,
                26 => 7,
                27 => 7,
                28 => 7,
                29 => 10,
                30 => 10,
                31 => 10,
                32 => 15,
                33 => 15,
                34 => 20,
                35 => 20,
                36 => 25,
                37 => 25,
                38 => 30,
                39 => 30,
                40 => 35,
                41 => 40,
                42 => 40,
                43 => 45,
                44 => 50,
                45 => 55,
                46 => 55,
                47 => 60,
                48 => 65,
                49 => 70,
                50 => 70,
                51 => 75,
                52 => 80,
                53 => 85,
                54 => 85,
                55 => 90,
                56 => 93,
                57 => 95,
                58 => 97,
                59 => 97,
                60 => 97,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 0,
                18 => 0,
                19 => 0,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 3,
                25 => 3,
                26 => 5,
                27 => 7,
                28 => 7,
                29 => 10,
                30 => 10,
                31 => 15,
                32 => 15,
                33 => 20,
                34 => 25,
                35 => 25,
                36 => 30,
                37 => 30,
                38 => 35,
                39 => 40,
                40 => 40,
                41 => 45,
                42 => 45,
                43 => 50,
                44 => 55,
                45 => 60,
                46 => 65,
                47 => 65,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 85,
                53 => 85,
                54 => 90,
                55 => 93,
                56 => 95,
                57 => 95,
                58 => 97,
                59 => 99,
                60 => 99,
                61 => 99,
                62 => 99,
                63 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 3,
                15 => 3,
                16 => 3,
                17 => 5,
                18 => 7,
                19 => 7,
                20 => 10,
                21 => 10,
                22 => 10,
                23 => 15,
                24 => 20,
                25 => 20,
                26 => 25,
                27 => 25,
                28 => 30,
                29 => 35,
                30 => 40,
                31 => 40,
                32 => 45,
                33 => 50,
                34 => 55,
                35 => 60,
                36 => 60,
                37 => 65,
                38 => 70,
                39 => 70,
                40 => 75,
                41 => 80,
                42 => 80,
                43 => 80,
                44 => 85,
                45 => 85,
                46 => 90,
                47 => 93,
                48 => 95,
                49 => 95,
                50 => 97,
                51 => 99,
                52 => 99,
                53 => 99,
                54 => 99,
                55 => 99,
                56 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 3,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 7,
                24 => 10,
                25 => 10,
                26 => 10,
                27 => 15,
                28 => 20,
                29 => 20,
                30 => 25,
                31 => 30,
                32 => 30,
                33 => 35,
                34 => 40,
                35 => 45,
                36 => 45,
                37 => 50,
                38 => 55,
                39 => 60,
                40 => 60,
                41 => 65,
                42 => 70,
                43 => 75,
                44 => 75,
                45 => 80,
                46 => 85,
                47 => 85,
                48 => 90,
                49 => 93,
                50 => 95,
                51 => 97,
                52 => 97,
                53 => 99,
                54 => 99,
                55 => 99,
                56 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 3,
                22 => 3,
                23 => 3,
                24 => 5,
                25 => 5,
                26 => 7,
                27 => 7,
                28 => 10,
                29 => 10,
                30 => 15,
                31 => 15,
                32 => 20,
                33 => 25,
                34 => 25,
                35 => 30,
                36 => 30,
                37 => 35,
                38 => 40,
                39 => 45,
                40 => 45,
                41 => 50,
                42 => 50,
                43 => 55,
                44 => 60,
                45 => 60,
                46 => 65,
                47 => 70,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 80,
                53 => 85,
                54 => 85,
                55 => 90,
                56 => 93,
                57 => 93,
                58 => 95,
                59 => 95,
                60 => 97,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
                65 => 99,
                66 => 99,
                67 => 99,
                68 => 99,
                69 => 99,
                70 => 99,
                71 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 3,
                19 => 3,
                20 => 3,
                21 => 5,
                22 => 5,
                23 => 7,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 10,
                28 => 15,
                29 => 15,
                30 => 20,
                31 => 20,
                32 => 25,
                33 => 25,
                34 => 25,
                35 => 30,
                36 => 35,
                37 => 35,
                38 => 40,
                39 => 40,
                40 => 45,
                41 => 50,
                42 => 50,
                43 => 55,
                44 => 55,
                45 => 60,
                46 => 65,
                47 => 70,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 85,
                53 => 85,
                54 => 90,
                55 => 93,
                56 => 95,
                57 => 95,
                58 => 97,
                59 => 99,
                60 => 99,
                61 => 99,
                62 => 99,
                63 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 3,
                22 => 3,
                23 => 5,
                24 => 5,
                25 => 5,
                26 => 7,
                27 => 7,
                28 => 10,
                29 => 10,
                30 => 10,
                31 => 15,
                32 => 15,
                33 => 20,
                34 => 20,
                35 => 25,
                36 => 25,
                37 => 30,
                38 => 30,
                39 => 35,
                40 => 40,
                41 => 40,
                42 => 45,
                43 => 50,
                44 => 50,
                45 => 55,
                46 => 60,
                47 => 60,
                48 => 65,
                49 => 70,
                50 => 75,
                51 => 75,
                52 => 80,
                53 => 85,
                54 => 85,
                55 => 90,
                56 => 93,
                57 => 95,
                58 => 97,
                59 => 97,
                60 => 99,
                61 => 99,
                62 => 99,
                63 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 3,
                16 => 3,
                17 => 5,
                18 => 5,
                19 => 7,
                20 => 7,
                21 => 10,
                22 => 10,
                23 => 15,
                24 => 15,
                25 => 20,
                26 => 20,
                27 => 20,
                28 => 25,
                29 => 25,
                30 => 30,
                31 => 30,
                32 => 35,
                33 => 40,
                34 => 40,
                35 => 45,
                36 => 45,
                37 => 50,
                38 => 55,
                39 => 55,
                40 => 60,
                41 => 65,
                42 => 65,
                43 => 70,
                44 => 70,
                45 => 75,
                46 => 80,
                47 => 80,
                48 => 85,
                49 => 85,
                50 => 90,
                51 => 93,
                52 => 95,
                53 => 95,
                54 => 97,
                55 => 97,
                56 => 99,
                57 => 99,
                58 => 99,
                59 => 99,
                60 => 99,
                61 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 3,
                17 => 3,
                18 => 3,
                19 => 5,
                20 => 5,
                21 => 5,
                22 => 7,
                23 => 10,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 15,
                28 => 20,
                29 => 20,
                30 => 25,
                31 => 25,
                32 => 30,
                33 => 30,
                34 => 35,
                35 => 35,
                36 => 40,
                37 => 40,
                38 => 45,
                39 => 45,
                40 => 50,
                41 => 55,
                42 => 55,
                43 => 60,
                44 => 60,
                45 => 65,
                46 => 65,
                47 => 70,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 80,
                53 => 85,
                54 => 85,
                55 => 90,
                56 => 93,
                57 => 95,
                58 => 95,
                59 => 97,
                60 => 97,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
                65 => 99,
                66 => 99,
                67 => 99,
                68 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 7,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 10,
                28 => 15,
                29 => 20,
                30 => 20,
                31 => 20,
                32 => 25,
                33 => 25,
                34 => 30,
                35 => 30,
                36 => 35,
                37 => 35,
                38 => 40,
                39 => 45,
                40 => 45,
                41 => 50,
                42 => 55,
                43 => 55,
                44 => 60,
                45 => 60,
                46 => 65,
                47 => 70,
                48 => 70,
                49 => 75,
                50 => 75,
                51 => 80,
                52 => 80,
                53 => 85,
                54 => 85,
                55 => 90,
                56 => 93,
                57 => 93,
                58 => 95,
                59 => 97,
                60 => 97,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
                65 => 99,
                66 => 99,
                67 => 99,
                68 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 7,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 15,
                28 => 15,
                29 => 20,
                30 => 20,
                31 => 25,
                32 => 25,
                33 => 30,
                34 => 30,
                35 => 35,
                36 => 40,
                37 => 45,
                38 => 50,
                39 => 50,
                40 => 55,
                41 => 55,
                42 => 60,
                43 => 65,
                44 => 65,
                45 => 70,
                46 => 75,
                47 => 75,
                48 => 80,
                49 => 80,
                50 => 85,
                51 => 85,
                52 => 85,
                53 => 90,
                54 => 93,
                55 => 95,
                56 => 95,
                57 => 97,
                58 => 97,
                59 => 99,
                60 => 99,
                61 => 99,
                62 => 99,
                63 => 99,
                64 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToThousand->value => [
        TestAudience::LoJ3->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 15,
                10 => 25,
                11 => 30,
                12 => 40,
                13 => 50,
                14 => 55,
                15 => 70,
                16 => 80,
                17 => 90,
                18 => 95,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 10,
                9 => 10,
                10 => 20,
                11 => 25,
                12 => 35,
                13 => 45,
                14 => 55,
                15 => 65,
                16 => 75,
                17 => 85,
                18 => 90,
                19 => 97,
                20 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 5,
                9 => 7,
                10 => 10,
                11 => 15,
                12 => 25,
                13 => 30,
                14 => 40,
                15 => 55,
                16 => 65,
                17 => 75,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 7,
                10 => 10,
                11 => 15,
                12 => 20,
                13 => 30,
                14 => 40,
                15 => 50,
                16 => 65,
                17 => 75,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 7,
                11 => 15,
                12 => 20,
                13 => 25,
                14 => 35,
                15 => 50,
                16 => 60,
                17 => 75,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 3,
                10 => 5,
                11 => 10,
                12 => 15,
                13 => 25,
                14 => 30,
                15 => 45,
                16 => 55,
                17 => 70,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 7,
                12 => 10,
                13 => 15,
                14 => 20,
                15 => 30,
                16 => 40,
                17 => 55,
                18 => 75,
                19 => 90,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 10,
                13 => 15,
                14 => 25,
                15 => 30,
                16 => 45,
                17 => 60,
                18 => 75,
                19 => 90,
                20 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 10,
                13 => 15,
                14 => 20,
                15 => 25,
                16 => 40,
                17 => 55,
                18 => 75,
                19 => 90,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 10,
                11 => 15,
                12 => 25,
                13 => 35,
                14 => 45,
                15 => 60,
                16 => 75,
                17 => 85,
                18 => 90,
                19 => 97,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToTen->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 5,
                5 => 7,
                6 => 10,
                7 => 15,
                8 => 25,
                9 => 45,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 3,
                5 => 5,
                6 => 7,
                7 => 10,
                8 => 15,
                9 => 30,
                10 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 10,
                9 => 20,
                10 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToHundred->value => [
        TestAudience::LoJ2->value => [
            TestMoment::Middle->value => [
                0 => 1,
                1 => 3,
                2 => 7,
                3 => 7,
                4 => 15,
                5 => 25,
                6 => 30,
                7 => 40,
                8 => 45,
                9 => 55,
                10 => 65,
                11 => 70,
                12 => 75,
                13 => 80,
                14 => 85,
                15 => 93,
                16 => 97,
                17 => 97,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 1,
                1 => 3,
                2 => 3,
                3 => 5,
                4 => 7,
                5 => 10,
                6 => 15,
                7 => 20,
                8 => 20,
                9 => 30,
                10 => 35,
                11 => 45,
                12 => 50,
                13 => 60,
                14 => 65,
                15 => 75,
                16 => 85,
                17 => 85,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 5,
                5 => 10,
                6 => 10,
                7 => 15,
                8 => 20,
                9 => 25,
                10 => 35,
                11 => 40,
                12 => 50,
                13 => 55,
                14 => 65,
                15 => 70,
                16 => 80,
                17 => 85,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 3,
                6 => 3,
                7 => 5,
                8 => 7,
                9 => 10,
                10 => 15,
                11 => 25,
                12 => 30,
                13 => 35,
                14 => 45,
                15 => 55,
                16 => 65,
                17 => 75,
                18 => 80,
                19 => 90,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 5,
                8 => 7,
                9 => 10,
                10 => 15,
                11 => 20,
                12 => 25,
                13 => 30,
                14 => 40,
                15 => 50,
                16 => 60,
                17 => 65,
                18 => 75,
                19 => 85,
                20 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 7,
                11 => 10,
                12 => 15,
                13 => 25,
                14 => 30,
                15 => 40,
                16 => 50,
                17 => 60,
                18 => 70,
                19 => 80,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToHundredThousand->value => [
        TestAudience::LoJ4->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 20,
                10 => 25,
                11 => 35,
                12 => 45,
                13 => 55,
                14 => 65,
                15 => 75,
                16 => 85,
                17 => 93,
                18 => 97,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 7,
                7 => 15,
                8 => 20,
                9 => 40,
                10 => 50,
                11 => 60,
                12 => 75,
                13 => 80,
                14 => 85,
                15 => 93,
                16 => 95,
                17 => 97,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 7,
                8 => 15,
                9 => 25,
                10 => 40,
                11 => 50,
                12 => 65,
                13 => 75,
                14 => 80,
                15 => 85,
                16 => 93,
                17 => 95,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 7,
                9 => 10,
                10 => 15,
                11 => 25,
                12 => 35,
                13 => 45,
                14 => 55,
                15 => 70,
                16 => 85,
                17 => 95,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 7,
                11 => 10,
                12 => 15,
                13 => 25,
                14 => 35,
                15 => 45,
                16 => 55,
                17 => 70,
                18 => 85,
                19 => 97,
                20 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 10,
                13 => 20,
                14 => 25,
                15 => 40,
                16 => 55,
                17 => 70,
                18 => 85,
                19 => 97,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 10,
                11 => 15,
                12 => 25,
                13 => 40,
                14 => 55,
                15 => 70,
                16 => 85,
                17 => 93,
                18 => 97,
                19 => 99,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToTwenty->value => [
        TestAudience::LoJ1->value => [
            TestMoment::End->value => [
                0 => 1,
                1 => 3,
                2 => 3,
                3 => 5,
                4 => 7,
                5 => 7,
                6 => 10,
                7 => 15,
                8 => 20,
                9 => 30,
                10 => 45,
                11 => 55,
                12 => 65,
                13 => 75,
                14 => 80,
                15 => 80,
                16 => 85,
                17 => 85,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 20,
                10 => 35,
                11 => 50,
                12 => 60,
                13 => 65,
                14 => 75,
                15 => 80,
                16 => 80,
                17 => 85,
                18 => 90,
                19 => 93,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 10,
                8 => 15,
                9 => 20,
                10 => 30,
                11 => 35,
                12 => 45,
                13 => 50,
                14 => 55,
                15 => 60,
                16 => 65,
                17 => 70,
                18 => 75,
                19 => 85,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 7,
                7 => 10,
                8 => 15,
                9 => 25,
                10 => 35,
                11 => 50,
                12 => 55,
                13 => 65,
                14 => 70,
                15 => 75,
                16 => 80,
                17 => 85,
                18 => 90,
                19 => 93,
                20 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 10,
                8 => 20,
                9 => 25,
                10 => 40,
                11 => 50,
                12 => 55,
                13 => 60,
                14 => 70,
                15 => 80,
                16 => 80,
                17 => 85,
                18 => 90,
                19 => 93,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::SubtractionUpToFive->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 3,
                5 => 5,
                6 => 7,
                7 => 10,
                8 => 15,
                9 => 25,
                10 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::DivisionTables->value => [
        TestAudience::LoJ2->value => [
            TestMoment::End->value => [
                0 => 1,
                1 => 3,
                2 => 7,
                3 => 10,
                4 => 20,
                5 => 30,
                6 => 35,
                7 => 45,
                8 => 55,
                9 => 70,
                10 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 1,
                1 => 5,
                2 => 7,
                3 => 15,
                4 => 20,
                5 => 30,
                6 => 40,
                7 => 50,
                8 => 55,
                9 => 70,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 3,
                3 => 5,
                4 => 7,
                5 => 10,
                6 => 15,
                7 => 20,
                8 => 25,
                9 => 40,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 7,
                5 => 10,
                6 => 15,
                7 => 20,
                8 => 25,
                9 => 45,
                10 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 3,
                5 => 5,
                6 => 7,
                7 => 10,
                8 => 15,
                9 => 35,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 3,
                3 => 5,
                4 => 10,
                5 => 20,
                6 => 50,
                7 => 65,
                8 => 80,
                9 => 90,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 3,
                5 => 7,
                6 => 10,
                7 => 20,
                8 => 30,
                9 => 50,
                10 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 5,
                5 => 10,
                6 => 15,
                7 => 20,
                8 => 35,
                9 => 50,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 1,
                2 => 3,
                3 => 7,
                4 => 15,
                5 => 30,
                6 => 45,
                7 => 60,
                8 => 70,
                9 => 85,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 1,
                1 => 1,
                2 => 3,
                3 => 10,
                4 => 20,
                5 => 45,
                6 => 85,
                7 => 97,
                8 => 99,
                9 => 99,
                10 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 1,
                1 => 1,
                2 => 3,
                3 => 7,
                4 => 20,
                5 => 50,
                6 => 85,
                7 => 97,
                8 => 99,
                9 => 99,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 3,
                4 => 7,
                5 => 20,
                6 => 40,
                7 => 70,
                8 => 93,
                9 => 97,
                10 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::MultiplicationTables->value => [
        TestAudience::LoJ2->value => [
            TestMoment::End->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 7,
                5 => 15,
                6 => 20,
                7 => 25,
                8 => 35,
                9 => 55,
                10 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 5,
                4 => 10,
                5 => 20,
                6 => 25,
                7 => 35,
                8 => 45,
                9 => 60,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 5,
                6 => 10,
                7 => 10,
                8 => 15,
                9 => 50,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 15,
                9 => 40,
                10 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 7,
                8 => 10,
                9 => 35,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 3,
                5 => 7,
                6 => 15,
                7 => 20,
                8 => 35,
                9 => 50,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 7,
                7 => 10,
                8 => 25,
                9 => 50,
                10 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 7,
                7 => 15,
                8 => 25,
                9 => 55,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 7,
                5 => 10,
                6 => 20,
                7 => 30,
                8 => 45,
                9 => 70,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 10,
                5 => 20,
                6 => 50,
                7 => 65,
                8 => 85,
                9 => 90,
                10 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 5,
                4 => 10,
                5 => 25,
                6 => 45,
                7 => 65,
                8 => 80,
                9 => 93,
                10 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 5,
                5 => 10,
                6 => 15,
                7 => 30,
                8 => 50,
                9 => 80,
                10 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToThousand->value => [
        TestAudience::LoJ3->value => [
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 15,
                9 => 25,
                10 => 35,
                11 => 50,
                12 => 60,
                13 => 70,
                14 => 80,
                15 => 85,
                16 => 90,
                17 => 95,
                18 => 97,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 3,
                8 => 7,
                9 => 10,
                10 => 15,
                11 => 25,
                12 => 30,
                13 => 45,
                14 => 55,
                15 => 65,
                16 => 75,
                17 => 80,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 7,
                11 => 10,
                12 => 20,
                13 => 25,
                14 => 40,
                15 => 50,
                16 => 60,
                17 => 70,
                18 => 85,
                19 => 93,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 5,
                12 => 7,
                13 => 10,
                14 => 20,
                15 => 35,
                16 => 50,
                17 => 60,
                18 => 75,
                19 => 90,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 7,
                10 => 7,
                11 => 15,
                12 => 20,
                13 => 35,
                14 => 50,
                15 => 60,
                16 => 70,
                17 => 80,
                18 => 90,
                19 => 97,
                20 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 7,
                11 => 10,
                12 => 20,
                13 => 30,
                14 => 45,
                15 => 55,
                16 => 65,
                17 => 80,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 5,
                13 => 7,
                14 => 10,
                15 => 20,
                16 => 25,
                17 => 40,
                18 => 55,
                19 => 80,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 3,
                12 => 5,
                13 => 7,
                14 => 10,
                15 => 15,
                16 => 30,
                17 => 45,
                18 => 60,
                19 => 80,
                20 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 10,
                15 => 15,
                16 => 25,
                17 => 40,
                18 => 60,
                19 => 80,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 3,
                12 => 7,
                13 => 15,
                14 => 20,
                15 => 30,
                16 => 40,
                17 => 60,
                18 => 70,
                19 => 85,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToTen->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 30,
                10 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 15,
                10 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 10,
                10 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToHundred->value => [
        TestAudience::LoJ2->value => [
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 3,
                4 => 5,
                5 => 10,
                6 => 15,
                7 => 20,
                8 => 30,
                9 => 35,
                10 => 45,
                11 => 55,
                12 => 65,
                13 => 70,
                14 => 80,
                15 => 85,
                16 => 90,
                17 => 93,
                18 => 95,
                19 => 97,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 3,
                5 => 5,
                6 => 7,
                7 => 7,
                8 => 10,
                9 => 15,
                10 => 20,
                11 => 30,
                12 => 35,
                13 => 45,
                14 => 55,
                15 => 65,
                16 => 70,
                17 => 75,
                18 => 85,
                19 => 93,
                20 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 10,
                10 => 20,
                11 => 25,
                12 => 35,
                13 => 45,
                14 => 55,
                15 => 65,
                16 => 70,
                17 => 75,
                18 => 85,
                19 => 90,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 10,
                13 => 20,
                14 => 25,
                15 => 40,
                16 => 45,
                17 => 60,
                18 => 70,
                19 => 85,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 3,
                8 => 7,
                9 => 10,
                10 => 15,
                11 => 25,
                12 => 30,
                13 => 45,
                14 => 55,
                15 => 65,
                16 => 75,
                17 => 80,
                18 => 90,
                19 => 95,
                20 => 99,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 10,
                15 => 15,
                16 => 25,
                17 => 30,
                18 => 45,
                19 => 65,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToHundredThousand->value => [
        TestAudience::LoJ4->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 7,
                10 => 10,
                11 => 20,
                12 => 30,
                13 => 40,
                14 => 55,
                15 => 65,
                16 => 75,
                17 => 85,
                18 => 93,
                19 => 99,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 20,
                10 => 30,
                11 => 45,
                12 => 55,
                13 => 70,
                14 => 85,
                15 => 90,
                16 => 95,
                17 => 97,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 7,
                9 => 10,
                10 => 20,
                11 => 30,
                12 => 50,
                13 => 65,
                14 => 75,
                15 => 85,
                16 => 93,
                17 => 97,
                18 => 99,
                19 => 99,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 10,
                12 => 15,
                13 => 25,
                14 => 35,
                15 => 50,
                16 => 70,
                17 => 85,
                18 => 95,
                19 => 97,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 3,
                12 => 7,
                13 => 10,
                14 => 15,
                15 => 25,
                16 => 35,
                17 => 50,
                18 => 75,
                19 => 90,
                20 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 10,
                15 => 15,
                16 => 30,
                17 => 50,
                18 => 75,
                19 => 97,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 5,
                12 => 10,
                13 => 15,
                14 => 25,
                15 => 40,
                16 => 55,
                17 => 70,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToTwenty->value => [
        TestAudience::LoJ1->value => [
            TestMoment::End->value => [
                0 => 1,
                1 => 1,
                2 => 3,
                3 => 3,
                4 => 5,
                5 => 7,
                6 => 10,
                7 => 15,
                8 => 20,
                9 => 30,
                10 => 35,
                11 => 45,
                12 => 55,
                13 => 60,
                14 => 65,
                15 => 70,
                16 => 75,
                17 => 80,
                18 => 85,
                19 => 90,
                20 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 10,
                8 => 15,
                9 => 20,
                10 => 25,
                11 => 35,
                12 => 45,
                13 => 55,
                14 => 60,
                15 => 65,
                16 => 70,
                17 => 80,
                18 => 80,
                19 => 90,
                20 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 3,
                8 => 5,
                9 => 7,
                10 => 10,
                11 => 15,
                12 => 20,
                13 => 25,
                14 => 30,
                15 => 35,
                16 => 45,
                17 => 50,
                18 => 55,
                19 => 70,
                20 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 3,
                7 => 7,
                8 => 7,
                9 => 10,
                10 => 20,
                11 => 30,
                12 => 35,
                13 => 45,
                14 => 55,
                15 => 60,
                16 => 70,
                17 => 75,
                18 => 80,
                19 => 85,
                20 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 7,
                9 => 10,
                10 => 20,
                11 => 30,
                12 => 40,
                13 => 45,
                14 => 55,
                15 => 60,
                16 => 70,
                17 => 75,
                18 => 80,
                19 => 85,
                20 => 99,
            ],
        ],
    ],
    FollowUpSystemSubType::AdditionUpToFive->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 3,
                8 => 7,
                9 => 10,
                10 => 99,
            ],
        ],
    ],
];
