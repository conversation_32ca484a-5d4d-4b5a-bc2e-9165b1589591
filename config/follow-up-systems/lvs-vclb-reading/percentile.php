<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;

return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::LoJ1->value => [
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 7,
                13 => 10,
                14 => 10,
                15 => 15,
                16 => 15,
                17 => 20,
                18 => 20,
                19 => 25,
                20 => 25,
                21 => 30,
                22 => 35,
                23 => 35,
                24 => 40,
                25 => 40,
                26 => 45,
                27 => 50,
                28 => 50,
                29 => 55,
                30 => 60,
                31 => 60,
                32 => 65,
                33 => 65,
                34 => 70,
                35 => 70,
                36 => 75,
                37 => 75,
                38 => 80,
                39 => 80,
                40 => 80,
                41 => 80,
                42 => 80,
                43 => 85,
                44 => 85,
                45 => 85,
                46 => 85,
                47 => 85,
                48 => 85,
                49 => 90,
                50 => 90,
                51 => 90,
                52 => 90,
                53 => 90,
                54 => 93,
                55 => 93,
                56 => 93,
                57 => 93,
                58 => 93,
                59 => 95,
                60 => 95,
                61 => 95,
                62 => 95,
                63 => 95,
                64 => 95,
                65 => 97,
                66 => 97,
                67 => 97,
                68 => 97,
                69 => 97,
                70 => 97,
                71 => 97,
                72 => 97,
                73 => 97,
                74 => 97,
                75 => 97,
                76 => 97,
                77 => 97,
                78 => 99,
                79 => 99,
                80 => 99,
                81 => 99,
                82 => 99,
                83 => 99,
                84 => 99,
                85 => 99,
                86 => 99,
                87 => 99,
                88 => 99,
                89 => 99,
                90 => 99,
                91 => 99,
                92 => 99,
                93 => 99,
                94 => 99,
                95 => 99,
                96 => 99,
                97 => 99,
                98 => 99,
                99 => 99,
                100 => 99,
                101 => 99,
                102 => 99,
                103 => 99,
                104 => 99,
                105 => 99,
                106 => 99,
                107 => 99,
                108 => 99,
                109 => 99,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 3,
                14 => 3,
                15 => 3,
                16 => 5,
                17 => 5,
                18 => 7,
                19 => 7,
                20 => 10,
                21 => 10,
                22 => 15,
                23 => 15,
                24 => 15,
                25 => 20,
                26 => 20,
                27 => 25,
                28 => 25,
                29 => 30,
                30 => 30,
                31 => 35,
                32 => 35,
                33 => 40,
                34 => 40,
                35 => 45,
                36 => 45,
                37 => 50,
                38 => 50,
                39 => 50,
                40 => 55,
                41 => 55,
                42 => 60,
                43 => 60,
                44 => 60,
                45 => 65,
                46 => 65,
                47 => 65,
                48 => 70,
                49 => 70,
                50 => 70,
                51 => 75,
                52 => 75,
                53 => 75,
                54 => 75,
                55 => 80,
                56 => 80,
                57 => 80,
                58 => 80,
                59 => 85,
                60 => 85,
                61 => 85,
                62 => 85,
                63 => 85,
                64 => 85,
                65 => 90,
                66 => 90,
                67 => 90,
                68 => 90,
                69 => 90,
                70 => 90,
                71 => 93,
                72 => 93,
                73 => 93,
                74 => 93,
                75 => 95,
                76 => 95,
                77 => 95,
                78 => 95,
                79 => 95,
                80 => 95,
                81 => 97,
                82 => 97,
                83 => 97,
                84 => 97,
                85 => 97,
                86 => 97,
                87 => 97,
                88 => 97,
                89 => 99,
                90 => 99,
                91 => 99,
                92 => 99,
                93 => 99,
                94 => 99,
                95 => 99,
                96 => 99,
                97 => 99,
                98 => 99,
                99 => 99,
                100 => 99,
                101 => 99,
                102 => 99,
                103 => 99,
                104 => 99,
                105 => 99,
                106 => 99,
                107 => 99,
                108 => 99,
                109 => 99,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
        ],
        TestAudience::LoJ2->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 3,
                20 => 3,
                21 => 5,
                22 => 7,
                23 => 7,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 15,
                28 => 15,
                29 => 20,
                30 => 20,
                31 => 20,
                32 => 25,
                33 => 25,
                34 => 25,
                35 => 30,
                36 => 30,
                37 => 30,
                38 => 30,
                39 => 35,
                40 => 35,
                41 => 35,
                42 => 40,
                43 => 40,
                44 => 40,
                45 => 40,
                46 => 45,
                47 => 45,
                48 => 45,
                49 => 50,
                50 => 50,
                51 => 50,
                52 => 50,
                53 => 55,
                54 => 55,
                55 => 55,
                56 => 60,
                57 => 60,
                58 => 60,
                59 => 60,
                60 => 65,
                61 => 65,
                62 => 65,
                63 => 65,
                64 => 65,
                65 => 70,
                66 => 70,
                67 => 70,
                68 => 75,
                69 => 75,
                70 => 75,
                71 => 80,
                72 => 80,
                73 => 80,
                74 => 80,
                75 => 80,
                76 => 80,
                77 => 80,
                78 => 85,
                79 => 85,
                80 => 85,
                81 => 85,
                82 => 85,
                83 => 85,
                84 => 85,
                85 => 85,
                86 => 90,
                87 => 90,
                88 => 90,
                89 => 90,
                90 => 90,
                91 => 93,
                92 => 93,
                93 => 93,
                94 => 93,
                95 => 93,
                96 => 99,
                97 => 99,
                98 => 99,
                99 => 99,
                100 => 99,
                101 => 99,
                102 => 99,
                103 => 99,
                104 => 99,
                105 => 99,
                106 => 99,
                107 => 99,
                108 => 99,
                109 => 99,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 0,
                18 => 0,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 1,
                25 => 3,
                26 => 3,
                27 => 3,
                28 => 3,
                29 => 5,
                30 => 5,
                31 => 5,
                32 => 7,
                33 => 7,
                34 => 7,
                35 => 7,
                36 => 10,
                37 => 10,
                38 => 10,
                39 => 10,
                40 => 10,
                41 => 15,
                42 => 15,
                43 => 15,
                44 => 15,
                45 => 20,
                46 => 20,
                47 => 20,
                48 => 20,
                49 => 20,
                50 => 25,
                51 => 25,
                52 => 25,
                53 => 25,
                54 => 30,
                55 => 30,
                56 => 30,
                57 => 30,
                58 => 35,
                59 => 35,
                60 => 35,
                61 => 40,
                62 => 40,
                63 => 40,
                64 => 40,
                65 => 45,
                66 => 45,
                67 => 50,
                68 => 50,
                69 => 55,
                70 => 55,
                71 => 55,
                72 => 55,
                73 => 60,
                74 => 60,
                75 => 60,
                76 => 60,
                77 => 65,
                78 => 65,
                79 => 65,
                80 => 65,
                81 => 70,
                82 => 70,
                83 => 70,
                84 => 70,
                85 => 75,
                86 => 75,
                87 => 75,
                88 => 75,
                89 => 80,
                90 => 80,
                91 => 80,
                92 => 80,
                93 => 85,
                94 => 85,
                95 => 85,
                96 => 85,
                97 => 85,
                98 => 85,
                99 => 99,
                100 => 99,
                101 => 99,
                102 => 99,
                103 => 99,
                104 => 99,
                105 => 99,
                106 => 99,
                107 => 99,
                108 => 99,
                109 => 99,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 1,
                25 => 1,
                26 => 1,
                27 => 3,
                28 => 3,
                29 => 3,
                30 => 3,
                31 => 5,
                32 => 5,
                33 => 5,
                34 => 7,
                35 => 7,
                36 => 7,
                37 => 10,
                38 => 10,
                39 => 10,
                40 => 10,
                41 => 10,
                42 => 10,
                43 => 10,
                44 => 15,
                45 => 15,
                46 => 15,
                47 => 15,
                48 => 15,
                49 => 20,
                50 => 20,
                51 => 20,
                52 => 20,
                53 => 25,
                54 => 25,
                55 => 25,
                56 => 25,
                57 => 30,
                58 => 30,
                59 => 30,
                60 => 30,
                61 => 35,
                62 => 35,
                63 => 35,
                64 => 40,
                65 => 40,
                66 => 45,
                67 => 45,
                68 => 45,
                69 => 50,
                70 => 50,
                71 => 55,
                72 => 55,
                73 => 55,
                74 => 60,
                75 => 60,
                76 => 60,
                77 => 65,
                78 => 65,
                79 => 65,
                80 => 70,
                81 => 70,
                82 => 70,
                83 => 70,
                84 => 75,
                85 => 75,
                86 => 75,
                87 => 80,
                88 => 80,
                89 => 80,
                90 => 80,
                91 => 85,
                92 => 85,
                93 => 85,
                94 => 85,
                95 => 85,
                96 => 85,
                97 => 85,
                98 => 99,
                99 => 99,
                100 => 99,
                101 => 99,
                102 => 99,
                103 => 99,
                104 => 99,
                105 => 99,
                106 => 99,
                107 => 99,
                108 => 99,
                109 => 99,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::Begin->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 0,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 1,
                25 => 1,
                26 => 1,
                27 => 1,
                28 => 1,
                29 => 3,
                30 => 3,
                31 => 3,
                32 => 5,
                33 => 5,
                34 => 5,
                35 => 5,
                36 => 5,
                37 => 7,
                38 => 7,
                39 => 7,
                40 => 7,
                41 => 7,
                42 => 10,
                43 => 10,
                44 => 10,
                45 => 10,
                46 => 10,
                47 => 10,
                48 => 15,
                49 => 15,
                50 => 15,
                51 => 15,
                52 => 15,
                53 => 15,
                54 => 20,
                55 => 20,
                56 => 20,
                57 => 20,
                58 => 25,
                59 => 25,
                60 => 25,
                61 => 25,
                62 => 30,
                63 => 30,
                64 => 30,
                65 => 35,
                66 => 35,
                67 => 35,
                68 => 35,
                69 => 40,
                70 => 40,
                71 => 40,
                72 => 40,
                73 => 45,
                74 => 45,
                75 => 45,
                76 => 50,
                77 => 50,
                78 => 50,
                79 => 55,
                80 => 55,
                81 => 55,
                82 => 60,
                83 => 60,
                84 => 60,
                85 => 65,
                86 => 65,
                87 => 65,
                88 => 70,
                89 => 70,
                90 => 70,
                91 => 70,
                92 => 75,
                93 => 75,
                94 => 75,
                95 => 80,
                96 => 80,
                97 => 80,
                98 => 80,
                99 => 85,
                100 => 85,
                101 => 85,
                102 => 85,
                103 => 85,
                104 => 90,
                105 => 90,
                106 => 90,
                107 => 93,
                108 => 93,
                109 => 93,
                110 => 95,
                111 => 95,
                112 => 97,
                113 => 97,
                114 => 97,
                115 => 97,
                116 => 97,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
            TestMoment::Middle->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 0,
                18 => 0,
                19 => 0,
                20 => 0,
                21 => 0,
                22 => 0,
                23 => 1,
                24 => 1,
                25 => 1,
                26 => 1,
                27 => 1,
                28 => 1,
                29 => 1,
                30 => 1,
                31 => 1,
                32 => 1,
                33 => 1,
                34 => 1,
                35 => 3,
                36 => 3,
                37 => 3,
                38 => 3,
                39 => 3,
                40 => 5,
                41 => 5,
                42 => 5,
                43 => 7,
                44 => 7,
                45 => 10,
                46 => 10,
                47 => 10,
                48 => 10,
                49 => 10,
                50 => 15,
                51 => 15,
                52 => 15,
                53 => 20,
                54 => 20,
                55 => 20,
                56 => 25,
                57 => 25,
                58 => 25,
                59 => 30,
                60 => 30,
                61 => 35,
                62 => 35,
                63 => 35,
                64 => 40,
                65 => 45,
                66 => 45,
                67 => 45,
                68 => 50,
                69 => 50,
                70 => 50,
                71 => 55,
                72 => 55,
                73 => 60,
                74 => 60,
                75 => 65,
                76 => 65,
                77 => 65,
                78 => 70,
                79 => 70,
                80 => 70,
                81 => 75,
                82 => 75,
                83 => 75,
                84 => 80,
                85 => 80,
                86 => 80,
                87 => 80,
                88 => 85,
                89 => 85,
                90 => 85,
                91 => 85,
                92 => 90,
                93 => 90,
                94 => 90,
                95 => 90,
                96 => 90,
                97 => 93,
                98 => 93,
                99 => 93,
                100 => 93,
                101 => 95,
                102 => 95,
                103 => 95,
                104 => 95,
                105 => 97,
                106 => 97,
                107 => 97,
                108 => 97,
                109 => 97,
                110 => 97,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
            TestMoment::End->value => [
                0 => 0,
                1 => 0,
                2 => 0,
                3 => 0,
                4 => 0,
                5 => 0,
                6 => 0,
                7 => 0,
                8 => 0,
                9 => 0,
                10 => 0,
                11 => 0,
                12 => 0,
                13 => 0,
                14 => 0,
                15 => 0,
                16 => 0,
                17 => 0,
                18 => 0,
                19 => 0,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 1,
                25 => 1,
                26 => 1,
                27 => 1,
                28 => 1,
                29 => 1,
                30 => 1,
                31 => 1,
                32 => 3,
                33 => 3,
                34 => 3,
                35 => 3,
                36 => 5,
                37 => 5,
                38 => 5,
                39 => 7,
                40 => 7,
                41 => 7,
                42 => 7,
                43 => 10,
                44 => 10,
                45 => 10,
                46 => 10,
                47 => 10,
                48 => 10,
                49 => 15,
                50 => 15,
                51 => 15,
                52 => 15,
                53 => 15,
                54 => 20,
                55 => 20,
                56 => 20,
                57 => 25,
                58 => 25,
                59 => 25,
                60 => 30,
                61 => 30,
                62 => 35,
                63 => 35,
                64 => 35,
                65 => 40,
                66 => 45,
                67 => 45,
                68 => 45,
                69 => 50,
                70 => 50,
                71 => 55,
                72 => 55,
                73 => 55,
                74 => 60,
                75 => 65,
                76 => 65,
                77 => 65,
                78 => 70,
                79 => 70,
                80 => 70,
                81 => 70,
                82 => 75,
                83 => 75,
                84 => 75,
                85 => 80,
                86 => 80,
                87 => 80,
                88 => 85,
                89 => 85,
                90 => 85,
                91 => 85,
                92 => 85,
                93 => 90,
                94 => 90,
                95 => 90,
                96 => 90,
                97 => 93,
                98 => 93,
                99 => 93,
                100 => 93,
                101 => 95,
                102 => 95,
                103 => 95,
                104 => 95,
                105 => 95,
                106 => 97,
                107 => 97,
                108 => 97,
                109 => 97,
                110 => 99,
                111 => 99,
                112 => 99,
                113 => 99,
                114 => 99,
                115 => 99,
                116 => 99,
                117 => 99,
                118 => 99,
                119 => 99,
                120 => 99,
            ],
        ],
    ],
];
