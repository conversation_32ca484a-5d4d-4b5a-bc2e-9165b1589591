<?php

use Cfa\Common\Domain\School\Group\TargetAudience\TargetAudienceType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;
use Cfa\Evaluation\Domain\FollowUpSystem\Zone\AviOldZone;

return [
    'targetAudiences' => [
        TargetAudienceType::Lo->value => [
            1,
            2,
            3,
            4,
            5,
            6,
        ],
    ],
    'testAudiences' => [
        TestAudience::LoJ1->value,
        TestAudience::LoJ2->value,
        TestAudience::LoJ3->value,
        TestAudience::LoJ4->value,
        TestAudience::LoJ5->value,
        TestAudience::LoJ6->value,
    ],
    'testMoments' => [
        TestMoment::Begin->value,
        TestMoment::Middle->value,
        TestMoment::End->value,
    ],
    'zones' => [
        AviOldZone::One->value,
        AviOldZone::Two->value,
        AviOldZone::Three->value,
        AviOldZone::Four->value,
        AviOldZone::Five->value,
        AviOldZone::Six->value,
        AviOldZone::Seven->value,
        AviOldZone::Eight->value,
        AviOldZone::Nine->value,
        AviOldZone::GreaterThanNine->value,
    ],
    'zoneName' => 'avi-old-zone',
];
