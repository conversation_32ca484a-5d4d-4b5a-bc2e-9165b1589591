<?php

use Cfa\Evaluation\Domain\FollowUpSystem\FollowUpSystemSubType;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestAudience;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\TestMoment;

// Generated from the Excel provided by the author team.
// Mapping from every possible score to a percentile.
// The first level contains every subtype.
// The second level contains every test audience.
// The third level contains every test moment.
return [
    FollowUpSystemSubType::Default->value => [
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 3,
                14 => 3,
                15 => 5,
                16 => 7,
                17 => 10,
                18 => 10,
                19 => 15,
                20 => 20,
                21 => 25,
                22 => 35,
                23 => 50,
                24 => 65,
                25 => 85,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 3,
                15 => 3,
                16 => 3,
                17 => 5,
                18 => 5,
                19 => 7,
                20 => 10,
                21 => 15,
                22 => 15,
                23 => 20,
                24 => 25,
                25 => 35,
                26 => 40,
                27 => 50,
                28 => 60,
                29 => 75,
                30 => 90,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 3,
                5 => 3,
                6 => 5,
                7 => 5,
                8 => 5,
                9 => 5,
                10 => 5,
                11 => 7,
                12 => 7,
                13 => 7,
                14 => 10,
                15 => 10,
                16 => 10,
                17 => 15,
                18 => 15,
                19 => 20,
                20 => 25,
                21 => 30,
                22 => 40,
                23 => 45,
                24 => 55,
                25 => 60,
                26 => 70,
                27 => 75,
                28 => 80,
                29 => 90,
                30 => 95,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 3,
                16 => 3,
                17 => 3,
                18 => 3,
                19 => 5,
                20 => 7,
                21 => 7,
                22 => 10,
                23 => 10,
                24 => 15,
                25 => 15,
                26 => 20,
                27 => 25,
                28 => 30,
                29 => 40,
                30 => 45,
                31 => 55,
                32 => 60,
                33 => 70,
                34 => 85,
                35 => 95,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 3,
                22 => 3,
                23 => 5,
                24 => 7,
                25 => 10,
                26 => 15,
                27 => 20,
                28 => 25,
                29 => 30,
                30 => 40,
                31 => 45,
                32 => 55,
                33 => 70,
                34 => 80,
                35 => 95,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 3,
                24 => 3,
                25 => 5,
                26 => 7,
                27 => 10,
                28 => 15,
                29 => 20,
                30 => 30,
                31 => 40,
                32 => 45,
                33 => 60,
                34 => 70,
                35 => 80,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 7,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 15,
                28 => 20,
                29 => 25,
                30 => 30,
                31 => 30,
                32 => 35,
                33 => 45,
                34 => 50,
                35 => 60,
                36 => 65,
                37 => 75,
                38 => 85,
                39 => 90,
                40 => 95,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 5,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 15,
                28 => 20,
                29 => 25,
                30 => 30,
                31 => 35,
                32 => 45,
                33 => 50,
                34 => 60,
                35 => 70,
                36 => 80,
                37 => 90,
                38 => 95,
                39 => 95,
                40 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 1,
                24 => 3,
                25 => 5,
                26 => 7,
                27 => 7,
                28 => 10,
                29 => 15,
                30 => 20,
                31 => 25,
                32 => 30,
                33 => 40,
                34 => 50,
                35 => 60,
                36 => 70,
                37 => 80,
                38 => 90,
                39 => 95,
                40 => 99,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 1,
                22 => 1,
                23 => 3,
                24 => 3,
                25 => 5,
                26 => 7,
                27 => 7,
                28 => 10,
                29 => 10,
                30 => 15,
                31 => 20,
                32 => 25,
                33 => 30,
                34 => 35,
                35 => 40,
                36 => 45,
                37 => 55,
                38 => 65,
                39 => 70,
                40 => 80,
                41 => 85,
                42 => 90,
                43 => 95,
                44 => 99,
                45 => 99,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 3,
                19 => 3,
                20 => 5,
                21 => 5,
                22 => 7,
                23 => 7,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 15,
                28 => 20,
                29 => 25,
                30 => 30,
                31 => 35,
                32 => 40,
                33 => 50,
                34 => 55,
                35 => 60,
                36 => 70,
                37 => 75,
                38 => 80,
                39 => 85,
                40 => 90,
                41 => 95,
                42 => 99,
                43 => 99,
                44 => 99,
                45 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 3,
                19 => 3,
                20 => 5,
                21 => 7,
                22 => 7,
                23 => 10,
                24 => 10,
                25 => 15,
                26 => 20,
                27 => 20,
                28 => 25,
                29 => 25,
                30 => 30,
                31 => 35,
                32 => 40,
                33 => 45,
                34 => 50,
                35 => 55,
                36 => 60,
                37 => 70,
                38 => 75,
                39 => 80,
                40 => 85,
                41 => 90,
                42 => 95,
                43 => 95,
                44 => 95,
                45 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 1,
                21 => 3,
                22 => 5,
                23 => 5,
                24 => 7,
                25 => 10,
                26 => 10,
                27 => 15,
                28 => 20,
                29 => 25,
                30 => 30,
                31 => 35,
                32 => 40,
                33 => 50,
                34 => 60,
                35 => 65,
                36 => 75,
                37 => 85,
                38 => 90,
                39 => 95,
                40 => 99,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 1,
                20 => 3,
                21 => 5,
                22 => 5,
                23 => 7,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 20,
                28 => 25,
                29 => 30,
                30 => 35,
                31 => 45,
                32 => 50,
                33 => 60,
                34 => 70,
                35 => 75,
                36 => 80,
                37 => 90,
                38 => 95,
                39 => 95,
                40 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 1,
                16 => 1,
                17 => 1,
                18 => 1,
                19 => 3,
                20 => 3,
                21 => 3,
                22 => 5,
                23 => 7,
                24 => 10,
                25 => 10,
                26 => 15,
                27 => 20,
                28 => 25,
                29 => 25,
                30 => 30,
                31 => 35,
                32 => 45,
                33 => 50,
                34 => 55,
                35 => 60,
                36 => 70,
                37 => 75,
                38 => 75,
                39 => 85,
                40 => 90,
            ],
        ],
    ],
    FollowUpSystemSubType::Sentences->value => [
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 7,
                10 => 10,
                11 => 15,
                12 => 25,
                13 => 35,
                14 => 50,
                15 => 75,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 5,
                12 => 7,
                13 => 10,
                14 => 15,
                15 => 20,
                16 => 30,
                17 => 40,
                18 => 50,
                19 => 65,
                20 => 85,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 3,
                3 => 5,
                4 => 5,
                5 => 5,
                6 => 5,
                7 => 7,
                8 => 7,
                9 => 7,
                10 => 10,
                11 => 10,
                12 => 15,
                13 => 20,
                14 => 25,
                15 => 35,
                16 => 40,
                17 => 55,
                18 => 65,
                19 => 80,
                20 => 90,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 3,
                11 => 5,
                12 => 7,
                13 => 10,
                14 => 10,
                15 => 15,
                16 => 25,
                17 => 35,
                18 => 45,
                19 => 60,
                20 => 85,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 7,
                15 => 15,
                16 => 20,
                17 => 30,
                18 => 45,
                19 => 65,
                20 => 85,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 3,
                14 => 5,
                15 => 10,
                16 => 20,
                17 => 25,
                18 => 40,
                19 => 60,
                20 => 80,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 10,
                13 => 15,
                14 => 20,
                15 => 30,
                16 => 35,
                17 => 50,
                18 => 60,
                19 => 75,
                20 => 90,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 5,
                12 => 7,
                13 => 10,
                14 => 15,
                15 => 20,
                16 => 30,
                17 => 40,
                18 => 60,
                19 => 75,
                20 => 95,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 5,
                12 => 7,
                13 => 10,
                14 => 15,
                15 => 25,
                16 => 40,
                17 => 50,
                18 => 70,
                19 => 80,
                20 => 95,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 1,
                13 => 1,
                14 => 1,
                15 => 3,
                16 => 5,
                17 => 7,
                18 => 7,
                19 => 10,
                20 => 15,
                21 => 20,
                22 => 25,
                23 => 35,
                24 => 45,
                25 => 55,
                26 => 65,
                27 => 80,
                28 => 90,
                29 => 95,
                30 => 99,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 3,
                12 => 5,
                13 => 7,
                14 => 7,
                15 => 10,
                16 => 15,
                17 => 20,
                18 => 25,
                19 => 30,
                20 => 35,
                21 => 45,
                22 => 50,
                23 => 60,
                24 => 70,
                25 => 75,
                26 => 85,
                27 => 90,
                28 => 95,
                29 => 99,
                30 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 3,
                12 => 5,
                13 => 7,
                14 => 10,
                15 => 10,
                16 => 15,
                17 => 20,
                18 => 20,
                19 => 25,
                20 => 30,
                21 => 35,
                22 => 45,
                23 => 50,
                24 => 60,
                25 => 65,
                26 => 75,
                27 => 85,
                28 => 90,
                29 => 95,
                30 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 7,
                15 => 10,
                16 => 15,
                17 => 20,
                18 => 25,
                19 => 35,
                20 => 45,
                21 => 60,
                22 => 70,
                23 => 80,
                24 => 95,
                25 => 99,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 3,
                12 => 3,
                13 => 5,
                14 => 10,
                15 => 15,
                16 => 20,
                17 => 25,
                18 => 35,
                19 => 45,
                20 => 55,
                21 => 65,
                22 => 80,
                23 => 90,
                24 => 95,
                25 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 3,
                12 => 3,
                13 => 3,
                14 => 5,
                15 => 7,
                16 => 10,
                17 => 15,
                18 => 25,
                19 => 30,
                20 => 40,
                21 => 45,
                22 => 55,
                23 => 70,
                24 => 80,
                25 => 90,
            ],
        ],
    ],
    FollowUpSystemSubType::Words->value => [
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 7,
                8 => 15,
                9 => 30,
                10 => 55,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 5,
                3 => 7,
                4 => 10,
                5 => 20,
                6 => 25,
                7 => 40,
                8 => 55,
                9 => 75,
                10 => 90,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 15,
                10 => 20,
                11 => 30,
                12 => 40,
                13 => 50,
                14 => 65,
                15 => 85,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 3,
                10 => 5,
                11 => 7,
                12 => 15,
                13 => 25,
                14 => 40,
                15 => 65,
            ],
        ],
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 7,
                9 => 10,
                10 => 20,
                11 => 25,
                12 => 40,
                13 => 55,
                14 => 70,
                15 => 90,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 3,
                11 => 7,
                12 => 10,
                13 => 20,
                14 => 30,
                15 => 40,
                16 => 50,
                17 => 70,
                18 => 85,
                19 => 95,
                20 => 99,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 1,
                9 => 1,
                10 => 1,
                11 => 1,
                12 => 3,
                13 => 5,
                14 => 7,
                15 => 15,
                16 => 25,
                17 => 40,
                18 => 55,
                19 => 75,
                20 => 95,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 7,
                9 => 10,
                10 => 20,
                11 => 30,
                12 => 45,
                13 => 60,
                14 => 80,
                15 => 95,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 7,
                8 => 10,
                9 => 15,
                10 => 25,
                11 => 35,
                12 => 55,
                13 => 75,
                14 => 90,
                15 => 99,
            ],
        ],
        TestAudience::LoJ6->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 7,
                10 => 10,
                11 => 20,
                12 => 30,
                13 => 45,
                14 => 65,
                15 => 85,
            ],
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 10,
                10 => 15,
                11 => 25,
                12 => 35,
                13 => 55,
                14 => 70,
                15 => 90,
            ],
            TestMoment::DictationRevision3->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 3,
                7 => 5,
                8 => 10,
                9 => 15,
                10 => 25,
                11 => 30,
                12 => 40,
                13 => 55,
                14 => 75,
                15 => 85,
            ],
        ],
    ],
    FollowUpSystemSubType::Verbs->value => [
        TestAudience::LoJ4->value => [
            TestMoment::DictationRevision1->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 5,
                4 => 15,
                5 => 40,
            ],
        ],
    ],
    FollowUpSystemSubType::WordsInSentence->value => [
        TestAudience::LoJ2->value => [
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 3,
                6 => 5,
                7 => 10,
                8 => 20,
                9 => 35,
                10 => 60,
            ],
        ],
        TestAudience::LoJ3->value => [
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 1,
                8 => 3,
                9 => 5,
                10 => 10,
                11 => 15,
                12 => 25,
                13 => 40,
                14 => 55,
                15 => 80,
            ],
        ],
        TestAudience::LoJ5->value => [
            TestMoment::DictationRevision2->value => [
                0 => 1,
                1 => 1,
                2 => 1,
                3 => 1,
                4 => 1,
                5 => 1,
                6 => 1,
                7 => 3,
                8 => 5,
                9 => 10,
                10 => 20,
                11 => 30,
                12 => 50,
                13 => 70,
                14 => 85,
                15 => 99,
            ],
        ],
    ],
];
