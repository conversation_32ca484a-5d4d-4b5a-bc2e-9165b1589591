<?php

use Illuminate\Support\Str;

return [
    /*
    |--------------------------------------------------------------------------
    | AWS SDK Configuration
    |--------------------------------------------------------------------------
    |
    | The configuration options set in this file will be passed directly to the
    | `Aws\Sdk` object, from which all client objects are created. The minimum
    | required options are declared here, but the full set of possible options
    | are documented at:
    | http://docs.aws.amazon.com/aws-sdk-php/v3/guide/guide/configuration.html
    |
    */

    'credentials' => env('AWS_KEY') ? [
        'key' => env('AWS_KEY'),
        'secret' => env('AWS_SECRET'),
        'token' => env('AWS_SESSION_TOKEN'),
    ] : null,
    'region' => env('AWS_REGION', 'eu-west-1'),
    'version' => 'latest',
    'ua_append' => [],
    'local' => [
        'mfa_serial' => env('LOCAL_AWS_MFA_SERIAL'),
        'dev_role' => env('LOCAL_AWS_DEV_ROLE'),
    ],
    'rds' => [
        'arn' => 'arn:aws:rds:' . env('AWS_REGION', 'eu-west-1') . ':' . env('AWS_ACCOUNT_ID') . ':cluster:' .
            Str::before(env('DB_HOST_WRITE'), '.'),
    ],
    'elasticache' => [
        'replicationGroupId' => Str::before(env('REDIS_HOST'), '.'),
    ],
    'cloudfront' => [
        'cloudfrontUrl' => env('AWS_CLOUDFRONT_URL'),
        'privateKey' => env('AWS_CLOUDFRONT_PRIVATE_KEY_PATH'),
        'keyPairId' => env('AWS_CLOUDFRONT_KEYPAIR_ID'),
    ],

    'cloudsearch' => [
        'searchEndpoint' => env('AWS_CLOUDSEARCH_SEARCH'),
        'documentEndpoint' => env('AWS_CLOUDSEARCH_DOCUMENT'),
        'name' => env('AWS_CLOUDSEARCH_NAME'),
    ],
    'http' => ['proxy' => env('AWS_PROXY', '')],
];
