<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Queue Driver
    |--------------------------------------------------------------------------
    |
    | The Laravel queue API supports a variety of back-ends via an unified
    | API, giving you convenient access to each back-end using the same
    | syntax for each one. Here you may set the default queue driver.
    |
    | Supported: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'default' => env('QUEUE_DRIVER', 'sync'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    */

    'connections' => [
        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'default',
            'retry_after' => 90,
        ],

        'beanstalkd' => [
            'driver' => 'beanstalkd',
            'host' => 'localhost',
            'queue' => 'default',
            'retry_after' => 90,
        ],

        'sqs' => [
            'driver' => 'external-sqs',
            'key' => env('AWS_KEY'),
            'queue' => 'default',
            'secret' => env('AWS_SECRET'),
            'token' => env('AWS_SESSION_TOKEN'),
            'prefix' => 'https://sqs.' . env('AWS_REGION', 'eu-west-1') . '.amazonaws.com/' . env('AWS_ACCOUNT_ID'),
            'region' => env('AWS_REGION'),
        ],

        // Needed for ACC, using smd_fl_acc_sol2.fifo on the PRD account (but used in other envs as well for simplicity).
        'sqs-smd' => [
            'driver' => 'external-sqs',
            'key' => env('AWS_KEY'),
            'queue' => 'default',
            'secret' => env('AWS_SECRET'),
            'token' => env('AWS_SESSION_TOKEN'),
            'prefix' => 'https://sqs.' . env('AWS_REGION', 'eu-west-1') . '.amazonaws.com/' . env('AWS_ACCOUNT_ID_SMD'),
            'region' => env('AWS_REGION'),
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'queue',
            'queue' => 'default',
            'retry_after' => ((int) env('HORIZON_TIMEOUT', 1803)) + 30,
        ],

        'exports' => [
            'driver' => 'sync',
        ],
    ],

    'queue-names' => [
        'bingel-results' => env('AWS_BINGEL_RESULTS_SQS_QUEUE'),
        'lambda-results' => env('AWS_LAMBDA_RESULTS_SQS_QUEUE'),
        'smd' => env('AWS_SMD_SQS_QUEUE'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],
];
