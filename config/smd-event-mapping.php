<?php

use Cfa\Common\Application\Jobs\Smd\GroupEventJob;
use Cfa\Common\Application\Jobs\Smd\InstituteEventJob;
use Cfa\Common\Application\Jobs\Smd\MergeEventJob;
use Cfa\Common\Application\Jobs\Smd\PersonEventJob;
use Cfa\Common\Application\Jobs\Smd\RoleEventJob;
use Cfa\Common\Application\Jobs\Smd\SchoolEventJob;

return [
    /*
    |--------------------------------------------------------------------------
    | SMD event mapping
    |--------------------------------------------------------------------------
    |
    | Every incoming smd event has a corresponding job.
    | Underneath the correct mapping can be found.
    |
    */

    'GROUP' => GroupEventJob::class,
    'INSTITUTE' => InstituteEventJob::class,
    'PERSON' => PersonEventJob::class,
    'ROLE' => RoleEventJob::class,
    'SCHOOL' => SchoolEventJob::class,
    'MERGE_PERSON' => MergeEventJob::class,
];
