<?php

use App\Models\ActivityLog\ActivityLog;
use App\Models\DatabaseNotification\DatabaseNotification;
use App\Models\SMD\SmdEventLog;
use App\Models\SQS\SqsEventLog;
use Cfa\Care\Domain\CareInfo\CareInfo;
use Cfa\Care\Domain\CareInfo\Diagnosis\Diagnosis;
use Cfa\Care\Domain\CareInfo\Redicodi\Redicodi;
use Cfa\Care\Domain\CareInput\CareFile\CareFile;
use Cfa\Care\Domain\CareInput\CareInput;
use Cfa\Care\Domain\CareInput\CareTheme\CareTheme;
use Cfa\Care\Domain\CareInput\CareType\CareType;
use Cfa\Care\Domain\Task\Task;
use Cfa\Common\Domain\Permission\CollectionLicense;
use Cfa\Common\Domain\Permission\PermissionSchool;
use Cfa\Common\Domain\Permission\PermissionSchoolUserAccess;
use Cfa\Common\Domain\School\Group\GroupAccessLog\GroupAccessLog;
use Cfa\Common\Domain\Subject\Subject;
use Cfa\Common\Domain\User\User;
use Cfa\Common\Domain\UserDataTransfer\UserDataTransfer;
use Cfa\Evaluation\Domain\BingelTestResult\BingelTestResult;
use Cfa\Evaluation\Domain\FollowUpSystem\PredefinedFollowUpSystem\Score\PredefinedFollowUpSystemScore;
use Cfa\Evaluation\Domain\Report\Calculation\EvaluationReportJob;
use Cfa\Evaluation\Domain\Settings\Report\SubjectQuotation\SubjectQuotationSettings;
use Cfa\Planner\Domain\CalendarItem\CalendarItem;
use Cfa\Planner\Domain\CalendarItem\Row\CalendarItemRow;
use Cfa\Planner\Domain\Collection\Chapter\Chapter;
use Cfa\Planner\Domain\Collection\PlannerCollection;
use Cfa\Planner\Domain\Collection\UserCollection\PlannerCollectionAccess as LegacyPlannerCollectionAccess;
use Cfa\Planner\Domain\CollectionV2\PlannerCollectionAccess;
use Cfa\Planner\Domain\Record\Material\Material;
use Cfa\Planner\Domain\Record\Record;
use Cfa\Planner\Domain\Roster\Roster;
use Cfa\Planner\Domain\Roster\Timeslot\Timeslot;

return [
    // All models in this array using Prunable or MassPrunable will be cleaned.
    'models' => [
        ActivityLog::class,
        CalendarItem::class,
        CalendarItemRow::class,
        CareFile::class,
        CareInfo::class,
        CareInput::class,
        CareTheme::class,
        CareType::class,
        Chapter::class,
        CollectionLicense::class,
        DatabaseNotification::class,
        Diagnosis::class,
        EvaluationReportJob::class,
        GroupAccessLog::class,
        LegacyPlannerCollectionAccess::class,
        Material::class,
        PermissionSchool::class,
        PermissionSchoolUserAccess::class,
        PlannerCollection::class,
        PlannerCollectionAccess::class,
        PredefinedFollowUpSystemScore::class,
        Record::class,
        Redicodi::class,
        Roster::class,
        Subject::class,
        SubjectQuotationSettings::class,
        Task::class,
        Timeslot::class,
        User::class,
        UserDataTransfer::class,
        BingelTestResult::class,
        SmdEventLog::class,
        SqsEventLog::class,
    ],
];
