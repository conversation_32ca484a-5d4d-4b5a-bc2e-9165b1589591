# Zip service

## Description

This service reacts to a JSON file containing paths to zip to be uploaded to S3 in the `app/lambda/zipservice/input` directory.
The structure is expected to be like this:

```
[
    {
        "key" : "path/to/file/on/s3/file.jpg",
        "name": "attachments/folder/in/zip/file.jpg"
    }
]
```

When the lambda ends successfully, the resulting zip will be put in `app/lambda/zipservice/output` and have the same name
as the JSON file with the paths.

A queue message will be dispatched to the bingeltms_fl_prd_lambda queue after the lambda has run, even if it failed.
The message contains both the original request (the reference to the S3 file) and a response containing either the error
message or the path to the resulting zip file.

## How to execute locally?

There are two ways to execute the functionality locally:
- Using the deployed Lambda function: testing the end-to-end functionality, use this if you don't need to change the code of the Lambda itself
- Using your local Lambda function: if you want to make changes to the code of the Lambda, but don't need end-to-end testing

### Use the deployed Lambda function

Execute these commands to authenticate with AWS and start listening on the Lambda results queue in AWS:

```
php artisan tms:aws:auth
docker compose up horizon-sqs
```

### Use your local Lambda function

Note that the local Lambda function will not receive triggers from the files put on S3, so you'll need to manually trigger the function. The output of the function will still be stored on S3, but it will not put a message on the SQS queue, so you won't see the results in TMS.

Start the Lambda container:

```
php artisan tms:aws:auth
source .env && docker run --rm -p 9027:8080 -v ./docker/lambda/zipservice/app.js:/var/task/app.js -e AWS_LAMBDA_FUNCTION_TIMEOUT=900 -e AWS_LAMBDA_FUNCTION_MEMORY_SIZE=1024 -e AWS_ACCESS_KEY_ID=${AWS_KEY} -e AWS_SECRET_ACCESS_KEY=${AWS_SECRET} -e AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} $(docker build -q ./docker/lambda/zipservice)
```

Upload any test file to S3 (bingeltms-fl-dev bucket), then create a test.json file with the path to that file and upload it to the same bucket.

```
[
    {
        "key" : "path/to/file/on/s3/file.jpg",
        "name": "attachments/folder/in/zip/file.jpg"
    }
]
```

Invoke the function by referring to the bucket and the path of the input.json file uploaded earlier:

```
curl -XPOST "http://localhost:9027/2015-03-31/functions/function/invocations" -d '{"Records": [{"s3": {"bucket": {"name": "bingeltms-fl-dev"}, "object": {"key": "test.json"}}}]}'
```

The result should be a ZIP file being created in the same bucket.

## How to publish a new version?

- In Bitbucket click "Run pipeline", choose the master branch and "custom: deploy-zipservice" as pipeline.
- In the vnn-bingeltms-cloudformation repository, change the version number of the image in shared/includes/lambda/zipservice.yaml.
