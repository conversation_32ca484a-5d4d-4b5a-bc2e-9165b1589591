events {

}

user nginx;
pid /tmp/nginx.pid;

http {
    access_log /dev/null;

    server {
        listen 80;
        include /etc/nginx/mime.types;
        root /usr/share/nginx/html/dist;

        server_name *.bingeltms.test;

        index index.php;

        charset utf-8;

        client_max_body_size 500M;

        location ^~ /resources {
            alias /usr/share/nginx/html/resources/;
        }

        location ^~ /dist {
            alias /usr/share/nginx/html/dist;
        }

        location ~* .(eot|ttf|woff|woff2|js) {
            add_header Access-Control-Allow-Origin http://internal.bingeltms.test;
        }

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        error_page 404 /index.php;

        fastcgi_read_timeout 400;
        fastcgi_buffers 8 8k;
        fastcgi_buffer_size 16k;


        location ~ \.php$ {
            fastcgi_pass fpm:9000;
            fastcgi_param SCRIPT_FILENAME /opt/local/bingeltms/public/index.php;
            include /etc/nginx/fastcgi_params;
        }

        location ~ /\.(?!well-known).* {
            deny all;
        }
    }
}
